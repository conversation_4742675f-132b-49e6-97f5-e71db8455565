# 游戏管理后台系统 (Game Management Web)

## 项目架构

本项目采用混合架构设计，结合 MAUI Blazor Hybrid 和 React Next.js 的优势：

### 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    游戏管理后台系统                          │
├─────────────────────────────────────────────────────────────┤
│  客户端层 (Client Layer)                                    │
│  ┌─────────────────────┐  ┌─────────────────────────────────┐│
│  │  MAUI Blazor Hybrid │  │     React Next.js Web App      ││
│  │  (桌面/移动客户端)    │  │        (Web 管理端)           ││
│  │  - 实时监控面板      │  │  - 数据分析和报表             ││
│  │  - 快速操作工具      │  │  - 复杂表单和配置             ││
│  │  - 离线功能支持      │  │  - 用户管理界面               ││
│  └─────────────────────┘  └─────────────────────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  API 网关层 (API Gateway)                                   │
│  ┌─────────────────────────────────────────────────────────┐│
│  │  ASP.NET Core Web API                                   ││
│  │  - 统一身份认证 (JWT)                                   ││
│  │  - 请求路由和负载均衡                                   ││
│  │  - 限流和安全控制                                       ││
│  │  - API 版本管理                                         ││
│  └─────────────────────────────────────────────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  业务服务层 (Business Services)                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐│
│  │ 运营数据服务 │ │ 游戏数据服务 │ │ 客服管理服务 │ │ 监控服务 ││
│  │ - 用户统计   │ │ - 角色管理   │ │ - 公告系统   │ │ - 服务器 ││
│  │ - 付费分析   │ │ - 经济系统   │ │ - 用户处理   │ │ - 数据库 ││
│  │ - 留存分析   │ │ - 活动管理   │ │ - 奖励发放   │ │ - 告警   ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘│
├─────────────────────────────────────────────────────────────┤
│  数据访问层 (Data Access Layer)                             │
│  ┌─────────────────────────────────────────────────────────┐│
│  │  Entity Framework Core + Repository Pattern             ││
│  │  - 数据模型映射                                         ││
│  │  - 查询优化                                             ││
│  │  - 事务管理                                             ││
│  └─────────────────────────────────────────────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  数据存储层 (Data Storage)                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐│
│  │ SQL Server  │ │    Redis    │ │ File Storage│ │ MongoDB ││
│  │ (主数据库)   │ │  (缓存)     │ │ (文件存储)   │ │ (日志)   ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘│
└─────────────────────────────────────────────────────────────┘
```

## 技术栈

### 前端技术
- **MAUI Blazor Hybrid**: 跨平台桌面和移动应用
- **React 18 + Next.js 14**: 现代化 Web 应用
- **TypeScript**: 类型安全的 JavaScript
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Ant Design**: 企业级 UI 组件库
- **Chart.js / Recharts**: 数据可视化

### 后端技术
- **ASP.NET Core 8**: Web API 框架
- **Entity Framework Core**: ORM 框架
- **SignalR**: 实时通信
- **AutoMapper**: 对象映射
- **FluentValidation**: 数据验证
- **Serilog**: 结构化日志

### 数据存储
- **SQL Server**: 主数据库
- **Redis**: 缓存和会话存储
- **MongoDB**: 日志和文档存储
- **Azure Blob Storage**: 文件存储

### 开发工具
- **Docker**: 容器化部署
- **GitHub Actions**: CI/CD 流水线
- **SonarQube**: 代码质量分析
- **Swagger**: API 文档

## 项目结构

```
gamemanageweb/
├── src/
│   ├── GameManagement.MAUI/              # MAUI Blazor Hybrid 应用
│   │   ├── Components/                   # Blazor 组件
│   │   ├── Services/                     # 客户端服务
│   │   ├── Models/                       # 数据模型
│   │   └── Platforms/                    # 平台特定代码
│   │
│   ├── GameManagement.Web/               # React Next.js Web 应用
│   │   ├── components/                   # React 组件
│   │   ├── pages/                        # 页面路由
│   │   ├── services/                     # API 服务
│   │   ├── hooks/                        # 自定义 Hooks
│   │   ├── utils/                        # 工具函数
│   │   └── types/                        # TypeScript 类型定义
│   │
│   ├── GameManagement.API/               # ASP.NET Core Web API
│   │   ├── Controllers/                  # API 控制器
│   │   ├── Services/                     # 业务服务
│   │   ├── Models/                       # 数据传输对象
│   │   ├── Middleware/                   # 中间件
│   │   └── Configuration/                # 配置文件
│   │
│   ├── GameManagement.Core/              # 核心业务逻辑
│   │   ├── Entities/                     # 实体模型
│   │   ├── Interfaces/                   # 接口定义
│   │   ├── Services/                     # 业务服务
│   │   └── Specifications/               # 查询规范
│   │
│   ├── GameManagement.Infrastructure/    # 基础设施层
│   │   ├── Data/                         # 数据访问
│   │   ├── Repositories/                 # 仓储实现
│   │   ├── Services/                     # 外部服务
│   │   └── Configuration/                # 配置实现
│   │
│   └── GameManagement.Shared/            # 共享库
│       ├── DTOs/                         # 数据传输对象
│       ├── Enums/                        # 枚举定义
│       ├── Constants/                    # 常量定义
│       └── Extensions/                   # 扩展方法
│
├── tests/                                # 测试项目
├── docs/                                 # 文档
├── scripts/                              # 脚本文件
└── docker/                               # Docker 配置
```

## 开发计划

### 第一阶段：基础架构 (2-3周)
1. 创建解决方案和项目结构
2. 配置数据库和基础实体
3. 实现身份认证和授权
4. 搭建基础 API 框架
5. 创建基础 UI 框架

### 第二阶段：核心功能 (4-6周)
1. 运营数据模块
2. 用户管理功能
3. 基础报表功能
4. 权限管理系统

### 第三阶段：业务功能 (6-8周)
1. 游戏数据管理
2. 客服管理功能
3. 活动和商城管理
4. 高级报表和分析

### 第四阶段：运维功能 (4-6周)
1. 服务器健康监控
2. 数据库管理
3. 日志和告警系统
4. 自动化运维工具

## 快速开始

### 环境要求
- .NET 8 SDK
- Node.js 18+
- SQL Server 2019+
- Redis 6+
- Visual Studio 2022 或 VS Code

### 安装步骤
1. 克隆项目
2. 配置数据库连接
3. 运行数据库迁移
4. 启动后端 API
5. 启动前端应用

详细的安装和配置说明请参考 [安装指南](docs/installation.md)。

## 贡献指南

请参考 [贡献指南](docs/contributing.md) 了解如何参与项目开发。

## 许可证

本项目采用 MIT 许可证，详情请参考 [LICENSE](LICENSE) 文件。
