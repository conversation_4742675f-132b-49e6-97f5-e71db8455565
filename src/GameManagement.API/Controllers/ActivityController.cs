using GameManagement.Core.Interfaces;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace GameManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ActivityController : ControllerBase
{
    private readonly IActivityService _activityService;
    private readonly ILogger<ActivityController> _logger;

    public ActivityController(IActivityService activityService, ILogger<ActivityController> logger)
    {
        _activityService = activityService;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有活动
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<ActivityDto>>> GetActivities()
    {
        try
        {
            var activities = await _activityService.GetActivitiesAsync();
            return Ok(activities);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting activities");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 根据ID获取活动
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<ActivityDto>> GetActivity(int id)
    {
        try
        {
            var activity = await _activityService.GetActivityByIdAsync(id);
            if (activity == null)
                return NotFound();

            return Ok(activity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting activity {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取活跃的活动
    /// </summary>
    [HttpGet("active")]
    public async Task<ActionResult<IEnumerable<ActivityDto>>> GetActiveActivities()
    {
        try
        {
            var activities = await _activityService.GetActiveActivitiesAsync();
            return Ok(activities);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active activities");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 根据状态获取活动
    /// </summary>
    [HttpGet("status/{status}")]
    public async Task<ActionResult<IEnumerable<ActivityDto>>> GetActivitiesByStatus(ActivityStatus status)
    {
        try
        {
            var activities = await _activityService.GetActivitiesByStatusAsync(status);
            return Ok(activities);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting activities by status {Status}", status);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 根据日期范围获取活动
    /// </summary>
    [HttpGet("date-range")]
    public async Task<ActionResult<IEnumerable<ActivityDto>>> GetActivitiesByDateRange(
        [FromQuery] DateTime startDate, 
        [FromQuery] DateTime endDate)
    {
        try
        {
            var activities = await _activityService.GetActivitiesByDateRangeAsync(startDate, endDate);
            return Ok(activities);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting activities by date range {StartDate} - {EndDate}", startDate, endDate);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 创建新活动
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "SystemAdmin,ProductManager")]
    public async Task<ActionResult<ActivityDto>> CreateActivity([FromBody] CreateActivityDto createActivityDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var activity = await _activityService.CreateActivityAsync(createActivityDto);
            return CreatedAtAction(nameof(GetActivity), new { id = activity.Id }, activity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating activity");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 更新活动
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "SystemAdmin,ProductManager")]
    public async Task<ActionResult<ActivityDto>> UpdateActivity(int id, [FromBody] UpdateActivityDto updateActivityDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var activity = await _activityService.UpdateActivityAsync(id, updateActivityDto);
            return Ok(activity);
        }
        catch (ArgumentException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating activity {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 删除活动
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "SystemAdmin")]
    public async Task<ActionResult> DeleteActivity(int id)
    {
        try
        {
            var success = await _activityService.DeleteActivityAsync(id);
            if (!success)
                return NotFound();

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting activity {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 启动活动
    /// </summary>
    [HttpPost("{id}/start")]
    [Authorize(Roles = "SystemAdmin,ProductManager")]
    public async Task<ActionResult> StartActivity(int id)
    {
        try
        {
            var success = await _activityService.StartActivityAsync(id);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting activity {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 暂停活动
    /// </summary>
    [HttpPost("{id}/pause")]
    [Authorize(Roles = "SystemAdmin,ProductManager")]
    public async Task<ActionResult> PauseActivity(int id)
    {
        try
        {
            var success = await _activityService.PauseActivityAsync(id);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error pausing activity {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 恢复活动
    /// </summary>
    [HttpPost("{id}/resume")]
    [Authorize(Roles = "SystemAdmin,ProductManager")]
    public async Task<ActionResult> ResumeActivity(int id)
    {
        try
        {
            var success = await _activityService.ResumeActivityAsync(id);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resuming activity {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 结束活动
    /// </summary>
    [HttpPost("{id}/end")]
    [Authorize(Roles = "SystemAdmin,ProductManager")]
    public async Task<ActionResult> EndActivity(int id)
    {
        try
        {
            var success = await _activityService.EndActivityAsync(id);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ending activity {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 取消活动
    /// </summary>
    [HttpPost("{id}/cancel")]
    [Authorize(Roles = "SystemAdmin")]
    public async Task<ActionResult> CancelActivity(int id)
    {
        try
        {
            var success = await _activityService.CancelActivityAsync(id);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling activity {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取活动统计
    /// </summary>
    [HttpGet("{id}/stats")]
    public async Task<ActionResult<ActivityStatsDto>> GetActivityStats(int id)
    {
        try
        {
            var stats = await _activityService.GetActivityStatsAsync(id);
            return Ok(stats);
        }
        catch (ArgumentException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting activity stats {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取所有活动统计
    /// </summary>
    [HttpGet("stats")]
    public async Task<ActionResult<IEnumerable<ActivityStatsDto>>> GetAllActivitiesStats()
    {
        try
        {
            var stats = await _activityService.GetAllActivitiesStatsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all activities stats");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取活动参与趋势
    /// </summary>
    [HttpGet("{id}/participation-trends")]
    public async Task<ActionResult<Dictionary<string, int>>> GetActivityParticipationTrends(int id, [FromQuery] int days = 30)
    {
        try
        {
            var trends = await _activityService.GetActivityParticipationTrendsAsync(id, days);
            return Ok(trends);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting participation trends for activity {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取活动性能指标
    /// </summary>
    [HttpGet("{id}/performance-metrics")]
    public async Task<ActionResult<Dictionary<string, object>>> GetActivityPerformanceMetrics(int id)
    {
        try
        {
            var metrics = await _activityService.GetActivityPerformanceMetricsAsync(id);
            return Ok(metrics);
        }
        catch (ArgumentException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance metrics for activity {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 添加玩家到活动
    /// </summary>
    [HttpPost("{activityId}/participants/{playerId}")]
    [Authorize(Roles = "SystemAdmin,ProductManager,CustomerServiceManager")]
    public async Task<ActionResult> AddPlayerToActivity(int activityId, int playerId)
    {
        try
        {
            var success = await _activityService.AddPlayerToActivityAsync(activityId, playerId);
            if (!success)
                return BadRequest("Failed to add player to activity");

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding player {PlayerId} to activity {ActivityId}", playerId, activityId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 从活动中移除玩家
    /// </summary>
    [HttpDelete("{activityId}/participants/{playerId}")]
    [Authorize(Roles = "SystemAdmin,ProductManager,CustomerServiceManager")]
    public async Task<ActionResult> RemovePlayerFromActivity(int activityId, int playerId)
    {
        try
        {
            var success = await _activityService.RemovePlayerFromActivityAsync(activityId, playerId);
            if (!success)
                return NotFound();

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing player {PlayerId} from activity {ActivityId}", playerId, activityId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 完成玩家活动
    /// </summary>
    [HttpPost("{activityId}/participants/{playerId}/complete")]
    [Authorize(Roles = "SystemAdmin,ProductManager,CustomerServiceManager")]
    public async Task<ActionResult> CompleteActivityForPlayer(int activityId, int playerId, [FromBody] CompleteActivityDto? completeDto = null)
    {
        try
        {
            var success = await _activityService.CompleteActivityForPlayerAsync(activityId, playerId, completeDto?.RewardsReceived);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing activity {ActivityId} for player {PlayerId}", activityId, playerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取活动参与者
    /// </summary>
    [HttpGet("{id}/participants")]
    public async Task<ActionResult<IEnumerable<PlayerActivityDto>>> GetActivityParticipants(int id)
    {
        try
        {
            var participants = await _activityService.GetActivityParticipantsAsync(id);
            return Ok(participants);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting participants for activity {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取玩家参与的活动
    /// </summary>
    [HttpGet("player/{playerId}")]
    public async Task<ActionResult<IEnumerable<ActivityDto>>> GetPlayerActivities(int playerId)
    {
        try
        {
            var activities = await _activityService.GetPlayerActivitiesAsync(playerId);
            return Ok(activities);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting activities for player {PlayerId}", playerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 更新活动奖励
    /// </summary>
    [HttpPut("{id}/rewards")]
    [Authorize(Roles = "SystemAdmin,ProductManager")]
    public async Task<ActionResult> UpdateActivityRewards(int id, [FromBody] UpdateRewardsDto updateRewardsDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var success = await _activityService.UpdateActivityRewardsAsync(id, updateRewardsDto.Rewards);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating rewards for activity {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 更新活动条件
    /// </summary>
    [HttpPut("{id}/conditions")]
    [Authorize(Roles = "SystemAdmin,ProductManager")]
    public async Task<ActionResult> UpdateActivityConditions(int id, [FromBody] UpdateConditionsDto updateConditionsDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var success = await _activityService.UpdateActivityConditionsAsync(id, updateConditionsDto.Conditions);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating conditions for activity {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 分发活动奖励
    /// </summary>
    [HttpPost("{id}/distribute-rewards")]
    [Authorize(Roles = "SystemAdmin,ProductManager")]
    public async Task<ActionResult> DistributeRewards(int id)
    {
        try
        {
            var success = await _activityService.DistributeRewardsAsync(id);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error distributing rewards for activity {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取活动状态摘要
    /// </summary>
    [HttpGet("status-summary")]
    public async Task<ActionResult<Dictionary<ActivityStatus, int>>> GetActivityStatusSummary()
    {
        try
        {
            var summary = await _activityService.GetActivityStatusSummaryAsync();
            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting activity status summary");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取即将到期的活动
    /// </summary>
    [HttpGet("expiring")]
    public async Task<ActionResult<IEnumerable<ActivityDto>>> GetExpiringActivities([FromQuery] int daysAhead = 7)
    {
        try
        {
            var activities = await _activityService.GetExpiringActivitiesAsync(daysAhead);
            return Ok(activities);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting expiring activities");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取热门活动
    /// </summary>
    [HttpGet("popular")]
    public async Task<ActionResult<IEnumerable<ActivityDto>>> GetPopularActivities([FromQuery] int limit = 10)
    {
        try
        {
            var activities = await _activityService.GetPopularActivitiesAsync(limit);
            return Ok(activities);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting popular activities");
            return StatusCode(500, "Internal server error");
        }
    }
}

// Helper DTOs for controller actions
public class CompleteActivityDto
{
    public string? RewardsReceived { get; set; }
}

public class UpdateRewardsDto
{
    public string Rewards { get; set; } = string.Empty;
}

public class UpdateConditionsDto
{
    public string Conditions { get; set; } = string.Empty;
}
