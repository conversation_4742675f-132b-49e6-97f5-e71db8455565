using GameManagement.Core.Interfaces;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace GameManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "SystemAdmin")]
public class AnnouncementController : ControllerBase
{
    private readonly IAnnouncementService _announcementService;
    private readonly ILogger<AnnouncementController> _logger;

    public AnnouncementController(IAnnouncementService announcementService, ILogger<AnnouncementController> logger)
    {
        _announcementService = announcementService;
        _logger = logger;
    }

    // 基础CRUD操作
    [HttpGet]
    public async Task<ActionResult<IEnumerable<GameAnnouncementDto>>> GetAnnouncements([FromQuery] int page = 1, [FromQuery] int pageSize = 50)
    {
        try
        {
            var announcements = await _announcementService.GetAnnouncementsAsync(page, pageSize);
            return Ok(announcements);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcements");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<GameAnnouncementDto>> GetAnnouncement(int id)
    {
        try
        {
            var announcement = await _announcementService.GetAnnouncementByIdAsync(id);
            if (announcement == null)
                return NotFound($"Announcement with ID {id} not found");

            return Ok(announcement);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcement: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost]
    public async Task<ActionResult<GameAnnouncementDto>> CreateAnnouncement([FromBody] CreateGameAnnouncementDto createAnnouncementDto)
    {
        try
        {
            var announcement = await _announcementService.CreateAnnouncementAsync(createAnnouncementDto);
            return CreatedAtAction(nameof(GetAnnouncement), new { id = announcement.Id }, announcement);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating announcement");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPut("{id}")]
    public async Task<ActionResult<GameAnnouncementDto>> UpdateAnnouncement(int id, [FromBody] UpdateGameAnnouncementDto updateAnnouncementDto)
    {
        try
        {
            var announcement = await _announcementService.UpdateAnnouncementAsync(id, updateAnnouncementDto);
            return Ok(announcement);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating announcement: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteAnnouncement(int id)
    {
        try
        {
            var result = await _announcementService.DeleteAnnouncementAsync(id);
            if (!result)
                return NotFound($"Announcement with ID {id} not found");

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting announcement: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    // 状态管理
    [HttpPost("{id}/activate")]
    public async Task<ActionResult> ActivateAnnouncement(int id)
    {
        try
        {
            var result = await _announcementService.ActivateAnnouncementAsync(id);
            if (!result)
                return NotFound($"Announcement with ID {id} not found");

            return Ok(new { message = "Announcement activated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating announcement: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("{id}/deactivate")]
    public async Task<ActionResult> DeactivateAnnouncement(int id)
    {
        try
        {
            var result = await _announcementService.DeactivateAnnouncementAsync(id);
            if (!result)
                return NotFound($"Announcement with ID {id} not found");

            return Ok(new { message = "Announcement deactivated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating announcement: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("{id}/publish")]
    public async Task<ActionResult> PublishAnnouncement(int id)
    {
        try
        {
            var result = await _announcementService.PublishAnnouncementAsync(id);
            if (!result)
                return NotFound($"Announcement with ID {id} not found");

            return Ok(new { message = "Announcement published successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing announcement: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("{id}/unpublish")]
    public async Task<ActionResult> UnpublishAnnouncement(int id)
    {
        try
        {
            var result = await _announcementService.UnpublishAnnouncementAsync(id);
            if (!result)
                return NotFound($"Announcement with ID {id} not found");

            return Ok(new { message = "Announcement unpublished successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unpublishing announcement: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    // 筛选和搜索
    [HttpGet("type/{type}")]
    public async Task<ActionResult<IEnumerable<GameAnnouncementDto>>> GetAnnouncementsByType(AnnouncementType type)
    {
        try
        {
            var announcements = await _announcementService.GetAnnouncementsByTypeAsync(type);
            return Ok(announcements);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcements by type: {Type}", type);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("active")]
    public async Task<ActionResult<IEnumerable<GameAnnouncementDto>>> GetActiveAnnouncements()
    {
        try
        {
            var announcements = await _announcementService.GetActiveAnnouncementsAsync();
            return Ok(announcements);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active announcements");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("date-range")]
    public async Task<ActionResult<IEnumerable<GameAnnouncementDto>>> GetAnnouncementsByDateRange([FromQuery] DateTime startDate, [FromQuery] DateTime endDate)
    {
        try
        {
            var announcements = await _announcementService.GetAnnouncementsByDateRangeAsync(startDate, endDate);
            return Ok(announcements);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcements by date range");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<GameAnnouncementDto>>> SearchAnnouncements([FromQuery] string searchTerm)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return BadRequest("Search term is required");

            var announcements = await _announcementService.SearchAnnouncementsAsync(searchTerm);
            return Ok(announcements);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching announcements");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("priority/{minPriority}")]
    public async Task<ActionResult<IEnumerable<GameAnnouncementDto>>> GetAnnouncementsByPriority(int minPriority)
    {
        try
        {
            var announcements = await _announcementService.GetAnnouncementsByPriorityAsync(minPriority);
            return Ok(announcements);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcements by priority");
            return StatusCode(500, "Internal server error");
        }
    }

    // 目标管理
    [HttpGet("server/{serverId}")]
    public async Task<ActionResult<IEnumerable<GameAnnouncementDto>>> GetAnnouncementsForServer(int serverId)
    {
        try
        {
            var announcements = await _announcementService.GetAnnouncementsForServerAsync(serverId);
            return Ok(announcements);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcements for server: {ServerId}", serverId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("player/{playerId}")]
    public async Task<ActionResult<IEnumerable<GameAnnouncementDto>>> GetAnnouncementsForPlayer(string playerId)
    {
        try
        {
            var announcements = await _announcementService.GetAnnouncementsForPlayerAsync(playerId);
            return Ok(announcements);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcements for player: {PlayerId}", playerId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPut("{id}/targets")]
    public async Task<ActionResult> UpdateAnnouncementTargets(int id, [FromBody] UpdateAnnouncementTargetsDto targetsDto)
    {
        try
        {
            var result = await _announcementService.UpdateAnnouncementTargetsAsync(id, targetsDto);
            if (!result)
                return NotFound($"Announcement with ID {id} not found");

            return Ok(new { message = "Announcement targets updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating announcement targets: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    // 批量操作
    [HttpPost("batch/activate")]
    public async Task<ActionResult> BatchActivateAnnouncements([FromBody] IEnumerable<int> announcementIds)
    {
        try
        {
            var count = await _announcementService.BatchActivateAnnouncementsAsync(announcementIds);
            return Ok(new { message = $"Successfully activated {count} announcements", count });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch activating announcements");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("batch/deactivate")]
    public async Task<ActionResult> BatchDeactivateAnnouncements([FromBody] IEnumerable<int> announcementIds)
    {
        try
        {
            var count = await _announcementService.BatchDeactivateAnnouncementsAsync(announcementIds);
            return Ok(new { message = $"Successfully deactivated {count} announcements", count });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch deactivating announcements");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("batch/delete")]
    public async Task<ActionResult> BatchDeleteAnnouncements([FromBody] IEnumerable<int> announcementIds)
    {
        try
        {
            var count = await _announcementService.BatchDeleteAnnouncementsAsync(announcementIds);
            return Ok(new { message = $"Successfully deleted {count} announcements", count });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch deleting announcements");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("batch/publish")]
    public async Task<ActionResult> BatchPublishAnnouncements([FromBody] IEnumerable<int> announcementIds)
    {
        try
        {
            var count = await _announcementService.BatchPublishAnnouncementsAsync(announcementIds);
            return Ok(new { message = $"Successfully published {count} announcements", count });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch publishing announcements");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("batch/unpublish")]
    public async Task<ActionResult> BatchUnpublishAnnouncements([FromBody] IEnumerable<int> announcementIds)
    {
        try
        {
            var count = await _announcementService.BatchUnpublishAnnouncementsAsync(announcementIds);
            return Ok(new { message = $"Successfully unpublished {count} announcements", count });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch unpublishing announcements");
            return StatusCode(500, "Internal server error");
        }
    }

    // 统计和分析
    [HttpGet("stats")]
    public async Task<ActionResult<AnnouncementStatsDto>> GetAnnouncementStats()
    {
        try
        {
            var stats = await _announcementService.GetAnnouncementStatsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcement stats");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("stats/views")]
    public async Task<ActionResult<IEnumerable<AnnouncementViewStatsDto>>> GetAnnouncementViewStats([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var stats = await _announcementService.GetAnnouncementViewStatsAsync(startDate, endDate);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcement view stats");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("popular")]
    public async Task<ActionResult<IEnumerable<GameAnnouncementDto>>> GetPopularAnnouncements([FromQuery] int limit = 10)
    {
        try
        {
            var announcements = await _announcementService.GetPopularAnnouncementsAsync(limit);
            return Ok(announcements);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting popular announcements");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{id}/performance")]
    public async Task<ActionResult<AnnouncementPerformanceDto>> GetAnnouncementPerformance(int id, [FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var performance = await _announcementService.GetAnnouncementPerformanceAsync(id, startDate, endDate);
            return Ok(performance);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcement performance: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    // 高级功能
    [HttpPost("{id}/schedule")]
    public async Task<ActionResult> ScheduleAnnouncement(int id, [FromBody] DateTime publishTime)
    {
        try
        {
            var result = await _announcementService.ScheduleAnnouncementAsync(id, publishTime);
            if (!result)
                return NotFound($"Announcement with ID {id} not found");

            return Ok(new { message = "Announcement scheduled successfully", publishTime });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling announcement: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPut("{id}/priority")]
    public async Task<ActionResult> UpdateAnnouncementPriority(int id, [FromBody] int priority)
    {
        try
        {
            var result = await _announcementService.UpdateAnnouncementPriorityAsync(id, priority);
            if (!result)
                return NotFound($"Announcement with ID {id} not found");

            return Ok(new { message = "Announcement priority updated successfully", priority });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating announcement priority: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("{id}/view")]
    public async Task<ActionResult> IncrementViewCount(int id)
    {
        try
        {
            var result = await _announcementService.IncrementViewCountAsync(id);
            if (!result)
                return NotFound($"Announcement with ID {id} not found");

            return Ok(new { message = "View count incremented successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing view count: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("scheduled")]
    public async Task<ActionResult<IEnumerable<GameAnnouncementDto>>> GetScheduledAnnouncements()
    {
        try
        {
            var announcements = await _announcementService.GetScheduledAnnouncementsAsync();
            return Ok(announcements);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting scheduled announcements");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("expired")]
    public async Task<ActionResult<IEnumerable<GameAnnouncementDto>>> GetExpiredAnnouncements()
    {
        try
        {
            var announcements = await _announcementService.GetExpiredAnnouncementsAsync();
            return Ok(announcements);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting expired announcements");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("cleanup")]
    public async Task<ActionResult> CleanupExpiredAnnouncements()
    {
        try
        {
            var count = await _announcementService.CleanupExpiredAnnouncementsAsync();
            return Ok(new { message = $"Successfully cleaned up {count} expired announcements", count });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired announcements");
            return StatusCode(500, "Internal server error");
        }
    }

    // 导入导出
    [HttpPost("import")]
    public async Task<ActionResult<IEnumerable<GameAnnouncementDto>>> ImportAnnouncements([FromBody] IEnumerable<CreateGameAnnouncementDto> announcements)
    {
        try
        {
            var importedAnnouncements = await _announcementService.ImportAnnouncementsAsync(announcements);
            return Ok(importedAnnouncements);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing announcements");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("export")]
    public async Task<ActionResult> ExportAnnouncements([FromQuery] AnnouncementExportFormat format = AnnouncementExportFormat.JSON, [FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var exportData = await _announcementService.ExportAnnouncementsAsync(format, startDate, endDate);

            var contentType = format switch
            {
                AnnouncementExportFormat.JSON => "application/json",
                AnnouncementExportFormat.CSV => "text/csv",
                AnnouncementExportFormat.Excel => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                AnnouncementExportFormat.PDF => "application/pdf",
                _ => "text/plain"
            };

            var fileName = $"announcements_export_{DateTime.UtcNow:yyyyMMdd_HHmmss}.{format.ToString().ToLower()}";

            return File(System.Text.Encoding.UTF8.GetBytes(exportData), contentType, fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting announcements");
            return StatusCode(500, "Internal server error");
        }
    }
}
