using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GameManagement.Core.Interfaces;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using System.Security.Claims;

namespace GameManagement.API.Controllers;

public partial class ChannelController
{
    /// <summary>
    /// 批量更新佣金率
    /// </summary>
    [HttpPost("batch/update-commission-rate")]
    [Authorize(Roles = "SystemAdmin,Admin")]
    public async Task<ActionResult> BatchUpdateCommissionRate([FromBody] BatchUpdateCommissionRateRequest request)
    {
        try
        {
            if (!ModelState.IsValid || !request.ChannelCommissions.Any())
            {
                return BadRequest("Channel commissions are required");
            }

            var result = await _channelService.BatchUpdateCommissionRateAsync(request.ChannelCommissions);
            if (!result)
            {
                return BadRequest("Failed to update commission rates");
            }

            return Ok(new { message = $"Successfully updated commission rates for {request.ChannelCommissions.Count} channels" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch updating commission rates");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 批量删除渠道
    /// </summary>
    [HttpPost("batch/delete")]
    [Authorize(Roles = "SystemAdmin,Admin")]
    public async Task<ActionResult> BatchDeleteChannels([FromBody] BatchOperationRequest request)
    {
        try
        {
            if (!ModelState.IsValid || !request.ChannelIds.Any())
            {
                return BadRequest("Channel IDs are required");
            }

            var result = await _channelService.BatchDeleteChannelsAsync(request.ChannelIds);
            if (!result)
            {
                return BadRequest("Failed to delete channels");
            }

            return Ok(new { message = $"Successfully processed deletion for {request.ChannelIds.Count()} channels" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch deleting channels");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 导入渠道
    /// </summary>
    [HttpPost("import")]
    [Authorize(Roles = "SystemAdmin,Admin")]
    public async Task<ActionResult<IEnumerable<ChannelDto>>> ImportChannels([FromBody] ImportChannelsRequest request)
    {
        try
        {
            if (!ModelState.IsValid || !request.Channels.Any())
            {
                return BadRequest("Channels data is required");
            }

            var importedChannels = await _channelService.ImportChannelsAsync(request.Channels);
            return Ok(new { 
                message = $"Successfully imported {importedChannels.Count()} channels",
                channels = importedChannels
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing channels");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 导出渠道
    /// </summary>
    [HttpGet("export")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult> ExportChannels([FromQuery] ChannelExportFormat format = ChannelExportFormat.JSON, [FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var exportData = await _channelService.ExportChannelsAsync(format, startDate, endDate);
            
            var contentType = format switch
            {
                ChannelExportFormat.JSON => "application/json",
                ChannelExportFormat.CSV => "text/csv",
                ChannelExportFormat.Excel => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ChannelExportFormat.PDF => "application/pdf",
                _ => "application/octet-stream"
            };

            var fileName = $"channels_export_{DateTime.UtcNow:yyyyMMdd_HHmmss}.{format.ToString().ToLower()}";
            
            return File(System.Text.Encoding.UTF8.GetBytes(exportData), contentType, fileName);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting channels");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 导出渠道数据
    /// </summary>
    [HttpGet("{channelId}/export-data")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult> ExportChannelData(int channelId, [FromQuery] ChannelExportFormat format = ChannelExportFormat.JSON, [FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var exportData = await _channelService.ExportChannelDataAsync(channelId, format, startDate, endDate);
            
            var contentType = format switch
            {
                ChannelExportFormat.JSON => "application/json",
                ChannelExportFormat.CSV => "text/csv",
                ChannelExportFormat.Excel => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ChannelExportFormat.PDF => "application/pdf",
                _ => "application/octet-stream"
            };

            var fileName = $"channel_{channelId}_data_export_{DateTime.UtcNow:yyyyMMdd_HHmmss}.{format.ToString().ToLower()}";
            
            return File(System.Text.Encoding.UTF8.GetBytes(exportData), contentType, fileName);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting channel data for channel {ChannelId}", channelId);
            return StatusCode(500, "Internal server error");
        }
    }
}

// 请求DTOs
public class CompareChannelsRequest
{
    public IEnumerable<int> ChannelIds { get; set; } = new List<int>();
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
}

public class UpdateCommissionRateRequest
{
    public decimal CommissionRate { get; set; }
}

public class ValidateApiKeyRequest
{
    public string ApiKey { get; set; } = string.Empty;
}

public class UpdateContractRequest
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
}

public class RenewContractRequest
{
    public DateTime NewEndDate { get; set; }
}

public class BatchOperationRequest
{
    public IEnumerable<int> ChannelIds { get; set; } = new List<int>();
}

public class BatchUpdateCommissionRateRequest
{
    public Dictionary<int, decimal> ChannelCommissions { get; set; } = new Dictionary<int, decimal>();
}

public class ImportChannelsRequest
{
    public IEnumerable<CreateChannelDto> Channels { get; set; } = new List<CreateChannelDto>();
}
