using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GameManagement.Core.Interfaces;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using System.Security.Claims;

namespace GameManagement.API.Controllers;

public partial class ChannelController
{
    /// <summary>
    /// 比较渠道性能
    /// </summary>
    [HttpPost("compare")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<ChannelComparisonDto>> CompareChannels([FromBody] CompareChannelsRequest request)
    {
        try
        {
            if (!ModelState.IsValid || !request.ChannelIds.Any())
            {
                return BadRequest("Channel IDs are required");
            }

            var comparison = await _channelService.CompareChannelsAsync(request.ChannelIds, request.StartDate, request.EndDate);
            return Ok(comparison);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error comparing channels");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 更新渠道佣金率
    /// </summary>
    [HttpPut("{id}/commission-rate")]
    [Authorize(Roles = "SystemAdmin,Admin,ChannelManager")]
    public async Task<ActionResult> UpdateChannelCommissionRate(int id, [FromBody] UpdateCommissionRateRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _channelService.UpdateChannelCommissionRateAsync(id, request.CommissionRate);
            if (!result)
            {
                return NotFound("Channel not found");
            }

            return Ok(new { message = "Commission rate updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating commission rate for channel {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 计算渠道佣金
    /// </summary>
    [HttpGet("{channelId}/commission")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<decimal>> CalculateChannelCommission(int channelId, [FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var commission = await _channelService.CalculateChannelCommissionAsync(channelId, startDate, endDate);
            return Ok(new { channelId, commission, startDate, endDate });
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating commission for channel {ChannelId}", channelId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取所有渠道佣金
    /// </summary>
    [HttpGet("commissions")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<IEnumerable<ChannelCommissionDto>>> GetChannelCommissions([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var commissions = await _channelService.GetChannelCommissionsAsync(startDate, endDate);
            return Ok(commissions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving channel commissions");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 生成API密钥
    /// </summary>
    [HttpPost("{channelId}/generate-api-key")]
    [Authorize(Roles = "SystemAdmin,Admin")]
    public async Task<ActionResult> GenerateApiKey(int channelId)
    {
        try
        {
            var result = await _channelService.GenerateApiKeyAsync(channelId);
            if (!result)
            {
                return NotFound("Channel not found");
            }

            return Ok(new { message = "API key generated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating API key for channel {ChannelId}", channelId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 重新生成API密钥
    /// </summary>
    [HttpPost("{channelId}/regenerate-api-key")]
    [Authorize(Roles = "SystemAdmin,Admin")]
    public async Task<ActionResult> RegenerateApiKey(int channelId)
    {
        try
        {
            var result = await _channelService.RegenerateApiKeyAsync(channelId);
            if (!result)
            {
                return NotFound("Channel not found");
            }

            return Ok(new { message = "API key regenerated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error regenerating API key for channel {ChannelId}", channelId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 撤销API密钥
    /// </summary>
    [HttpPost("{channelId}/revoke-api-key")]
    [Authorize(Roles = "SystemAdmin,Admin")]
    public async Task<ActionResult> RevokeApiKey(int channelId)
    {
        try
        {
            var result = await _channelService.RevokeApiKeyAsync(channelId);
            if (!result)
            {
                return NotFound("Channel not found");
            }

            return Ok(new { message = "API key revoked successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking API key for channel {ChannelId}", channelId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 验证API密钥
    /// </summary>
    [HttpPost("validate-api-key")]
    [AllowAnonymous]
    public async Task<ActionResult> ValidateApiKey([FromBody] ValidateApiKeyRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.ApiKey))
            {
                return BadRequest("API key is required");
            }

            var isValid = await _channelService.ValidateApiKeyAsync(request.ApiKey);
            return Ok(new { isValid });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating API key");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 根据API密钥获取渠道
    /// </summary>
    [HttpGet("by-api-key/{apiKey}")]
    [Authorize(Roles = "SystemAdmin,Admin")]
    public async Task<ActionResult<ChannelDto>> GetChannelByApiKey(string apiKey)
    {
        try
        {
            var channel = await _channelService.GetChannelByApiKeyAsync(apiKey);
            if (channel == null)
            {
                return NotFound("Channel not found or inactive");
            }

            return Ok(channel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving channel by API key");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 更新渠道合同
    /// </summary>
    [HttpPut("{id}/contract")]
    [Authorize(Roles = "SystemAdmin,Admin,ChannelManager")]
    public async Task<ActionResult> UpdateChannelContract(int id, [FromBody] UpdateContractRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _channelService.UpdateChannelContractAsync(id, request.StartDate, request.EndDate);
            if (!result)
            {
                return NotFound("Channel not found");
            }

            return Ok(new { message = "Contract updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating contract for channel {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 续签渠道合同
    /// </summary>
    [HttpPost("{id}/renew-contract")]
    [Authorize(Roles = "SystemAdmin,Admin,ChannelManager")]
    public async Task<ActionResult> RenewChannelContract(int id, [FromBody] RenewContractRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _channelService.RenewChannelContractAsync(id, request.NewEndDate);
            if (!result)
            {
                return NotFound("Channel not found");
            }

            return Ok(new { message = "Contract renewed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error renewing contract for channel {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取合同已过期的渠道
    /// </summary>
    [HttpGet("expired-contracts")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<IEnumerable<ChannelDto>>> GetChannelsWithExpiredContracts()
    {
        try
        {
            var channels = await _channelService.GetChannelsWithExpiredContractsAsync();
            return Ok(channels);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving channels with expired contracts");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 批量激活渠道
    /// </summary>
    [HttpPost("batch/activate")]
    [Authorize(Roles = "SystemAdmin,Admin")]
    public async Task<ActionResult> BatchActivateChannels([FromBody] BatchOperationRequest request)
    {
        try
        {
            if (!ModelState.IsValid || !request.ChannelIds.Any())
            {
                return BadRequest("Channel IDs are required");
            }

            var result = await _channelService.BatchActivateChannelsAsync(request.ChannelIds);
            if (!result)
            {
                return BadRequest("Failed to activate channels");
            }

            return Ok(new { message = $"Successfully activated {request.ChannelIds.Count()} channels" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch activating channels");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 批量停用渠道
    /// </summary>
    [HttpPost("batch/deactivate")]
    [Authorize(Roles = "SystemAdmin,Admin")]
    public async Task<ActionResult> BatchDeactivateChannels([FromBody] BatchOperationRequest request)
    {
        try
        {
            if (!ModelState.IsValid || !request.ChannelIds.Any())
            {
                return BadRequest("Channel IDs are required");
            }

            var result = await _channelService.BatchDeactivateChannelsAsync(request.ChannelIds);
            if (!result)
            {
                return BadRequest("Failed to deactivate channels");
            }

            return Ok(new { message = $"Successfully deactivated {request.ChannelIds.Count()} channels" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch deactivating channels");
            return StatusCode(500, "Internal server error");
        }
    }
}
