using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GameManagement.Core.Interfaces;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using System.Security.Claims;

namespace GameManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public partial class ChannelController : ControllerBase
{
    private readonly IChannelService _channelService;
    private readonly ILogger<ChannelController> _logger;

    public ChannelController(IChannelService channelService, ILogger<ChannelController> logger)
    {
        _channelService = channelService;
        _logger = logger;
    }

    /// <summary>
    /// 获取渠道列表
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<IEnumerable<ChannelDto>>> GetChannels([FromQuery] int page = 1, [FromQuery] int pageSize = 50)
    {
        try
        {
            var channels = await _channelService.GetChannelsAsync(page, pageSize);
            return Ok(channels);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving channels");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 根据ID获取渠道
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<ChannelDto>> GetChannel(int id)
    {
        try
        {
            var channel = await _channelService.GetChannelByIdAsync(id);
            if (channel == null)
            {
                return NotFound();
            }
            return Ok(channel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving channel {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 根据代码获取渠道
    /// </summary>
    [HttpGet("code/{code}")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<ChannelDto>> GetChannelByCode(string code)
    {
        try
        {
            var channel = await _channelService.GetChannelByCodeAsync(code);
            if (channel == null)
            {
                return NotFound();
            }
            return Ok(channel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving channel by code {Code}", code);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 创建新渠道
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "SystemAdmin,Admin,ChannelManager")]
    public async Task<ActionResult<ChannelDto>> CreateChannel([FromBody] CreateChannelDto createChannelDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var channel = await _channelService.CreateChannelAsync(createChannelDto);
            return CreatedAtAction(nameof(GetChannel), new { id = channel.Id }, channel);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating channel");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 更新渠道
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "SystemAdmin,Admin,ChannelManager")]
    public async Task<ActionResult<ChannelDto>> UpdateChannel(int id, [FromBody] UpdateChannelDto updateChannelDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var channel = await _channelService.UpdateChannelAsync(id, updateChannelDto);
            return Ok(channel);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating channel {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 删除渠道
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "SystemAdmin,Admin")]
    public async Task<ActionResult> DeleteChannel(int id)
    {
        try
        {
            var result = await _channelService.DeleteChannelAsync(id);
            if (!result)
            {
                return NotFound();
            }
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting channel {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 激活渠道
    /// </summary>
    [HttpPost("{id}/activate")]
    [Authorize(Roles = "SystemAdmin,Admin,ChannelManager")]
    public async Task<ActionResult> ActivateChannel(int id)
    {
        try
        {
            var result = await _channelService.ActivateChannelAsync(id);
            if (!result)
            {
                return NotFound();
            }
            return Ok(new { message = "Channel activated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating channel {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 停用渠道
    /// </summary>
    [HttpPost("{id}/deactivate")]
    [Authorize(Roles = "SystemAdmin,Admin,ChannelManager")]
    public async Task<ActionResult> DeactivateChannel(int id)
    {
        try
        {
            var result = await _channelService.DeactivateChannelAsync(id);
            if (!result)
            {
                return NotFound();
            }
            return Ok(new { message = "Channel deactivated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating channel {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取活跃渠道
    /// </summary>
    [HttpGet("active")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<IEnumerable<ChannelDto>>> GetActiveChannels()
    {
        try
        {
            var channels = await _channelService.GetActiveChannelsAsync();
            return Ok(channels);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active channels");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取非活跃渠道
    /// </summary>
    [HttpGet("inactive")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<IEnumerable<ChannelDto>>> GetInactiveChannels()
    {
        try
        {
            var channels = await _channelService.GetInactiveChannelsAsync();
            return Ok(channels);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving inactive channels");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 搜索渠道
    /// </summary>
    [HttpGet("search")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<IEnumerable<ChannelDto>>> SearchChannels([FromQuery] string searchTerm)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return BadRequest("Search term is required");
            }

            var channels = await _channelService.SearchChannelsAsync(searchTerm);
            return Ok(channels);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching channels with term {SearchTerm}", searchTerm);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 根据佣金率范围获取渠道
    /// </summary>
    [HttpGet("commission-range")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<IEnumerable<ChannelDto>>> GetChannelsByCommissionRange([FromQuery] decimal minRate, [FromQuery] decimal maxRate)
    {
        try
        {
            if (minRate < 0 || maxRate < 0 || minRate > maxRate)
            {
                return BadRequest("Invalid commission rate range");
            }

            var channels = await _channelService.GetChannelsByCommissionRangeAsync(minRate, maxRate);
            return Ok(channels);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving channels by commission range {MinRate}-{MaxRate}", minRate, maxRate);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取有活跃合同的渠道
    /// </summary>
    [HttpGet("active-contracts")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<IEnumerable<ChannelDto>>> GetChannelsWithActiveContracts()
    {
        try
        {
            var channels = await _channelService.GetChannelsWithActiveContractsAsync();
            return Ok(channels);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving channels with active contracts");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取合同即将到期的渠道
    /// </summary>
    [HttpGet("expiring-contracts")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<IEnumerable<ChannelDto>>> GetChannelsWithExpiringContracts([FromQuery] int daysFromNow = 30)
    {
        try
        {
            var channels = await _channelService.GetChannelsWithExpiringContractsAsync(daysFromNow);
            return Ok(channels);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving channels with expiring contracts");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 添加渠道数据
    /// </summary>
    [HttpPost("{channelId}/data")]
    [Authorize(Roles = "SystemAdmin,Admin,ChannelManager")]
    public async Task<ActionResult> AddChannelData(int channelId, [FromBody] AddChannelDataRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _channelService.AddChannelDataAsync(channelId, request.MetricName, request.Value, request.Date, request.AdditionalData);
            if (!result)
            {
                return NotFound("Channel not found");
            }

            return Ok(new { message = "Channel data added successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding channel data for channel {ChannelId}", channelId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取渠道数据
    /// </summary>
    [HttpGet("{channelId}/data")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<IEnumerable<ChannelDataDto>>> GetChannelData(int channelId, [FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var channelData = await _channelService.GetChannelDataAsync(channelId, startDate, endDate);
            return Ok(channelData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving channel data for channel {ChannelId}", channelId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 根据指标获取渠道数据
    /// </summary>
    [HttpGet("{channelId}/data/{metricName}")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<IEnumerable<ChannelDataDto>>> GetChannelDataByMetric(int channelId, string metricName, [FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var channelData = await _channelService.GetChannelDataByMetricAsync(channelId, metricName, startDate, endDate);
            return Ok(channelData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving channel data by metric for channel {ChannelId}, metric {MetricName}", channelId, metricName);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 更新渠道数据
    /// </summary>
    [HttpPut("data/{dataId}")]
    [Authorize(Roles = "SystemAdmin,Admin,ChannelManager")]
    public async Task<ActionResult> UpdateChannelData(int dataId, [FromBody] UpdateChannelDataRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _channelService.UpdateChannelDataAsync(dataId, request.Value, request.AdditionalData);
            if (!result)
            {
                return NotFound("Channel data not found");
            }

            return Ok(new { message = "Channel data updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating channel data {DataId}", dataId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 删除渠道数据
    /// </summary>
    [HttpDelete("data/{dataId}")]
    [Authorize(Roles = "SystemAdmin,Admin")]
    public async Task<ActionResult> DeleteChannelData(int dataId)
    {
        try
        {
            var result = await _channelService.DeleteChannelDataAsync(dataId);
            if (!result)
            {
                return NotFound("Channel data not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting channel data {DataId}", dataId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取渠道统计
    /// </summary>
    [HttpGet("stats")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<ChannelStatsDto>> GetChannelStats()
    {
        try
        {
            var stats = await _channelService.GetChannelStatsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving channel stats");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取渠道性能
    /// </summary>
    [HttpGet("{channelId}/performance")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<ChannelPerformanceDto>> GetChannelPerformance(int channelId, [FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var performance = await _channelService.GetChannelPerformanceAsync(channelId, startDate, endDate);
            return Ok(performance);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving channel performance for channel {ChannelId}", channelId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取所有渠道性能
    /// </summary>
    [HttpGet("performance")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<IEnumerable<ChannelPerformanceDto>>> GetAllChannelsPerformance([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var performances = await _channelService.GetAllChannelsPerformanceAsync(startDate, endDate);
            return Ok(performances);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all channels performance");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取表现最佳的渠道
    /// </summary>
    [HttpGet("top-performing")]
    [Authorize(Roles = "SystemAdmin,Admin,ProductManager,ProductSpecialist,ChannelManager")]
    public async Task<ActionResult<IEnumerable<ChannelDto>>> GetTopPerformingChannels([FromQuery] int count = 10, [FromQuery] string metric = "revenue")
    {
        try
        {
            var channels = await _channelService.GetTopPerformingChannelsAsync(count, metric);
            return Ok(channels);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving top performing channels");
            return StatusCode(500, "Internal server error");
        }
    }

    private string GetCurrentUsername()
    {
        return User.FindFirst(ClaimTypes.Name)?.Value ?? "Unknown";
    }
}

// 请求DTOs
public class AddChannelDataRequest
{
    public string MetricName { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public DateTime Date { get; set; }
    public string? AdditionalData { get; set; }
}

public class UpdateChannelDataRequest
{
    public decimal Value { get; set; }
    public string? AdditionalData { get; set; }
}
