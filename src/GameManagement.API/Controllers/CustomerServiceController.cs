using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GameManagement.Core.Interfaces;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;

namespace GameManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class CustomerServiceController : ControllerBase
{
    private readonly ICustomerServiceService _customerServiceService;
    private readonly ILogger<CustomerServiceController> _logger;

    public CustomerServiceController(ICustomerServiceService customerServiceService, ILogger<CustomerServiceController> logger)
    {
        _customerServiceService = customerServiceService;
        _logger = logger;
    }

    // Ticket Management Endpoints
    [HttpGet("tickets")]
    public async Task<ActionResult<IEnumerable<CustomerServiceTicketDto>>> GetTickets(
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 20)
    {
        try
        {
            var tickets = await _customerServiceService.GetTicketsAsync(page, pageSize);
            return Ok(tickets);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tickets");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("tickets/{id}")]
    public async Task<ActionResult<CustomerServiceTicketDto>> GetTicket(int id)
    {
        try
        {
            var ticket = await _customerServiceService.GetTicketByIdAsync(id);
            if (ticket == null)
                return NotFound();

            return Ok(ticket);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving ticket {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("tickets/ticket-id/{ticketId}")]
    public async Task<ActionResult<CustomerServiceTicketDto>> GetTicketByTicketId(string ticketId)
    {
        try
        {
            var ticket = await _customerServiceService.GetTicketByTicketIdAsync(ticketId);
            if (ticket == null)
                return NotFound();

            return Ok(ticket);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving ticket by ticket ID {TicketId}", ticketId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("tickets/status/{status}")]
    public async Task<ActionResult<IEnumerable<CustomerServiceTicketDto>>> GetTicketsByStatus(TicketStatus status)
    {
        try
        {
            var tickets = await _customerServiceService.GetTicketsByStatusAsync(status);
            return Ok(tickets);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tickets by status {Status}", status);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("tickets/priority/{priority}")]
    public async Task<ActionResult<IEnumerable<CustomerServiceTicketDto>>> GetTicketsByPriority(TicketPriority priority)
    {
        try
        {
            var tickets = await _customerServiceService.GetTicketsByPriorityAsync(priority);
            return Ok(tickets);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tickets by priority {Priority}", priority);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("tickets/category/{category}")]
    public async Task<ActionResult<IEnumerable<CustomerServiceTicketDto>>> GetTicketsByCategory(TicketCategory category)
    {
        try
        {
            var tickets = await _customerServiceService.GetTicketsByCategoryAsync(category);
            return Ok(tickets);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tickets by category {Category}", category);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("tickets/player/{playerId}")]
    public async Task<ActionResult<IEnumerable<CustomerServiceTicketDto>>> GetTicketsByPlayer(int playerId)
    {
        try
        {
            var tickets = await _customerServiceService.GetTicketsByPlayerAsync(playerId);
            return Ok(tickets);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tickets by player {PlayerId}", playerId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("tickets/assigned/{userId}")]
    public async Task<ActionResult<IEnumerable<CustomerServiceTicketDto>>> GetAssignedTickets(int userId)
    {
        try
        {
            var tickets = await _customerServiceService.GetAssignedTicketsAsync(userId);
            return Ok(tickets);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving assigned tickets for user {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("tickets/search")]
    public async Task<ActionResult<IEnumerable<CustomerServiceTicketDto>>> SearchTickets([FromQuery] string searchTerm)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return BadRequest("Search term is required");

            var tickets = await _customerServiceService.SearchTicketsAsync(searchTerm);
            return Ok(tickets);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching tickets with term {SearchTerm}", searchTerm);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("tickets")]
    public async Task<ActionResult<CustomerServiceTicketDto>> CreateTicket([FromBody] CreateCustomerServiceTicketDto createTicketDto)
    {
        try
        {
            var ticket = await _customerServiceService.CreateTicketAsync(createTicketDto);
            if (ticket == null)
            {
                return BadRequest("Failed to create ticket");
            }
            return CreatedAtAction(nameof(GetTicket), new { id = ticket.Id }, ticket);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating ticket");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPut("tickets/{id}")]
    public async Task<ActionResult<CustomerServiceTicketDto>> UpdateTicket(int id, [FromBody] UpdateCustomerServiceTicketDto updateTicketDto)
    {
        try
        {
            var ticket = await _customerServiceService.UpdateTicketAsync(id, updateTicketDto);
            return Ok(ticket);
        }
        catch (ArgumentException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating ticket {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("tickets/{ticketId}/assign/{userId}")]
    public async Task<ActionResult> AssignTicket(int ticketId, int userId)
    {
        try
        {
            var success = await _customerServiceService.AssignTicketAsync(ticketId, userId);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning ticket {TicketId} to user {UserId}", ticketId, userId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("tickets/{ticketId}/unassign")]
    public async Task<ActionResult> UnassignTicket(int ticketId)
    {
        try
        {
            var success = await _customerServiceService.UnassignTicketAsync(ticketId);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unassigning ticket {TicketId}", ticketId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("tickets/{ticketId}/status")]
    public async Task<ActionResult> ChangeTicketStatus(int ticketId, [FromBody] ChangeTicketStatusDto statusDto)
    {
        try
        {
            var success = await _customerServiceService.ChangeTicketStatusAsync(ticketId, statusDto.Status);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing ticket {TicketId} status", ticketId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("tickets/{ticketId}/priority")]
    public async Task<ActionResult> ChangeTicketPriority(int ticketId, [FromBody] ChangeTicketPriorityDto priorityDto)
    {
        try
        {
            var success = await _customerServiceService.ChangeTicketPriorityAsync(ticketId, priorityDto.Priority);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing ticket {TicketId} priority", ticketId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("tickets/{ticketId}/resolve")]
    public async Task<ActionResult> ResolveTicket(int ticketId, [FromBody] ResolveTicketDto resolveDto)
    {
        try
        {
            var success = await _customerServiceService.ResolveTicketAsync(ticketId, resolveDto.Resolution);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving ticket {TicketId}", ticketId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("tickets/{ticketId}/close")]
    public async Task<ActionResult> CloseTicket(int ticketId)
    {
        try
        {
            var success = await _customerServiceService.CloseTicketAsync(ticketId);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error closing ticket {TicketId}", ticketId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("tickets/{ticketId}/reopen")]
    public async Task<ActionResult> ReopenTicket(int ticketId)
    {
        try
        {
            var success = await _customerServiceService.ReopenTicketAsync(ticketId);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reopening ticket {TicketId}", ticketId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("stats")]
    public async Task<ActionResult<CustomerServiceStatsDto>> GetCustomerServiceStats()
    {
        try
        {
            var stats = await _customerServiceService.GetCustomerServiceStatsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving customer service stats");
            return StatusCode(500, "Internal server error");
        }
    }

    // Message Management Endpoints
    [HttpGet("tickets/{ticketId}/messages")]
    public async Task<ActionResult<IEnumerable<TicketMessageDto>>> GetTicketMessages(int ticketId)
    {
        try
        {
            var messages = await _customerServiceService.GetTicketMessagesAsync(ticketId);
            return Ok(messages);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving messages for ticket {TicketId}", ticketId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("tickets/{ticketId}/messages")]
    public async Task<ActionResult<TicketMessageDto>> AddTicketMessage(int ticketId, [FromBody] CreateTicketMessageDto createMessageDto)
    {
        try
        {
            var message = await _customerServiceService.AddTicketMessageAsync(ticketId, createMessageDto);
            return Ok(message);
        }
        catch (ArgumentException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding message to ticket {TicketId}", ticketId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("tickets/{ticketId}/internal-notes")]
    public async Task<ActionResult<TicketMessageDto>> AddInternalNote(int ticketId, [FromBody] CreateTicketMessageDto createMessageDto)
    {
        try
        {
            var message = await _customerServiceService.AddInternalNoteAsync(ticketId, createMessageDto);
            return Ok(message);
        }
        catch (ArgumentException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding internal note to ticket {TicketId}", ticketId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpDelete("messages/{messageId}")]
    public async Task<ActionResult> DeleteTicketMessage(int messageId)
    {
        try
        {
            var success = await _customerServiceService.DeleteTicketMessageAsync(messageId);
            if (!success)
                return NotFound();

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting message {MessageId}", messageId);
            return StatusCode(500, "Internal server error");
        }
    }

    // Customer Dialogue Endpoints
    [HttpGet("dialogues/active/{userId}")]
    public async Task<ActionResult<IEnumerable<CustomerServiceTicketDto>>> GetActiveDialogues(int userId)
    {
        try
        {
            var dialogues = await _customerServiceService.GetActiveDialoguesAsync(userId);
            return Ok(dialogues);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active dialogues for user {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("dialogues/{ticketId}/start/{userId}")]
    public async Task<ActionResult> StartDialogue(int ticketId, int userId)
    {
        try
        {
            var success = await _customerServiceService.StartDialogueAsync(ticketId, userId);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting dialogue for ticket {TicketId}", ticketId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("dialogues/{ticketId}/end")]
    public async Task<ActionResult> EndDialogue(int ticketId)
    {
        try
        {
            var success = await _customerServiceService.EndDialogueAsync(ticketId);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ending dialogue for ticket {TicketId}", ticketId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("dialogues/stats/{userId}")]
    public async Task<ActionResult<CustomerDialogueStatsDto>> GetDialogueStats(int userId)
    {
        try
        {
            var stats = await _customerServiceService.GetDialogueStatsAsync(userId);
            return Ok(stats);
        }
        catch (ArgumentException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving dialogue stats for user {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    // Satisfaction & Feedback Endpoints
    [HttpPost("tickets/{ticketId}/satisfaction")]
    public async Task<ActionResult> SubmitSatisfactionRating(int ticketId, [FromBody] SatisfactionRatingDto ratingDto)
    {
        try
        {
            var success = await _customerServiceService.SubmitSatisfactionRatingAsync(ticketId, ratingDto.Rating, ratingDto.Feedback);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting satisfaction rating for ticket {TicketId}", ticketId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("satisfaction/stats")]
    public async Task<ActionResult<SatisfactionStatsDto>> GetSatisfactionStats()
    {
        try
        {
            var stats = await _customerServiceService.GetSatisfactionStatsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving satisfaction stats");
            return StatusCode(500, "Internal server error");
        }
    }
}

// Helper DTOs for controller actions
public class ChangeTicketStatusDto
{
    public TicketStatus Status { get; set; }
}

public class ChangeTicketPriorityDto
{
    public TicketPriority Priority { get; set; }
}

public class ResolveTicketDto
{
    public string Resolution { get; set; } = string.Empty;
}

public class SatisfactionRatingDto
{
    public int Rating { get; set; }
    public string? Feedback { get; set; }
}
