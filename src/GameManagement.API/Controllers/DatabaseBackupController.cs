using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GameManagement.Core.Interfaces;
using GameManagement.Shared.DTOs;

namespace GameManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class DatabaseBackupController : ControllerBase
{
    private readonly IDatabaseBackupService _backupService;
    private readonly ILogger<DatabaseBackupController> _logger;

    public DatabaseBackupController(
        IDatabaseBackupService backupService,
        ILogger<DatabaseBackupController> logger)
    {
        _backupService = backupService;
        _logger = logger;
    }

    // 备份管理
    [HttpGet]
    public async Task<ActionResult<IEnumerable<DatabaseBackupDto>>> GetBackups()
    {
        try
        {
            var result = await _backupService.GetBackupsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting backups");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{backupId}")]
    public async Task<ActionResult<DatabaseBackupDto>> GetBackup(int backupId)
    {
        try
        {
            var result = await _backupService.GetBackupByIdAsync(backupId);
            if (result == null)
                return NotFound($"Backup with ID {backupId} not found");

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting backup {BackupId}", backupId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost]
    public async Task<ActionResult<DatabaseBackupDto>> CreateBackup([FromBody] CreateDatabaseBackupDto createBackupDto)
    {
        try
        {
            var result = await _backupService.CreateBackupAsync(createBackupDto);
            return CreatedAtAction(nameof(GetBackup), new { backupId = result.Id }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating backup");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpDelete("{backupId}")]
    public async Task<ActionResult> DeleteBackup(int backupId)
    {
        try
        {
            var result = await _backupService.DeleteBackupAsync(backupId);
            if (!result)
                return NotFound($"Backup with ID {backupId} not found");

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting backup {BackupId}", backupId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("{backupId}/restore")]
    public async Task<ActionResult> RestoreBackup(int backupId)
    {
        try
        {
            var result = await _backupService.RestoreBackupAsync(backupId);
            if (!result)
                return NotFound($"Backup with ID {backupId} not found or cannot be restored");

            return Ok(new { message = "Backup restore initiated" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error restoring backup {BackupId}", backupId);
            return StatusCode(500, "Internal server error");
        }
    }

    // 备份调度
    [HttpGet("schedules")]
    public async Task<ActionResult<IEnumerable<BackupScheduleDto>>> GetBackupSchedules()
    {
        try
        {
            var result = await _backupService.GetBackupSchedulesAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting backup schedules");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("schedules/{scheduleId}")]
    public async Task<ActionResult<BackupScheduleDto>> GetBackupSchedule(int scheduleId)
    {
        try
        {
            var result = await _backupService.GetBackupScheduleByIdAsync(scheduleId);
            if (result == null)
                return NotFound($"Backup schedule with ID {scheduleId} not found");

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting backup schedule {ScheduleId}", scheduleId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("schedules")]
    public async Task<ActionResult<BackupScheduleDto>> CreateBackupSchedule([FromBody] CreateBackupScheduleDto createScheduleDto)
    {
        try
        {
            var result = await _backupService.CreateBackupScheduleAsync(createScheduleDto);
            return CreatedAtAction(nameof(GetBackupSchedule), new { scheduleId = result.Id }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating backup schedule");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPut("schedules/{scheduleId}")]
    public async Task<ActionResult<BackupScheduleDto>> UpdateBackupSchedule(int scheduleId, [FromBody] UpdateBackupScheduleDto updateScheduleDto)
    {
        try
        {
            var result = await _backupService.UpdateBackupScheduleAsync(scheduleId, updateScheduleDto);
            if (result == null)
                return NotFound($"Backup schedule with ID {scheduleId} not found");

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating backup schedule {ScheduleId}", scheduleId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpDelete("schedules/{scheduleId}")]
    public async Task<ActionResult> DeleteBackupSchedule(int scheduleId)
    {
        try
        {
            var result = await _backupService.DeleteBackupScheduleAsync(scheduleId);
            if (!result)
                return NotFound($"Backup schedule with ID {scheduleId} not found");

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting backup schedule {ScheduleId}", scheduleId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPut("schedules/{scheduleId}/toggle")]
    public async Task<ActionResult> ToggleBackupSchedule(int scheduleId, [FromBody] bool isEnabled)
    {
        try
        {
            var result = await _backupService.ToggleBackupScheduleAsync(scheduleId, isEnabled);
            if (!result)
                return NotFound($"Backup schedule with ID {scheduleId} not found");

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling backup schedule {ScheduleId}", scheduleId);
            return StatusCode(500, "Internal server error");
        }
    }

    // 备份验证
    [HttpPost("{backupId}/validate")]
    public async Task<ActionResult<BackupValidationResultDto>> ValidateBackup(int backupId)
    {
        try
        {
            var result = await _backupService.ValidateBackupAsync(backupId);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating backup {BackupId}", backupId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("validate-all")]
    public async Task<ActionResult<IEnumerable<BackupValidationResultDto>>> ValidateAllBackups()
    {
        try
        {
            var result = await _backupService.ValidateAllBackupsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating all backups");
            return StatusCode(500, "Internal server error");
        }
    }

    // 备份统计
    [HttpGet("stats")]
    public async Task<ActionResult<BackupStatsDto>> GetBackupStats()
    {
        try
        {
            var result = await _backupService.GetBackupStatsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting backup stats");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{backupId}/size")]
    public async Task<ActionResult<long>> GetBackupSize(int backupId)
    {
        try
        {
            var result = await _backupService.GetBackupSizeAsync(backupId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting backup size for backup {BackupId}", backupId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("expired")]
    public async Task<ActionResult<IEnumerable<DatabaseBackupDto>>> GetExpiredBackups()
    {
        try
        {
            var result = await _backupService.GetExpiredBackupsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting expired backups");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("cleanup-expired")]
    public async Task<ActionResult<int>> CleanupExpiredBackups()
    {
        try
        {
            var result = await _backupService.CleanupExpiredBackupsAsync();
            return Ok(new { deletedCount = result });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired backups");
            return StatusCode(500, "Internal server error");
        }
    }

    // 远程存储
    [HttpPost("{backupId}/upload")]
    public async Task<ActionResult> UploadBackupToRemote(int backupId, [FromBody] string remoteLocation)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(remoteLocation))
                return BadRequest("Remote location cannot be empty");

            var result = await _backupService.UploadBackupToRemoteAsync(backupId, remoteLocation);
            if (!result)
                return NotFound($"Backup with ID {backupId} not found or upload failed");

            return Ok(new { message = "Backup uploaded to remote location" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading backup {BackupId} to remote", backupId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("download")]
    public async Task<ActionResult> DownloadBackupFromRemote([FromBody] DownloadBackupRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.RemoteLocation))
                return BadRequest("Remote location cannot be empty");

            if (string.IsNullOrWhiteSpace(request.LocalPath))
                return BadRequest("Local path cannot be empty");

            var result = await _backupService.DownloadBackupFromRemoteAsync(request.RemoteLocation, request.LocalPath);
            if (!result)
                return BadRequest("Download failed");

            return Ok(new { message = "Backup downloaded from remote location" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading backup from remote {RemoteLocation}", request.RemoteLocation);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("remote")]
    public async Task<ActionResult<IEnumerable<RemoteBackupDto>>> GetRemoteBackups()
    {
        try
        {
            var result = await _backupService.GetRemoteBackupsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting remote backups");
            return StatusCode(500, "Internal server error");
        }
    }
}

public class DownloadBackupRequest
{
    public string RemoteLocation { get; set; } = string.Empty;
    public string LocalPath { get; set; } = string.Empty;
}
