using GameManagement.Core.Interfaces;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using LogLevel = GameManagement.Shared.Enums.LogLevel;

namespace GameManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class GameServerController : ControllerBase
{
    private readonly IGameServerService _gameServerService;
    private readonly ILogger<GameServerController> _logger;

    public GameServerController(IGameServerService gameServerService, ILogger<GameServerController> logger)
    {
        _gameServerService = gameServerService;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有游戏服务器
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<ServerStatusDto>>> GetAllServers()
    {
        try
        {
            var servers = await _gameServerService.GetAllServersAsync();
            return Ok(servers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all servers");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 根据ID获取服务器信息
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<ServerStatusDto>> GetServerById(int id)
    {
        try
        {
            var server = await _gameServerService.GetServerByIdAsync(id);
            if (server == null)
                return NotFound($"Server with ID {id} not found");

            return Ok(server);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server {ServerId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 根据名称获取服务器信息
    /// </summary>
    [HttpGet("by-name/{name}")]
    public async Task<ActionResult<ServerStatusDto>> GetServerByName(string name)
    {
        try
        {
            var server = await _gameServerService.GetServerByNameAsync(name);
            if (server == null)
                return NotFound($"Server with name '{name}' not found");

            return Ok(server);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server by name {ServerName}", name);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 创建新的游戏服务器
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "SystemAdmin")]
    public async Task<ActionResult<ServerStatusDto>> CreateServer([FromBody] CreateServerDto createServerDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var server = await _gameServerService.CreateServerAsync(createServerDto);
            return CreatedAtAction(nameof(GetServerById), new { id = server.Id }, server);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating server");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 更新服务器信息
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "SystemAdmin")]
    public async Task<ActionResult<ServerStatusDto>> UpdateServer(int id, [FromBody] UpdateServerDto updateServerDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var server = await _gameServerService.UpdateServerAsync(id, updateServerDto);
            if (server == null)
                return NotFound($"Server with ID {id} not found");

            return Ok(server);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating server {ServerId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 删除服务器
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "SystemAdmin")]
    public async Task<ActionResult> DeleteServer(int id)
    {
        try
        {
            var result = await _gameServerService.DeleteServerAsync(id);
            if (!result)
                return NotFound($"Server with ID {id} not found or cannot be deleted");

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting server {ServerId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 更新服务器状态
    /// </summary>
    [HttpPut("{id}/status")]
    public async Task<ActionResult> UpdateServerStatus(int id, [FromBody] ServerStatus status)
    {
        try
        {
            var result = await _gameServerService.UpdateServerStatusAsync(id, status);
            if (!result)
                return NotFound($"Server with ID {id} not found");

            return Ok(new { message = "Server status updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating server status {ServerId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 更新服务器玩家数量
    /// </summary>
    [HttpPut("{id}/player-count")]
    public async Task<ActionResult> UpdatePlayerCount(int id, [FromBody] int currentPlayers)
    {
        try
        {
            var result = await _gameServerService.UpdatePlayerCountAsync(id, currentPlayers);
            if (!result)
                return NotFound($"Server with ID {id} not found");

            return Ok(new { message = "Player count updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating player count for server {ServerId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 根据状态获取服务器列表
    /// </summary>
    [HttpGet("by-status/{status}")]
    public async Task<ActionResult<IEnumerable<ServerStatusDto>>> GetServersByStatus(ServerStatus status)
    {
        try
        {
            var servers = await _gameServerService.GetServersByStatusAsync(status);
            return Ok(servers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting servers by status {Status}", status);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取在线服务器列表
    /// </summary>
    [HttpGet("online")]
    public async Task<ActionResult<IEnumerable<ServerStatusDto>>> GetOnlineServers()
    {
        try
        {
            var servers = await _gameServerService.GetOnlineServersAsync();
            return Ok(servers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting online servers");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 重启服务器
    /// </summary>
    [HttpPost("{id}/restart")]
    [Authorize(Roles = "SystemAdmin")]
    public async Task<ActionResult> RestartServer(int id)
    {
        try
        {
            var result = await _gameServerService.RestartServerAsync(id);
            if (!result)
                return NotFound($"Server with ID {id} not found");

            return Ok(new { message = "Server restart initiated" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error restarting server {ServerId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 启动服务器
    /// </summary>
    [HttpPost("{id}/start")]
    [Authorize(Roles = "SystemAdmin")]
    public async Task<ActionResult> StartServer(int id)
    {
        try
        {
            var result = await _gameServerService.StartServerAsync(id);
            if (!result)
                return NotFound($"Server with ID {id} not found");

            return Ok(new { message = "Server started successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting server {ServerId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 停止服务器
    /// </summary>
    [HttpPost("{id}/stop")]
    [Authorize(Roles = "SystemAdmin")]
    public async Task<ActionResult> StopServer(int id)
    {
        try
        {
            var result = await _gameServerService.StopServerAsync(id);
            if (!result)
                return NotFound($"Server with ID {id} not found");

            return Ok(new { message = "Server stopped successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping server {ServerId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 发送服务器命令
    /// </summary>
    [HttpPost("{id}/command")]
    [Authorize(Roles = "SystemAdmin")]
    public async Task<ActionResult> SendServerCommand(int id, [FromBody] string command)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(command))
                return BadRequest("Command cannot be empty");

            var result = await _gameServerService.SendServerCommandAsync(id, command);
            if (!result)
                return NotFound($"Server with ID {id} not found");

            return Ok(new { message = "Command sent successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending command to server {ServerId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取服务器统计信息
    /// </summary>
    [HttpGet("{id}/stats")]
    public async Task<ActionResult<ServerStatsDto>> GetServerStats(int id)
    {
        try
        {
            var stats = await _gameServerService.GetServerStatsAsync(id);
            return Ok(stats);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server stats {ServerId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取所有服务器统计信息
    /// </summary>
    [HttpGet("stats")]
    public async Task<ActionResult<IEnumerable<ServerStatsDto>>> GetAllServerStats()
    {
        try
        {
            var stats = await _gameServerService.GetAllServerStatsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all server stats");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取服务器性能数据
    /// </summary>
    [HttpGet("{id}/performance")]
    public async Task<ActionResult<ServerPerformanceDto>> GetServerPerformance(
        int id,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var performance = await _gameServerService.GetServerPerformanceAsync(id, startDate, endDate);
            return Ok(performance);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server performance {ServerId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取服务器日志
    /// </summary>
    [HttpGet("{id}/logs")]
    public async Task<ActionResult<IEnumerable<ServerLogDto>>> GetServerLogs(
        int id,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 50,
        [FromQuery] LogLevel? level = null)
    {
        try
        {
            if (page < 1) page = 1;
            if (pageSize < 1 || pageSize > 100) pageSize = 50;

            var logs = await _gameServerService.GetServerLogsAsync(id, page, pageSize, level);
            return Ok(logs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server logs {ServerId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 添加服务器日志
    /// </summary>
    [HttpPost("{id}/logs")]
    public async Task<ActionResult<ServerLogDto>> AddServerLog(int id, [FromBody] CreateServerLogDto createLogDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            // 确保日志的ServerId与路径参数一致
            createLogDto.ServerId = id;

            var log = await _gameServerService.AddServerLogAsync(createLogDto);
            return CreatedAtAction(nameof(GetServerLogs), new { id }, log);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding server log");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取服务器维护记录
    /// </summary>
    [HttpGet("{id}/maintenances")]
    public async Task<ActionResult<IEnumerable<ServerMaintenanceDto>>> GetServerMaintenances(int id)
    {
        try
        {
            var maintenances = await _gameServerService.GetServerMaintenancesAsync(id);
            return Ok(maintenances);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server maintenances {ServerId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 安排服务器维护
    /// </summary>
    [HttpPost("{id}/maintenances")]
    [Authorize(Roles = "SystemAdmin")]
    public async Task<ActionResult<ServerMaintenanceDto>> ScheduleMaintenance(int id, [FromBody] CreateServerMaintenanceDto createMaintenanceDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            // 确保维护的ServerId与路径参数一致
            createMaintenanceDto.ServerId = id;

            var maintenance = await _gameServerService.ScheduleMaintenanceAsync(createMaintenanceDto);
            return CreatedAtAction(nameof(GetServerMaintenances), new { id }, maintenance);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling maintenance");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 开始维护
    /// </summary>
    [HttpPost("maintenances/{maintenanceId}/start")]
    [Authorize(Roles = "SystemAdmin")]
    public async Task<ActionResult> StartMaintenance(int maintenanceId)
    {
        try
        {
            var result = await _gameServerService.StartMaintenanceAsync(maintenanceId);
            if (!result)
                return NotFound($"Maintenance with ID {maintenanceId} not found");

            return Ok(new { message = "Maintenance started successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting maintenance {MaintenanceId}", maintenanceId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 完成维护
    /// </summary>
    [HttpPost("maintenances/{maintenanceId}/complete")]
    [Authorize(Roles = "SystemAdmin")]
    public async Task<ActionResult> CompleteMaintenance(int maintenanceId)
    {
        try
        {
            var result = await _gameServerService.CompleteMaintenanceAsync(maintenanceId);
            if (!result)
                return NotFound($"Maintenance with ID {maintenanceId} not found");

            return Ok(new { message = "Maintenance completed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing maintenance {MaintenanceId}", maintenanceId);
            return StatusCode(500, "Internal server error");
        }
    }
}
