using GameManagement.Core.Interfaces;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace GameManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ItemController : ControllerBase
{
    private readonly IItemService _itemService;
    private readonly ILogger<ItemController> _logger;

    public ItemController(IItemService itemService, ILogger<ItemController> logger)
    {
        _itemService = itemService;
        _logger = logger;
    }

    /// <summary>
    /// 获取道具列表
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<GameItemDto>>> GetItems([FromQuery] int page = 1, [FromQuery] int pageSize = 50)
    {
        try
        {
            var items = await _itemService.GetItemsAsync(page, pageSize);
            return Ok(items);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting items");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 根据ID获取道具
    /// </summary>
    [HttpGet("{id:int}")]
    public async Task<ActionResult<GameItemDto>> GetItem(int id)
    {
        try
        {
            var item = await _itemService.GetItemByIdAsync(id);
            if (item == null)
            {
                return NotFound($"Item with ID {id} not found");
            }

            return Ok(item);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting item {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 根据ItemId获取道具
    /// </summary>
    [HttpGet("by-item-id/{itemId}")]
    public async Task<ActionResult<GameItemDto>> GetItemByItemId(string itemId)
    {
        try
        {
            var item = await _itemService.GetItemByItemIdAsync(itemId);
            if (item == null)
            {
                return NotFound($"Item with ItemId '{itemId}' not found");
            }

            return Ok(item);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting item by ItemId {ItemId}", itemId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 搜索道具
    /// </summary>
    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<GameItemDto>>> SearchItems([FromQuery] string searchTerm)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return BadRequest("Search term cannot be empty");
            }

            var items = await _itemService.SearchItemsAsync(searchTerm);
            return Ok(items);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching items with term {SearchTerm}", searchTerm);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 创建新道具
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "SystemAdmin,Admin,GameMaster")]
    public async Task<ActionResult<GameItemDto>> CreateItem([FromBody] CreateGameItemDto createItemDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var item = await _itemService.CreateItemAsync(createItemDto);
            return CreatedAtAction(nameof(GetItem), new { id = item.Id }, item);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating item");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 更新道具
    /// </summary>
    [HttpPut("{id:int}")]
    [Authorize(Roles = "SystemAdmin,Admin,GameMaster")]
    public async Task<ActionResult<GameItemDto>> UpdateItem(int id, [FromBody] UpdateGameItemDto updateItemDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var item = await _itemService.UpdateItemAsync(id, updateItemDto);
            return Ok(item);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating item {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 删除道具
    /// </summary>
    [HttpDelete("{id:int}")]
    [Authorize(Roles = "SystemAdmin,Admin")]
    public async Task<ActionResult> DeleteItem(int id)
    {
        try
        {
            var result = await _itemService.DeleteItemAsync(id);
            if (!result)
            {
                return NotFound($"Item with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting item {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 激活道具
    /// </summary>
    [HttpPost("{id:int}/activate")]
    [Authorize(Roles = "SystemAdmin,Admin,GameMaster")]
    public async Task<ActionResult> ActivateItem(int id)
    {
        try
        {
            var result = await _itemService.ActivateItemAsync(id);
            if (!result)
            {
                return NotFound($"Item with ID {id} not found");
            }

            return Ok(new { message = "Item activated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating item {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 停用道具
    /// </summary>
    [HttpPost("{id:int}/deactivate")]
    [Authorize(Roles = "SystemAdmin,Admin,GameMaster")]
    public async Task<ActionResult> DeactivateItem(int id)
    {
        try
        {
            var result = await _itemService.DeactivateItemAsync(id);
            if (!result)
            {
                return NotFound($"Item with ID {id} not found");
            }

            return Ok(new { message = "Item deactivated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating item {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 根据类型获取道具
    /// </summary>
    [HttpGet("by-type/{type}")]
    public async Task<ActionResult<IEnumerable<GameItemDto>>> GetItemsByType(ItemType type)
    {
        try
        {
            var items = await _itemService.GetItemsByTypeAsync(type);
            return Ok(items);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting items by type {Type}", type);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 根据稀有度获取道具
    /// </summary>
    [HttpGet("by-rarity/{rarity}")]
    public async Task<ActionResult<IEnumerable<GameItemDto>>> GetItemsByRarity(ItemRarity rarity)
    {
        try
        {
            var items = await _itemService.GetItemsByRarityAsync(rarity);
            return Ok(items);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting items by rarity {Rarity}", rarity);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 根据等级范围获取道具
    /// </summary>
    [HttpGet("by-level-range")]
    public async Task<ActionResult<IEnumerable<GameItemDto>>> GetItemsByLevelRange([FromQuery] int minLevel, [FromQuery] int maxLevel)
    {
        try
        {
            if (minLevel < 0 || maxLevel < minLevel)
            {
                return BadRequest("Invalid level range");
            }

            var items = await _itemService.GetItemsByLevelRangeAsync(minLevel, maxLevel);
            return Ok(items);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting items by level range {MinLevel}-{MaxLevel}", minLevel, maxLevel);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取激活的道具
    /// </summary>
    [HttpGet("active")]
    public async Task<ActionResult<IEnumerable<GameItemDto>>> GetActiveItems()
    {
        try
        {
            var items = await _itemService.GetActiveItemsAsync();
            return Ok(items);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active items");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取可交易的道具
    /// </summary>
    [HttpGet("tradeable")]
    public async Task<ActionResult<IEnumerable<GameItemDto>>> GetTradeableItems()
    {
        try
        {
            var items = await _itemService.GetTradeableItemsAsync();
            return Ok(items);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tradeable items");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 更新道具价格
    /// </summary>
    [HttpPut("{id:int}/price")]
    [Authorize(Roles = "SystemAdmin,Admin,GameMaster")]
    public async Task<ActionResult> UpdateItemPrice(int id, [FromBody] UpdateItemPriceDto updatePriceDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _itemService.UpdateItemPriceAsync(id, updatePriceDto.Price);
            if (!result)
            {
                return NotFound($"Item with ID {id} not found");
            }

            return Ok(new { message = "Item price updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating price for item {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 批量更新道具价格
    /// </summary>
    [HttpPut("batch-update-prices")]
    [Authorize(Roles = "SystemAdmin,Admin,GameMaster")]
    public async Task<ActionResult> BatchUpdatePrices([FromBody] Dictionary<int, decimal> priceUpdates)
    {
        try
        {
            if (priceUpdates == null || !priceUpdates.Any())
            {
                return BadRequest("Price updates cannot be empty");
            }

            var result = await _itemService.BatchUpdatePricesAsync(priceUpdates);
            return Ok(new { message = $"Updated prices for {priceUpdates.Count} items" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch updating prices");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 根据价格范围获取道具
    /// </summary>
    [HttpGet("by-price-range")]
    public async Task<ActionResult<IEnumerable<GameItemDto>>> GetItemsByPriceRange([FromQuery] decimal minPrice, [FromQuery] decimal maxPrice)
    {
        try
        {
            if (minPrice < 0 || maxPrice < minPrice)
            {
                return BadRequest("Invalid price range");
            }

            var items = await _itemService.GetItemsByPriceRangeAsync(minPrice, maxPrice);
            return Ok(items);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting items by price range {MinPrice}-{MaxPrice}", minPrice, maxPrice);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取道具统计信息
    /// </summary>
    [HttpGet("stats")]
    [Authorize(Roles = "SystemAdmin,Admin,GameMaster,Analyst")]
    public async Task<ActionResult<ItemStatsDto>> GetItemStats()
    {
        try
        {
            var stats = await _itemService.GetItemStatsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting item stats");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取道具使用统计
    /// </summary>
    [HttpGet("usage-stats")]
    [Authorize(Roles = "SystemAdmin,Admin,GameMaster,Analyst")]
    public async Task<ActionResult<IEnumerable<ItemUsageStatsDto>>> GetItemUsageStats([FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
    {
        try
        {
            var stats = await _itemService.GetItemUsageStatsAsync(startDate, endDate);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting item usage stats");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取热门道具
    /// </summary>
    [HttpGet("popular")]
    public async Task<ActionResult<IEnumerable<GameItemDto>>> GetPopularItems([FromQuery] int count = 10)
    {
        try
        {
            if (count <= 0 || count > 100)
            {
                return BadRequest("Count must be between 1 and 100");
            }

            var items = await _itemService.GetPopularItemsAsync(count);
            return Ok(items);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting popular items");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取热销道具
    /// </summary>
    [HttpGet("top-selling")]
    public async Task<ActionResult<IEnumerable<GameItemDto>>> GetTopSellingItems([FromQuery] int count = 10)
    {
        try
        {
            if (count <= 0 || count > 100)
            {
                return BadRequest("Count must be between 1 and 100");
            }

            var items = await _itemService.GetTopSellingItemsAsync(count);
            return Ok(items);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting top selling items");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 更新道具属性
    /// </summary>
    [HttpPut("{id:int}/properties")]
    [Authorize(Roles = "SystemAdmin,Admin,GameMaster")]
    public async Task<ActionResult> UpdateItemProperties(int id, [FromBody] UpdateItemPropertiesDto updatePropertiesDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _itemService.UpdateItemPropertiesAsync(id, updatePropertiesDto.Properties);
            if (!result)
            {
                return NotFound($"Item with ID {id} not found");
            }

            return Ok(new { message = "Item properties updated successfully" });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating properties for item {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 根据属性获取道具
    /// </summary>
    [HttpGet("by-property")]
    public async Task<ActionResult<IEnumerable<GameItemDto>>> GetItemsByProperty([FromQuery] string propertyKey, [FromQuery] string propertyValue)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(propertyKey) || string.IsNullOrWhiteSpace(propertyValue))
            {
                return BadRequest("Property key and value cannot be empty");
            }

            var items = await _itemService.GetItemsByPropertyAsync(propertyKey, propertyValue);
            return Ok(items);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting items by property {Key}={Value}", propertyKey, propertyValue);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 批量激活道具
    /// </summary>
    [HttpPost("batch-activate")]
    [Authorize(Roles = "SystemAdmin,Admin,GameMaster")]
    public async Task<ActionResult> BatchActivateItems([FromBody] BatchItemOperationDto batchDto)
    {
        try
        {
            if (batchDto?.ItemIds == null || !batchDto.ItemIds.Any())
            {
                return BadRequest("Item IDs cannot be empty");
            }

            var result = await _itemService.BatchActivateItemsAsync(batchDto.ItemIds);
            return Ok(new { message = $"Activated {batchDto.ItemIds.Count()} items" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch activating items");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 批量停用道具
    /// </summary>
    [HttpPost("batch-deactivate")]
    [Authorize(Roles = "SystemAdmin,Admin,GameMaster")]
    public async Task<ActionResult> BatchDeactivateItems([FromBody] BatchItemOperationDto batchDto)
    {
        try
        {
            if (batchDto?.ItemIds == null || !batchDto.ItemIds.Any())
            {
                return BadRequest("Item IDs cannot be empty");
            }

            var result = await _itemService.BatchDeactivateItemsAsync(batchDto.ItemIds);
            return Ok(new { message = $"Deactivated {batchDto.ItemIds.Count()} items" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch deactivating items");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 批量删除道具
    /// </summary>
    [HttpDelete("batch-delete")]
    [Authorize(Roles = "SystemAdmin,Admin")]
    public async Task<ActionResult> BatchDeleteItems([FromBody] BatchItemOperationDto batchDto)
    {
        try
        {
            if (batchDto?.ItemIds == null || !batchDto.ItemIds.Any())
            {
                return BadRequest("Item IDs cannot be empty");
            }

            var result = await _itemService.BatchDeleteItemsAsync(batchDto.ItemIds);
            return Ok(new { message = $"Deleted {batchDto.ItemIds.Count()} items" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch deleting items");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 导入道具
    /// </summary>
    [HttpPost("import")]
    [Authorize(Roles = "SystemAdmin,Admin,GameMaster")]
    public async Task<ActionResult<IEnumerable<GameItemDto>>> ImportItems([FromBody] IEnumerable<CreateGameItemDto> items)
    {
        try
        {
            if (items == null || !items.Any())
            {
                return BadRequest("Items cannot be empty");
            }

            var importedItems = await _itemService.ImportItemsAsync(items);
            return Ok(importedItems);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing items");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取道具性能分析
    /// </summary>
    [HttpGet("{id:int}/performance")]
    [Authorize(Roles = "SystemAdmin,Admin,GameMaster,Analyst")]
    public async Task<ActionResult<ItemPerformanceDto>> GetItemPerformance(int id, [FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
    {
        try
        {
            var performance = await _itemService.GetItemPerformanceAsync(id, startDate, endDate);
            return Ok(performance);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting item performance for item {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取道具趋势分析
    /// </summary>
    [HttpGet("trends")]
    [Authorize(Roles = "SystemAdmin,Admin,GameMaster,Analyst")]
    public async Task<ActionResult<IEnumerable<ItemTrendDto>>> GetItemTrends([FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
    {
        try
        {
            var trends = await _itemService.GetItemTrendsAsync(startDate, endDate);
            return Ok(trends);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting item trends");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 获取道具库存信息
    /// </summary>
    [HttpGet("inventory")]
    [Authorize(Roles = "SystemAdmin,Admin,GameMaster,Analyst")]
    public async Task<ActionResult<ItemInventoryDto>> GetItemInventory()
    {
        try
        {
            var inventory = await _itemService.GetItemInventoryAsync();
            return Ok(inventory);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting item inventory");
            return StatusCode(500, "Internal server error");
        }
    }
}

// Helper DTOs for controller actions
public class UpdateItemPriceDto
{
    public decimal Price { get; set; }
}

public class UpdateItemPropertiesDto
{
    public string Properties { get; set; } = string.Empty;
}

public class BatchItemOperationDto
{
    public IEnumerable<int> ItemIds { get; set; } = new List<int>();
}
