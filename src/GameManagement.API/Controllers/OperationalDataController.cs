using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GameManagement.Core.Interfaces;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;

namespace GameManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class OperationalDataController : ControllerBase
{
    private readonly IOperationalDataService _operationalDataService;
    private readonly IAuditService _auditService;

    public OperationalDataController(
        IOperationalDataService operationalDataService,
        IAuditService auditService)
    {
        _operationalDataService = operationalDataService;
        _auditService = auditService;
    }

    /// <summary>
    /// 获取全局数据统计
    /// </summary>
    [HttpGet("global-stats")]
    [Authorize(Roles = "SystemAdmin,ProductManager,ProductSpecialist")]
    public async Task<ActionResult<GlobalStatsDto>> GetGlobalStats()
    {
        try
        {
            var stats = await _operationalDataService.GetGlobalStatsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "获取全局统计数据失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取指定日期的全局数据统计
    /// </summary>
    [HttpGet("global-stats/{date:datetime}")]
    [Authorize(Roles = "SystemAdmin,ProductManager,ProductSpecialist")]
    public async Task<ActionResult<GlobalStatsDto>> GetGlobalStatsByDate(DateTime date)
    {
        try
        {
            var stats = await _operationalDataService.GetGlobalStatsByDateAsync(date);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "获取指定日期全局统计数据失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取用户信息统计
    /// </summary>
    [HttpGet("user-info-stats")]
    [Authorize(Roles = "SystemAdmin,ProductManager,ProductSpecialist,CustomerServiceManager")]
    public async Task<ActionResult<UserInfoStatsDto>> GetUserInfoStats()
    {
        try
        {
            var stats = await _operationalDataService.GetUserInfoStatsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "获取用户信息统计失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取指定日期范围的用户信息统计
    /// </summary>
    [HttpGet("user-info-stats/{startDate:datetime}/{endDate:datetime}")]
    [Authorize(Roles = "SystemAdmin,ProductManager,ProductSpecialist,CustomerServiceManager")]
    public async Task<ActionResult<UserInfoStatsDto>> GetUserInfoStatsByDateRange(DateTime startDate, DateTime endDate)
    {
        try
        {
            if (startDate > endDate)
            {
                return BadRequest(new { message = "开始日期不能大于结束日期" });
            }

            var stats = await _operationalDataService.GetUserInfoStatsByDateRangeAsync(startDate, endDate);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "获取用户信息统计失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取付费信息统计
    /// </summary>
    [HttpGet("payment-info-stats")]
    [Authorize(Roles = "SystemAdmin,ProductManager,ProductSpecialist")]
    public async Task<ActionResult<PaymentInfoStatsDto>> GetPaymentInfoStats()
    {
        try
        {
            var stats = await _operationalDataService.GetPaymentInfoStatsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "获取付费信息统计失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取指定日期范围的付费信息统计
    /// </summary>
    [HttpGet("payment-info-stats/{startDate:datetime}/{endDate:datetime}")]
    [Authorize(Roles = "SystemAdmin,ProductManager,ProductSpecialist")]
    public async Task<ActionResult<PaymentInfoStatsDto>> GetPaymentInfoStatsByDateRange(DateTime startDate, DateTime endDate)
    {
        try
        {
            if (startDate > endDate)
            {
                return BadRequest(new { message = "开始日期不能大于结束日期" });
            }

            var stats = await _operationalDataService.GetPaymentInfoStatsByDateRangeAsync(startDate, endDate);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "获取付费信息统计失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取客户端数据统计
    /// </summary>
    [HttpGet("client-data-stats")]
    [Authorize(Roles = "SystemAdmin,ProductManager,ProductSpecialist")]
    public async Task<ActionResult<ClientDataStatsDto>> GetClientDataStats()
    {
        try
        {
            var stats = await _operationalDataService.GetClientDataStatsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "获取客户端数据统计失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取转化率分析
    /// </summary>
    [HttpGet("conversion-analysis/{date:datetime}")]
    [Authorize(Roles = "SystemAdmin,ProductManager,ProductSpecialist")]
    public async Task<ActionResult<ConversionAnalysisDto>> GetConversionAnalysis(DateTime date)
    {
        try
        {
            var analysis = await _operationalDataService.GetConversionAnalysisAsync(date);
            return Ok(analysis);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "获取转化率分析失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取流失率分析
    /// </summary>
    [HttpGet("churn-analysis/{date:datetime}")]
    [Authorize(Roles = "SystemAdmin,ProductManager,ProductSpecialist")]
    public async Task<ActionResult<ChurnAnalysisDto>> GetChurnAnalysis(DateTime date)
    {
        try
        {
            var analysis = await _operationalDataService.GetChurnAnalysisAsync(date);
            return Ok(analysis);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "获取流失率分析失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取留存率分析
    /// </summary>
    [HttpGet("retention-analysis/{date:datetime}")]
    [Authorize(Roles = "SystemAdmin,ProductManager,ProductSpecialist")]
    public async Task<ActionResult<RetentionAnalysisDto>> GetRetentionAnalysis(DateTime date)
    {
        try
        {
            var analysis = await _operationalDataService.GetRetentionAnalysisAsync(date);
            return Ok(analysis);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "获取留存率分析失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取活跃用户分析
    /// </summary>
    [HttpGet("active-user-analysis/{date:datetime}")]
    [Authorize(Roles = "SystemAdmin,ProductManager,ProductSpecialist")]
    public async Task<ActionResult<ActiveUserAnalysisDto>> GetActiveUserAnalysis(DateTime date)
    {
        try
        {
            var analysis = await _operationalDataService.GetActiveUserAnalysisAsync(date);
            return Ok(analysis);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "获取活跃用户分析失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取用户生命周期分析
    /// </summary>
    [HttpGet("user-lifecycle-analysis")]
    [Authorize(Roles = "SystemAdmin,ProductManager,ProductSpecialist")]
    public async Task<ActionResult<IEnumerable<UserLifecycleDto>>> GetUserLifecycleAnalysis(
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 20)
    {
        try
        {
            if (page < 1 || pageSize < 1 || pageSize > 100)
            {
                return BadRequest(new { message = "页码和页大小参数无效" });
            }

            var analysis = await _operationalDataService.GetUserLifecycleAnalysisAsync(page, pageSize);
            return Ok(analysis);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "获取用户生命周期分析失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取历史数据
    /// </summary>
    [HttpGet("historical-data")]
    [Authorize(Roles = "SystemAdmin,ProductManager,ProductSpecialist")]
    public async Task<ActionResult<IEnumerable<OperationalDataDto>>> GetHistoricalData(
        [FromQuery] string dataType,
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] int? serverId = null)
    {
        try
        {
            if (string.IsNullOrEmpty(dataType))
            {
                return BadRequest(new { message = "数据类型不能为空" });
            }

            if (startDate > endDate)
            {
                return BadRequest(new { message = "开始日期不能大于结束日期" });
            }

            var data = await _operationalDataService.GetHistoricalDataAsync(dataType, startDate, endDate, serverId);
            return Ok(data);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "获取历史数据失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取指标趋势
    /// </summary>
    [HttpGet("metric-trend")]
    [Authorize(Roles = "SystemAdmin,ProductManager,ProductSpecialist")]
    public async Task<ActionResult<Dictionary<DateTime, decimal>>> GetMetricTrend(
        [FromQuery] string dataType,
        [FromQuery] string metricName,
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] int? serverId = null)
    {
        try
        {
            if (string.IsNullOrEmpty(dataType) || string.IsNullOrEmpty(metricName))
            {
                return BadRequest(new { message = "数据类型和指标名称不能为空" });
            }

            if (startDate > endDate)
            {
                return BadRequest(new { message = "开始日期不能大于结束日期" });
            }

            var trend = await _operationalDataService.GetMetricTrendAsync(dataType, metricName, startDate, endDate, serverId);
            return Ok(trend);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "获取指标趋势失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 记录访问数据
    /// </summary>
    [HttpPost("record-visit")]
    [AllowAnonymous] // 允许匿名访问以记录访问数据
    public async Task<ActionResult> RecordVisit([FromBody] RecordVisitRequest request)
    {
        try
        {
            var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
            var userAgent = HttpContext.Request.Headers["User-Agent"].ToString();
            
            await _operationalDataService.RecordVisitAsync(
                ipAddress, 
                userAgent, 
                request.Referrer, 
                request.Channel, 
                request.ServerId);

            return Ok(new { message = "访问记录成功" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "记录访问数据失败", error = ex.Message });
        }
    }
}

// 请求模型
public class RecordVisitRequest
{
    public string? Referrer { get; set; }
    public string? Channel { get; set; }
    public int? ServerId { get; set; }
}
