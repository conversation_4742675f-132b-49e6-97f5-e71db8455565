using GameManagement.Core.Interfaces;
using GameManagement.Shared.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace GameManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ReportController : ControllerBase
{
    private readonly IReportService _reportService;
    private readonly ILogger<ReportController> _logger;

    public ReportController(IReportService reportService, ILogger<ReportController> logger)
    {
        _reportService = reportService;
        _logger = logger;
    }

    /// <summary>
    /// Get dashboard statistics
    /// </summary>
    [HttpGet("dashboard")]
    public async Task<ActionResult<DashboardStatsDto>> GetDashboardStats()
    {
        try
        {
            var stats = await _reportService.GetDashboardStatsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboard stats");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get revenue report for a date range
    /// </summary>
    [HttpGet("revenue")]
    public async Task<ActionResult<RevenueReportDto>> GetRevenueReport(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate)
    {
        try
        {
            if (startDate > endDate)
            {
                return BadRequest("Start date cannot be after end date");
            }

            var report = await _reportService.GetRevenueReportAsync(startDate, endDate);
            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting revenue report for period {StartDate} to {EndDate}", startDate, endDate);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get player report for a date range
    /// </summary>
    [HttpGet("player")]
    public async Task<ActionResult<PlayerReportDto>> GetPlayerReport(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate)
    {
        try
        {
            if (startDate > endDate)
            {
                return BadRequest("Start date cannot be after end date");
            }

            var report = await _reportService.GetPlayerReportAsync(startDate, endDate);
            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting player report for period {StartDate} to {EndDate}", startDate, endDate);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get retention report for a date range
    /// </summary>
    [HttpGet("retention")]
    public async Task<ActionResult<RetentionReportDto>> GetRetentionReport(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate)
    {
        try
        {
            if (startDate > endDate)
            {
                return BadRequest("Start date cannot be after end date");
            }

            var report = await _reportService.GetRetentionReportAsync(startDate, endDate);
            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting retention report for period {StartDate} to {EndDate}", startDate, endDate);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Export report in specified format
    /// </summary>
    [HttpGet("export")]
    public async Task<IActionResult> ExportReport(
        [FromQuery] string reportType,
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] string format = "json")
    {
        try
        {
            if (string.IsNullOrEmpty(reportType))
            {
                return BadRequest("Report type is required");
            }

            if (startDate > endDate)
            {
                return BadRequest("Start date cannot be after end date");
            }

            var validFormats = new[] { "json", "csv", "excel" };
            if (!validFormats.Contains(format.ToLower()))
            {
                return BadRequest($"Invalid format. Supported formats: {string.Join(", ", validFormats)}");
            }

            var validReportTypes = new[] { "revenue", "player", "retention", "dashboard" };
            if (!validReportTypes.Contains(reportType.ToLower()))
            {
                return BadRequest($"Invalid report type. Supported types: {string.Join(", ", validReportTypes)}");
            }

            var exportData = await _reportService.ExportReportAsync(reportType, startDate, endDate, format);

            var contentType = format.ToLower() switch
            {
                "json" => "application/json",
                "csv" => "text/csv",
                "excel" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                _ => "application/octet-stream"
            };

            var fileName = $"{reportType}_report_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}.{format.ToLower()}";

            return File(exportData, contentType, fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting {ReportType} report in {Format} format", reportType, format);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get available report types
    /// </summary>
    [HttpGet("types")]
    public ActionResult<object> GetReportTypes()
    {
        var reportTypes = new
        {
            Types = new[]
            {
                new { Name = "dashboard", DisplayName = "Dashboard Statistics", Description = "Overall system statistics" },
                new { Name = "revenue", DisplayName = "Revenue Report", Description = "Revenue analysis by date range" },
                new { Name = "player", DisplayName = "Player Report", Description = "Player statistics and activity" },
                new { Name = "retention", DisplayName = "Retention Report", Description = "Player retention analysis" }
            },
            Formats = new[]
            {
                new { Name = "json", DisplayName = "JSON", Description = "JavaScript Object Notation" },
                new { Name = "csv", DisplayName = "CSV", Description = "Comma Separated Values" },
                new { Name = "excel", DisplayName = "Excel", Description = "Microsoft Excel format" }
            }
        };

        return Ok(reportTypes);
    }

    /// <summary>
    /// Get quick stats for the last 30 days
    /// </summary>
    [HttpGet("quick-stats")]
    public async Task<ActionResult<object>> GetQuickStats()
    {
        try
        {
            var endDate = DateTime.UtcNow.Date;
            var startDate = endDate.AddDays(-30);

            var revenueReport = await _reportService.GetRevenueReportAsync(startDate, endDate);
            var playerReport = await _reportService.GetPlayerReportAsync(startDate, endDate);
            var dashboardStats = await _reportService.GetDashboardStatsAsync();

            var quickStats = new
            {
                Period = new { StartDate = startDate, EndDate = endDate },
                Revenue = new
                {
                    Total = revenueReport.TotalRevenue,
                    Transactions = revenueReport.TotalTransactions,
                    Average = revenueReport.AverageTransactionValue
                },
                Players = new
                {
                    New = playerReport.NewPlayers,
                    Active = playerReport.ActivePlayers,
                    Total = dashboardStats.TotalPlayers
                },
                Servers = new
                {
                    Online = dashboardStats.OnlineServers
                }
            };

            return Ok(quickStats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting quick stats");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get report generation status (placeholder for async report generation)
    /// </summary>
    [HttpGet("status/{reportId}")]
    public ActionResult<object> GetReportStatus(string reportId)
    {
        // Placeholder for async report generation status
        // In a real implementation, you'd track report generation jobs
        var status = new
        {
            ReportId = reportId,
            Status = "completed", // pending, processing, completed, failed
            Progress = 100,
            CreatedAt = DateTime.UtcNow.AddMinutes(-5),
            CompletedAt = DateTime.UtcNow,
            DownloadUrl = $"/api/report/download/{reportId}"
        };

        return Ok(status);
    }

    /// <summary>
    /// Download generated report (placeholder)
    /// </summary>
    [HttpGet("download/{reportId}")]
    public ActionResult DownloadReport(string reportId)
    {
        // Placeholder for downloading pre-generated reports
        // In a real implementation, you'd retrieve the file from storage
        return NotFound("Report not found or expired");
    }
}
