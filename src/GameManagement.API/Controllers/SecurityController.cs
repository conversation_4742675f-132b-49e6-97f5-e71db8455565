using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GameManagement.Core.Interfaces;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;

namespace GameManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class SecurityController : ControllerBase
{
    private readonly ISecurityService _securityService;
    private readonly ILogger<SecurityController> _logger;

    public SecurityController(ISecurityService securityService, ILogger<SecurityController> logger)
    {
        _securityService = securityService;
        _logger = logger;
    }

    // Security Events Endpoints
    [HttpGet("events")]
    public async Task<ActionResult<IEnumerable<SecurityEventDto>>> GetSecurityEvents(
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 20)
    {
        try
        {
            var events = await _securityService.GetSecurityEventsAsync(page, pageSize);
            return Ok(events);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving security events");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("events/{id}")]
    public async Task<ActionResult<SecurityEventDto>> GetSecurityEvent(int id)
    {
        try
        {
            var securityEvent = await _securityService.GetSecurityEventByIdAsync(id);
            if (securityEvent == null)
                return NotFound();

            return Ok(securityEvent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving security event {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("events/type/{eventType}")]
    public async Task<ActionResult<IEnumerable<SecurityEventDto>>> GetSecurityEventsByType(SecurityEventType eventType)
    {
        try
        {
            var events = await _securityService.GetSecurityEventsByTypeAsync(eventType);
            return Ok(events);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving security events by type {EventType}", eventType);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("events/risk/{riskLevel}")]
    public async Task<ActionResult<IEnumerable<SecurityEventDto>>> GetSecurityEventsByRiskLevel(SecurityRiskLevel riskLevel)
    {
        try
        {
            var events = await _securityService.GetSecurityEventsByRiskLevelAsync(riskLevel);
            return Ok(events);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving security events by risk level {RiskLevel}", riskLevel);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("events/pending")]
    public async Task<ActionResult<IEnumerable<SecurityEventDto>>> GetPendingSecurityEvents()
    {
        try
        {
            var events = await _securityService.GetPendingSecurityEventsAsync();
            return Ok(events);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving pending security events");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("events")]
    public async Task<ActionResult<SecurityEventDto>> CreateSecurityEvent([FromBody] CreateSecurityEventDto createSecurityEventDto)
    {
        try
        {
            var securityEvent = await _securityService.CreateSecurityEventAsync(createSecurityEventDto);
            return CreatedAtAction(nameof(GetSecurityEvent), new { id = securityEvent.Id }, securityEvent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating security event");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPut("events/{id}")]
    public async Task<ActionResult<SecurityEventDto>> UpdateSecurityEvent(int id, [FromBody] UpdateSecurityEventDto updateSecurityEventDto)
    {
        try
        {
            var securityEvent = await _securityService.UpdateSecurityEventAsync(id, updateSecurityEventDto);
            return Ok(securityEvent);
        }
        catch (ArgumentException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating security event {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("events/{id}/handle")]
    public async Task<ActionResult> HandleSecurityEvent(int id, [FromBody] HandleSecurityEventDto handleDto)
    {
        try
        {
            var success = await _securityService.HandleSecurityEventAsync(id, handleDto.HandledByUserId, handleDto.HandlingNotes);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling security event {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("events/{id}/resolve")]
    public async Task<ActionResult> ResolveSecurityEvent(int id)
    {
        try
        {
            var success = await _securityService.MarkSecurityEventAsResolvedAsync(id);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving security event {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("events/{id}/false-positive")]
    public async Task<ActionResult> MarkSecurityEventAsFalsePositive(int id)
    {
        try
        {
            var success = await _securityService.MarkSecurityEventAsFalsePositiveAsync(id);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking security event {Id} as false positive", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("stats")]
    public async Task<ActionResult<SecurityStatsDto>> GetSecurityStats()
    {
        try
        {
            var stats = await _securityService.GetSecurityStatsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving security stats");
            return StatusCode(500, "Internal server error");
        }
    }

    // Security Rules Endpoints
    [HttpGet("rules")]
    public async Task<ActionResult<IEnumerable<SecurityRuleDto>>> GetSecurityRules()
    {
        try
        {
            var rules = await _securityService.GetSecurityRulesAsync();
            return Ok(rules);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving security rules");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("rules/{id}")]
    public async Task<ActionResult<SecurityRuleDto>> GetSecurityRule(int id)
    {
        try
        {
            var rule = await _securityService.GetSecurityRuleByIdAsync(id);
            if (rule == null)
                return NotFound();

            return Ok(rule);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving security rule {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("rules/active")]
    public async Task<ActionResult<IEnumerable<SecurityRuleDto>>> GetActiveSecurityRules()
    {
        try
        {
            var rules = await _securityService.GetActiveSecurityRulesAsync();
            return Ok(rules);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active security rules");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("rules")]
    public async Task<ActionResult<SecurityRuleDto>> CreateSecurityRule([FromBody] CreateSecurityRuleDto createSecurityRuleDto)
    {
        try
        {
            var rule = await _securityService.CreateSecurityRuleAsync(createSecurityRuleDto);
            return CreatedAtAction(nameof(GetSecurityRule), new { id = rule.Id }, rule);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating security rule");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPut("rules/{id}")]
    public async Task<ActionResult<SecurityRuleDto>> UpdateSecurityRule(int id, [FromBody] UpdateSecurityRuleDto updateSecurityRuleDto)
    {
        try
        {
            var rule = await _securityService.UpdateSecurityRuleAsync(id, updateSecurityRuleDto);
            return Ok(rule);
        }
        catch (ArgumentException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating security rule {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpDelete("rules/{id}")]
    public async Task<ActionResult> DeleteSecurityRule(int id)
    {
        try
        {
            var success = await _securityService.DeleteSecurityRuleAsync(id);
            if (!success)
                return NotFound();

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting security rule {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("rules/{id}/activate")]
    public async Task<ActionResult> ActivateSecurityRule(int id)
    {
        try
        {
            var success = await _securityService.ActivateSecurityRuleAsync(id);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating security rule {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("rules/{id}/deactivate")]
    public async Task<ActionResult> DeactivateSecurityRule(int id)
    {
        try
        {
            var success = await _securityService.DeactivateSecurityRuleAsync(id);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating security rule {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    // Risk Detection Endpoints
    [HttpGet("risk-assessment/{playerId}")]
    public async Task<ActionResult<RiskAssessmentDto>> AssessPlayerRisk(string playerId)
    {
        try
        {
            var assessment = await _securityService.AssessPlayerRiskAsync(playerId);
            return Ok(assessment);
        }
        catch (ArgumentException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assessing player risk for {PlayerId}", playerId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("detect-suspicious")]
    public async Task<ActionResult<IEnumerable<SecurityEventDto>>> DetectSuspiciousActivities()
    {
        try
        {
            var events = await _securityService.DetectSuspiciousActivitiesAsync();
            return Ok(events);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting suspicious activities");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("rules/{ruleId}/trigger")]
    public async Task<ActionResult> TriggerSecurityRule(int ruleId, [FromBody] TriggerSecurityRuleDto triggerDto)
    {
        try
        {
            var success = await _securityService.TriggerSecurityRuleAsync(ruleId, triggerDto.PlayerId, triggerDto.Evidence);
            if (!success)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error triggering security rule {RuleId}", ruleId);
            return StatusCode(500, "Internal server error");
        }
    }
}

// Helper DTOs for controller actions
public class HandleSecurityEventDto
{
    public int HandledByUserId { get; set; }
    public string HandlingNotes { get; set; } = string.Empty;
}

public class TriggerSecurityRuleDto
{
    public string PlayerId { get; set; } = string.Empty;
    public string Evidence { get; set; } = string.Empty;
}
