using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GameManagement.Core.Interfaces;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;

namespace GameManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ServerMonitoringController : ControllerBase
{
    private readonly IServerMonitoringService _serverMonitoringService;
    private readonly ILogger<ServerMonitoringController> _logger;

    public ServerMonitoringController(
        IServerMonitoringService serverMonitoringService,
        ILogger<ServerMonitoringController> logger)
    {
        _serverMonitoringService = serverMonitoringService;
        _logger = logger;
    }

    // 基础监控
    [HttpGet("servers/status")]
    public async Task<ActionResult<IEnumerable<ServerStatusDto>>> GetAllServerStatus()
    {
        try
        {
            var result = await _serverMonitoringService.GetAllServerStatusAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all server status");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("servers/{serverId}/status")]
    public async Task<ActionResult<ServerStatusDto>> GetServerStatus(int serverId)
    {
        try
        {
            var result = await _serverMonitoringService.GetServerStatusAsync(serverId);
            if (result == null)
                return NotFound($"Server with ID {serverId} not found");

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server status for server {ServerId}", serverId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPut("servers/{serverId}/status")]
    public async Task<ActionResult> UpdateServerStatus(int serverId, [FromBody] ServerStatus status)
    {
        try
        {
            var result = await _serverMonitoringService.UpdateServerStatusAsync(serverId, status);
            if (!result)
                return NotFound($"Server with ID {serverId} not found");

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating server status for server {ServerId}", serverId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("servers/{serverId}/health")]
    public async Task<ActionResult<ServerHealthDto>> GetServerHealth(int serverId)
    {
        try
        {
            var result = await _serverMonitoringService.GetServerHealthAsync(serverId);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server health for server {ServerId}", serverId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("servers/health")]
    public async Task<ActionResult<IEnumerable<ServerHealthDto>>> GetAllServerHealth()
    {
        try
        {
            var result = await _serverMonitoringService.GetAllServerHealthAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all server health");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("servers/{serverId}/logs")]
    public async Task<ActionResult<IEnumerable<ServerLogDto>>> GetServerLogs(
        int serverId, 
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 50)
    {
        try
        {
            var result = await _serverMonitoringService.GetServerLogsAsync(serverId, page, pageSize);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server logs for server {ServerId}", serverId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("servers/{serverId}/restart")]
    public async Task<ActionResult> RestartServer(int serverId)
    {
        try
        {
            var result = await _serverMonitoringService.RestartServerAsync(serverId);
            if (!result)
                return NotFound($"Server with ID {serverId} not found");

            return Ok(new { message = "Server restart initiated" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error restarting server {ServerId}", serverId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("servers/{serverId}/command")]
    public async Task<ActionResult> SendServerCommand(int serverId, [FromBody] string command)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(command))
                return BadRequest("Command cannot be empty");

            var result = await _serverMonitoringService.SendServerCommandAsync(serverId, command);
            if (!result)
                return NotFound($"Server with ID {serverId} not found");

            return Ok(new { message = "Command sent successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending command to server {ServerId}", serverId);
            return StatusCode(500, "Internal server error");
        }
    }

    // 性能监控
    [HttpPost("monitoring")]
    public async Task<ActionResult<ServerMonitoringDto>> AddMonitoringData([FromBody] CreateServerMonitoringDto createMonitoringDto)
    {
        try
        {
            var result = await _serverMonitoringService.AddMonitoringDataAsync(createMonitoringDto);
            return CreatedAtAction(nameof(GetMonitoringData), new { serverId = result.ServerId }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding monitoring data");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("servers/{serverId}/monitoring")]
    public async Task<ActionResult<IEnumerable<ServerMonitoringDto>>> GetMonitoringData(
        int serverId,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var result = await _serverMonitoringService.GetMonitoringDataAsync(serverId, startDate, endDate);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting monitoring data for server {ServerId}", serverId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("servers/{serverId}/performance")]
    public async Task<ActionResult<ServerPerformanceDto>> GetServerPerformance(
        int serverId,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var result = await _serverMonitoringService.GetServerPerformanceAsync(serverId, startDate, endDate);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server performance for server {ServerId}", serverId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("servers/performance")]
    public async Task<ActionResult<IEnumerable<ServerPerformanceDto>>> GetAllServerPerformance(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var result = await _serverMonitoringService.GetAllServerPerformanceAsync(startDate, endDate);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all server performance");
            return StatusCode(500, "Internal server error");
        }
    }

    // 告警管理
    [HttpGet("alerts/active")]
    public async Task<ActionResult<IEnumerable<ServerAlertDto>>> GetActiveAlerts()
    {
        try
        {
            var result = await _serverMonitoringService.GetActiveAlertsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active alerts");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("servers/{serverId}/alerts")]
    public async Task<ActionResult<IEnumerable<ServerAlertDto>>> GetServerAlerts(int serverId)
    {
        try
        {
            var result = await _serverMonitoringService.GetServerAlertsAsync(serverId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting alerts for server {ServerId}", serverId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("alerts")]
    public async Task<ActionResult<ServerAlertDto>> CreateAlert([FromBody] CreateServerAlertDto createAlertDto)
    {
        try
        {
            var result = await _serverMonitoringService.CreateAlertAsync(createAlertDto);
            return CreatedAtAction(nameof(GetServerAlerts), new { serverId = result.ServerId }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating alert");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPut("alerts/{alertId}/acknowledge")]
    public async Task<ActionResult> AcknowledgeAlert(int alertId, [FromBody] string acknowledgedBy)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(acknowledgedBy))
                return BadRequest("AcknowledgedBy cannot be empty");

            var result = await _serverMonitoringService.AcknowledgeAlertAsync(alertId, acknowledgedBy);
            if (!result)
                return NotFound($"Alert with ID {alertId} not found");

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error acknowledging alert {AlertId}", alertId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPut("alerts/{alertId}/resolve")]
    public async Task<ActionResult> ResolveAlert(int alertId, [FromBody] ResolveAlertRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.ResolvedBy))
                return BadRequest("ResolvedBy cannot be empty");

            if (string.IsNullOrWhiteSpace(request.Resolution))
                return BadRequest("Resolution cannot be empty");

            var result = await _serverMonitoringService.ResolveAlertAsync(alertId, request.ResolvedBy, request.Resolution);
            if (!result)
                return NotFound($"Alert with ID {alertId} not found");

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving alert {AlertId}", alertId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpDelete("alerts/{alertId}")]
    public async Task<ActionResult> DeleteAlert(int alertId)
    {
        try
        {
            var result = await _serverMonitoringService.DeleteAlertAsync(alertId);
            if (!result)
                return NotFound($"Alert with ID {alertId} not found");

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting alert {AlertId}", alertId);
            return StatusCode(500, "Internal server error");
        }
    }

    // 告警规则管理
    [HttpGet("alert-rules")]
    public async Task<ActionResult<IEnumerable<AlertRuleDto>>> GetAlertRules()
    {
        try
        {
            var result = await _serverMonitoringService.GetAlertRulesAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting alert rules");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("alert-rules/{ruleId}")]
    public async Task<ActionResult<AlertRuleDto>> GetAlertRule(int ruleId)
    {
        try
        {
            var result = await _serverMonitoringService.GetAlertRuleByIdAsync(ruleId);
            if (result == null)
                return NotFound($"Alert rule with ID {ruleId} not found");

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting alert rule {RuleId}", ruleId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("alert-rules")]
    public async Task<ActionResult<AlertRuleDto>> CreateAlertRule([FromBody] CreateAlertRuleDto createRuleDto)
    {
        try
        {
            var result = await _serverMonitoringService.CreateAlertRuleAsync(createRuleDto);
            return CreatedAtAction(nameof(GetAlertRule), new { ruleId = result.Id }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating alert rule");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPut("alert-rules/{ruleId}")]
    public async Task<ActionResult<AlertRuleDto>> UpdateAlertRule(int ruleId, [FromBody] UpdateAlertRuleDto updateRuleDto)
    {
        try
        {
            var result = await _serverMonitoringService.UpdateAlertRuleAsync(ruleId, updateRuleDto);
            if (result == null)
                return NotFound($"Alert rule with ID {ruleId} not found");

            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating alert rule {RuleId}", ruleId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpDelete("alert-rules/{ruleId}")]
    public async Task<ActionResult> DeleteAlertRule(int ruleId)
    {
        try
        {
            var result = await _serverMonitoringService.DeleteAlertRuleAsync(ruleId);
            if (!result)
                return NotFound($"Alert rule with ID {ruleId} not found");

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting alert rule {RuleId}", ruleId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPut("alert-rules/{ruleId}/toggle")]
    public async Task<ActionResult> ToggleAlertRule(int ruleId, [FromBody] bool isEnabled)
    {
        try
        {
            var result = await _serverMonitoringService.ToggleAlertRuleAsync(ruleId, isEnabled);
            if (!result)
                return NotFound($"Alert rule with ID {ruleId} not found");

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling alert rule {RuleId}", ruleId);
            return StatusCode(500, "Internal server error");
        }
    }

    // 健康检查
    [HttpPost("servers/{serverId}/health-check")]
    public async Task<ActionResult> PerformHealthCheck(int serverId)
    {
        try
        {
            var result = await _serverMonitoringService.PerformHealthCheckAsync(serverId);
            if (!result)
                return NotFound($"Server with ID {serverId} not found");

            return Ok(new { message = "Health check completed" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing health check for server {ServerId}", serverId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("health-checks")]
    public async Task<ActionResult<IEnumerable<HealthCheckResultDto>>> PerformAllHealthChecks()
    {
        try
        {
            var result = await _serverMonitoringService.PerformAllHealthChecksAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing all health checks");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("servers/{serverId}/health-check/latest")]
    public async Task<ActionResult<HealthCheckResultDto>> GetLatestHealthCheck(int serverId)
    {
        try
        {
            var result = await _serverMonitoringService.GetLatestHealthCheckAsync(serverId);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting latest health check for server {ServerId}", serverId);
            return StatusCode(500, "Internal server error");
        }
    }

    // 监控统计
    [HttpGet("stats")]
    public async Task<ActionResult<MonitoringStatsDto>> GetMonitoringStats()
    {
        try
        {
            var result = await _serverMonitoringService.GetMonitoringStatsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting monitoring stats");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("servers/{serverId}/metrics/{metricType}")]
    public async Task<ActionResult<Dictionary<string, object>>> GetServerMetrics(
        int serverId,
        string metricType,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var result = await _serverMonitoringService.GetServerMetricsAsync(serverId, metricType, startDate, endDate);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server metrics for server {ServerId}, metric {MetricType}", serverId, metricType);
            return StatusCode(500, "Internal server error");
        }
    }
}

public class ResolveAlertRequest
{
    public string ResolvedBy { get; set; } = string.Empty;
    public string Resolution { get; set; } = string.Empty;
}
