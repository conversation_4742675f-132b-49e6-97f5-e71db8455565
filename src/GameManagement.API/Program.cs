using GameManagement.Core.Interfaces;
using GameManagement.Infrastructure.Services;
using GameManagement.Infrastructure.Data;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.EntityFrameworkCore;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddOpenApi();

// Add Authentication
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = false,
            ValidateAudience = false,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = false,
            ClockSkew = TimeSpan.Zero
        };

        // 简化的令牌验证 - 仅用于开发
        options.Events = new JwtBearerEvents
        {
            OnMessageReceived = context =>
            {
                // 获取Authorization header中的token
                var authHeader = context.Request.Headers["Authorization"].ToString();
                if (authHeader.StartsWith("Bearer "))
                {
                    var token = authHeader.Substring("Bearer ".Length).Trim();
                    if (token.StartsWith("test-jwt-token-"))
                    {
                        // 创建用户身份
                        var claims = new[]
                        {
                            new System.Security.Claims.Claim(System.Security.Claims.ClaimTypes.Name, "111"),
                            new System.Security.Claims.Claim(System.Security.Claims.ClaimTypes.Role, "SystemAdmin")
                        };
                        var identity = new System.Security.Claims.ClaimsIdentity(claims, "jwt");
                        context.Principal = new System.Security.Claims.ClaimsPrincipal(identity);
                        context.Success();
                    }
                }
                return Task.CompletedTask;
            }
        };
    });

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowReactApp", policy =>
    {
        policy.WithOrigins("http://localhost:3000", "http://localhost:3001", "http://localhost:3002", "http://localhost:3003")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

// Add Entity Framework
builder.Services.AddDbContext<GameManagementDbContext>(options =>
{
    // 使用内存数据库进行开发和测试
    options.UseInMemoryDatabase("GameManagementDb");
    options.EnableSensitiveDataLogging();
    options.EnableDetailedErrors();
});

// Add repositories and services
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IOperationalDataService, OperationalDataService>();
builder.Services.AddScoped<IPlayerService, PlayerService>();
builder.Services.AddScoped<ISecurityService, SecurityService>();
builder.Services.AddScoped<ICustomerServiceService, CustomerServiceService>();
builder.Services.AddScoped<IReportService, ReportService>();
builder.Services.AddScoped<IAuditService, AuditService>();

var app = builder.Build();

// Initialize database with sample data
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<GameManagementDbContext>();
    await SeedDatabase(context);
}

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

app.UseCors("AllowReactApp");
app.UseHttpsRedirection();
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.Run();

// 数据库种子数据方法
static async Task SeedDatabase(GameManagementDbContext context)
{
    // 确保数据库已创建
    await context.Database.EnsureCreatedAsync();

    // 如果已有数据，则跳过
    var existingServers = await context.GameServers.ToListAsync();
    if (existingServers.Any())
        return;

    // 添加游戏服务器
    var servers = new[]
    {
        new GameManagement.Core.Entities.GameServer { Name = "服务器1", Host = "server1.game.com", Port = 8001, Status = GameManagement.Shared.Enums.ServerStatus.Online, MaxPlayers = 1000, CurrentPlayers = 456, Version = "1.0.0" },
        new GameManagement.Core.Entities.GameServer { Name = "服务器2", Host = "server2.game.com", Port = 8002, Status = GameManagement.Shared.Enums.ServerStatus.Online, MaxPlayers = 1000, CurrentPlayers = 678, Version = "1.0.0" },
        new GameManagement.Core.Entities.GameServer { Name = "测试服务器", Host = "test.game.com", Port = 8003, Status = GameManagement.Shared.Enums.ServerStatus.Maintenance, MaxPlayers = 100, CurrentPlayers = 0, Version = "1.1.0-beta" }
    };
    context.GameServers.AddRange(servers);

    // 添加渠道
    var channels = new[]
    {
        new GameManagement.Core.Entities.Channel { Name = "官网", Code = "official", IsActive = true, CommissionRate = 0 },
        new GameManagement.Core.Entities.Channel { Name = "微信", Code = "wechat", IsActive = true, CommissionRate = 0.05m },
        new GameManagement.Core.Entities.Channel { Name = "QQ", Code = "qq", IsActive = true, CommissionRate = 0.05m },
        new GameManagement.Core.Entities.Channel { Name = "应用宝", Code = "myapp", IsActive = true, CommissionRate = 0.10m }
    };
    context.Channels.AddRange(channels);

    await context.SaveChangesAsync();

    // 添加示例数据
    var random = new Random();
    var now = DateTime.UtcNow;

    // 添加访问记录（最近30天）
    var visitRecords = new List<GameManagement.Core.Entities.VisitRecord>();
    for (int i = 0; i < 30; i++)
    {
        var date = now.AddDays(-i);
        var dailyVisits = random.Next(100, 500);

        for (int j = 0; j < dailyVisits; j++)
        {
            visitRecords.Add(new GameManagement.Core.Entities.VisitRecord
            {
                IpAddress = $"192.168.{random.Next(1, 255)}.{random.Next(1, 255)}",
                UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                Channel = channels[random.Next(channels.Length)].Code,
                VisitTime = date.AddHours(random.Next(0, 24)).AddMinutes(random.Next(0, 60))
            });
        }
    }
    context.VisitRecords.AddRange(visitRecords);

    // 添加注册记录
    var registrationRecords = new List<GameManagement.Core.Entities.UserRegistration>();
    for (int i = 0; i < 1000; i++)
    {
        var channel = channels[random.Next(channels.Length)];
        registrationRecords.Add(new GameManagement.Core.Entities.UserRegistration
        {
            AccountId = $"user{i:D6}",
            IpAddress = $"192.168.{random.Next(1, 255)}.{random.Next(1, 255)}",
            ChannelId = channel.Id,
            ServerId = servers[random.Next(servers.Length)].Id,
            RegistrationTime = now.AddDays(-random.Next(0, 90)).AddHours(random.Next(0, 24)),
            HasCreatedCharacter = random.NextDouble() > 0.2, // 80%的用户创建了角色
            FirstLoginTime = random.NextDouble() > 0.1 ? now.AddDays(-random.Next(0, 90)) : null // 90%的用户至少登录过一次
        });
    }
    context.UserRegistrations.AddRange(registrationRecords);

    await context.SaveChangesAsync();

    // 添加登录记录
    var loginRecords = new List<GameManagement.Core.Entities.UserLogin>();
    foreach (var registration in registrationRecords.Where(r => r.FirstLoginTime.HasValue))
    {
        var loginCount = random.Next(1, 50);
        for (int i = 0; i < loginCount; i++)
        {
            var loginTime = registration.FirstLoginTime.Value.AddDays(random.Next(0, 30)).AddHours(random.Next(0, 24));
            var sessionDuration = TimeSpan.FromMinutes(random.Next(10, 300));

            loginRecords.Add(new GameManagement.Core.Entities.UserLogin
            {
                AccountId = registration.AccountId,
                IpAddress = $"192.168.{random.Next(1, 255)}.{random.Next(1, 255)}",
                ServerId = registration.ServerId,
                LoginTime = loginTime,
                LogoutTime = loginTime.Add(sessionDuration),
                SessionDuration = sessionDuration
            });
        }
    }
    context.UserLogins.AddRange(loginRecords);

    // 添加支付记录
    var paymentRecords = new List<GameManagement.Core.Entities.PaymentRecord>();
    var payingUsers = registrationRecords.Take(200).ToList(); // 20%的用户有付费

    foreach (var user in payingUsers)
    {
        var paymentCount = random.Next(1, 10);
        for (int i = 0; i < paymentCount; i++)
        {
            var amount = (decimal)(random.NextDouble() * 500 + 10); // 10-510元
            paymentRecords.Add(new GameManagement.Core.Entities.PaymentRecord
            {
                PlayerId = user.Id, // 这里简化处理，实际应该关联Player表
                ChannelId = user.ChannelId,
                OrderId = $"ORDER{DateTime.UtcNow.Ticks}{random.Next(1000, 9999)}",
                Amount = Math.Round(amount, 2),
                Currency = "CNY",
                DiamondsPurchased = (int)(amount * 10), // 1元=10钻石
                Status = GameManagement.Shared.Enums.PaymentStatus.Completed,
                PaymentMethod = new[] { "支付宝", "微信支付", "银行卡" }[random.Next(3)],
                CompletedAt = user.FirstLoginTime?.AddDays(random.Next(0, 30))
            });
        }
    }
    context.PaymentRecords.AddRange(paymentRecords);

    await context.SaveChangesAsync();
}
