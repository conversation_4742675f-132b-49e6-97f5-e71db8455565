{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"GameManagement.API/1.0.0": {"dependencies": {"GameManagement.Core": "1.0.0", "GameManagement.Infrastructure": "1.0.0", "GameManagement.Shared": "1.0.0", "Microsoft.AspNetCore.OpenApi": "9.0.6"}, "runtime": {"GameManagement.API.dll": {}}}, "Microsoft.AspNetCore.OpenApi/9.0.6": {"dependencies": {"Microsoft.OpenApi": "1.6.17"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.OpenApi/1.6.17": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.17.0", "fileVersion": "1.6.17.0"}}}, "GameManagement.Core/1.0.0": {"dependencies": {"GameManagement.Shared": "1.0.0"}, "runtime": {"GameManagement.Core.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "GameManagement.Infrastructure/1.0.0": {"dependencies": {"GameManagement.Core": "1.0.0", "GameManagement.Shared": "1.0.0"}, "runtime": {"GameManagement.Infrastructure.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "GameManagement.Shared/1.0.0": {"runtime": {"GameManagement.Shared.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"GameManagement.API/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.OpenApi/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-MOJ4DG1xd3NlWMYh+JdGNT9uvBtEk1XQU/FQlpNZFlAzM8t0oB5IimvnGlnK7jmyY4vQagLPB1xw1HjJ8CHrZg==", "path": "microsoft.aspnetcore.openapi/9.0.6", "hashPath": "microsoft.aspnetcore.openapi.9.0.6.nupkg.sha512"}, "Microsoft.OpenApi/1.6.17": {"type": "package", "serviceable": true, "sha512": "sha512-Le+kehlmrlQfuDFUt1zZ2dVwrhFQtKREdKBo+rexOwaCoYP0/qpgT9tLxCsZjsgR5Itk1UKPcbgO+FyaNid/bA==", "path": "microsoft.openapi/1.6.17", "hashPath": "microsoft.openapi.1.6.17.nupkg.sha512"}, "GameManagement.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "GameManagement.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "GameManagement.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}