/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/appsettings.Development.json
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/appsettings.json
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/GameManagement.API.staticwebassets.endpoints.json
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/GameManagement.API
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/GameManagement.API.deps.json
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/GameManagement.API.runtimeconfig.json
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/GameManagement.API.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/GameManagement.API.pdb
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/Microsoft.AspNetCore.OpenApi.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/Microsoft.OpenApi.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/GameManagement.Core.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/GameManagement.Infrastructure.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/GameManagement.Shared.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/GameManagement.Core.pdb
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/GameManagement.Infrastructure.pdb
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/GameManagement.Shared.pdb
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/GameManagement.API.csproj.AssemblyReference.cache
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/rpswa.dswa.cache.json
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/GameManagement.API.GeneratedMSBuildEditorConfig.editorconfig
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/GameManagement.API.AssemblyInfoInputs.cache
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/GameManagement.API.AssemblyInfo.cs
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/GameManagement.API.csproj.CoreCompileInputs.cache
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/GameManagement.API.MvcApplicationPartsAssemblyInfo.cs
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/GameManagement.API.MvcApplicationPartsAssemblyInfo.cache
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/rjimswa.dswa.cache.json
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/rjsmrazor.dswa.cache.json
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/rjsmcshtml.dswa.cache.json
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/scopedcss/bundle/GameManagement.API.styles.css
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/staticwebassets.build.json
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/staticwebassets.build.json.cache
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/staticwebassets.development.json
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/staticwebassets.build.endpoints.json
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/GameMana.F0D24A43.Up2Date
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/GameManagement.API.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/refint/GameManagement.API.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/GameManagement.API.pdb
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/GameManagement.API.genruntimeconfig.cache
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/Debug/net9.0/ref/GameManagement.API.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/Microsoft.EntityFrameworkCore.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/Microsoft.EntityFrameworkCore.Abstractions.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/Microsoft.EntityFrameworkCore.InMemory.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/Microsoft.Extensions.Caching.Abstractions.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/Microsoft.Extensions.Caching.Memory.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/Microsoft.Extensions.DependencyInjection.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/Microsoft.Extensions.Logging.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/Microsoft.Extensions.Logging.Abstractions.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/Microsoft.Extensions.Options.dll
/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/bin/Debug/net9.0/Microsoft.Extensions.Primitives.dll
