{"format": 1, "restore": {"/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/GameManagement.API.csproj": {}}, "projects": {"/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/GameManagement.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/GameManagement.API.csproj", "projectName": "GameManagement.API", "projectPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/GameManagement.API.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Core/GameManagement.Core.csproj": {"projectPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Core/GameManagement.Core.csproj"}, "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Infrastructure/GameManagement.Infrastructure.csproj": {"projectPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Infrastructure/GameManagement.Infrastructure.csproj"}, "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Shared/GameManagement.Shared.csproj": {"projectPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Shared/GameManagement.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Core/GameManagement.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Core/GameManagement.Core.csproj", "projectName": "GameManagement.Core", "projectPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Core/GameManagement.Core.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Core/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Shared/GameManagement.Shared.csproj": {"projectPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Shared/GameManagement.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Infrastructure/GameManagement.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Infrastructure/GameManagement.Infrastructure.csproj", "projectName": "GameManagement.Infrastructure", "projectPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Infrastructure/GameManagement.Infrastructure.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Infrastructure/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Core/GameManagement.Core.csproj": {"projectPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Core/GameManagement.Core.csproj"}, "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Shared/GameManagement.Shared.csproj": {"projectPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Shared/GameManagement.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Shared/GameManagement.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Shared/GameManagement.Shared.csproj", "projectName": "GameManagement.Shared", "projectPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Shared/GameManagement.Shared.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Shared/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}