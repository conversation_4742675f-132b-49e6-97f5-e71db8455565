{"version": 3, "targets": {"net9.0": {"Microsoft.AspNetCore.OpenApi/9.0.6": {"type": "package", "dependencies": {"Microsoft.OpenApi": "1.6.17"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.OpenApi/1.6.17": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}}, "GameManagement.Core/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"GameManagement.Shared": "1.0.0"}, "compile": {"bin/placeholder/GameManagement.Core.dll": {}}, "runtime": {"bin/placeholder/GameManagement.Core.dll": {}}}, "GameManagement.Infrastructure/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"GameManagement.Core": "1.0.0", "GameManagement.Shared": "1.0.0"}, "compile": {"bin/placeholder/GameManagement.Infrastructure.dll": {}}, "runtime": {"bin/placeholder/GameManagement.Infrastructure.dll": {}}}, "GameManagement.Shared/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "compile": {"bin/placeholder/GameManagement.Shared.dll": {}}, "runtime": {"bin/placeholder/GameManagement.Shared.dll": {}}}}}, "libraries": {"Microsoft.AspNetCore.OpenApi/9.0.6": {"sha512": "MOJ4DG1xd3NlWMYh+JdGNT9uvBtEk1XQU/FQlpNZFlAzM8t0oB5IimvnGlnK7jmyY4vQagLPB1xw1HjJ8CHrZg==", "type": "package", "path": "microsoft.aspnetcore.openapi/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0/Microsoft.AspNetCore.OpenApi.dll", "lib/net9.0/Microsoft.AspNetCore.OpenApi.xml", "microsoft.aspnetcore.openapi.9.0.6.nupkg.sha512", "microsoft.aspnetcore.openapi.nuspec"]}, "Microsoft.OpenApi/1.6.17": {"sha512": "Le+kehlmrlQfuDFUt1zZ2dVwrhFQtKREdKBo+rexOwaCoYP0/qpgT9tLxCsZjsgR5Itk1UKPcbgO+FyaNid/bA==", "type": "package", "path": "microsoft.openapi/1.6.17", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/netstandard2.0/Microsoft.OpenApi.dll", "lib/netstandard2.0/Microsoft.OpenApi.pdb", "lib/netstandard2.0/Microsoft.OpenApi.xml", "microsoft.openapi.1.6.17.nupkg.sha512", "microsoft.openapi.nuspec"]}, "GameManagement.Core/1.0.0": {"type": "project", "path": "../GameManagement.Core/GameManagement.Core.csproj", "msbuildProject": "../GameManagement.Core/GameManagement.Core.csproj"}, "GameManagement.Infrastructure/1.0.0": {"type": "project", "path": "../GameManagement.Infrastructure/GameManagement.Infrastructure.csproj", "msbuildProject": "../GameManagement.Infrastructure/GameManagement.Infrastructure.csproj"}, "GameManagement.Shared/1.0.0": {"type": "project", "path": "../GameManagement.Shared/GameManagement.Shared.csproj", "msbuildProject": "../GameManagement.Shared/GameManagement.Shared.csproj"}}, "projectFileDependencyGroups": {"net9.0": ["GameManagement.Core >= 1.0.0", "GameManagement.Infrastructure >= 1.0.0", "GameManagement.Shared >= 1.0.0", "Microsoft.AspNetCore.OpenApi >= 9.0.6"]}, "packageFolders": {"/Users/<USER>/.nuget/packages/": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/GameManagement.API.csproj", "projectName": "GameManagement.API", "projectPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/GameManagement.API.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.API/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Core/GameManagement.Core.csproj": {"projectPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Core/GameManagement.Core.csproj"}, "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Infrastructure/GameManagement.Infrastructure.csproj": {"projectPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Infrastructure/GameManagement.Infrastructure.csproj"}, "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Shared/GameManagement.Shared.csproj": {"projectPath": "/Users/<USER>/Documents/gamemanageweb/src/GameManagement.Shared/GameManagement.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.301/PortableRuntimeIdentifierGraph.json"}}}}