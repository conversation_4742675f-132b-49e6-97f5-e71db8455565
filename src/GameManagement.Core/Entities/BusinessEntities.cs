using GameManagement.Shared.Enums;

namespace GameManagement.Core.Entities;

// 访问记录实体
public class VisitRecord : BaseEntity
{
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string? Referrer { get; set; }
    public string? SessionId { get; set; }
    public int? UserId { get; set; }
    public string? Channel { get; set; }
    public DateTime VisitTime { get; set; }

    // Navigation properties
    public virtual User? User { get; set; }
}

// 客服管理相关实体
public class CustomerServiceTicket : BaseEntity
{
    public string TicketId { get; set; } = string.Empty;
    public int PlayerId { get; set; }
    public string PlayerAccountId { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TicketStatus Status { get; set; } = TicketStatus.Open;
    public TicketPriority Priority { get; set; } = TicketPriority.Medium;
    public TicketCategory Category { get; set; }
    public int? AssignedToUserId { get; set; }
    public DateTime? FirstResponseAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public DateTime? ClosedAt { get; set; }
    public string? Resolution { get; set; }
    public int? SatisfactionRating { get; set; } // 1-5 stars
    public string? SatisfactionFeedback { get; set; }
    
    // Navigation properties
    public virtual Player Player { get; set; } = null!;
    public virtual User? AssignedToUser { get; set; }
    public virtual ICollection<TicketMessage> Messages { get; set; } = new List<TicketMessage>();
}

public class TicketMessage : BaseEntity
{
    public int TicketId { get; set; }
    public int? UserId { get; set; }
    public string SenderName { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public bool IsFromPlayer { get; set; }
    public bool IsInternal { get; set; } = false; // 内部备注
    public string? Attachments { get; set; } // JSON格式的附件信息
    
    // Navigation properties
    public virtual CustomerServiceTicket Ticket { get; set; } = null!;
    public virtual User? User { get; set; }
}

// 渠道管理相关实体
public class Channel : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;
    public string? ContactPerson { get; set; }
    public string? ContactEmail { get; set; }
    public string? ContactPhone { get; set; }
    public decimal CommissionRate { get; set; } = 0;
    public string? ApiKey { get; set; }
    public string? ApiSecret { get; set; }
    public string? CallbackUrl { get; set; }
    public DateTime? ContractStartDate { get; set; }
    public DateTime? ContractEndDate { get; set; }
    
    // Navigation properties
    public virtual ICollection<UserRegistration> UserRegistrations { get; set; } = new List<UserRegistration>();
    public virtual ICollection<PaymentRecord> PaymentRecords { get; set; } = new List<PaymentRecord>();
    public virtual ICollection<ChannelData> ChannelData { get; set; } = new List<ChannelData>();
}

public class ChannelData : BaseEntity
{
    public int ChannelId { get; set; }
    public DateTime Date { get; set; }
    public string MetricName { get; set; } = string.Empty; // 指标名称：注册数、付费数等
    public decimal Value { get; set; }
    public string? AdditionalData { get; set; } // JSON格式的额外数据
    
    // Navigation properties
    public virtual Channel Channel { get; set; } = null!;
}

// 安全管理相关实体
public class SecurityEvent : BaseEntity
{
    public string EventId { get; set; } = string.Empty;
    public SecurityEventType EventType { get; set; }
    public string? PlayerId { get; set; }
    public string? IpAddress { get; set; }
    public string Description { get; set; } = string.Empty;
    public SecurityRiskLevel RiskLevel { get; set; }
    public SecurityEventStatus Status { get; set; } = SecurityEventStatus.Pending;
    public int? HandledByUserId { get; set; }
    public DateTime? HandledAt { get; set; }
    public string? HandlingNotes { get; set; }
    public string? Evidence { get; set; } // JSON格式的证据数据
    public bool IsAutoDetected { get; set; } = true;
    
    // Navigation properties
    public virtual User? HandledByUser { get; set; }
}

public class SecurityRule : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public SecurityEventType EventType { get; set; }
    public string Conditions { get; set; } = string.Empty; // JSON格式的规则条件
    public string Actions { get; set; } = string.Empty; // JSON格式的处理动作
    public bool IsActive { get; set; } = true;
    public int Priority { get; set; } = 0;
    public DateTime? LastTriggeredAt { get; set; }
    public int TriggerCount { get; set; } = 0;
}

// 游戏内容管理相关实体
public class GameItem : BaseEntity
{
    public string ItemId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public ItemType Type { get; set; }
    public ItemRarity Rarity { get; set; }
    public int Level { get; set; } = 1;
    public decimal Price { get; set; } = 0;
    public string? Properties { get; set; } // JSON格式的物品属性
    public bool IsActive { get; set; } = true;
    public string? IconUrl { get; set; }
    public int MaxStack { get; set; } = 1;
    public bool IsTradeable { get; set; } = true;
}

public class GameAnnouncement : BaseEntity
{
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public AnnouncementType Type { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public bool IsActive { get; set; } = true;
    public string? TargetServers { get; set; } // JSON格式的目标服务器列表
    public string? TargetPlayers { get; set; } // JSON格式的目标玩家条件
    public int Priority { get; set; } = 0;
    public int ViewCount { get; set; } = 0;
    public string? ImageUrl { get; set; }
    public string? LinkUrl { get; set; }
}

// 报表相关实体
public class Report : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ReportType Type { get; set; }
    public string Parameters { get; set; } = string.Empty; // JSON格式的报表参数
    public string? Data { get; set; } // JSON格式的报表数据
    public DateTime? GeneratedAt { get; set; }
    public int GeneratedByUserId { get; set; }
    public ReportStatus Status { get; set; } = ReportStatus.Pending;
    public string? FilePath { get; set; }
    public long? FileSize { get; set; }
    
    // Navigation properties
    public virtual User GeneratedByUser { get; set; } = null!;
}

// 服务器管理相关实体
public class ServerMaintenance : BaseEntity
{
    public int ServerId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public MaintenanceType Type { get; set; }
    public MaintenanceStatus Status { get; set; } = MaintenanceStatus.Scheduled;
    public bool NotifyPlayers { get; set; } = true;
    public string? NotificationMessage { get; set; }
    public int? EstimatedDurationMinutes { get; set; }
    
    // Navigation properties
    public virtual GameServer Server { get; set; } = null!;
}

public class ServerMonitoring : BaseEntity
{
    public int ServerId { get; set; }
    public DateTime Timestamp { get; set; }
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public double DiskUsage { get; set; }
    public double NetworkIn { get; set; }
    public double NetworkOut { get; set; }
    public int ActiveConnections { get; set; }
    public double ResponseTime { get; set; }
    public bool IsHealthy { get; set; } = true;
    public string? AlertMessage { get; set; }

    // Navigation properties
    public virtual GameServer Server { get; set; } = null!;
}

public class ServerAlert : BaseEntity
{
    public int ServerId { get; set; }
    public string AlertType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public AlertSeverity Severity { get; set; }
    public AlertStatus Status { get; set; } = AlertStatus.Active;
    public DateTime? AcknowledgedAt { get; set; }
    public string? AcknowledgedBy { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public string? ResolvedBy { get; set; }
    public string? Resolution { get; set; }
    public string? Metadata { get; set; } // JSON string for additional data

    // Navigation properties
    public virtual GameServer Server { get; set; } = null!;
}

public class AlertRule : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string MetricType { get; set; } = string.Empty; // CPU, Memory, Disk, Network, etc.
    public string Condition { get; set; } = string.Empty; // >, <, >=, <=, ==
    public double Threshold { get; set; }
    public AlertSeverity Severity { get; set; }
    public bool IsEnabled { get; set; } = true;
    public int? ServerId { get; set; } // null means applies to all servers

    // Navigation properties
    public virtual GameServer? Server { get; set; }
    public virtual ICollection<ServerAlert> Alerts { get; set; } = new List<ServerAlert>();
}

public class HealthCheck : BaseEntity
{
    public int ServerId { get; set; }
    public string CheckType { get; set; } = string.Empty;
    public bool IsHealthy { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? Message { get; set; }
    public double ResponseTime { get; set; }
    public DateTime CheckTime { get; set; }
    public string? Details { get; set; } // JSON string for additional details

    // Navigation properties
    public virtual GameServer Server { get; set; } = null!;
}

// 数据库备份相关实体
public class DatabaseBackup : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public BackupType Type { get; set; }
    public BackupStatus Status { get; set; } = BackupStatus.Pending;
    public string FilePath { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? ErrorMessage { get; set; }
    public bool IsVerified { get; set; }
    public DateTime? VerifiedAt { get; set; }
    public string? RemoteLocation { get; set; }
    public string? FileHash { get; set; }
}

public class BackupSchedule : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public BackupType Type { get; set; }
    public string CronExpression { get; set; } = string.Empty;
    public bool IsEnabled { get; set; } = true;
    public int RetentionDays { get; set; } = 30;
    public bool VerifyAfterBackup { get; set; } = true;
    public bool UploadToRemote { get; set; } = false;
    public string? RemoteLocation { get; set; }
    public DateTime? LastRun { get; set; }
    public DateTime? NextRun { get; set; }

    // Navigation properties
    public virtual ICollection<DatabaseBackup> Backups { get; set; } = new List<DatabaseBackup>();
}

public class BackupValidation : BaseEntity
{
    public int BackupId { get; set; }
    public bool IsValid { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
    public DateTime ValidatedAt { get; set; }
    public long FileSize { get; set; }
    public string FileHash { get; set; } = string.Empty;

    // Navigation properties
    public virtual DatabaseBackup Backup { get; set; } = null!;
}
