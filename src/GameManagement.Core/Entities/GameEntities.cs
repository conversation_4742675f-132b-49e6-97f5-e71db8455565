using GameManagement.Shared.Enums;

namespace GameManagement.Core.Entities;

public class GameServer : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string Host { get; set; } = string.Empty;
    public int Port { get; set; }
    public ServerStatus Status { get; set; }
    public string? Version { get; set; }
    public int MaxPlayers { get; set; }
    public int CurrentPlayers { get; set; }
    public DateTime? LastHeartbeat { get; set; }
    public string? Region { get; set; }
    
    // Navigation properties
    public virtual ICollection<Player> Players { get; set; } = new List<Player>();
    public virtual ICollection<ServerLog> ServerLogs { get; set; } = new List<ServerLog>();
}

public class Player : BaseEntity
{
    public string AccountId { get; set; } = string.Empty;
    public string Nickname { get; set; } = string.Empty;
    public int Level { get; set; }
    public string Class { get; set; } = string.Empty;
    public long Experience { get; set; }
    public decimal Gold { get; set; }
    public int Diamonds { get; set; }
    public int VipLevel { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public TimeSpan TotalPlayTime { get; set; }
    public string? IpAddress { get; set; }
    public int ServerId { get; set; }
    public bool IsBanned { get; set; } = false;
    public DateTime? BannedUntil { get; set; }
    public string? BanReason { get; set; }
    
    // Navigation properties
    public virtual GameServer Server { get; set; } = null!;
    public virtual ICollection<PaymentRecord> PaymentRecords { get; set; } = new List<PaymentRecord>();
    public virtual ICollection<PlayerActivity> PlayerActivities { get; set; } = new List<PlayerActivity>();
}

public class PaymentRecord : BaseEntity
{
    public int PlayerId { get; set; }
    public string OrderId { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "USD";
    public int DiamondsPurchased { get; set; }
    public PaymentStatus Status { get; set; }
    public string PaymentMethod { get; set; } = string.Empty;
    public string? TransactionId { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? FailureReason { get; set; }
    
    // Navigation properties
    public virtual Player Player { get; set; } = null!;
}

public class GameActivity : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ActivityStatus Status { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string? Conditions { get; set; }
    public string? Rewards { get; set; }
    public int ParticipantCount { get; set; }
    public int CompletionCount { get; set; }
    
    // Navigation properties
    public virtual ICollection<PlayerActivity> PlayerActivities { get; set; } = new List<PlayerActivity>();
}

public class PlayerActivity : BaseEntity
{
    public int PlayerId { get; set; }
    public int ActivityId { get; set; }
    public DateTime ParticipatedAt { get; set; }
    public bool IsCompleted { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? RewardsReceived { get; set; }
    
    // Navigation properties
    public virtual Player Player { get; set; } = null!;
    public virtual GameActivity Activity { get; set; } = null!;
}

public class ServerLog : BaseEntity
{
    public int ServerId { get; set; }
    public LogLevel Level { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? Exception { get; set; }
    public string? Source { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual GameServer Server { get; set; } = null!;
}

public class Announcement : BaseEntity
{
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public bool IsActive { get; set; } = true;
    public string? TargetServers { get; set; }
    public string? TargetPlayers { get; set; }
    public int Priority { get; set; } = 0;
}

// 运营数据相关实体
public class OperationalData : BaseEntity
{
    public DateTime Date { get; set; }
    public int? ServerId { get; set; }
    public string DataType { get; set; } = string.Empty; // 数据类型：访问、注册、登录、付费等
    public string MetricName { get; set; } = string.Empty; // 指标名称
    public decimal Value { get; set; }
    public string? AdditionalData { get; set; } // JSON格式的额外数据

    // Navigation properties
    public GameServer? Server { get; set; }
}

public class UserVisit : BaseEntity
{
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string? Referrer { get; set; }
    public string? Channel { get; set; } // 渠道来源
    public int? ServerId { get; set; }
    public DateTime VisitTime { get; set; }
    public bool IsUniqueVisit { get; set; } // 是否为独立访问

    // Navigation properties
    public GameServer? Server { get; set; }
}

public class UserRegistration : BaseEntity
{
    public string AccountId { get; set; } = string.Empty;
    public string? IpAddress { get; set; }
    public string? Channel { get; set; } // 注册渠道
    public int? ServerId { get; set; }
    public DateTime RegistrationTime { get; set; }
    public bool HasCreatedCharacter { get; set; } // 是否已创建角色
    public DateTime? FirstLoginTime { get; set; }

    // Navigation properties
    public GameServer? Server { get; set; }
}

public class UserLogin : BaseEntity
{
    public string AccountId { get; set; } = string.Empty;
    public string? IpAddress { get; set; }
    public int? ServerId { get; set; }
    public DateTime LoginTime { get; set; }
    public DateTime? LogoutTime { get; set; }
    public TimeSpan? SessionDuration { get; set; }
    public bool IsSameIpAsLastLogin { get; set; } // 是否与上次登录IP相同

    // Navigation properties
    public GameServer? Server { get; set; }
}

public class ClientVersion : BaseEntity
{
    public string Version { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty; // iOS, Android, PC等
    public DateTime ReleaseDate { get; set; }
    public bool IsActive { get; set; }
    public string? Description { get; set; }
}

public class ClientUsage : BaseEntity
{
    public string AccountId { get; set; } = string.Empty;
    public int ClientVersionId { get; set; }
    public string? IpAddress { get; set; }
    public DateTime InstallTime { get; set; }
    public DateTime? UninstallTime { get; set; }
    public DateTime LastUsedTime { get; set; }
    public int UsageCount { get; set; }

    // Navigation properties
    public ClientVersion? ClientVersion { get; set; }
}
