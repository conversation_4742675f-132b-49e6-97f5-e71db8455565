using GameManagement.Shared.Enums;

namespace GameManagement.Core.Entities;

public class User : BaseEntity
{
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PasswordHash { get; set; } = string.Empty;
    public string? DisplayName { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public bool IsActive { get; set; } = true;
    public string? RefreshToken { get; set; }
    public DateTime? RefreshTokenExpiryTime { get; set; }
    
    // Navigation properties
    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
    public virtual ICollection<AuditLog> AuditLogs { get; set; } = new List<AuditLog>();
    public virtual ICollection<CustomerServiceTicket> AssignedTickets { get; set; } = new List<CustomerServiceTicket>();
    public virtual ICollection<TicketMessage> TicketMessages { get; set; } = new List<TicketMessage>();
    public virtual ICollection<SecurityEvent> HandledSecurityEvents { get; set; } = new List<SecurityEvent>();
    public virtual ICollection<Report> GeneratedReports { get; set; } = new List<Report>();
    public virtual ICollection<VisitRecord> VisitRecords { get; set; } = new List<VisitRecord>();
}

public class UserRole : BaseEntity
{
    public int UserId { get; set; }
    public string RoleName { get; set; } = string.Empty;
    
    // Navigation properties
    public virtual User User { get; set; } = null!;
}

public class AuditLog : BaseEntity
{
    public int? UserId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string EntityName { get; set; } = string.Empty;
    public string? EntityId { get; set; }
    public string? OldValues { get; set; }
    public string? NewValues { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    
    // Navigation properties
    public virtual User? User { get; set; }
}

public abstract class BaseEntity
{
    public int Id { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; }
    public string? CreatedBy { get; set; }
    public string? UpdatedBy { get; set; }
    public bool IsDeleted { get; set; } = false;
}
