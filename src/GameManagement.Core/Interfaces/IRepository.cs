using System.Linq.Expressions;
using GameManagement.Shared.Enums;
using GameManagement.Core.Entities;

namespace GameManagement.Core.Interfaces;

public interface IRepository<T> where T : BaseEntity
{
    Task<T?> GetByIdAsync(int id);
    Task<IEnumerable<T>> GetAllAsync();
    Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);
    Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate);
    Task<T> AddAsync(T entity);
    Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities);
    Task UpdateAsync(T entity);
    Task UpdateRangeAsync(IEnumerable<T> entities);
    Task DeleteAsync(T entity);
    Task DeleteRangeAsync(IEnumerable<T> entities);
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null);
    Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate);
    Task<IEnumerable<T>> GetPagedAsync(int page, int pageSize, Expression<Func<T, bool>>? predicate = null);
}

public interface IUserRepository : IRepository<User>
{
    Task<User?> GetByUsernameAsync(string username);
    Task<User?> GetByEmailAsync(string email);
    Task<User?> GetByRefreshTokenAsync(string refreshToken);
    Task<IEnumerable<User>> GetUsersByRoleAsync(string role);
    Task<bool> IsUsernameExistsAsync(string username);
    Task<bool> IsEmailExistsAsync(string email);
}

public interface IPlayerRepository : IRepository<Player>
{
    Task<Player?> GetByAccountIdAsync(string accountId);
    Task<IEnumerable<Player>> GetPlayersByServerAsync(int serverId);
    Task<IEnumerable<Player>> GetTopPlayersByLevelAsync(int count);
    Task<IEnumerable<Player>> GetVipPlayersAsync(int minVipLevel = 1);
    Task<IEnumerable<Player>> GetActivePlayersAsync(DateTime since);
    Task<decimal> GetTotalRevenueAsync();
    Task<decimal> GetRevenueByDateRangeAsync(DateTime startDate, DateTime endDate);
    Task<int> GetActivePlayerCountAsync();
    Task<Dictionary<string, int>> GetPlayerDistributionByLevelAsync();
}

public interface IPaymentRepository : IRepository<PaymentRecord>
{
    Task<IEnumerable<PaymentRecord>> GetPaymentsByPlayerAsync(int playerId);
    Task<IEnumerable<PaymentRecord>> GetPaymentsByDateRangeAsync(DateTime startDate, DateTime endDate);
    Task<decimal> GetTotalRevenueAsync();
    Task<decimal> GetRevenueByDateAsync(DateTime date);
    Task<Dictionary<string, decimal>> GetRevenueByPaymentMethodAsync();
    Task<IEnumerable<PaymentRecord>> GetFailedPaymentsAsync();
    Task<IEnumerable<PaymentRecord>> GetTopPayersAsync(int count);
}

public interface IGameServerRepository : IRepository<GameServer>
{
    Task<IEnumerable<GameServer>> GetOnlineServersAsync();
    Task<IEnumerable<GameServer>> GetServersByStatusAsync(ServerStatus status);
    Task<GameServer?> GetServerByNameAsync(string name);
    Task UpdateServerStatusAsync(int serverId, ServerStatus status);
    Task UpdatePlayerCountAsync(int serverId, int currentPlayers);
}

public interface IActivityRepository : IRepository<GameActivity>
{
    Task<IEnumerable<GameActivity>> GetActiveActivitiesAsync();
    Task<IEnumerable<GameActivity>> GetActivitiesByDateRangeAsync(DateTime startDate, DateTime endDate);
    Task<Dictionary<int, int>> GetActivityParticipationStatsAsync();
}

public interface IAuditLogRepository : IRepository<AuditLog>
{
    Task<IEnumerable<AuditLog>> GetLogsByUserAsync(int userId);
    Task<IEnumerable<AuditLog>> GetLogsByEntityAsync(string entityName, string entityId);
    Task<IEnumerable<AuditLog>> GetLogsByDateRangeAsync(DateTime startDate, DateTime endDate);
    Task<IEnumerable<AuditLog>> GetLogsByActionAsync(string action);
}

// 运营数据相关Repository接口
public interface IOperationalDataRepository : IRepository<OperationalData>
{
    Task<IEnumerable<OperationalData>> GetByDateRangeAsync(DateTime startDate, DateTime endDate);
    Task<IEnumerable<OperationalData>> GetByDataTypeAsync(string dataType);
    Task<IEnumerable<OperationalData>> GetByServerAsync(int serverId);
    Task<decimal> GetMetricValueAsync(string dataType, string metricName, DateTime date, int? serverId = null);
    Task<Dictionary<DateTime, decimal>> GetMetricTrendAsync(string dataType, string metricName, DateTime startDate, DateTime endDate, int? serverId = null);
}

public interface IUserVisitRepository : IRepository<UserVisit>
{
    Task<int> GetVisitCountByDateAsync(DateTime date);
    Task<int> GetUniqueVisitCountByDateAsync(DateTime date);
    Task<IEnumerable<UserVisit>> GetVisitsByChannelAsync(string channel, DateTime startDate, DateTime endDate);
    Task<Dictionary<string, int>> GetVisitsByChannelStatsAsync(DateTime startDate, DateTime endDate);
    Task<Dictionary<int, int>> GetVisitsByHourStatsAsync(DateTime date);
    Task<bool> IsUniqueVisitAsync(string ipAddress, DateTime date);
}

public interface IUserRegistrationRepository : IRepository<UserRegistration>
{
    Task<int> GetRegistrationCountByDateAsync(DateTime date);
    Task<IEnumerable<UserRegistration>> GetRegistrationsByChannelAsync(string channel, DateTime startDate, DateTime endDate);
    Task<Dictionary<string, int>> GetRegistrationsByChannelStatsAsync(DateTime startDate, DateTime endDate);
    Task<int> GetUsersWithoutCharacterCountAsync();
    Task<int> GetUsersNeverLoggedInCountAsync();
    Task<UserRegistration?> GetByAccountIdAsync(string accountId);
}

public interface IUserLoginRepository : IRepository<UserLogin>
{
    Task<int> GetLoginCountByDateAsync(DateTime date);
    Task<IEnumerable<UserLogin>> GetLoginsByAccountIdAsync(string accountId);
    Task<int> GetSameIpLoginCountAsync(DateTime startDate, DateTime endDate);
    Task<double> GetAverageSessionTimeAsync(DateTime startDate, DateTime endDate);
    Task<int> GetActiveUserCountAsync(DateTime date);
    Task<Dictionary<int, int>> GetLoginsByHourStatsAsync(DateTime date);
}

public interface IClientVersionRepository : IRepository<ClientVersion>
{
    Task<IEnumerable<ClientVersion>> GetActiveVersionsAsync();
    Task<ClientVersion?> GetByVersionAsync(string version, string platform);
    Task<Dictionary<string, int>> GetVersionUsageStatsAsync();
}

public interface IClientUsageRepository : IRepository<ClientUsage>
{
    Task<int> GetInstallCountByDateAsync(DateTime date);
    Task<int> GetUninstallCountByDateAsync(DateTime date);
    Task<int> GetActiveInstallCountAsync();
    Task<Dictionary<string, int>> GetInstallsByVersionStatsAsync();
    Task<Dictionary<string, int>> GetInstallsByPlatformStatsAsync();
    Task<IEnumerable<ClientUsage>> GetUsageByAccountIdAsync(string accountId);
}

public interface IUnitOfWork : IDisposable
{
    IUserRepository Users { get; }
    IPlayerRepository Players { get; }
    IPaymentRepository Payments { get; }
    IGameServerRepository GameServers { get; }
    IActivityRepository Activities { get; }
    IAuditLogRepository AuditLogs { get; }
    IOperationalDataRepository OperationalData { get; }
    IUserVisitRepository UserVisits { get; }
    IUserRegistrationRepository UserRegistrations { get; }
    IUserLoginRepository UserLogins { get; }
    IClientVersionRepository ClientVersions { get; }
    IClientUsageRepository ClientUsage { get; }

    Task<int> SaveChangesAsync();
    Task BeginTransactionAsync();
    Task CommitTransactionAsync();
    Task RollbackTransactionAsync();
}
