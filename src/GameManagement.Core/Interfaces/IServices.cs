using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using GameManagement.Core.Entities;

namespace GameManagement.Core.Interfaces;

public interface IAuthService
{
    Task<LoginResponseDto> LoginAsync(LoginDto loginDto);
    Task<LoginResponseDto> RefreshTokenAsync(string refreshToken);
    Task LogoutAsync(string refreshToken);
    Task<UserDto> RegisterAsync(CreateUserDto createUserDto);
    Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);
    Task<bool> ResetPasswordAsync(string email);
}

public interface IUserService
{
    Task<UserDto?> GetUserByIdAsync(int id);
    Task<UserDto?> GetUserByUsernameAsync(string username);
    Task<IEnumerable<UserDto>> GetAllUsersAsync();
    Task<UserDto> CreateUserAsync(CreateUserDto createUserDto);
    Task<UserDto> UpdateUserAsync(int id, UpdateUserDto updateUserDto);
    Task<bool> DeleteUserAsync(int id);
    Task<bool> ActivateUserAsync(int id);
    Task<bool> DeactivateUserAsync(int id);
    Task<IEnumerable<UserDto>> GetUsersByRoleAsync(string role);
}

public interface IPlayerService
{
    Task<PlayerDto?> GetPlayerByIdAsync(int id);
    Task<PlayerDto?> GetPlayerByAccountIdAsync(string accountId);
    Task<IEnumerable<PlayerDto>> GetPlayersAsync(int page, int pageSize);
    Task<IEnumerable<PlayerDto>> SearchPlayersAsync(string searchTerm);
    Task<PlayerStatsDto> GetPlayerStatsAsync();
    Task<IEnumerable<PlayerDto>> GetTopPlayersByLevelAsync(int count);
    Task<IEnumerable<PlayerDto>> GetVipPlayersAsync(int minVipLevel = 1);
    Task<bool> BanPlayerAsync(int playerId, DateTime? bannedUntil, string reason);
    Task<bool> UnbanPlayerAsync(int playerId);
    Task<bool> UpdatePlayerDataAsync(int playerId, UpdatePlayerDto updatePlayerDto);
}

public interface IPaymentService
{
    Task<PaymentDto?> GetPaymentByIdAsync(int id);
    Task<IEnumerable<PaymentDto>> GetPaymentsByPlayerAsync(int playerId);
    Task<IEnumerable<PaymentDto>> GetPaymentsAsync(int page, int pageSize);
    Task<PaymentStatsDto> GetPaymentStatsAsync();
    Task<PaymentStatsDto> GetPaymentStatsByDateRangeAsync(DateTime startDate, DateTime endDate);
    Task<IEnumerable<PaymentDto>> GetFailedPaymentsAsync();
    Task<bool> ProcessRefundAsync(int paymentId, string reason);
    Task<bool> RetryFailedPaymentAsync(int paymentId);
}

public interface IGameServerService
{
    // 基础CRUD操作
    Task<IEnumerable<ServerStatusDto>> GetAllServersAsync();
    Task<ServerStatusDto?> GetServerByIdAsync(int serverId);
    Task<ServerStatusDto?> GetServerByNameAsync(string name);
    Task<ServerStatusDto> CreateServerAsync(CreateServerDto createServerDto);
    Task<ServerStatusDto?> UpdateServerAsync(int serverId, UpdateServerDto updateServerDto);
    Task<bool> DeleteServerAsync(int serverId);

    // 服务器状态管理
    Task<bool> UpdateServerStatusAsync(int serverId, ServerStatus status);
    Task<bool> UpdatePlayerCountAsync(int serverId, int currentPlayers);
    Task<IEnumerable<ServerStatusDto>> GetServersByStatusAsync(ServerStatus status);
    Task<IEnumerable<ServerStatusDto>> GetOnlineServersAsync();

    // 服务器操作
    Task<bool> RestartServerAsync(int serverId);
    Task<bool> StartServerAsync(int serverId);
    Task<bool> StopServerAsync(int serverId);
    Task<bool> SendServerCommandAsync(int serverId, string command);

    // 服务器统计
    Task<ServerStatsDto> GetServerStatsAsync(int serverId);
    Task<IEnumerable<ServerStatsDto>> GetAllServerStatsAsync();
    Task<ServerPerformanceDto> GetServerPerformanceAsync(int serverId, DateTime? startDate = null, DateTime? endDate = null);

    // 服务器日志
    Task<IEnumerable<ServerLogDto>> GetServerLogsAsync(int serverId, int page = 1, int pageSize = 50, LogLevel? level = null);
    Task<ServerLogDto> AddServerLogAsync(CreateServerLogDto createLogDto);

    // 服务器维护
    Task<IEnumerable<ServerMaintenanceDto>> GetServerMaintenancesAsync(int serverId);
    Task<ServerMaintenanceDto> ScheduleMaintenanceAsync(CreateServerMaintenanceDto createMaintenanceDto);
    Task<bool> StartMaintenanceAsync(int maintenanceId);
    Task<bool> CompleteMaintenanceAsync(int maintenanceId);
}

public interface IServerMonitoringService
{
    // 基础监控
    Task<IEnumerable<ServerStatusDto>> GetAllServerStatusAsync();
    Task<ServerStatusDto?> GetServerStatusAsync(int serverId);
    Task<bool> UpdateServerStatusAsync(int serverId, ServerStatus status);
    Task<ServerHealthDto> GetServerHealthAsync(int serverId);
    Task<IEnumerable<ServerHealthDto>> GetAllServerHealthAsync();
    Task<IEnumerable<ServerLogDto>> GetServerLogsAsync(int serverId, int page, int pageSize);
    Task<bool> RestartServerAsync(int serverId);
    Task<bool> SendServerCommandAsync(int serverId, string command);

    // 性能监控
    Task<ServerMonitoringDto> AddMonitoringDataAsync(CreateServerMonitoringDto createMonitoringDto);
    Task<IEnumerable<ServerMonitoringDto>> GetMonitoringDataAsync(int serverId, DateTime? startDate = null, DateTime? endDate = null);
    Task<ServerPerformanceDto> GetServerPerformanceAsync(int serverId, DateTime? startDate = null, DateTime? endDate = null);
    Task<IEnumerable<ServerPerformanceDto>> GetAllServerPerformanceAsync(DateTime? startDate = null, DateTime? endDate = null);

    // 告警管理
    Task<IEnumerable<ServerAlertDto>> GetActiveAlertsAsync();
    Task<IEnumerable<ServerAlertDto>> GetServerAlertsAsync(int serverId);
    Task<ServerAlertDto> CreateAlertAsync(CreateServerAlertDto createAlertDto);
    Task<bool> AcknowledgeAlertAsync(int alertId, string acknowledgedBy);
    Task<bool> ResolveAlertAsync(int alertId, string resolvedBy, string resolution);
    Task<bool> DeleteAlertAsync(int alertId);

    // 告警规则管理
    Task<IEnumerable<AlertRuleDto>> GetAlertRulesAsync();
    Task<AlertRuleDto?> GetAlertRuleByIdAsync(int ruleId);
    Task<AlertRuleDto> CreateAlertRuleAsync(CreateAlertRuleDto createRuleDto);
    Task<AlertRuleDto?> UpdateAlertRuleAsync(int ruleId, UpdateAlertRuleDto updateRuleDto);
    Task<bool> DeleteAlertRuleAsync(int ruleId);
    Task<bool> ToggleAlertRuleAsync(int ruleId, bool isEnabled);

    // 健康检查
    Task<bool> PerformHealthCheckAsync(int serverId);
    Task<IEnumerable<HealthCheckResultDto>> PerformAllHealthChecksAsync();
    Task<HealthCheckResultDto> GetLatestHealthCheckAsync(int serverId);

    // 监控统计
    Task<MonitoringStatsDto> GetMonitoringStatsAsync();
    Task<Dictionary<string, object>> GetServerMetricsAsync(int serverId, string metricType, DateTime? startDate = null, DateTime? endDate = null);
}

public interface IDatabaseBackupService
{
    // 备份管理
    Task<IEnumerable<DatabaseBackupDto>> GetBackupsAsync();
    Task<DatabaseBackupDto?> GetBackupByIdAsync(int backupId);
    Task<DatabaseBackupDto> CreateBackupAsync(CreateDatabaseBackupDto createBackupDto);
    Task<bool> DeleteBackupAsync(int backupId);
    Task<bool> RestoreBackupAsync(int backupId);

    // 备份调度
    Task<IEnumerable<BackupScheduleDto>> GetBackupSchedulesAsync();
    Task<BackupScheduleDto?> GetBackupScheduleByIdAsync(int scheduleId);
    Task<BackupScheduleDto> CreateBackupScheduleAsync(CreateBackupScheduleDto createScheduleDto);
    Task<BackupScheduleDto?> UpdateBackupScheduleAsync(int scheduleId, UpdateBackupScheduleDto updateScheduleDto);
    Task<bool> DeleteBackupScheduleAsync(int scheduleId);
    Task<bool> ToggleBackupScheduleAsync(int scheduleId, bool isEnabled);

    // 备份验证
    Task<BackupValidationResultDto> ValidateBackupAsync(int backupId);
    Task<IEnumerable<BackupValidationResultDto>> ValidateAllBackupsAsync();

    // 备份统计
    Task<BackupStatsDto> GetBackupStatsAsync();
    Task<long> GetBackupSizeAsync(int backupId);
    Task<IEnumerable<DatabaseBackupDto>> GetExpiredBackupsAsync();
    Task<int> CleanupExpiredBackupsAsync();

    // 远程存储
    Task<bool> UploadBackupToRemoteAsync(int backupId, string remoteLocation);
    Task<bool> DownloadBackupFromRemoteAsync(string remoteLocation, string localPath);
    Task<IEnumerable<RemoteBackupDto>> GetRemoteBackupsAsync();
}

public interface IActivityService
{
    // Basic CRUD operations
    Task<ActivityDto?> GetActivityByIdAsync(int id);
    Task<IEnumerable<ActivityDto>> GetActivitiesAsync();
    Task<IEnumerable<ActivityDto>> GetActiveActivitiesAsync();
    Task<IEnumerable<ActivityDto>> GetActivitiesByStatusAsync(ActivityStatus status);
    Task<IEnumerable<ActivityDto>> GetActivitiesByDateRangeAsync(DateTime startDate, DateTime endDate);
    Task<ActivityDto> CreateActivityAsync(CreateActivityDto createActivityDto);
    Task<ActivityDto> UpdateActivityAsync(int id, UpdateActivityDto updateActivityDto);
    Task<bool> DeleteActivityAsync(int id);

    // Activity status management
    Task<bool> StartActivityAsync(int id);
    Task<bool> PauseActivityAsync(int id);
    Task<bool> ResumeActivityAsync(int id);
    Task<bool> EndActivityAsync(int id);
    Task<bool> CancelActivityAsync(int id);

    // Activity statistics and analytics
    Task<ActivityStatsDto> GetActivityStatsAsync(int id);
    Task<IEnumerable<ActivityStatsDto>> GetAllActivitiesStatsAsync();
    Task<Dictionary<string, int>> GetActivityParticipationTrendsAsync(int activityId, int days = 30);
    Task<Dictionary<string, object>> GetActivityPerformanceMetricsAsync(int activityId);

    // Player participation management
    Task<bool> AddPlayerToActivityAsync(int activityId, int playerId);
    Task<bool> RemovePlayerFromActivityAsync(int activityId, int playerId);
    Task<bool> CompleteActivityForPlayerAsync(int activityId, int playerId, string? rewardsReceived = null);
    Task<IEnumerable<PlayerActivityDto>> GetActivityParticipantsAsync(int activityId);
    Task<IEnumerable<ActivityDto>> GetPlayerActivitiesAsync(int playerId);

    // Activity rewards and conditions
    Task<bool> UpdateActivityRewardsAsync(int activityId, string rewards);
    Task<bool> UpdateActivityConditionsAsync(int activityId, string conditions);
    Task<bool> DistributeRewardsAsync(int activityId);

    // Activity monitoring and reporting
    Task<Dictionary<ActivityStatus, int>> GetActivityStatusSummaryAsync();
    Task<IEnumerable<ActivityDto>> GetExpiringActivitiesAsync(int daysAhead = 7);
    Task<IEnumerable<ActivityDto>> GetPopularActivitiesAsync(int limit = 10);
}

// 公告管理服务接口
public interface IAnnouncementService
{
    // 基础CRUD操作
    Task<IEnumerable<GameAnnouncementDto>> GetAnnouncementsAsync(int page = 1, int pageSize = 50);
    Task<GameAnnouncementDto?> GetAnnouncementByIdAsync(int id);
    Task<GameAnnouncementDto> CreateAnnouncementAsync(CreateGameAnnouncementDto createAnnouncementDto);
    Task<GameAnnouncementDto> UpdateAnnouncementAsync(int id, UpdateGameAnnouncementDto updateAnnouncementDto);
    Task<bool> DeleteAnnouncementAsync(int id);

    // 状态管理
    Task<bool> ActivateAnnouncementAsync(int id);
    Task<bool> DeactivateAnnouncementAsync(int id);
    Task<bool> PublishAnnouncementAsync(int id);
    Task<bool> UnpublishAnnouncementAsync(int id);

    // 筛选和搜索
    Task<IEnumerable<GameAnnouncementDto>> GetAnnouncementsByTypeAsync(AnnouncementType type);
    Task<IEnumerable<GameAnnouncementDto>> GetActiveAnnouncementsAsync();
    Task<IEnumerable<GameAnnouncementDto>> GetAnnouncementsByDateRangeAsync(DateTime startDate, DateTime endDate);
    Task<IEnumerable<GameAnnouncementDto>> SearchAnnouncementsAsync(string searchTerm);
    Task<IEnumerable<GameAnnouncementDto>> GetAnnouncementsByPriorityAsync(int minPriority);

    // 目标管理
    Task<IEnumerable<GameAnnouncementDto>> GetAnnouncementsForServerAsync(int serverId);
    Task<IEnumerable<GameAnnouncementDto>> GetAnnouncementsForPlayerAsync(string playerId);
    Task<bool> UpdateAnnouncementTargetsAsync(int id, UpdateAnnouncementTargetsDto targetsDto);

    // 批量操作
    Task<int> BatchActivateAnnouncementsAsync(IEnumerable<int> announcementIds);
    Task<int> BatchDeactivateAnnouncementsAsync(IEnumerable<int> announcementIds);
    Task<int> BatchDeleteAnnouncementsAsync(IEnumerable<int> announcementIds);
    Task<int> BatchPublishAnnouncementsAsync(IEnumerable<int> announcementIds);
    Task<int> BatchUnpublishAnnouncementsAsync(IEnumerable<int> announcementIds);

    // 统计和分析
    Task<AnnouncementStatsDto> GetAnnouncementStatsAsync();
    Task<IEnumerable<AnnouncementViewStatsDto>> GetAnnouncementViewStatsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<IEnumerable<GameAnnouncementDto>> GetPopularAnnouncementsAsync(int limit = 10);
    Task<AnnouncementPerformanceDto> GetAnnouncementPerformanceAsync(int id, DateTime? startDate = null, DateTime? endDate = null);

    // 高级功能
    Task<bool> ScheduleAnnouncementAsync(int id, DateTime publishTime);
    Task<bool> UpdateAnnouncementPriorityAsync(int id, int priority);
    Task<bool> IncrementViewCountAsync(int id);
    Task<IEnumerable<GameAnnouncementDto>> GetScheduledAnnouncementsAsync();
    Task<IEnumerable<GameAnnouncementDto>> GetExpiredAnnouncementsAsync();
    Task<int> CleanupExpiredAnnouncementsAsync();

    // 导入导出
    Task<IEnumerable<GameAnnouncementDto>> ImportAnnouncementsAsync(IEnumerable<CreateGameAnnouncementDto> announcements);
    Task<string> ExportAnnouncementsAsync(AnnouncementExportFormat format, DateTime? startDate = null, DateTime? endDate = null);
}

// 渠道管理服务接口
public interface IChannelService
{
    // 基础CRUD操作
    Task<IEnumerable<ChannelDto>> GetChannelsAsync(int page = 1, int pageSize = 50);
    Task<ChannelDto?> GetChannelByIdAsync(int id);
    Task<ChannelDto?> GetChannelByCodeAsync(string code);
    Task<ChannelDto> CreateChannelAsync(CreateChannelDto createChannelDto);
    Task<ChannelDto> UpdateChannelAsync(int id, UpdateChannelDto updateChannelDto);
    Task<bool> DeleteChannelAsync(int id);

    // 状态管理
    Task<bool> ActivateChannelAsync(int id);
    Task<bool> DeactivateChannelAsync(int id);
    Task<IEnumerable<ChannelDto>> GetActiveChannelsAsync();
    Task<IEnumerable<ChannelDto>> GetInactiveChannelsAsync();

    // 筛选和搜索
    Task<IEnumerable<ChannelDto>> SearchChannelsAsync(string searchTerm);
    Task<IEnumerable<ChannelDto>> GetChannelsByCommissionRangeAsync(decimal minRate, decimal maxRate);
    Task<IEnumerable<ChannelDto>> GetChannelsWithActiveContractsAsync();
    Task<IEnumerable<ChannelDto>> GetChannelsWithExpiringContractsAsync(int daysFromNow = 30);

    // 渠道数据管理
    Task<bool> AddChannelDataAsync(int channelId, string metricName, decimal value, DateTime date, string? additionalData = null);
    Task<IEnumerable<ChannelDataDto>> GetChannelDataAsync(int channelId, DateTime? startDate = null, DateTime? endDate = null);
    Task<IEnumerable<ChannelDataDto>> GetChannelDataByMetricAsync(int channelId, string metricName, DateTime? startDate = null, DateTime? endDate = null);
    Task<bool> UpdateChannelDataAsync(int dataId, decimal value, string? additionalData = null);
    Task<bool> DeleteChannelDataAsync(int dataId);

    // 统计和分析
    Task<ChannelStatsDto> GetChannelStatsAsync();
    Task<ChannelPerformanceDto> GetChannelPerformanceAsync(int channelId, DateTime? startDate = null, DateTime? endDate = null);
    Task<IEnumerable<ChannelPerformanceDto>> GetAllChannelsPerformanceAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<IEnumerable<ChannelDto>> GetTopPerformingChannelsAsync(int count = 10, string metric = "revenue");
    Task<ChannelComparisonDto> CompareChannelsAsync(IEnumerable<int> channelIds, DateTime? startDate = null, DateTime? endDate = null);

    // 佣金管理
    Task<bool> UpdateChannelCommissionRateAsync(int id, decimal commissionRate);
    Task<decimal> CalculateChannelCommissionAsync(int channelId, DateTime? startDate = null, DateTime? endDate = null);
    Task<IEnumerable<ChannelCommissionDto>> GetChannelCommissionsAsync(DateTime? startDate = null, DateTime? endDate = null);

    // API密钥管理
    Task<bool> GenerateApiKeyAsync(int channelId);
    Task<bool> RegenerateApiKeyAsync(int channelId);
    Task<bool> RevokeApiKeyAsync(int channelId);
    Task<bool> ValidateApiKeyAsync(string apiKey);
    Task<ChannelDto?> GetChannelByApiKeyAsync(string apiKey);

    // 合同管理
    Task<bool> UpdateChannelContractAsync(int id, DateTime startDate, DateTime endDate);
    Task<bool> RenewChannelContractAsync(int id, DateTime newEndDate);
    Task<IEnumerable<ChannelDto>> GetChannelsWithExpiredContractsAsync();

    // 批量操作
    Task<bool> BatchActivateChannelsAsync(IEnumerable<int> channelIds);
    Task<bool> BatchDeactivateChannelsAsync(IEnumerable<int> channelIds);
    Task<bool> BatchUpdateCommissionRateAsync(Dictionary<int, decimal> channelCommissions);
    Task<bool> BatchDeleteChannelsAsync(IEnumerable<int> channelIds);

    // 导入导出
    Task<IEnumerable<ChannelDto>> ImportChannelsAsync(IEnumerable<CreateChannelDto> channels);
    Task<string> ExportChannelsAsync(ChannelExportFormat format, DateTime? startDate = null, DateTime? endDate = null);
    Task<string> ExportChannelDataAsync(int channelId, ChannelExportFormat format, DateTime? startDate = null, DateTime? endDate = null);
}

public interface IItemService
{
    // 基础CRUD操作
    Task<GameItemDto?> GetItemByIdAsync(int id);
    Task<GameItemDto?> GetItemByItemIdAsync(string itemId);
    Task<IEnumerable<GameItemDto>> GetItemsAsync(int page = 1, int pageSize = 50);
    Task<IEnumerable<GameItemDto>> SearchItemsAsync(string searchTerm);
    Task<GameItemDto> CreateItemAsync(CreateGameItemDto createItemDto);
    Task<GameItemDto> UpdateItemAsync(int id, UpdateGameItemDto updateItemDto);
    Task<bool> DeleteItemAsync(int id);
    Task<bool> ActivateItemAsync(int id);
    Task<bool> DeactivateItemAsync(int id);

    // 分类和筛选
    Task<IEnumerable<GameItemDto>> GetItemsByTypeAsync(ItemType type);
    Task<IEnumerable<GameItemDto>> GetItemsByRarityAsync(ItemRarity rarity);
    Task<IEnumerable<GameItemDto>> GetItemsByLevelRangeAsync(int minLevel, int maxLevel);
    Task<IEnumerable<GameItemDto>> GetActiveItemsAsync();
    Task<IEnumerable<GameItemDto>> GetTradeableItemsAsync();

    // 价格管理
    Task<bool> UpdateItemPriceAsync(int id, decimal newPrice);
    Task<bool> BatchUpdatePricesAsync(Dictionary<int, decimal> priceUpdates);
    Task<IEnumerable<GameItemDto>> GetItemsByPriceRangeAsync(decimal minPrice, decimal maxPrice);

    // 库存和统计
    Task<ItemStatsDto> GetItemStatsAsync();
    Task<IEnumerable<ItemUsageStatsDto>> GetItemUsageStatsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<IEnumerable<GameItemDto>> GetPopularItemsAsync(int count = 10);
    Task<IEnumerable<GameItemDto>> GetTopSellingItemsAsync(int count = 10);

    // 属性管理
    Task<bool> UpdateItemPropertiesAsync(int id, string properties);
    Task<IEnumerable<GameItemDto>> GetItemsByPropertyAsync(string propertyKey, string propertyValue);

    // 批量操作
    Task<bool> BatchActivateItemsAsync(IEnumerable<int> itemIds);
    Task<bool> BatchDeactivateItemsAsync(IEnumerable<int> itemIds);
    Task<bool> BatchDeleteItemsAsync(IEnumerable<int> itemIds);
    Task<IEnumerable<GameItemDto>> ImportItemsAsync(IEnumerable<CreateGameItemDto> items);

    // 监控和分析
    Task<ItemPerformanceDto> GetItemPerformanceAsync(int id, DateTime? startDate = null, DateTime? endDate = null);
    Task<IEnumerable<ItemTrendDto>> GetItemTrendsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<ItemInventoryDto> GetItemInventoryAsync();
}

public interface IReportService
{
    Task<DashboardStatsDto> GetDashboardStatsAsync();
    Task<RevenueReportDto> GetRevenueReportAsync(DateTime startDate, DateTime endDate);
    Task<PlayerReportDto> GetPlayerReportAsync(DateTime startDate, DateTime endDate);
    Task<RetentionReportDto> GetRetentionReportAsync(DateTime startDate, DateTime endDate);
    Task<byte[]> ExportReportAsync(string reportType, DateTime startDate, DateTime endDate, string format);
}

public interface IAuditService
{
    Task LogActionAsync(string action, string entityName, string? entityId, object? oldValues, object? newValues, int? userId = null);
    Task<IEnumerable<AuditLogDto>> GetAuditLogsAsync(int page, int pageSize);
    Task<IEnumerable<AuditLogDto>> GetAuditLogsByUserAsync(int userId);
    Task<IEnumerable<AuditLogDto>> GetAuditLogsByEntityAsync(string entityName, string entityId);
}

public interface ISecurityService
{
    // Security Events
    Task<SecurityEventDto?> GetSecurityEventByIdAsync(int id);
    Task<IEnumerable<SecurityEventDto>> GetSecurityEventsAsync(int page, int pageSize);
    Task<IEnumerable<SecurityEventDto>> GetSecurityEventsByTypeAsync(SecurityEventType eventType);
    Task<IEnumerable<SecurityEventDto>> GetSecurityEventsByRiskLevelAsync(SecurityRiskLevel riskLevel);
    Task<IEnumerable<SecurityEventDto>> GetPendingSecurityEventsAsync();
    Task<SecurityEventDto> CreateSecurityEventAsync(CreateSecurityEventDto createSecurityEventDto);
    Task<SecurityEventDto> UpdateSecurityEventAsync(int id, UpdateSecurityEventDto updateSecurityEventDto);
    Task<bool> HandleSecurityEventAsync(int id, int handledByUserId, string handlingNotes);
    Task<bool> MarkSecurityEventAsResolvedAsync(int id);
    Task<bool> MarkSecurityEventAsFalsePositiveAsync(int id);
    Task<SecurityStatsDto> GetSecurityStatsAsync();

    // Security Rules
    Task<SecurityRuleDto?> GetSecurityRuleByIdAsync(int id);
    Task<IEnumerable<SecurityRuleDto>> GetSecurityRulesAsync();
    Task<IEnumerable<SecurityRuleDto>> GetActiveSecurityRulesAsync();
    Task<SecurityRuleDto> CreateSecurityRuleAsync(CreateSecurityRuleDto createSecurityRuleDto);
    Task<SecurityRuleDto> UpdateSecurityRuleAsync(int id, UpdateSecurityRuleDto updateSecurityRuleDto);
    Task<bool> DeleteSecurityRuleAsync(int id);
    Task<bool> ActivateSecurityRuleAsync(int id);
    Task<bool> DeactivateSecurityRuleAsync(int id);

    // Risk Detection
    Task<RiskAssessmentDto> AssessPlayerRiskAsync(string playerId);
    Task<IEnumerable<SecurityEventDto>> DetectSuspiciousActivitiesAsync();
    Task<bool> TriggerSecurityRuleAsync(int ruleId, string playerId, string evidence);
}

public interface ICustomerServiceService
{
    // Ticket Management
    Task<CustomerServiceTicketDto?> GetTicketByIdAsync(int id);
    Task<CustomerServiceTicketDto?> GetTicketByTicketIdAsync(string ticketId);
    Task<IEnumerable<CustomerServiceTicketDto>> GetTicketsAsync(int page, int pageSize);
    Task<IEnumerable<CustomerServiceTicketDto>> GetTicketsByStatusAsync(TicketStatus status);
    Task<IEnumerable<CustomerServiceTicketDto>> GetTicketsByPriorityAsync(TicketPriority priority);
    Task<IEnumerable<CustomerServiceTicketDto>> GetTicketsByCategoryAsync(TicketCategory category);
    Task<IEnumerable<CustomerServiceTicketDto>> GetTicketsByPlayerAsync(int playerId);
    Task<IEnumerable<CustomerServiceTicketDto>> GetAssignedTicketsAsync(int userId);
    Task<IEnumerable<CustomerServiceTicketDto>> SearchTicketsAsync(string searchTerm);
    Task<CustomerServiceTicketDto> CreateTicketAsync(CreateCustomerServiceTicketDto createTicketDto);
    Task<CustomerServiceTicketDto> UpdateTicketAsync(int id, UpdateCustomerServiceTicketDto updateTicketDto);
    Task<bool> AssignTicketAsync(int ticketId, int userId);
    Task<bool> UnassignTicketAsync(int ticketId);
    Task<bool> ChangeTicketStatusAsync(int ticketId, TicketStatus status);
    Task<bool> ChangeTicketPriorityAsync(int ticketId, TicketPriority priority);
    Task<bool> ResolveTicketAsync(int ticketId, string resolution);
    Task<bool> CloseTicketAsync(int ticketId);
    Task<bool> ReopenTicketAsync(int ticketId);
    Task<CustomerServiceStatsDto> GetCustomerServiceStatsAsync();

    // Message Management
    Task<IEnumerable<TicketMessageDto>> GetTicketMessagesAsync(int ticketId);
    Task<TicketMessageDto> AddTicketMessageAsync(int ticketId, CreateTicketMessageDto createMessageDto);
    Task<TicketMessageDto> AddInternalNoteAsync(int ticketId, CreateTicketMessageDto createMessageDto);
    Task<bool> DeleteTicketMessageAsync(int messageId);

    // Customer Dialogue
    Task<IEnumerable<CustomerServiceTicketDto>> GetActiveDialoguesAsync(int userId);
    Task<bool> StartDialogueAsync(int ticketId, int userId);
    Task<bool> EndDialogueAsync(int ticketId);
    Task<CustomerDialogueStatsDto> GetDialogueStatsAsync(int userId);

    // Satisfaction & Feedback
    Task<bool> SubmitSatisfactionRatingAsync(int ticketId, int rating, string? feedback);
    Task<SatisfactionStatsDto> GetSatisfactionStatsAsync();
}

// 运营数据相关Service接口
public interface IOperationalDataService
{
    // 全局数据统计
    Task<GlobalStatsDto> GetGlobalStatsAsync();
    Task<GlobalStatsDto> GetGlobalStatsByDateAsync(DateTime date);

    // 用户信息管理
    Task<UserInfoStatsDto> GetUserInfoStatsAsync();
    Task<UserInfoStatsDto> GetUserInfoStatsByDateRangeAsync(DateTime startDate, DateTime endDate);

    // 付费信息管理
    Task<PaymentInfoStatsDto> GetPaymentInfoStatsAsync();
    Task<PaymentInfoStatsDto> GetPaymentInfoStatsByDateRangeAsync(DateTime startDate, DateTime endDate);

    // 客户端数据管理
    Task<ClientDataStatsDto> GetClientDataStatsAsync();
    Task<ClientDataStatsDto> GetClientDataStatsByDateRangeAsync(DateTime startDate, DateTime endDate);

    // 扩展数据分析
    Task<ConversionAnalysisDto> GetConversionAnalysisAsync(DateTime date);
    Task<ChurnAnalysisDto> GetChurnAnalysisAsync(DateTime date);
    Task<RetentionAnalysisDto> GetRetentionAnalysisAsync(DateTime date);
    Task<ActiveUserAnalysisDto> GetActiveUserAnalysisAsync(DateTime date);
    Task<IEnumerable<UserLifecycleDto>> GetUserLifecycleAnalysisAsync(int page, int pageSize);

    // 数据记录
    Task RecordVisitAsync(string? ipAddress, string? userAgent, string? referrer, string? channel, int? serverId);
    Task RecordRegistrationAsync(string accountId, string? ipAddress, string? channel, int? serverId);
    Task RecordLoginAsync(string accountId, string? ipAddress, int? serverId);
    Task RecordLogoutAsync(string accountId, DateTime loginTime);
    Task RecordClientInstallAsync(string accountId, string version, string platform, string? ipAddress);
    Task RecordClientUninstallAsync(string accountId, string version, string platform);

    // 历史数据查询
    Task<IEnumerable<OperationalDataDto>> GetHistoricalDataAsync(string dataType, DateTime startDate, DateTime endDate, int? serverId = null);
    Task<Dictionary<DateTime, decimal>> GetMetricTrendAsync(string dataType, string metricName, DateTime startDate, DateTime endDate, int? serverId = null);
}
