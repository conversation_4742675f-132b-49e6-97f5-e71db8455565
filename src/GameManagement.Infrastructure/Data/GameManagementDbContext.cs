using Microsoft.EntityFrameworkCore;
using GameManagement.Core.Entities;
using System.Linq.Expressions;

namespace GameManagement.Infrastructure.Data;

public class GameManagementDbContext : DbContext
{
    public GameManagementDbContext(DbContextOptions<GameManagementDbContext> options) : base(options)
    {
    }

    // 用户管理
    public DbSet<User> Users { get; set; }
    public DbSet<UserRole> UserRoles { get; set; }
    public DbSet<AuditLog> AuditLogs { get; set; }

    // 游戏相关
    public DbSet<GameServer> GameServers { get; set; }
    public DbSet<Player> Players { get; set; }
    public DbSet<PaymentRecord> PaymentRecords { get; set; }
    public DbSet<GameActivity> GameActivities { get; set; }
    public DbSet<PlayerActivity> PlayerActivities { get; set; }
    public DbSet<ServerLog> ServerLogs { get; set; }
    public DbSet<Announcement> Announcements { get; set; }

    // 运营数据
    public DbSet<OperationalData> OperationalData { get; set; }
    public DbSet<UserVisit> UserVisits { get; set; }
    public DbSet<UserRegistration> UserRegistrations { get; set; }
    public DbSet<UserLogin> UserLogins { get; set; }
    public DbSet<ClientVersion> ClientVersions { get; set; }
    public DbSet<ClientUsage> ClientUsages { get; set; }

    // 业务实体
    public DbSet<VisitRecord> VisitRecords { get; set; }
    public DbSet<CustomerServiceTicket> CustomerServiceTickets { get; set; }
    public DbSet<TicketMessage> TicketMessages { get; set; }
    public DbSet<Channel> Channels { get; set; }
    public DbSet<ChannelData> ChannelData { get; set; }
    public DbSet<SecurityEvent> SecurityEvents { get; set; }
    public DbSet<SecurityRule> SecurityRules { get; set; }
    public DbSet<GameItem> GameItems { get; set; }
    public DbSet<GameAnnouncement> GameAnnouncements { get; set; }
    public DbSet<Report> Reports { get; set; }
    public DbSet<ServerMaintenance> ServerMaintenances { get; set; }
    public DbSet<ServerMonitoring> ServerMonitorings { get; set; }
    public DbSet<ServerAlert> ServerAlerts { get; set; }
    public DbSet<AlertRule> AlertRules { get; set; }
    public DbSet<HealthCheck> HealthChecks { get; set; }
    public DbSet<DatabaseBackup> DatabaseBackups { get; set; }
    public DbSet<BackupSchedule> BackupSchedules { get; set; }
    public DbSet<BackupValidation> BackupValidations { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // 配置实体关系和约束
        ConfigureUserEntities(modelBuilder);
        ConfigureGameEntities(modelBuilder);
        ConfigureOperationalDataEntities(modelBuilder);
        ConfigureBusinessEntities(modelBuilder);

        // 配置软删除全局查询过滤器
        ConfigureSoftDelete(modelBuilder);

        // 配置索引
        ConfigureIndexes(modelBuilder);
    }

    private void ConfigureUserEntities(ModelBuilder modelBuilder)
    {
        // User配置
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Username).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Email).IsRequired().HasMaxLength(100);
            entity.Property(e => e.PasswordHash).IsRequired();
            entity.Property(e => e.DisplayName).HasMaxLength(100);
            
            entity.HasIndex(e => e.Username).IsUnique();
            entity.HasIndex(e => e.Email).IsUnique();
        });

        // UserRole配置
        modelBuilder.Entity<UserRole>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.RoleName).IsRequired().HasMaxLength(50);
            
            entity.HasOne(e => e.User)
                .WithMany(e => e.UserRoles)
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // AuditLog配置
        modelBuilder.Entity<AuditLog>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Action).IsRequired().HasMaxLength(100);
            entity.Property(e => e.EntityName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.IpAddress).HasMaxLength(45);
            
            entity.HasOne(e => e.User)
                .WithMany(e => e.AuditLogs)
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private void ConfigureGameEntities(ModelBuilder modelBuilder)
    {
        // GameServer配置
        modelBuilder.Entity<GameServer>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Host).IsRequired().HasMaxLength(255);
            entity.Property(e => e.Version).HasMaxLength(50);
            entity.Property(e => e.Region).HasMaxLength(50);
        });

        // Player配置
        modelBuilder.Entity<Player>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.AccountId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Nickname).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Class).HasMaxLength(50);
            entity.Property(e => e.Gold).HasPrecision(18, 2);
            entity.Property(e => e.IpAddress).HasMaxLength(45);
            
            entity.HasIndex(e => e.AccountId).IsUnique();
            entity.HasIndex(e => e.Nickname);
            
            entity.HasOne(e => e.Server)
                .WithMany(e => e.Players)
                .HasForeignKey(e => e.ServerId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // PaymentRecord配置
        modelBuilder.Entity<PaymentRecord>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.OrderId).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Amount).HasPrecision(18, 2);
            entity.Property(e => e.Currency).HasMaxLength(10);
            entity.Property(e => e.PaymentMethod).HasMaxLength(50);
            entity.Property(e => e.TransactionId).HasMaxLength(100);
            
            entity.HasIndex(e => e.OrderId).IsUnique();
            
            entity.HasOne(e => e.Player)
                .WithMany(e => e.PaymentRecords)
                .HasForeignKey(e => e.PlayerId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // ServerLog配置
        modelBuilder.Entity<ServerLog>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Message).IsRequired();
            entity.Property(e => e.Source).HasMaxLength(100);
            
            entity.HasOne(e => e.Server)
                .WithMany(e => e.ServerLogs)
                .HasForeignKey(e => e.ServerId)
                .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private void ConfigureOperationalDataEntities(ModelBuilder modelBuilder)
    {
        // OperationalData配置
        modelBuilder.Entity<OperationalData>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.DataType).IsRequired().HasMaxLength(50);
            entity.Property(e => e.MetricName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Value).HasPrecision(18, 4);
            
            entity.HasIndex(e => new { e.Date, e.ServerId, e.MetricName });
        });

        // UserVisit配置
        modelBuilder.Entity<UserVisit>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.IpAddress).HasMaxLength(45);
            entity.Property(e => e.Channel).HasMaxLength(50);
            
            entity.HasIndex(e => new { e.VisitTime, e.IpAddress });
        });

        // UserRegistration配置
        modelBuilder.Entity<UserRegistration>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.AccountId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.IpAddress).HasMaxLength(45);

            entity.HasIndex(e => e.AccountId).IsUnique();
            entity.HasIndex(e => new { e.RegistrationTime, e.ChannelId });

            // 配置与Channel的关系
            entity.HasOne(e => e.Channel)
                .WithMany(c => c.UserRegistrations)
                .HasForeignKey(e => e.ChannelId)
                .OnDelete(DeleteBehavior.SetNull);

            // 配置与Server的关系
            entity.HasOne(e => e.Server)
                .WithMany(s => s.UserRegistrations)
                .HasForeignKey(e => e.ServerId)
                .OnDelete(DeleteBehavior.SetNull);
        });

        // UserLogin配置
        modelBuilder.Entity<UserLogin>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.AccountId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.IpAddress).HasMaxLength(45);
            
            entity.HasIndex(e => new { e.AccountId, e.LoginTime });
            entity.HasIndex(e => new { e.LoginTime, e.IpAddress });
        });
    }

    private void ConfigureBusinessEntities(ModelBuilder modelBuilder)
    {
        // VisitRecord配置
        modelBuilder.Entity<VisitRecord>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.IpAddress).HasMaxLength(45);
            entity.Property(e => e.Channel).HasMaxLength(50);
            entity.Property(e => e.SessionId).HasMaxLength(100);

            entity.HasIndex(e => new { e.VisitTime, e.IpAddress });

            entity.HasOne(e => e.User)
                .WithMany(e => e.VisitRecords)
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.SetNull);
        });

        // CustomerServiceTicket配置
        modelBuilder.Entity<CustomerServiceTicket>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.TicketId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.PlayerAccountId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Description).IsRequired();
            
            entity.HasIndex(e => e.TicketId).IsUnique();
            entity.HasIndex(e => new { e.Status, e.Priority });
            
            entity.HasOne(e => e.Player)
                .WithMany(e => e.CustomerServiceTickets)
                .HasForeignKey(e => e.PlayerId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // Channel配置
        modelBuilder.Entity<Channel>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Code).IsRequired().HasMaxLength(50);
            entity.Property(e => e.ContactEmail).HasMaxLength(100);
            entity.Property(e => e.ContactPhone).HasMaxLength(20);
            entity.Property(e => e.CommissionRate).HasPrecision(5, 4);
            
            entity.HasIndex(e => e.Code).IsUnique();
        });

        // SecurityEvent配置
        modelBuilder.Entity<SecurityEvent>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.EventId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.PlayerId).HasMaxLength(50);
            entity.Property(e => e.IpAddress).HasMaxLength(45);
            entity.Property(e => e.Description).IsRequired();
            
            entity.HasIndex(e => e.EventId).IsUnique();
            entity.HasIndex(e => new { e.EventType, e.RiskLevel, e.Status });
        });
    }

    private void ConfigureSoftDelete(ModelBuilder modelBuilder)
    {
        // 为所有继承BaseEntity的实体配置软删除过滤器
        // 注意：In-Memory数据库对复杂查询过滤器支持有限，暂时禁用
        // TODO: 在使用真实数据库时重新启用
        /*
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            if (typeof(BaseEntity).IsAssignableFrom(entityType.ClrType))
            {
                modelBuilder.Entity(entityType.ClrType).HasQueryFilter(
                    Expression.Lambda(
                        Expression.Equal(
                            Expression.Property(
                                Expression.Parameter(entityType.ClrType, "e"),
                                nameof(BaseEntity.IsDeleted)
                            ),
                            Expression.Constant(false)
                        ),
                        Expression.Parameter(entityType.ClrType, "e")
                    )
                );
            }
        }
        */
    }

    private void ConfigureIndexes(ModelBuilder modelBuilder)
    {
        // 为常用查询字段添加索引
        modelBuilder.Entity<Player>()
            .HasIndex(p => new { p.ServerId, p.Level });

        modelBuilder.Entity<PaymentRecord>()
            .HasIndex(p => new { p.Status, p.CreatedAt });

        modelBuilder.Entity<ServerLog>()
            .HasIndex(s => new { s.ServerId, s.Level, s.Timestamp });

        modelBuilder.Entity<OperationalData>()
            .HasIndex(o => new { o.Date, o.DataType, o.MetricName });
    }

    public override int SaveChanges()
    {
        UpdateTimestamps();
        return base.SaveChanges();
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateTimestamps();
        return await base.SaveChangesAsync(cancellationToken);
    }

    private void UpdateTimestamps()
    {
        var entries = ChangeTracker.Entries<BaseEntity>();

        foreach (var entry in entries)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = DateTime.UtcNow;
                    break;
                case EntityState.Modified:
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    break;
            }
        }
    }
}
