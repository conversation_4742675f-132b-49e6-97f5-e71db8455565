using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace GameManagement.Infrastructure.Data;

public class GameManagementDbContextFactory : IDesignTimeDbContextFactory<GameManagementDbContext>
{
    public GameManagementDbContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<GameManagementDbContext>();
        
        // 使用SQLite作为设计时数据库提供程序
        optionsBuilder.UseSqlite("Data Source=gamemanagement.db");
        
        return new GameManagementDbContext(optionsBuilder.Options);
    }
}
