using GameManagement.Core.Entities;
using GameManagement.Core.Interfaces;
using GameManagement.Infrastructure.Data;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace GameManagement.Infrastructure.Services;

public class ActivityService : IActivityService
{
    private readonly GameManagementDbContext _context;
    private readonly ILogger<ActivityService> _logger;

    public ActivityService(GameManagementDbContext context, ILogger<ActivityService> logger)
    {
        _context = context;
        _logger = logger;
    }

    // Basic CRUD operations
    public async Task<ActivityDto?> GetActivityByIdAsync(int id)
    {
        try
        {
            var activity = await _context.GameActivities
                .Include(a => a.PlayerActivities)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (activity == null)
                return null;

            return MapToActivityDto(activity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting activity by ID {Id}", id);
            throw;
        }
    }

    public async Task<IEnumerable<ActivityDto>> GetActivitiesAsync()
    {
        try
        {
            var activities = await _context.GameActivities
                .Include(a => a.PlayerActivities)
                .OrderByDescending(a => a.CreatedAt)
                .ToListAsync();

            return activities.Select(MapToActivityDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all activities");
            throw;
        }
    }

    public async Task<IEnumerable<ActivityDto>> GetActiveActivitiesAsync()
    {
        try
        {
            var now = DateTime.UtcNow;
            var activities = await _context.GameActivities
                .Include(a => a.PlayerActivities)
                .Where(a => a.Status == ActivityStatus.Active && 
                           a.StartTime <= now && 
                           a.EndTime >= now)
                .OrderBy(a => a.StartTime)
                .ToListAsync();

            return activities.Select(MapToActivityDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active activities");
            throw;
        }
    }

    public async Task<IEnumerable<ActivityDto>> GetActivitiesByStatusAsync(ActivityStatus status)
    {
        try
        {
            var activities = await _context.GameActivities
                .Include(a => a.PlayerActivities)
                .Where(a => a.Status == status)
                .OrderByDescending(a => a.CreatedAt)
                .ToListAsync();

            return activities.Select(MapToActivityDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting activities by status {Status}", status);
            throw;
        }
    }

    public async Task<IEnumerable<ActivityDto>> GetActivitiesByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        try
        {
            var activities = await _context.GameActivities
                .Include(a => a.PlayerActivities)
                .Where(a => a.StartTime >= startDate && a.StartTime <= endDate)
                .OrderBy(a => a.StartTime)
                .ToListAsync();

            return activities.Select(MapToActivityDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting activities by date range {StartDate} - {EndDate}", startDate, endDate);
            throw;
        }
    }

    public async Task<ActivityDto> CreateActivityAsync(CreateActivityDto createActivityDto)
    {
        try
        {
            var activity = new GameActivity
            {
                Name = createActivityDto.Name,
                Description = createActivityDto.Description,
                Status = ActivityStatus.Draft,
                StartTime = createActivityDto.StartTime,
                EndTime = createActivityDto.EndTime,
                Conditions = createActivityDto.Conditions,
                Rewards = createActivityDto.Rewards,
                ParticipantCount = 0,
                CompletionCount = 0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.GameActivities.Add(activity);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Created new activity {ActivityId}: {Name}", activity.Id, activity.Name);

            return MapToActivityDto(activity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating activity");
            throw;
        }
    }

    public async Task<ActivityDto> UpdateActivityAsync(int id, UpdateActivityDto updateActivityDto)
    {
        try
        {
            var activity = await _context.GameActivities.FindAsync(id);
            if (activity == null)
                throw new ArgumentException($"Activity with ID {id} not found");

            // Update only provided fields
            if (!string.IsNullOrEmpty(updateActivityDto.Name))
                activity.Name = updateActivityDto.Name;
            
            if (!string.IsNullOrEmpty(updateActivityDto.Description))
                activity.Description = updateActivityDto.Description;
            
            if (updateActivityDto.StartTime.HasValue)
                activity.StartTime = updateActivityDto.StartTime.Value;
            
            if (updateActivityDto.EndTime.HasValue)
                activity.EndTime = updateActivityDto.EndTime.Value;
            
            if (updateActivityDto.Conditions != null)
                activity.Conditions = updateActivityDto.Conditions;
            
            if (updateActivityDto.Rewards != null)
                activity.Rewards = updateActivityDto.Rewards;

            activity.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated activity {ActivityId}", id);

            return MapToActivityDto(activity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating activity {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeleteActivityAsync(int id)
    {
        try
        {
            var activity = await _context.GameActivities
                .Include(a => a.PlayerActivities)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (activity == null)
                return false;

            // Check if activity can be deleted (not active)
            if (activity.Status == ActivityStatus.Active)
            {
                _logger.LogWarning("Cannot delete active activity {ActivityId}", id);
                return false;
            }

            // Remove related player activities first
            _context.PlayerActivities.RemoveRange(activity.PlayerActivities);
            _context.GameActivities.Remove(activity);

            await _context.SaveChangesAsync();

            _logger.LogInformation("Deleted activity {ActivityId}: {Name}", id, activity.Name);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting activity {Id}", id);
            throw;
        }
    }

    // Activity status management
    public async Task<bool> StartActivityAsync(int id)
    {
        try
        {
            var activity = await _context.GameActivities.FindAsync(id);
            if (activity == null)
                return false;

            if (activity.Status != ActivityStatus.Draft && activity.Status != ActivityStatus.Paused)
            {
                _logger.LogWarning("Cannot start activity {ActivityId} with status {Status}", id, activity.Status);
                return false;
            }

            activity.Status = ActivityStatus.Active;
            activity.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Started activity {ActivityId}: {Name}", id, activity.Name);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting activity {Id}", id);
            throw;
        }
    }

    public async Task<bool> PauseActivityAsync(int id)
    {
        try
        {
            var activity = await _context.GameActivities.FindAsync(id);
            if (activity == null)
                return false;

            if (activity.Status != ActivityStatus.Active)
            {
                _logger.LogWarning("Cannot pause activity {ActivityId} with status {Status}", id, activity.Status);
                return false;
            }

            activity.Status = ActivityStatus.Paused;
            activity.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Paused activity {ActivityId}: {Name}", id, activity.Name);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error pausing activity {Id}", id);
            throw;
        }
    }

    public async Task<bool> ResumeActivityAsync(int id)
    {
        try
        {
            var activity = await _context.GameActivities.FindAsync(id);
            if (activity == null)
                return false;

            if (activity.Status != ActivityStatus.Paused)
            {
                _logger.LogWarning("Cannot resume activity {ActivityId} with status {Status}", id, activity.Status);
                return false;
            }

            activity.Status = ActivityStatus.Active;
            activity.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Resumed activity {ActivityId}: {Name}", id, activity.Name);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resuming activity {Id}", id);
            throw;
        }
    }

    public async Task<bool> EndActivityAsync(int id)
    {
        try
        {
            var activity = await _context.GameActivities.FindAsync(id);
            if (activity == null)
                return false;

            if (activity.Status != ActivityStatus.Active && activity.Status != ActivityStatus.Paused)
            {
                _logger.LogWarning("Cannot end activity {ActivityId} with status {Status}", id, activity.Status);
                return false;
            }

            activity.Status = ActivityStatus.Ended;
            activity.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Ended activity {ActivityId}: {Name}", id, activity.Name);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ending activity {Id}", id);
            throw;
        }
    }

    public async Task<bool> CancelActivityAsync(int id)
    {
        try
        {
            var activity = await _context.GameActivities.FindAsync(id);
            if (activity == null)
                return false;

            if (activity.Status == ActivityStatus.Ended || activity.Status == ActivityStatus.Cancelled)
            {
                _logger.LogWarning("Cannot cancel activity {ActivityId} with status {Status}", id, activity.Status);
                return false;
            }

            activity.Status = ActivityStatus.Cancelled;
            activity.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Cancelled activity {ActivityId}: {Name}", id, activity.Name);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling activity {Id}", id);
            throw;
        }
    }

    // Activity statistics and analytics
    public async Task<ActivityStatsDto> GetActivityStatsAsync(int id)
    {
        try
        {
            var activity = await _context.GameActivities
                .Include(a => a.PlayerActivities)
                .ThenInclude(pa => pa.Player)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (activity == null)
                throw new ArgumentException($"Activity with ID {id} not found");

            var totalParticipants = activity.PlayerActivities.Count;
            var completedParticipants = activity.PlayerActivities.Count(pa => pa.IsCompleted);
            var completionRate = totalParticipants > 0 ? (double)completedParticipants / totalParticipants * 100 : 0;

            // Group by player level (assuming we have level info)
            var participationByLevel = activity.PlayerActivities
                .GroupBy(pa => pa.Player.Level.ToString())
                .ToDictionary(g => g.Key, g => g.Count());

            // Group by server (if we have server info)
            var participationByServer = activity.PlayerActivities
                .Where(pa => pa.Player.ServerId > 0)
                .GroupBy(pa => pa.Player.ServerId.ToString())
                .ToDictionary(g => g.Key, g => g.Count());

            return new ActivityStatsDto
            {
                ActivityId = activity.Id,
                ActivityName = activity.Name,
                TotalParticipants = totalParticipants,
                CompletedParticipants = completedParticipants,
                CompletionRate = completionRate,
                ParticipationByLevel = participationByLevel,
                ParticipationByServer = participationByServer,
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting activity stats for {Id}", id);
            throw;
        }
    }

    public async Task<IEnumerable<ActivityStatsDto>> GetAllActivitiesStatsAsync()
    {
        try
        {
            var activities = await _context.GameActivities
                .Include(a => a.PlayerActivities)
                .ThenInclude(pa => pa.Player)
                .ToListAsync();

            var statsList = new List<ActivityStatsDto>();

            foreach (var activity in activities)
            {
                var totalParticipants = activity.PlayerActivities.Count;
                var completedParticipants = activity.PlayerActivities.Count(pa => pa.IsCompleted);
                var completionRate = totalParticipants > 0 ? (double)completedParticipants / totalParticipants * 100 : 0;

                var participationByLevel = activity.PlayerActivities
                    .GroupBy(pa => pa.Player.Level.ToString())
                    .ToDictionary(g => g.Key, g => g.Count());

                var participationByServer = activity.PlayerActivities
                    .Where(pa => pa.Player.ServerId > 0)
                    .GroupBy(pa => pa.Player.ServerId.ToString())
                    .ToDictionary(g => g.Key, g => g.Count());

                statsList.Add(new ActivityStatsDto
                {
                    ActivityId = activity.Id,
                    ActivityName = activity.Name,
                    TotalParticipants = totalParticipants,
                    CompletedParticipants = completedParticipants,
                    CompletionRate = completionRate,
                    ParticipationByLevel = participationByLevel,
                    ParticipationByServer = participationByServer,
                    LastUpdated = DateTime.UtcNow
                });
            }

            return statsList;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all activities stats");
            throw;
        }
    }

    public async Task<Dictionary<string, int>> GetActivityParticipationTrendsAsync(int activityId, int days = 30)
    {
        try
        {
            var startDate = DateTime.UtcNow.AddDays(-days);
            var participations = await _context.PlayerActivities
                .Where(pa => pa.ActivityId == activityId && pa.ParticipatedAt >= startDate)
                .GroupBy(pa => pa.ParticipatedAt.Date)
                .Select(g => new { Date = g.Key, Count = g.Count() })
                .OrderBy(x => x.Date)
                .ToListAsync();

            return participations.ToDictionary(
                x => x.Date.ToString("yyyy-MM-dd"),
                x => x.Count
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting participation trends for activity {ActivityId}", activityId);
            throw;
        }
    }

    public async Task<Dictionary<string, object>> GetActivityPerformanceMetricsAsync(int activityId)
    {
        try
        {
            var activity = await _context.GameActivities
                .Include(a => a.PlayerActivities)
                .FirstOrDefaultAsync(a => a.Id == activityId);

            if (activity == null)
                throw new ArgumentException($"Activity with ID {activityId} not found");

            var metrics = new Dictionary<string, object>();
            var playerActivities = activity.PlayerActivities.ToList();

            metrics["TotalParticipants"] = playerActivities.Count;
            metrics["CompletedParticipants"] = playerActivities.Count(pa => pa.IsCompleted);
            metrics["CompletionRate"] = playerActivities.Count > 0
                ? (double)playerActivities.Count(pa => pa.IsCompleted) / playerActivities.Count * 100
                : 0;

            if (playerActivities.Any(pa => pa.IsCompleted && pa.CompletedAt.HasValue))
            {
                var completedActivities = playerActivities.Where(pa => pa.IsCompleted && pa.CompletedAt.HasValue).ToList();
                var avgCompletionTime = completedActivities
                    .Average(pa => (pa.CompletedAt!.Value - pa.ParticipatedAt).TotalHours);
                metrics["AverageCompletionTimeHours"] = Math.Round(avgCompletionTime, 2);
            }

            metrics["DailyParticipation"] = playerActivities
                .GroupBy(pa => pa.ParticipatedAt.Date)
                .ToDictionary(g => g.Key.ToString("yyyy-MM-dd"), g => g.Count());

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance metrics for activity {ActivityId}", activityId);
            throw;
        }
    }

    // Player participation management
    public async Task<bool> AddPlayerToActivityAsync(int activityId, int playerId)
    {
        try
        {
            var activity = await _context.GameActivities.FindAsync(activityId);
            if (activity == null)
                return false;

            var player = await _context.Players.FindAsync(playerId);
            if (player == null)
                return false;

            // Check if player is already participating
            var existingParticipation = await _context.PlayerActivities
                .FirstOrDefaultAsync(pa => pa.ActivityId == activityId && pa.PlayerId == playerId);

            if (existingParticipation != null)
                return false; // Already participating

            var playerActivity = new PlayerActivity
            {
                PlayerId = playerId,
                ActivityId = activityId,
                ParticipatedAt = DateTime.UtcNow,
                IsCompleted = false
            };

            _context.PlayerActivities.Add(playerActivity);

            // Update participant count
            activity.ParticipantCount++;
            activity.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Added player {PlayerId} to activity {ActivityId}", playerId, activityId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding player {PlayerId} to activity {ActivityId}", playerId, activityId);
            throw;
        }
    }

    public async Task<bool> RemovePlayerFromActivityAsync(int activityId, int playerId)
    {
        try
        {
            var playerActivity = await _context.PlayerActivities
                .FirstOrDefaultAsync(pa => pa.ActivityId == activityId && pa.PlayerId == playerId);

            if (playerActivity == null)
                return false;

            var activity = await _context.GameActivities.FindAsync(activityId);
            if (activity != null)
            {
                activity.ParticipantCount = Math.Max(0, activity.ParticipantCount - 1);
                if (playerActivity.IsCompleted)
                    activity.CompletionCount = Math.Max(0, activity.CompletionCount - 1);
                activity.UpdatedAt = DateTime.UtcNow;
            }

            _context.PlayerActivities.Remove(playerActivity);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Removed player {PlayerId} from activity {ActivityId}", playerId, activityId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing player {PlayerId} from activity {ActivityId}", playerId, activityId);
            throw;
        }
    }

    public async Task<bool> CompleteActivityForPlayerAsync(int activityId, int playerId, string? rewardsReceived = null)
    {
        try
        {
            var playerActivity = await _context.PlayerActivities
                .FirstOrDefaultAsync(pa => pa.ActivityId == activityId && pa.PlayerId == playerId);

            if (playerActivity == null)
                return false;

            if (playerActivity.IsCompleted)
                return true; // Already completed

            playerActivity.IsCompleted = true;
            playerActivity.CompletedAt = DateTime.UtcNow;
            playerActivity.RewardsReceived = rewardsReceived;

            var activity = await _context.GameActivities.FindAsync(activityId);
            if (activity != null)
            {
                activity.CompletionCount++;
                activity.UpdatedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Completed activity {ActivityId} for player {PlayerId}", activityId, playerId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing activity {ActivityId} for player {PlayerId}", activityId, playerId);
            throw;
        }
    }

    public async Task<IEnumerable<PlayerActivityDto>> GetActivityParticipantsAsync(int activityId)
    {
        try
        {
            var participants = await _context.PlayerActivities
                .Include(pa => pa.Player)
                .Include(pa => pa.Activity)
                .Where(pa => pa.ActivityId == activityId)
                .OrderByDescending(pa => pa.ParticipatedAt)
                .ToListAsync();

            return participants.Select(pa => new PlayerActivityDto
            {
                Id = pa.Id,
                PlayerId = pa.PlayerId,
                PlayerName = pa.Player.Nickname,
                PlayerAccountId = pa.Player.AccountId,
                ActivityId = pa.ActivityId,
                ActivityName = pa.Activity.Name,
                ParticipatedAt = pa.ParticipatedAt,
                IsCompleted = pa.IsCompleted,
                CompletedAt = pa.CompletedAt,
                RewardsReceived = pa.RewardsReceived
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting participants for activity {ActivityId}", activityId);
            throw;
        }
    }

    public async Task<IEnumerable<ActivityDto>> GetPlayerActivitiesAsync(int playerId)
    {
        try
        {
            var playerActivities = await _context.PlayerActivities
                .Include(pa => pa.Activity)
                .Where(pa => pa.PlayerId == playerId)
                .Select(pa => pa.Activity)
                .Distinct()
                .OrderByDescending(a => a.CreatedAt)
                .ToListAsync();

            return playerActivities.Select(MapToActivityDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting activities for player {PlayerId}", playerId);
            throw;
        }
    }

    // Activity rewards and conditions
    public async Task<bool> UpdateActivityRewardsAsync(int activityId, string rewards)
    {
        try
        {
            var activity = await _context.GameActivities.FindAsync(activityId);
            if (activity == null)
                return false;

            activity.Rewards = rewards;
            activity.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated rewards for activity {ActivityId}", activityId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating rewards for activity {ActivityId}", activityId);
            throw;
        }
    }

    public async Task<bool> UpdateActivityConditionsAsync(int activityId, string conditions)
    {
        try
        {
            var activity = await _context.GameActivities.FindAsync(activityId);
            if (activity == null)
                return false;

            activity.Conditions = conditions;
            activity.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated conditions for activity {ActivityId}", activityId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating conditions for activity {ActivityId}", activityId);
            throw;
        }
    }

    public async Task<bool> DistributeRewardsAsync(int activityId)
    {
        try
        {
            var activity = await _context.GameActivities
                .Include(a => a.PlayerActivities)
                .FirstOrDefaultAsync(a => a.Id == activityId);

            if (activity == null)
                return false;

            var completedParticipants = activity.PlayerActivities
                .Where(pa => pa.IsCompleted && string.IsNullOrEmpty(pa.RewardsReceived))
                .ToList();

            foreach (var participant in completedParticipants)
            {
                participant.RewardsReceived = activity.Rewards ?? "Default rewards";
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Distributed rewards for activity {ActivityId} to {Count} participants",
                activityId, completedParticipants.Count);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error distributing rewards for activity {ActivityId}", activityId);
            throw;
        }
    }

    // Activity monitoring and reporting
    public async Task<Dictionary<ActivityStatus, int>> GetActivityStatusSummaryAsync()
    {
        try
        {
            var statusCounts = await _context.GameActivities
                .GroupBy(a => a.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync();

            var summary = new Dictionary<ActivityStatus, int>();
            foreach (var status in Enum.GetValues<ActivityStatus>())
            {
                summary[status] = statusCounts.FirstOrDefault(sc => sc.Status == status)?.Count ?? 0;
            }

            return summary;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting activity status summary");
            throw;
        }
    }

    public async Task<IEnumerable<ActivityDto>> GetExpiringActivitiesAsync(int daysAhead = 7)
    {
        try
        {
            var cutoffDate = DateTime.UtcNow.AddDays(daysAhead);
            var activities = await _context.GameActivities
                .Include(a => a.PlayerActivities)
                .Where(a => a.Status == ActivityStatus.Active && a.EndTime <= cutoffDate)
                .OrderBy(a => a.EndTime)
                .ToListAsync();

            return activities.Select(MapToActivityDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting expiring activities");
            throw;
        }
    }

    public async Task<IEnumerable<ActivityDto>> GetPopularActivitiesAsync(int limit = 10)
    {
        try
        {
            var activities = await _context.GameActivities
                .Include(a => a.PlayerActivities)
                .OrderByDescending(a => a.ParticipantCount)
                .ThenByDescending(a => a.CompletionCount)
                .Take(limit)
                .ToListAsync();

            return activities.Select(MapToActivityDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting popular activities");
            throw;
        }
    }

    private ActivityDto MapToActivityDto(GameActivity activity)
    {
        return new ActivityDto
        {
            Id = activity.Id,
            Name = activity.Name,
            Description = activity.Description,
            Status = activity.Status,
            StartTime = activity.StartTime,
            EndTime = activity.EndTime,
            Conditions = activity.Conditions,
            Rewards = activity.Rewards,
            ParticipantCount = activity.ParticipantCount,
            CompletionCount = activity.CompletionCount,
            CreatedAt = activity.CreatedAt
        };
    }
}
