using GameManagement.Core.Entities;
using GameManagement.Core.Interfaces;
using GameManagement.Infrastructure.Data;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace GameManagement.Infrastructure.Services;

public class AnnouncementService : IAnnouncementService
{
    private readonly GameManagementDbContext _context;
    private readonly ILogger<AnnouncementService> _logger;

    public AnnouncementService(GameManagementDbContext context, ILogger<AnnouncementService> logger)
    {
        _context = context;
        _logger = logger;
    }

    // 基础CRUD操作
    public async Task<IEnumerable<GameAnnouncementDto>> GetAnnouncementsAsync(int page = 1, int pageSize = 50)
    {
        try
        {
            var announcements = await _context.GameAnnouncements
                .OrderByDescending(a => a.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return announcements.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcements");
            throw;
        }
    }

    public async Task<GameAnnouncementDto?> GetAnnouncementByIdAsync(int id)
    {
        try
        {
            var announcement = await _context.GameAnnouncements
                .FirstOrDefaultAsync(a => a.Id == id);

            return announcement != null ? MapToDto(announcement) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcement by ID: {Id}", id);
            throw;
        }
    }

    public async Task<GameAnnouncementDto> CreateAnnouncementAsync(CreateGameAnnouncementDto createAnnouncementDto)
    {
        try
        {
            var announcement = new GameAnnouncement
            {
                Title = createAnnouncementDto.Title,
                Content = createAnnouncementDto.Content,
                Type = createAnnouncementDto.Type,
                StartTime = createAnnouncementDto.StartTime,
                EndTime = createAnnouncementDto.EndTime,
                TargetServers = createAnnouncementDto.TargetServers,
                TargetPlayers = createAnnouncementDto.TargetPlayers,
                Priority = createAnnouncementDto.Priority,
                ImageUrl = createAnnouncementDto.ImageUrl,
                LinkUrl = createAnnouncementDto.LinkUrl,
                IsActive = true,
                ViewCount = 0,
                CreatedAt = DateTime.UtcNow
            };

            _context.GameAnnouncements.Add(announcement);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Created announcement: {Title}", announcement.Title);
            return MapToDto(announcement);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating announcement");
            throw;
        }
    }

    public async Task<GameAnnouncementDto> UpdateAnnouncementAsync(int id, UpdateGameAnnouncementDto updateAnnouncementDto)
    {
        try
        {
            var announcement = await _context.GameAnnouncements
                .FirstOrDefaultAsync(a => a.Id == id);

            if (announcement == null)
                throw new ArgumentException($"Announcement with ID {id} not found");

            // Update properties if provided
            if (!string.IsNullOrEmpty(updateAnnouncementDto.Title))
                announcement.Title = updateAnnouncementDto.Title;
            
            if (!string.IsNullOrEmpty(updateAnnouncementDto.Content))
                announcement.Content = updateAnnouncementDto.Content;
            
            if (updateAnnouncementDto.Type.HasValue)
                announcement.Type = updateAnnouncementDto.Type.Value;
            
            if (updateAnnouncementDto.StartTime.HasValue)
                announcement.StartTime = updateAnnouncementDto.StartTime.Value;
            
            if (updateAnnouncementDto.EndTime.HasValue)
                announcement.EndTime = updateAnnouncementDto.EndTime.Value;
            
            if (updateAnnouncementDto.TargetServers != null)
                announcement.TargetServers = updateAnnouncementDto.TargetServers;
            
            if (updateAnnouncementDto.TargetPlayers != null)
                announcement.TargetPlayers = updateAnnouncementDto.TargetPlayers;
            
            if (updateAnnouncementDto.Priority.HasValue)
                announcement.Priority = updateAnnouncementDto.Priority.Value;
            
            if (updateAnnouncementDto.ImageUrl != null)
                announcement.ImageUrl = updateAnnouncementDto.ImageUrl;
            
            if (updateAnnouncementDto.LinkUrl != null)
                announcement.LinkUrl = updateAnnouncementDto.LinkUrl;

            announcement.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated announcement: {Id}", id);
            return MapToDto(announcement);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating announcement: {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeleteAnnouncementAsync(int id)
    {
        try
        {
            var announcement = await _context.GameAnnouncements
                .FirstOrDefaultAsync(a => a.Id == id);

            if (announcement == null)
                return false;

            _context.GameAnnouncements.Remove(announcement);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Deleted announcement: {Id}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting announcement: {Id}", id);
            throw;
        }
    }

    // 状态管理
    public async Task<bool> ActivateAnnouncementAsync(int id)
    {
        try
        {
            var announcement = await _context.GameAnnouncements
                .FirstOrDefaultAsync(a => a.Id == id);

            if (announcement == null)
                return false;

            announcement.IsActive = true;
            announcement.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Activated announcement: {Id}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating announcement: {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeactivateAnnouncementAsync(int id)
    {
        try
        {
            var announcement = await _context.GameAnnouncements
                .FirstOrDefaultAsync(a => a.Id == id);

            if (announcement == null)
                return false;

            announcement.IsActive = false;
            announcement.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Deactivated announcement: {Id}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating announcement: {Id}", id);
            throw;
        }
    }

    public async Task<bool> PublishAnnouncementAsync(int id)
    {
        try
        {
            var announcement = await _context.GameAnnouncements
                .FirstOrDefaultAsync(a => a.Id == id);

            if (announcement == null)
                return false;

            announcement.IsActive = true;
            announcement.StartTime = DateTime.UtcNow;
            announcement.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Published announcement: {Id}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing announcement: {Id}", id);
            throw;
        }
    }

    public async Task<bool> UnpublishAnnouncementAsync(int id)
    {
        try
        {
            var announcement = await _context.GameAnnouncements
                .FirstOrDefaultAsync(a => a.Id == id);

            if (announcement == null)
                return false;

            announcement.IsActive = false;
            announcement.EndTime = DateTime.UtcNow;
            announcement.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Unpublished announcement: {Id}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unpublishing announcement: {Id}", id);
            throw;
        }
    }

    // 筛选和搜索
    public async Task<IEnumerable<GameAnnouncementDto>> GetAnnouncementsByTypeAsync(AnnouncementType type)
    {
        try
        {
            var announcements = await _context.GameAnnouncements
                .Where(a => a.Type == type)
                .OrderByDescending(a => a.CreatedAt)
                .ToListAsync();

            return announcements.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcements by type: {Type}", type);
            throw;
        }
    }

    public async Task<IEnumerable<GameAnnouncementDto>> GetActiveAnnouncementsAsync()
    {
        try
        {
            var now = DateTime.UtcNow;
            var announcements = await _context.GameAnnouncements
                .Where(a => a.IsActive &&
                           a.StartTime <= now &&
                           (a.EndTime == null || a.EndTime > now))
                .OrderByDescending(a => a.Priority)
                .ThenByDescending(a => a.CreatedAt)
                .ToListAsync();

            return announcements.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active announcements");
            throw;
        }
    }

    public async Task<IEnumerable<GameAnnouncementDto>> GetAnnouncementsByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        try
        {
            var announcements = await _context.GameAnnouncements
                .Where(a => a.StartTime >= startDate && a.StartTime <= endDate)
                .OrderByDescending(a => a.StartTime)
                .ToListAsync();

            return announcements.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcements by date range");
            throw;
        }
    }

    public async Task<IEnumerable<GameAnnouncementDto>> SearchAnnouncementsAsync(string searchTerm)
    {
        try
        {
            var announcements = await _context.GameAnnouncements
                .Where(a => a.Title.Contains(searchTerm) || a.Content.Contains(searchTerm))
                .OrderByDescending(a => a.CreatedAt)
                .ToListAsync();

            return announcements.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching announcements with term: {SearchTerm}", searchTerm);
            throw;
        }
    }

    public async Task<IEnumerable<GameAnnouncementDto>> GetAnnouncementsByPriorityAsync(int minPriority)
    {
        try
        {
            var announcements = await _context.GameAnnouncements
                .Where(a => a.Priority >= minPriority)
                .OrderByDescending(a => a.Priority)
                .ThenByDescending(a => a.CreatedAt)
                .ToListAsync();

            return announcements.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcements by priority: {MinPriority}", minPriority);
            throw;
        }
    }

    // 目标管理
    public async Task<IEnumerable<GameAnnouncementDto>> GetAnnouncementsForServerAsync(int serverId)
    {
        try
        {
            var announcements = await _context.GameAnnouncements
                .Where(a => a.IsActive &&
                           (string.IsNullOrEmpty(a.TargetServers) ||
                            a.TargetServers.Contains(serverId.ToString())))
                .OrderByDescending(a => a.Priority)
                .ThenByDescending(a => a.CreatedAt)
                .ToListAsync();

            return announcements.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcements for server: {ServerId}", serverId);
            throw;
        }
    }

    public async Task<IEnumerable<GameAnnouncementDto>> GetAnnouncementsForPlayerAsync(string playerId)
    {
        try
        {
            var announcements = await _context.GameAnnouncements
                .Where(a => a.IsActive &&
                           (string.IsNullOrEmpty(a.TargetPlayers) ||
                            a.TargetPlayers.Contains(playerId)))
                .OrderByDescending(a => a.Priority)
                .ThenByDescending(a => a.CreatedAt)
                .ToListAsync();

            return announcements.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcements for player: {PlayerId}", playerId);
            throw;
        }
    }

    public async Task<bool> UpdateAnnouncementTargetsAsync(int id, UpdateAnnouncementTargetsDto targetsDto)
    {
        try
        {
            var announcement = await _context.GameAnnouncements
                .FirstOrDefaultAsync(a => a.Id == id);

            if (announcement == null)
                return false;

            announcement.TargetServers = targetsDto.TargetServers;
            announcement.TargetPlayers = targetsDto.TargetPlayers;
            announcement.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated announcement targets: {Id}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating announcement targets: {Id}", id);
            throw;
        }
    }

    // 批量操作
    public async Task<int> BatchActivateAnnouncementsAsync(IEnumerable<int> announcementIds)
    {
        try
        {
            var announcements = await _context.GameAnnouncements
                .Where(a => announcementIds.Contains(a.Id))
                .ToListAsync();

            var count = 0;
            foreach (var announcement in announcements)
            {
                announcement.IsActive = true;
                announcement.UpdatedAt = DateTime.UtcNow;
                count++;
            }

            await _context.SaveChangesAsync();
            _logger.LogInformation("Batch activated {Count} announcements", count);
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch activating announcements");
            throw;
        }
    }

    public async Task<int> BatchDeactivateAnnouncementsAsync(IEnumerable<int> announcementIds)
    {
        try
        {
            var announcements = await _context.GameAnnouncements
                .Where(a => announcementIds.Contains(a.Id))
                .ToListAsync();

            var count = 0;
            foreach (var announcement in announcements)
            {
                announcement.IsActive = false;
                announcement.UpdatedAt = DateTime.UtcNow;
                count++;
            }

            await _context.SaveChangesAsync();
            _logger.LogInformation("Batch deactivated {Count} announcements", count);
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch deactivating announcements");
            throw;
        }
    }

    public async Task<int> BatchDeleteAnnouncementsAsync(IEnumerable<int> announcementIds)
    {
        try
        {
            var announcements = await _context.GameAnnouncements
                .Where(a => announcementIds.Contains(a.Id))
                .ToListAsync();

            var count = announcements.Count;
            _context.GameAnnouncements.RemoveRange(announcements);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Batch deleted {Count} announcements", count);
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch deleting announcements");
            throw;
        }
    }

    public async Task<int> BatchPublishAnnouncementsAsync(IEnumerable<int> announcementIds)
    {
        try
        {
            var announcements = await _context.GameAnnouncements
                .Where(a => announcementIds.Contains(a.Id))
                .ToListAsync();

            var count = 0;
            var now = DateTime.UtcNow;
            foreach (var announcement in announcements)
            {
                announcement.IsActive = true;
                announcement.StartTime = now;
                announcement.UpdatedAt = now;
                count++;
            }

            await _context.SaveChangesAsync();
            _logger.LogInformation("Batch published {Count} announcements", count);
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch publishing announcements");
            throw;
        }
    }

    public async Task<int> BatchUnpublishAnnouncementsAsync(IEnumerable<int> announcementIds)
    {
        try
        {
            var announcements = await _context.GameAnnouncements
                .Where(a => announcementIds.Contains(a.Id))
                .ToListAsync();

            var count = 0;
            var now = DateTime.UtcNow;
            foreach (var announcement in announcements)
            {
                announcement.IsActive = false;
                announcement.EndTime = now;
                announcement.UpdatedAt = now;
                count++;
            }

            await _context.SaveChangesAsync();
            _logger.LogInformation("Batch unpublished {Count} announcements", count);
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch unpublishing announcements");
            throw;
        }
    }

    // 统计和分析
    public async Task<AnnouncementStatsDto> GetAnnouncementStatsAsync()
    {
        try
        {
            var now = DateTime.UtcNow;
            var announcements = await _context.GameAnnouncements.ToListAsync();

            var stats = new AnnouncementStatsDto
            {
                TotalAnnouncements = announcements.Count,
                ActiveAnnouncements = announcements.Count(a => a.IsActive && a.StartTime <= now && (a.EndTime == null || a.EndTime > now)),
                InactiveAnnouncements = announcements.Count(a => !a.IsActive),
                ScheduledAnnouncements = announcements.Count(a => a.IsActive && a.StartTime > now),
                ExpiredAnnouncements = announcements.Count(a => a.EndTime.HasValue && a.EndTime < now),
                TotalViews = announcements.Sum(a => a.ViewCount),
                AverageViewsPerAnnouncement = announcements.Count > 0 ? (double)announcements.Sum(a => a.ViewCount) / announcements.Count : 0,
                LastAnnouncementDate = announcements.OrderByDescending(a => a.CreatedAt).FirstOrDefault()?.CreatedAt
            };

            // Group by type
            stats.AnnouncementsByType = announcements
                .GroupBy(a => a.Type.ToString())
                .ToDictionary(g => g.Key, g => g.Count());

            // Group by priority ranges
            stats.AnnouncementsByPriority = new Dictionary<string, int>
            {
                ["Low (0-25)"] = announcements.Count(a => a.Priority >= 0 && a.Priority <= 25),
                ["Medium (26-50)"] = announcements.Count(a => a.Priority >= 26 && a.Priority <= 50),
                ["High (51-75)"] = announcements.Count(a => a.Priority >= 51 && a.Priority <= 75),
                ["Critical (76-100)"] = announcements.Count(a => a.Priority >= 76 && a.Priority <= 100)
            };

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcement stats");
            throw;
        }
    }

    public async Task<IEnumerable<AnnouncementViewStatsDto>> GetAnnouncementViewStatsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var query = _context.GameAnnouncements.AsQueryable();

            if (startDate.HasValue)
                query = query.Where(a => a.StartTime >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(a => a.StartTime <= endDate.Value);

            var announcements = await query.ToListAsync();

            return announcements.Select(a =>
            {
                var daysSinceStart = (DateTime.UtcNow - a.StartTime).TotalDays;
                var viewsPerDay = daysSinceStart > 0 ? a.ViewCount / daysSinceStart : 0;

                // Generate simulated daily view data
                var viewsByDate = new Dictionary<string, int>();
                var random = new Random(a.Id); // Use ID as seed for consistent results
                var currentDate = a.StartTime.Date;
                var endDateForViews = a.EndTime?.Date ?? DateTime.UtcNow.Date;

                while (currentDate <= endDateForViews && currentDate <= DateTime.UtcNow.Date)
                {
                    var dailyViews = random.Next(0, Math.Max(1, a.ViewCount / Math.Max(1, (int)daysSinceStart) * 2));
                    viewsByDate[currentDate.ToString("yyyy-MM-dd")] = dailyViews;
                    currentDate = currentDate.AddDays(1);
                }

                return new AnnouncementViewStatsDto
                {
                    AnnouncementId = a.Id,
                    Title = a.Title,
                    Type = a.Type,
                    ViewCount = a.ViewCount,
                    StartTime = a.StartTime,
                    EndTime = a.EndTime,
                    ViewsPerDay = viewsPerDay,
                    ViewsByDate = viewsByDate
                };
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcement view stats");
            throw;
        }
    }

    public async Task<IEnumerable<GameAnnouncementDto>> GetPopularAnnouncementsAsync(int limit = 10)
    {
        try
        {
            var announcements = await _context.GameAnnouncements
                .OrderByDescending(a => a.ViewCount)
                .ThenByDescending(a => a.Priority)
                .Take(limit)
                .ToListAsync();

            return announcements.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting popular announcements");
            throw;
        }
    }

    public async Task<AnnouncementPerformanceDto> GetAnnouncementPerformanceAsync(int id, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var announcement = await _context.GameAnnouncements
                .FirstOrDefaultAsync(a => a.Id == id);

            if (announcement == null)
                throw new ArgumentException($"Announcement with ID {id} not found");

            var random = new Random(id); // Use ID as seed for consistent results
            var daysSinceStart = (DateTime.UtcNow - announcement.StartTime).TotalDays;

            // Generate simulated performance data
            var viewTrend = new Dictionary<string, int>();
            var viewsByServer = new Dictionary<string, int>();
            var viewsByPlayerType = new Dictionary<string, int>();

            // Generate daily trend data
            var currentDate = startDate ?? announcement.StartTime.Date;
            var endDateForTrend = endDate ?? DateTime.UtcNow.Date;

            while (currentDate <= endDateForTrend)
            {
                var dailyViews = random.Next(0, Math.Max(1, announcement.ViewCount / Math.Max(1, (int)daysSinceStart) * 2));
                viewTrend[currentDate.ToString("yyyy-MM-dd")] = dailyViews;
                currentDate = currentDate.AddDays(1);
            }

            // Generate server distribution
            for (int i = 1; i <= 5; i++)
            {
                viewsByServer[$"Server {i}"] = random.Next(0, announcement.ViewCount / 3);
            }

            // Generate player type distribution
            viewsByPlayerType["New Players"] = random.Next(0, announcement.ViewCount / 3);
            viewsByPlayerType["Regular Players"] = random.Next(0, announcement.ViewCount / 2);
            viewsByPlayerType["VIP Players"] = random.Next(0, announcement.ViewCount / 4);

            var peakViewDate = viewTrend.OrderByDescending(kvp => kvp.Value).FirstOrDefault();

            return new AnnouncementPerformanceDto
            {
                AnnouncementId = id,
                Title = announcement.Title,
                TotalViews = announcement.ViewCount,
                UniqueViewers = random.Next(announcement.ViewCount / 2, announcement.ViewCount),
                ClickThroughRate = random.NextDouble() * 0.15, // 0-15%
                EngagementRate = random.NextDouble() * 0.25, // 0-25%
                ViewTrend = viewTrend,
                ViewsByServer = viewsByServer,
                ViewsByPlayerType = viewsByPlayerType,
                PeakViewDate = DateTime.TryParse(peakViewDate.Key, out var peakDate) ? peakDate : null,
                PeakViewCount = peakViewDate.Value
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting announcement performance: {Id}", id);
            throw;
        }
    }

    // 高级功能
    public async Task<bool> ScheduleAnnouncementAsync(int id, DateTime publishTime)
    {
        try
        {
            var announcement = await _context.GameAnnouncements
                .FirstOrDefaultAsync(a => a.Id == id);

            if (announcement == null)
                return false;

            announcement.StartTime = publishTime;
            announcement.IsActive = publishTime <= DateTime.UtcNow;
            announcement.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Scheduled announcement {Id} for {PublishTime}", id, publishTime);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling announcement: {Id}", id);
            throw;
        }
    }

    public async Task<bool> UpdateAnnouncementPriorityAsync(int id, int priority)
    {
        try
        {
            var announcement = await _context.GameAnnouncements
                .FirstOrDefaultAsync(a => a.Id == id);

            if (announcement == null)
                return false;

            announcement.Priority = priority;
            announcement.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated announcement priority: {Id} to {Priority}", id, priority);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating announcement priority: {Id}", id);
            throw;
        }
    }

    public async Task<bool> IncrementViewCountAsync(int id)
    {
        try
        {
            var announcement = await _context.GameAnnouncements
                .FirstOrDefaultAsync(a => a.Id == id);

            if (announcement == null)
                return false;

            announcement.ViewCount++;
            await _context.SaveChangesAsync();

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing view count: {Id}", id);
            throw;
        }
    }

    public async Task<IEnumerable<GameAnnouncementDto>> GetScheduledAnnouncementsAsync()
    {
        try
        {
            var now = DateTime.UtcNow;
            var announcements = await _context.GameAnnouncements
                .Where(a => a.IsActive && a.StartTime > now)
                .OrderBy(a => a.StartTime)
                .ToListAsync();

            return announcements.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting scheduled announcements");
            throw;
        }
    }

    public async Task<IEnumerable<GameAnnouncementDto>> GetExpiredAnnouncementsAsync()
    {
        try
        {
            var now = DateTime.UtcNow;
            var announcements = await _context.GameAnnouncements
                .Where(a => a.EndTime.HasValue && a.EndTime < now)
                .OrderByDescending(a => a.EndTime)
                .ToListAsync();

            return announcements.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting expired announcements");
            throw;
        }
    }

    public async Task<int> CleanupExpiredAnnouncementsAsync()
    {
        try
        {
            var now = DateTime.UtcNow;
            var expiredAnnouncements = await _context.GameAnnouncements
                .Where(a => a.EndTime.HasValue && a.EndTime < now.AddDays(-30)) // Older than 30 days
                .ToListAsync();

            var count = expiredAnnouncements.Count;
            _context.GameAnnouncements.RemoveRange(expiredAnnouncements);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Cleaned up {Count} expired announcements", count);
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired announcements");
            throw;
        }
    }

    // 导入导出
    public async Task<IEnumerable<GameAnnouncementDto>> ImportAnnouncementsAsync(IEnumerable<CreateGameAnnouncementDto> announcements)
    {
        try
        {
            var importedAnnouncements = new List<GameAnnouncement>();

            foreach (var announcementDto in announcements)
            {
                var announcement = new GameAnnouncement
                {
                    Title = announcementDto.Title,
                    Content = announcementDto.Content,
                    Type = announcementDto.Type,
                    StartTime = announcementDto.StartTime,
                    EndTime = announcementDto.EndTime,
                    TargetServers = announcementDto.TargetServers,
                    TargetPlayers = announcementDto.TargetPlayers,
                    Priority = announcementDto.Priority,
                    ImageUrl = announcementDto.ImageUrl,
                    LinkUrl = announcementDto.LinkUrl,
                    IsActive = true,
                    ViewCount = 0,
                    CreatedAt = DateTime.UtcNow
                };

                importedAnnouncements.Add(announcement);
            }

            _context.GameAnnouncements.AddRange(importedAnnouncements);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Imported {Count} announcements", importedAnnouncements.Count);
            return importedAnnouncements.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing announcements");
            throw;
        }
    }

    public async Task<string> ExportAnnouncementsAsync(AnnouncementExportFormat format, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var query = _context.GameAnnouncements.AsQueryable();

            if (startDate.HasValue)
                query = query.Where(a => a.StartTime >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(a => a.StartTime <= endDate.Value);

            var announcements = await query.OrderByDescending(a => a.CreatedAt).ToListAsync();

            switch (format)
            {
                case AnnouncementExportFormat.JSON:
                    var jsonData = announcements.Select(MapToDto);
                    return JsonSerializer.Serialize(jsonData, new JsonSerializerOptions { WriteIndented = true });

                case AnnouncementExportFormat.CSV:
                    var csvBuilder = new System.Text.StringBuilder();
                    csvBuilder.AppendLine("Id,Title,Content,Type,StartTime,EndTime,IsActive,Priority,ViewCount,CreatedAt");

                    foreach (var announcement in announcements)
                    {
                        csvBuilder.AppendLine($"{announcement.Id}," +
                            $"\"{announcement.Title.Replace("\"", "\"\"")}\"," +
                            $"\"{announcement.Content.Replace("\"", "\"\"")}\"," +
                            $"{announcement.Type}," +
                            $"{announcement.StartTime:yyyy-MM-dd HH:mm:ss}," +
                            $"{announcement.EndTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? ""}," +
                            $"{announcement.IsActive}," +
                            $"{announcement.Priority}," +
                            $"{announcement.ViewCount}," +
                            $"{announcement.CreatedAt:yyyy-MM-dd HH:mm:ss}");
                    }
                    return csvBuilder.ToString();

                case AnnouncementExportFormat.Excel:
                    // For Excel format, return CSV for now (can be enhanced with actual Excel library)
                    return await ExportAnnouncementsAsync(AnnouncementExportFormat.CSV, startDate, endDate);

                case AnnouncementExportFormat.PDF:
                    // For PDF format, return a simple text representation
                    var pdfBuilder = new System.Text.StringBuilder();
                    pdfBuilder.AppendLine("ANNOUNCEMENT EXPORT REPORT");
                    pdfBuilder.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
                    pdfBuilder.AppendLine($"Total Announcements: {announcements.Count}");
                    pdfBuilder.AppendLine(new string('=', 50));

                    foreach (var announcement in announcements)
                    {
                        pdfBuilder.AppendLine($"ID: {announcement.Id}");
                        pdfBuilder.AppendLine($"Title: {announcement.Title}");
                        pdfBuilder.AppendLine($"Type: {announcement.Type}");
                        pdfBuilder.AppendLine($"Start Time: {announcement.StartTime:yyyy-MM-dd HH:mm:ss}");
                        pdfBuilder.AppendLine($"End Time: {announcement.EndTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "N/A"}");
                        pdfBuilder.AppendLine($"Active: {announcement.IsActive}");
                        pdfBuilder.AppendLine($"Priority: {announcement.Priority}");
                        pdfBuilder.AppendLine($"Views: {announcement.ViewCount}");
                        pdfBuilder.AppendLine($"Content: {announcement.Content}");
                        pdfBuilder.AppendLine(new string('-', 30));
                    }
                    return pdfBuilder.ToString();

                default:
                    throw new ArgumentException($"Unsupported export format: {format}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting announcements");
            throw;
        }
    }

    // Helper method to map entity to DTO
    private static GameAnnouncementDto MapToDto(GameAnnouncement announcement)
    {
        return new GameAnnouncementDto
        {
            Id = announcement.Id,
            Title = announcement.Title,
            Content = announcement.Content,
            Type = announcement.Type,
            StartTime = announcement.StartTime,
            EndTime = announcement.EndTime,
            IsActive = announcement.IsActive,
            TargetServers = announcement.TargetServers,
            TargetPlayers = announcement.TargetPlayers,
            Priority = announcement.Priority,
            ViewCount = announcement.ViewCount,
            ImageUrl = announcement.ImageUrl,
            LinkUrl = announcement.LinkUrl,
            CreatedAt = announcement.CreatedAt,
            UpdatedAt = announcement.UpdatedAt
        };
    }
}
