using GameManagement.Core.Interfaces;
using GameManagement.Shared.DTOs;

namespace GameManagement.Infrastructure.Services;

public class AuditService : IAuditService
{
    public async Task LogActionAsync(string action, string entityName, string? entityId, object? oldValues, object? newValues, int? userId = null)
    {
        // 简单实现 - 在实际项目中会保存到数据库
        await Task.Delay(1);
        Console.WriteLine($"Audit Log: {action} on {entityName} (ID: {entityId}) by User {userId}");
    }

    public async Task<IEnumerable<AuditLogDto>> GetAuditLogsAsync(int page, int pageSize)
    {
        await Task.Delay(1);
        return new List<AuditLogDto>();
    }

    public async Task<IEnumerable<AuditLogDto>> GetAuditLogsByUserAsync(int userId)
    {
        await Task.Delay(1);
        return new List<AuditLogDto>();
    }

    public async Task<IEnumerable<AuditLogDto>> GetAuditLogsByEntityAsync(string entityName, string entityId)
    {
        await Task.Delay(1);
        return new List<AuditLogDto>();
    }
}
