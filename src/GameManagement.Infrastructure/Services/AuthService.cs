using GameManagement.Core.Interfaces;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;

namespace GameManagement.Infrastructure.Services;

public class AuthService : IAuthService
{
    public async Task<LoginResponseDto> LoginAsync(LoginDto loginDto)
    {
        // 简单的硬编码验证，用于测试
        if (loginDto.Username == "111" && loginDto.Password == "111111")
        {
            var user = new UserDto
            {
                Id = 1,
                Username = "111",
                Email = "<EMAIL>",
                DisplayName = "Test User",
                Roles = new List<string> { "SystemAdmin" },
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            return new LoginResponseDto
            {
                Token = "test-jwt-token-" + Guid.NewGuid().ToString(),
                User = user,
                ExpiresAt = DateTime.UtcNow.AddHours(24)
            };
        }

        throw new UnauthorizedAccessException("Invalid username or password");
    }

    public async Task<LoginResponseDto> RefreshTokenAsync(string refreshToken)
    {
        // 简单实现
        await Task.Delay(1);
        throw new UnauthorizedAccessException("Invalid refresh token");
    }

    public async Task LogoutAsync(string refreshToken)
    {
        // 简单实现
        await Task.Delay(1);
    }

    public async Task<UserDto> RegisterAsync(CreateUserDto createUserDto)
    {
        // 简单实现
        await Task.Delay(1);
        throw new NotImplementedException("Registration not implemented yet");
    }

    public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
    {
        // 简单实现
        await Task.Delay(1);
        return false;
    }

    public async Task<bool> ResetPasswordAsync(string email)
    {
        // 简单实现
        await Task.Delay(1);
        return false;
    }
}
