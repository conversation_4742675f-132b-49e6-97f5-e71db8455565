using GameManagement.Core.Interfaces;
using GameManagement.Infrastructure.Data;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using GameManagement.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text;

namespace GameManagement.Infrastructure.Services;

public partial class ChannelService
{
    // 合同管理
    public async Task<bool> UpdateChannelContractAsync(int id, DateTime startDate, DateTime endDate)
    {
        try
        {
            var channel = await _context.Channels.FindAsync(id);
            if (channel == null)
            {
                _logger.LogWarning("Channel {ChannelId} not found for contract update", id);
                return false;
            }

            channel.ContractStartDate = startDate;
            channel.ContractEndDate = endDate;
            channel.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Contract updated for channel {ChannelId}: {StartDate} to {EndDate}", 
                id, startDate, endDate);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating contract for channel {Id}", id);
            throw;
        }
    }

    public async Task<bool> RenewChannelContractAsync(int id, DateTime newEndDate)
    {
        try
        {
            var channel = await _context.Channels.FindAsync(id);
            if (channel == null)
            {
                _logger.LogWarning("Channel {ChannelId} not found for contract renewal", id);
                return false;
            }

            channel.ContractEndDate = newEndDate;
            channel.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Contract renewed for channel {ChannelId} until {EndDate}", id, newEndDate);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error renewing contract for channel {Id}", id);
            throw;
        }
    }

    public async Task<IEnumerable<ChannelDto>> GetChannelsWithExpiredContractsAsync()
    {
        try
        {
            var now = DateTime.UtcNow;
            var channels = await _context.Channels
                .Include(c => c.UserRegistrations)
                .Include(c => c.PaymentRecords)
                .Where(c => c.ContractEndDate.HasValue && c.ContractEndDate < now)
                .ToListAsync();

            return channels.Select(MapToChannelDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting channels with expired contracts");
            throw;
        }
    }

    // 批量操作
    public async Task<bool> BatchActivateChannelsAsync(IEnumerable<int> channelIds)
    {
        try
        {
            var channels = await _context.Channels
                .Where(c => channelIds.Contains(c.Id))
                .ToListAsync();

            foreach (var channel in channels)
            {
                channel.IsActive = true;
                
                // 如果没有API密钥，生成一个
                if (string.IsNullOrEmpty(channel.ApiKey))
                {
                    channel.ApiKey = GenerateApiKey();
                    channel.ApiSecret = GenerateApiSecret();
                }
                
                channel.UpdatedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Batch activated {Count} channels", channels.Count);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch activating channels");
            throw;
        }
    }

    public async Task<bool> BatchDeactivateChannelsAsync(IEnumerable<int> channelIds)
    {
        try
        {
            var channels = await _context.Channels
                .Where(c => channelIds.Contains(c.Id))
                .ToListAsync();

            foreach (var channel in channels)
            {
                channel.IsActive = false;
                channel.UpdatedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Batch deactivated {Count} channels", channels.Count);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch deactivating channels");
            throw;
        }
    }

    public async Task<bool> BatchUpdateCommissionRateAsync(Dictionary<int, decimal> channelCommissions)
    {
        try
        {
            var channelIds = channelCommissions.Keys;
            var channels = await _context.Channels
                .Where(c => channelIds.Contains(c.Id))
                .ToListAsync();

            foreach (var channel in channels)
            {
                if (channelCommissions.TryGetValue(channel.Id, out var commissionRate))
                {
                    channel.CommissionRate = commissionRate;
                    channel.UpdatedAt = DateTime.UtcNow;
                }
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Batch updated commission rates for {Count} channels", channels.Count);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch updating commission rates");
            throw;
        }
    }

    public async Task<bool> BatchDeleteChannelsAsync(IEnumerable<int> channelIds)
    {
        try
        {
            var channels = await _context.Channels
                .Where(c => channelIds.Contains(c.Id))
                .ToListAsync();

            var softDeleteCount = 0;
            var hardDeleteCount = 0;

            foreach (var channel in channels)
            {
                // 检查是否有关联数据
                var hasRegistrations = await _context.UserRegistrations.AnyAsync(ur => ur.ChannelId == channel.Id);
                var hasPayments = await _context.PaymentRecords.AnyAsync(pr => pr.ChannelId == channel.Id);
                
                if (hasRegistrations || hasPayments)
                {
                    // 软删除：设为非活跃状态
                    channel.IsActive = false;
                    channel.UpdatedAt = DateTime.UtcNow;
                    softDeleteCount++;
                }
                else
                {
                    // 硬删除
                    _context.Channels.Remove(channel);
                    hardDeleteCount++;
                }
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Batch deleted channels: {SoftDelete} soft deleted, {HardDelete} hard deleted", 
                softDeleteCount, hardDeleteCount);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch deleting channels");
            throw;
        }
    }

    // 导入导出
    public async Task<IEnumerable<ChannelDto>> ImportChannelsAsync(IEnumerable<CreateChannelDto> channels)
    {
        try
        {
            var importedChannels = new List<ChannelDto>();
            var existingCodes = await _context.Channels
                .Select(c => c.Code)
                .ToListAsync();

            foreach (var createChannelDto in channels)
            {
                // 跳过已存在的渠道代码
                if (existingCodes.Contains(createChannelDto.Code))
                {
                    _logger.LogWarning("Channel with code '{Code}' already exists, skipping", createChannelDto.Code);
                    continue;
                }

                var channel = new Channel
                {
                    Name = createChannelDto.Name,
                    Code = createChannelDto.Code,
                    Description = createChannelDto.Description,
                    IsActive = createChannelDto.IsActive,
                    ContactPerson = createChannelDto.ContactPerson,
                    ContactEmail = createChannelDto.ContactEmail,
                    ContactPhone = createChannelDto.ContactPhone,
                    CommissionRate = createChannelDto.CommissionRate,
                    CallbackUrl = createChannelDto.CallbackUrl,
                    ContractStartDate = createChannelDto.ContractStartDate,
                    ContractEndDate = createChannelDto.ContractEndDate,
                    CreatedAt = DateTime.UtcNow
                };

                // 生成API密钥
                if (createChannelDto.IsActive)
                {
                    channel.ApiKey = GenerateApiKey();
                    channel.ApiSecret = GenerateApiSecret();
                }

                _context.Channels.Add(channel);
                importedChannels.Add(MapToChannelDto(channel));
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Imported {Count} channels successfully", importedChannels.Count);
            return importedChannels;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing channels");
            throw;
        }
    }

    public async Task<string> ExportChannelsAsync(ChannelExportFormat format, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var query = _context.Channels
                .Include(c => c.UserRegistrations)
                .Include(c => c.PaymentRecords)
                .AsQueryable();

            if (startDate.HasValue)
                query = query.Where(c => c.CreatedAt >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(c => c.CreatedAt <= endDate.Value);

            var channels = await query.ToListAsync();
            var channelDtos = channels.Select(MapToChannelDto).ToList();

            return format switch
            {
                ChannelExportFormat.JSON => JsonSerializer.Serialize(channelDtos, new JsonSerializerOptions { WriteIndented = true }),
                ChannelExportFormat.CSV => ExportChannelsToCSV(channelDtos),
                ChannelExportFormat.Excel => "Excel export not implemented yet",
                ChannelExportFormat.PDF => "PDF export not implemented yet",
                _ => throw new ArgumentException($"Unsupported export format: {format}")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting channels");
            throw;
        }
    }

    public async Task<string> ExportChannelDataAsync(int channelId, ChannelExportFormat format, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var channelData = await GetChannelDataAsync(channelId, startDate, endDate);
            var dataList = channelData.ToList();

            return format switch
            {
                ChannelExportFormat.JSON => JsonSerializer.Serialize(dataList, new JsonSerializerOptions { WriteIndented = true }),
                ChannelExportFormat.CSV => ExportChannelDataToCSV(dataList),
                ChannelExportFormat.Excel => "Excel export not implemented yet",
                ChannelExportFormat.PDF => "PDF export not implemented yet",
                _ => throw new ArgumentException($"Unsupported export format: {format}")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting channel data for channel {ChannelId}", channelId);
            throw;
        }
    }

    // 辅助方法
    private string ExportChannelsToCSV(IEnumerable<ChannelDto> channels)
    {
        var csv = new StringBuilder();
        csv.AppendLine("Id,Name,Code,Description,IsActive,ContactPerson,ContactEmail,ContactPhone,CommissionRate,CallbackUrl,ContractStartDate,ContractEndDate,CreatedAt,UpdatedAt,TotalRegistrations,TotalPayments,TotalRevenue,TotalCommission");

        foreach (var channel in channels)
        {
            csv.AppendLine($"{channel.Id},{EscapeCsvField(channel.Name)},{EscapeCsvField(channel.Code)},{EscapeCsvField(channel.Description)},{channel.IsActive},{EscapeCsvField(channel.ContactPerson)},{EscapeCsvField(channel.ContactEmail)},{EscapeCsvField(channel.ContactPhone)},{channel.CommissionRate},{EscapeCsvField(channel.CallbackUrl)},{channel.ContractStartDate:yyyy-MM-dd},{channel.ContractEndDate:yyyy-MM-dd},{channel.CreatedAt:yyyy-MM-dd HH:mm:ss},{channel.UpdatedAt:yyyy-MM-dd HH:mm:ss},{channel.TotalRegistrations},{channel.TotalPayments},{channel.TotalRevenue},{channel.TotalCommission}");
        }

        return csv.ToString();
    }

    private string ExportChannelDataToCSV(IEnumerable<ChannelDataDto> channelData)
    {
        var csv = new StringBuilder();
        csv.AppendLine("Id,ChannelId,ChannelName,Date,MetricName,Value,AdditionalData,CreatedAt,UpdatedAt");

        foreach (var data in channelData)
        {
            csv.AppendLine($"{data.Id},{data.ChannelId},{EscapeCsvField(data.ChannelName)},{data.Date:yyyy-MM-dd},{EscapeCsvField(data.MetricName)},{data.Value},{EscapeCsvField(data.AdditionalData)},{data.CreatedAt:yyyy-MM-dd HH:mm:ss},{data.UpdatedAt:yyyy-MM-dd HH:mm:ss}");
        }

        return csv.ToString();
    }

    private string EscapeCsvField(string? field)
    {
        if (string.IsNullOrEmpty(field))
            return "";

        if (field.Contains(',') || field.Contains('"') || field.Contains('\n'))
        {
            return $"\"{field.Replace("\"", "\"\"")}\"";
        }

        return field;
    }
}
