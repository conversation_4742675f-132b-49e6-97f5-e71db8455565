using GameManagement.Core.Interfaces;
using GameManagement.Infrastructure.Data;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using GameManagement.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Security.Cryptography;
using System.Text;

namespace GameManagement.Infrastructure.Services;

public partial class ChannelService
{
    // 继续统计和分析方法
    public async Task<IEnumerable<ChannelPerformanceDto>> GetAllChannelsPerformanceAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var channels = await _context.Channels
                .Include(c => c.UserRegistrations)
                .Include(c => c.PaymentRecords)
                .ToListAsync();

            var performances = new List<ChannelPerformanceDto>();

            foreach (var channel in channels)
            {
                var performance = await GetChannelPerformanceAsync(channel.Id, startDate, endDate);
                performances.Add(performance);
            }

            return performances.OrderByDescending(p => p.TotalRevenue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all channels performance");
            throw;
        }
    }

    public async Task<IEnumerable<ChannelDto>> GetTopPerformingChannelsAsync(int count = 10, string metric = "revenue")
    {
        try
        {
            var query = _context.Channels
                .Include(c => c.UserRegistrations)
                .Include(c => c.PaymentRecords)
                .Where(c => c.IsActive);

            switch (metric.ToLower())
            {
                case "revenue":
                    query = query.OrderByDescending(c => c.PaymentRecords
                        .Where(p => p.Status == PaymentStatus.Completed)
                        .Sum(p => p.Amount));
                    break;
                case "registrations":
                    query = query.OrderByDescending(c => c.UserRegistrations.Count);
                    break;
                case "payments":
                    query = query.OrderByDescending(c => c.PaymentRecords.Count);
                    break;
                case "commission":
                    query = query.OrderByDescending(c => c.PaymentRecords
                        .Where(p => p.Status == PaymentStatus.Completed)
                        .Sum(p => p.Amount) * c.CommissionRate);
                    break;
                default:
                    query = query.OrderByDescending(c => c.PaymentRecords
                        .Where(p => p.Status == PaymentStatus.Completed)
                        .Sum(p => p.Amount));
                    break;
            }

            var channels = await query.Take(count).ToListAsync();
            return channels.Select(MapToChannelDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting top performing channels by {Metric}", metric);
            throw;
        }
    }

    public async Task<ChannelComparisonDto> CompareChannelsAsync(IEnumerable<int> channelIds, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var performances = new List<ChannelPerformanceDto>();
            
            foreach (var channelId in channelIds)
            {
                var performance = await GetChannelPerformanceAsync(channelId, startDate, endDate);
                performances.Add(performance);
            }

            var totalsByMetric = new Dictionary<string, decimal>
            {
                ["TotalRegistrations"] = performances.Sum(p => p.TotalRegistrations),
                ["TotalPayments"] = performances.Sum(p => p.TotalPayments),
                ["TotalRevenue"] = performances.Sum(p => p.TotalRevenue),
                ["TotalCommission"] = performances.Sum(p => p.TotalCommission)
            };

            var averagesByMetric = new Dictionary<string, decimal>
            {
                ["AverageRegistrations"] = performances.Count > 0 ? (decimal)performances.Average(p => p.TotalRegistrations) : 0,
                ["AveragePayments"] = performances.Count > 0 ? (decimal)performances.Average(p => p.TotalPayments) : 0,
                ["AverageRevenue"] = performances.Count > 0 ? performances.Average(p => p.TotalRevenue) : 0,
                ["AverageCommission"] = performances.Count > 0 ? performances.Average(p => p.TotalCommission) : 0,
                ["AverageConversionRate"] = performances.Count > 0 ? performances.Average(p => p.ConversionRate) : 0
            };

            var topPerformer = performances.OrderByDescending(p => p.TotalRevenue).FirstOrDefault();
            var lowestPerformer = performances.OrderBy(p => p.TotalRevenue).FirstOrDefault();

            return new ChannelComparisonDto
            {
                Channels = performances,
                TotalsByMetric = totalsByMetric,
                AveragesByMetric = averagesByMetric,
                TopPerformer = topPerformer,
                LowestPerformer = lowestPerformer,
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-30),
                EndDate = endDate ?? DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error comparing channels");
            throw;
        }
    }

    // 佣金管理
    public async Task<bool> UpdateChannelCommissionRateAsync(int id, decimal commissionRate)
    {
        try
        {
            var channel = await _context.Channels.FindAsync(id);
            if (channel == null)
            {
                _logger.LogWarning("Channel {ChannelId} not found for commission rate update", id);
                return false;
            }

            channel.CommissionRate = commissionRate;
            channel.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Channel {ChannelId} commission rate updated to {CommissionRate}", id, commissionRate);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating commission rate for channel {Id}", id);
            throw;
        }
    }

    public async Task<decimal> CalculateChannelCommissionAsync(int channelId, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var channel = await _context.Channels
                .Include(c => c.PaymentRecords)
                .FirstOrDefaultAsync(c => c.Id == channelId);

            if (channel == null)
            {
                throw new InvalidOperationException($"Channel with ID {channelId} not found");
            }

            var query = channel.PaymentRecords
                .Where(p => p.Status == PaymentStatus.Completed);

            if (startDate.HasValue)
                query = query.Where(p => p.CreatedAt >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(p => p.CreatedAt <= endDate.Value);

            var totalRevenue = query.Sum(p => p.Amount);
            var commission = totalRevenue * channel.CommissionRate;

            _logger.LogInformation("Calculated commission for channel {ChannelId}: {Commission} (Revenue: {Revenue}, Rate: {Rate})", 
                channelId, commission, totalRevenue, channel.CommissionRate);

            return commission;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating commission for channel {ChannelId}", channelId);
            throw;
        }
    }

    public async Task<IEnumerable<ChannelCommissionDto>> GetChannelCommissionsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var channels = await _context.Channels
                .Include(c => c.PaymentRecords)
                .Where(c => c.IsActive)
                .ToListAsync();

            var commissions = new List<ChannelCommissionDto>();

            foreach (var channel in channels)
            {
                var query = channel.PaymentRecords
                    .Where(p => p.Status == PaymentStatus.Completed);

                if (startDate.HasValue)
                    query = query.Where(p => p.CreatedAt >= startDate.Value);

                if (endDate.HasValue)
                    query = query.Where(p => p.CreatedAt <= endDate.Value);

                var totalRevenue = query.Sum(p => p.Amount);
                var totalCommission = totalRevenue * channel.CommissionRate;

                if (totalRevenue > 0)
                {
                    commissions.Add(new ChannelCommissionDto
                    {
                        ChannelId = channel.Id,
                        ChannelName = channel.Name,
                        ChannelCode = channel.Code,
                        CommissionRate = channel.CommissionRate,
                        TotalRevenue = totalRevenue,
                        TotalCommission = totalCommission,
                        CalculationDate = DateTime.UtcNow,
                        StartDate = startDate ?? DateTime.UtcNow.AddDays(-30),
                        EndDate = endDate ?? DateTime.UtcNow
                    });
                }
            }

            return commissions.OrderByDescending(c => c.TotalCommission);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting channel commissions");
            throw;
        }
    }

    // API密钥管理
    public async Task<bool> GenerateApiKeyAsync(int channelId)
    {
        try
        {
            var channel = await _context.Channels.FindAsync(channelId);
            if (channel == null)
            {
                _logger.LogWarning("Channel {ChannelId} not found for API key generation", channelId);
                return false;
            }

            channel.ApiKey = GenerateApiKey();
            channel.ApiSecret = GenerateApiSecret();
            channel.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("API key generated for channel {ChannelId}", channelId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating API key for channel {ChannelId}", channelId);
            throw;
        }
    }

    public async Task<bool> RegenerateApiKeyAsync(int channelId)
    {
        try
        {
            var channel = await _context.Channels.FindAsync(channelId);
            if (channel == null)
            {
                _logger.LogWarning("Channel {ChannelId} not found for API key regeneration", channelId);
                return false;
            }

            channel.ApiKey = GenerateApiKey();
            channel.ApiSecret = GenerateApiSecret();
            channel.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("API key regenerated for channel {ChannelId}", channelId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error regenerating API key for channel {ChannelId}", channelId);
            throw;
        }
    }

    public async Task<bool> RevokeApiKeyAsync(int channelId)
    {
        try
        {
            var channel = await _context.Channels.FindAsync(channelId);
            if (channel == null)
            {
                _logger.LogWarning("Channel {ChannelId} not found for API key revocation", channelId);
                return false;
            }

            channel.ApiKey = null;
            channel.ApiSecret = null;
            channel.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("API key revoked for channel {ChannelId}", channelId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking API key for channel {ChannelId}", channelId);
            throw;
        }
    }

    public async Task<bool> ValidateApiKeyAsync(string apiKey)
    {
        try
        {
            var channel = await _context.Channels
                .FirstOrDefaultAsync(c => c.ApiKey == apiKey && c.IsActive);

            return channel != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating API key");
            throw;
        }
    }

    public async Task<ChannelDto?> GetChannelByApiKeyAsync(string apiKey)
    {
        try
        {
            var channel = await _context.Channels
                .Include(c => c.UserRegistrations)
                .Include(c => c.PaymentRecords)
                .FirstOrDefaultAsync(c => c.ApiKey == apiKey && c.IsActive);

            return channel == null ? null : MapToChannelDto(channel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting channel by API key");
            throw;
        }
    }
}
