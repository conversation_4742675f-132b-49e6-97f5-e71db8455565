using GameManagement.Core.Interfaces;
using GameManagement.Infrastructure.Data;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using GameManagement.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Security.Cryptography;
using System.Text;

namespace GameManagement.Infrastructure.Services;

public partial class ChannelService : IChannelService
{
    private readonly GameManagementDbContext _context;
    private readonly ILogger<ChannelService> _logger;

    public ChannelService(GameManagementDbContext context, ILogger<ChannelService> logger)
    {
        _context = context;
        _logger = logger;
    }

    // 基础CRUD操作
    public async Task<IEnumerable<ChannelDto>> GetChannelsAsync(int page = 1, int pageSize = 50)
    {
        try
        {
            var channels = await _context.Channels
                .Include(c => c.UserRegistrations)
                .Include(c => c.PaymentRecords)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return channels.Select(MapToChannelDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting channels");
            throw;
        }
    }

    public async Task<ChannelDto?> GetChannelByIdAsync(int id)
    {
        try
        {
            var channel = await _context.Channels
                .Include(c => c.UserRegistrations)
                .Include(c => c.PaymentRecords)
                .FirstOrDefaultAsync(c => c.Id == id);

            return channel == null ? null : MapToChannelDto(channel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting channel by ID {Id}", id);
            throw;
        }
    }

    public async Task<ChannelDto?> GetChannelByCodeAsync(string code)
    {
        try
        {
            var channel = await _context.Channels
                .Include(c => c.UserRegistrations)
                .Include(c => c.PaymentRecords)
                .FirstOrDefaultAsync(c => c.Code == code);

            return channel == null ? null : MapToChannelDto(channel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting channel by code {Code}", code);
            throw;
        }
    }

    public async Task<ChannelDto> CreateChannelAsync(CreateChannelDto createChannelDto)
    {
        try
        {
            // 检查代码是否已存在
            var existingChannel = await _context.Channels
                .FirstOrDefaultAsync(c => c.Code == createChannelDto.Code);
            
            if (existingChannel != null)
            {
                throw new InvalidOperationException($"Channel with code '{createChannelDto.Code}' already exists");
            }

            var channel = new Channel
            {
                Name = createChannelDto.Name,
                Code = createChannelDto.Code,
                Description = createChannelDto.Description,
                IsActive = createChannelDto.IsActive,
                ContactPerson = createChannelDto.ContactPerson,
                ContactEmail = createChannelDto.ContactEmail,
                ContactPhone = createChannelDto.ContactPhone,
                CommissionRate = createChannelDto.CommissionRate,
                CallbackUrl = createChannelDto.CallbackUrl,
                ContractStartDate = createChannelDto.ContractStartDate,
                ContractEndDate = createChannelDto.ContractEndDate,
                CreatedAt = DateTime.UtcNow
            };

            // 生成API密钥
            if (createChannelDto.IsActive)
            {
                channel.ApiKey = GenerateApiKey();
                channel.ApiSecret = GenerateApiSecret();
            }

            _context.Channels.Add(channel);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Channel {ChannelName} created with ID {ChannelId}", 
                channel.Name, channel.Id);

            return MapToChannelDto(channel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating channel");
            throw;
        }
    }

    public async Task<ChannelDto> UpdateChannelAsync(int id, UpdateChannelDto updateChannelDto)
    {
        try
        {
            var channel = await _context.Channels.FindAsync(id);
            if (channel == null)
            {
                throw new InvalidOperationException($"Channel with ID {id} not found");
            }

            // 更新字段
            if (!string.IsNullOrEmpty(updateChannelDto.Name))
                channel.Name = updateChannelDto.Name;
            
            if (updateChannelDto.Description != null)
                channel.Description = updateChannelDto.Description;
            
            if (updateChannelDto.IsActive.HasValue)
                channel.IsActive = updateChannelDto.IsActive.Value;
            
            if (updateChannelDto.ContactPerson != null)
                channel.ContactPerson = updateChannelDto.ContactPerson;
            
            if (updateChannelDto.ContactEmail != null)
                channel.ContactEmail = updateChannelDto.ContactEmail;
            
            if (updateChannelDto.ContactPhone != null)
                channel.ContactPhone = updateChannelDto.ContactPhone;
            
            if (updateChannelDto.CommissionRate.HasValue)
                channel.CommissionRate = updateChannelDto.CommissionRate.Value;
            
            if (updateChannelDto.CallbackUrl != null)
                channel.CallbackUrl = updateChannelDto.CallbackUrl;
            
            if (updateChannelDto.ContractStartDate.HasValue)
                channel.ContractStartDate = updateChannelDto.ContractStartDate;
            
            if (updateChannelDto.ContractEndDate.HasValue)
                channel.ContractEndDate = updateChannelDto.ContractEndDate;

            channel.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Channel {ChannelId} updated successfully", id);

            return MapToChannelDto(channel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating channel {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeleteChannelAsync(int id)
    {
        try
        {
            var channel = await _context.Channels.FindAsync(id);
            if (channel == null)
            {
                _logger.LogWarning("Channel {ChannelId} not found for deletion", id);
                return false;
            }

            // 检查是否有关联数据
            var hasRegistrations = await _context.UserRegistrations.AnyAsync(ur => ur.ChannelId == id);
            var hasPayments = await _context.PaymentRecords.AnyAsync(pr => pr.ChannelId == id);
            
            if (hasRegistrations || hasPayments)
            {
                // 软删除：设为非活跃状态
                channel.IsActive = false;
                channel.UpdatedAt = DateTime.UtcNow;
                _logger.LogInformation("Channel {ChannelId} soft deleted (deactivated) due to existing data", id);
            }
            else
            {
                // 硬删除
                _context.Channels.Remove(channel);
                _logger.LogInformation("Channel {ChannelId} hard deleted", id);
            }

            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting channel {Id}", id);
            throw;
        }
    }

    // 状态管理
    public async Task<bool> ActivateChannelAsync(int id)
    {
        try
        {
            var channel = await _context.Channels.FindAsync(id);
            if (channel == null)
            {
                _logger.LogWarning("Channel {ChannelId} not found for activation", id);
                return false;
            }

            channel.IsActive = true;
            
            // 如果没有API密钥，生成一个
            if (string.IsNullOrEmpty(channel.ApiKey))
            {
                channel.ApiKey = GenerateApiKey();
                channel.ApiSecret = GenerateApiSecret();
            }
            
            channel.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Channel {ChannelId} activated successfully", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating channel {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeactivateChannelAsync(int id)
    {
        try
        {
            var channel = await _context.Channels.FindAsync(id);
            if (channel == null)
            {
                _logger.LogWarning("Channel {ChannelId} not found for deactivation", id);
                return false;
            }

            channel.IsActive = false;
            channel.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Channel {ChannelId} deactivated successfully", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating channel {Id}", id);
            throw;
        }
    }

    public async Task<IEnumerable<ChannelDto>> GetActiveChannelsAsync()
    {
        try
        {
            var channels = await _context.Channels
                .Include(c => c.UserRegistrations)
                .Include(c => c.PaymentRecords)
                .Where(c => c.IsActive)
                .ToListAsync();

            return channels.Select(MapToChannelDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active channels");
            throw;
        }
    }

    public async Task<IEnumerable<ChannelDto>> GetInactiveChannelsAsync()
    {
        try
        {
            var channels = await _context.Channels
                .Include(c => c.UserRegistrations)
                .Include(c => c.PaymentRecords)
                .Where(c => !c.IsActive)
                .ToListAsync();

            return channels.Select(MapToChannelDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting inactive channels");
            throw;
        }
    }

    // 辅助方法
    private ChannelDto MapToChannelDto(Channel channel)
    {
        var totalRegistrations = channel.UserRegistrations?.Count ?? 0;
        var totalPayments = channel.PaymentRecords?.Count ?? 0;
        var totalRevenue = channel.PaymentRecords?.Where(p => p.Status == PaymentStatus.Completed).Sum(p => p.Amount) ?? 0;
        var totalCommission = totalRevenue * channel.CommissionRate;

        return new ChannelDto
        {
            Id = channel.Id,
            Name = channel.Name,
            Code = channel.Code,
            Description = channel.Description,
            IsActive = channel.IsActive,
            ContactPerson = channel.ContactPerson,
            ContactEmail = channel.ContactEmail,
            ContactPhone = channel.ContactPhone,
            CommissionRate = channel.CommissionRate,
            ApiKey = channel.ApiKey,
            CallbackUrl = channel.CallbackUrl,
            ContractStartDate = channel.ContractStartDate,
            ContractEndDate = channel.ContractEndDate,
            CreatedAt = channel.CreatedAt,
            UpdatedAt = channel.UpdatedAt,
            TotalRegistrations = totalRegistrations,
            TotalPayments = totalPayments,
            TotalRevenue = totalRevenue,
            TotalCommission = totalCommission
        };
    }

    private string GenerateApiKey()
    {
        return $"ch_{Guid.NewGuid():N}";
    }

    private string GenerateApiSecret()
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[32];
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes);
    }

    // 筛选和搜索
    public async Task<IEnumerable<ChannelDto>> SearchChannelsAsync(string searchTerm)
    {
        try
        {
            var channels = await _context.Channels
                .Include(c => c.UserRegistrations)
                .Include(c => c.PaymentRecords)
                .Where(c => c.Name.Contains(searchTerm) ||
                           c.Code.Contains(searchTerm) ||
                           (c.Description != null && c.Description.Contains(searchTerm)) ||
                           (c.ContactPerson != null && c.ContactPerson.Contains(searchTerm)))
                .ToListAsync();

            return channels.Select(MapToChannelDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching channels with term {SearchTerm}", searchTerm);
            throw;
        }
    }

    public async Task<IEnumerable<ChannelDto>> GetChannelsByCommissionRangeAsync(decimal minRate, decimal maxRate)
    {
        try
        {
            var channels = await _context.Channels
                .Include(c => c.UserRegistrations)
                .Include(c => c.PaymentRecords)
                .Where(c => c.CommissionRate >= minRate && c.CommissionRate <= maxRate)
                .ToListAsync();

            return channels.Select(MapToChannelDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting channels by commission range {MinRate}-{MaxRate}", minRate, maxRate);
            throw;
        }
    }

    public async Task<IEnumerable<ChannelDto>> GetChannelsWithActiveContractsAsync()
    {
        try
        {
            var now = DateTime.UtcNow;
            var channels = await _context.Channels
                .Include(c => c.UserRegistrations)
                .Include(c => c.PaymentRecords)
                .Where(c => c.ContractStartDate.HasValue &&
                           c.ContractEndDate.HasValue &&
                           c.ContractStartDate <= now &&
                           c.ContractEndDate >= now)
                .ToListAsync();

            return channels.Select(MapToChannelDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting channels with active contracts");
            throw;
        }
    }

    public async Task<IEnumerable<ChannelDto>> GetChannelsWithExpiringContractsAsync(int daysFromNow = 30)
    {
        try
        {
            var now = DateTime.UtcNow;
            var expiryDate = now.AddDays(daysFromNow);

            var channels = await _context.Channels
                .Include(c => c.UserRegistrations)
                .Include(c => c.PaymentRecords)
                .Where(c => c.ContractEndDate.HasValue &&
                           c.ContractEndDate >= now &&
                           c.ContractEndDate <= expiryDate)
                .ToListAsync();

            return channels.Select(MapToChannelDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting channels with expiring contracts");
            throw;
        }
    }

    // 渠道数据管理
    public async Task<bool> AddChannelDataAsync(int channelId, string metricName, decimal value, DateTime date, string? additionalData = null)
    {
        try
        {
            var channel = await _context.Channels.FindAsync(channelId);
            if (channel == null)
            {
                _logger.LogWarning("Channel {ChannelId} not found for adding data", channelId);
                return false;
            }

            var channelData = new ChannelData
            {
                ChannelId = channelId,
                Date = date,
                MetricName = metricName,
                Value = value,
                AdditionalData = additionalData,
                CreatedAt = DateTime.UtcNow
            };

            _context.ChannelData.Add(channelData);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Channel data added for channel {ChannelId}, metric {MetricName}", channelId, metricName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding channel data for channel {ChannelId}", channelId);
            throw;
        }
    }

    public async Task<IEnumerable<ChannelDataDto>> GetChannelDataAsync(int channelId, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var query = _context.ChannelData
                .Include(cd => cd.Channel)
                .Where(cd => cd.ChannelId == channelId);

            if (startDate.HasValue)
                query = query.Where(cd => cd.Date >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(cd => cd.Date <= endDate.Value);

            var channelData = await query.OrderByDescending(cd => cd.Date).ToListAsync();

            return channelData.Select(cd => new ChannelDataDto
            {
                Id = cd.Id,
                ChannelId = cd.ChannelId,
                ChannelName = cd.Channel.Name,
                Date = cd.Date,
                MetricName = cd.MetricName,
                Value = cd.Value,
                AdditionalData = cd.AdditionalData,
                CreatedAt = cd.CreatedAt,
                UpdatedAt = cd.UpdatedAt
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting channel data for channel {ChannelId}", channelId);
            throw;
        }
    }

    public async Task<IEnumerable<ChannelDataDto>> GetChannelDataByMetricAsync(int channelId, string metricName, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var query = _context.ChannelData
                .Include(cd => cd.Channel)
                .Where(cd => cd.ChannelId == channelId && cd.MetricName == metricName);

            if (startDate.HasValue)
                query = query.Where(cd => cd.Date >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(cd => cd.Date <= endDate.Value);

            var channelData = await query.OrderByDescending(cd => cd.Date).ToListAsync();

            return channelData.Select(cd => new ChannelDataDto
            {
                Id = cd.Id,
                ChannelId = cd.ChannelId,
                ChannelName = cd.Channel.Name,
                Date = cd.Date,
                MetricName = cd.MetricName,
                Value = cd.Value,
                AdditionalData = cd.AdditionalData,
                CreatedAt = cd.CreatedAt,
                UpdatedAt = cd.UpdatedAt
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting channel data by metric for channel {ChannelId}, metric {MetricName}", channelId, metricName);
            throw;
        }
    }

    public async Task<bool> UpdateChannelDataAsync(int dataId, decimal value, string? additionalData = null)
    {
        try
        {
            var channelData = await _context.ChannelData.FindAsync(dataId);
            if (channelData == null)
            {
                _logger.LogWarning("Channel data {DataId} not found for update", dataId);
                return false;
            }

            channelData.Value = value;
            channelData.AdditionalData = additionalData;
            channelData.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Channel data {DataId} updated successfully", dataId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating channel data {DataId}", dataId);
            throw;
        }
    }

    public async Task<bool> DeleteChannelDataAsync(int dataId)
    {
        try
        {
            var channelData = await _context.ChannelData.FindAsync(dataId);
            if (channelData == null)
            {
                _logger.LogWarning("Channel data {DataId} not found for deletion", dataId);
                return false;
            }

            _context.ChannelData.Remove(channelData);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Channel data {DataId} deleted successfully", dataId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting channel data {DataId}", dataId);
            throw;
        }
    }

    // 统计和分析
    public async Task<ChannelStatsDto> GetChannelStatsAsync()
    {
        try
        {
            var totalChannels = await _context.Channels.CountAsync();
            var activeChannels = await _context.Channels.CountAsync(c => c.IsActive);
            var inactiveChannels = totalChannels - activeChannels;

            var now = DateTime.UtcNow;
            var channelsWithActiveContracts = await _context.Channels
                .CountAsync(c => c.ContractStartDate.HasValue &&
                               c.ContractEndDate.HasValue &&
                               c.ContractStartDate <= now &&
                               c.ContractEndDate >= now);

            var channelsWithExpiringContracts = await _context.Channels
                .CountAsync(c => c.ContractEndDate.HasValue &&
                               c.ContractEndDate >= now &&
                               c.ContractEndDate <= now.AddDays(30));

            var averageCommissionRate = await _context.Channels
                .Where(c => c.IsActive)
                .AverageAsync(c => (decimal?)c.CommissionRate) ?? 0;

            var totalRevenue = await _context.PaymentRecords
                .Where(p => p.Status == PaymentStatus.Completed)
                .SumAsync(p => p.Amount);

            var totalCommissions = await _context.Channels
                .Include(c => c.PaymentRecords)
                .SelectMany(c => c.PaymentRecords.Where(p => p.Status == PaymentStatus.Completed))
                .SumAsync(p => p.Amount * p.Channel.CommissionRate);

            var totalRegistrations = await _context.UserRegistrations.CountAsync();
            var totalPayments = await _context.PaymentRecords.CountAsync();

            var channelsByStatus = await _context.Channels
                .GroupBy(c => c.IsActive ? "Active" : "Inactive")
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Status, x => x.Count);

            var revenueByChannel = await _context.Channels
                .Include(c => c.PaymentRecords)
                .Select(c => new {
                    ChannelName = c.Name,
                    Revenue = c.PaymentRecords.Where(p => p.Status == PaymentStatus.Completed).Sum(p => p.Amount)
                })
                .Where(x => x.Revenue > 0)
                .ToDictionaryAsync(x => x.ChannelName, x => x.Revenue);

            var lastChannelCreated = await _context.Channels
                .OrderByDescending(c => c.CreatedAt)
                .Select(c => (DateTime?)c.CreatedAt)
                .FirstOrDefaultAsync();

            return new ChannelStatsDto
            {
                TotalChannels = totalChannels,
                ActiveChannels = activeChannels,
                InactiveChannels = inactiveChannels,
                ChannelsWithActiveContracts = channelsWithActiveContracts,
                ChannelsWithExpiringContracts = channelsWithExpiringContracts,
                AverageCommissionRate = averageCommissionRate,
                TotalRevenue = totalRevenue,
                TotalCommissions = totalCommissions,
                TotalRegistrations = totalRegistrations,
                TotalPayments = totalPayments,
                ChannelsByStatus = channelsByStatus,
                RevenueByChannel = revenueByChannel,
                LastChannelCreated = lastChannelCreated
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting channel stats");
            throw;
        }
    }

    public async Task<ChannelPerformanceDto> GetChannelPerformanceAsync(int channelId, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var channel = await _context.Channels
                .Include(c => c.UserRegistrations)
                .Include(c => c.PaymentRecords)
                .FirstOrDefaultAsync(c => c.Id == channelId);

            if (channel == null)
            {
                throw new InvalidOperationException($"Channel with ID {channelId} not found");
            }

            var start = startDate ?? DateTime.UtcNow.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow;

            var registrations = channel.UserRegistrations
                .Where(ur => ur.CreatedAt >= start && ur.CreatedAt <= end)
                .ToList();

            var payments = channel.PaymentRecords
                .Where(pr => pr.CreatedAt >= start && pr.CreatedAt <= end && pr.Status == PaymentStatus.Completed)
                .ToList();

            var totalRegistrations = registrations.Count;
            var totalPayments = payments.Count;
            var totalRevenue = payments.Sum(p => p.Amount);
            var totalCommission = totalRevenue * channel.CommissionRate;
            var conversionRate = totalRegistrations > 0 ? (decimal)totalPayments / totalRegistrations : 0;
            var averageRevenuePerUser = totalRegistrations > 0 ? totalRevenue / totalRegistrations : 0;

            var registrationsByDate = registrations
                .GroupBy(r => r.CreatedAt.Date)
                .ToDictionary(g => g.Key.ToString("yyyy-MM-dd"), g => g.Count());

            var revenueByDate = payments
                .GroupBy(p => p.CreatedAt.Date)
                .ToDictionary(g => g.Key.ToString("yyyy-MM-dd"), g => g.Sum(p => p.Amount));

            return new ChannelPerformanceDto
            {
                ChannelId = channel.Id,
                ChannelName = channel.Name,
                ChannelCode = channel.Code,
                TotalRegistrations = totalRegistrations,
                TotalPayments = totalPayments,
                TotalRevenue = totalRevenue,
                TotalCommission = totalCommission,
                ConversionRate = conversionRate,
                AverageRevenuePerUser = averageRevenuePerUser,
                RegistrationsByDate = registrationsByDate,
                RevenueByDate = revenueByDate,
                StartDate = start,
                EndDate = end
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting channel performance for channel {ChannelId}", channelId);
            throw;
        }
    }
}
