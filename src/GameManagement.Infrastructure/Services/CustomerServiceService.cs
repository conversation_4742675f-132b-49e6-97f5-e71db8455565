using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using GameManagement.Core.Interfaces;
using GameManagement.Infrastructure.Data;
using GameManagement.Core.Entities;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;

namespace GameManagement.Infrastructure.Services;

public class CustomerServiceService : ICustomerServiceService
{
    private readonly GameManagementDbContext _context;
    private readonly ILogger<CustomerServiceService> _logger;

    public CustomerServiceService(GameManagementDbContext context, ILogger<CustomerServiceService> logger)
    {
        _context = context;
        _logger = logger;
    }

    // Ticket Management
    public async Task<CustomerServiceTicketDto?> GetTicketByIdAsync(int id)
    {
        try
        {
            var ticket = await _context.CustomerServiceTickets
                .Include(t => t.Player)
                .Include(t => t.AssignedToUser)
                .Include(t => t.Messages)
                .ThenInclude(m => m.User)
                .FirstOrDefaultAsync(t => t.Id == id);

            return ticket != null ? MapToCustomerServiceTicketDto(ticket) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving ticket by ID {Id}", id);
            throw;
        }
    }

    public async Task<CustomerServiceTicketDto?> GetTicketByTicketIdAsync(string ticketId)
    {
        try
        {
            var ticket = await _context.CustomerServiceTickets
                .Include(t => t.Player)
                .Include(t => t.AssignedToUser)
                .Include(t => t.Messages)
                .ThenInclude(m => m.User)
                .FirstOrDefaultAsync(t => t.TicketId == ticketId);

            return ticket != null ? MapToCustomerServiceTicketDto(ticket) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving ticket by ticket ID {TicketId}", ticketId);
            throw;
        }
    }

    public async Task<IEnumerable<CustomerServiceTicketDto>> GetTicketsAsync(int page, int pageSize)
    {
        try
        {
            var tickets = await _context.CustomerServiceTickets
                .Include(t => t.Player)
                .Include(t => t.AssignedToUser)
                .Include(t => t.Messages)
                .OrderByDescending(t => t.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return tickets.Select(MapToCustomerServiceTicketDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tickets page {Page}, size {PageSize}", page, pageSize);
            throw;
        }
    }

    public async Task<IEnumerable<CustomerServiceTicketDto>> GetTicketsByStatusAsync(TicketStatus status)
    {
        try
        {
            var tickets = await _context.CustomerServiceTickets
                .Include(t => t.Player)
                .Include(t => t.AssignedToUser)
                .Include(t => t.Messages)
                .Where(t => t.Status == status)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();

            return tickets.Select(MapToCustomerServiceTicketDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tickets by status {Status}", status);
            throw;
        }
    }

    public async Task<IEnumerable<CustomerServiceTicketDto>> GetTicketsByPriorityAsync(TicketPriority priority)
    {
        try
        {
            var tickets = await _context.CustomerServiceTickets
                .Include(t => t.Player)
                .Include(t => t.AssignedToUser)
                .Include(t => t.Messages)
                .Where(t => t.Priority == priority)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();

            return tickets.Select(MapToCustomerServiceTicketDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tickets by priority {Priority}", priority);
            throw;
        }
    }

    public async Task<IEnumerable<CustomerServiceTicketDto>> GetTicketsByCategoryAsync(TicketCategory category)
    {
        try
        {
            var tickets = await _context.CustomerServiceTickets
                .Include(t => t.Player)
                .Include(t => t.AssignedToUser)
                .Include(t => t.Messages)
                .Where(t => t.Category == category)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();

            return tickets.Select(MapToCustomerServiceTicketDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tickets by category {Category}", category);
            throw;
        }
    }

    public async Task<IEnumerable<CustomerServiceTicketDto>> GetTicketsByPlayerAsync(int playerId)
    {
        try
        {
            var tickets = await _context.CustomerServiceTickets
                .Include(t => t.Player)
                .Include(t => t.AssignedToUser)
                .Include(t => t.Messages)
                .Where(t => t.PlayerId == playerId)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();

            return tickets.Select(MapToCustomerServiceTicketDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tickets by player {PlayerId}", playerId);
            throw;
        }
    }

    public async Task<IEnumerable<CustomerServiceTicketDto>> GetAssignedTicketsAsync(int userId)
    {
        try
        {
            var tickets = await _context.CustomerServiceTickets
                .Include(t => t.Player)
                .Include(t => t.AssignedToUser)
                .Include(t => t.Messages)
                .Where(t => t.AssignedToUserId == userId)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();

            return tickets.Select(MapToCustomerServiceTicketDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving assigned tickets for user {UserId}", userId);
            throw;
        }
    }

    public async Task<IEnumerable<CustomerServiceTicketDto>> SearchTicketsAsync(string searchTerm)
    {
        try
        {
            var tickets = await _context.CustomerServiceTickets
                .Include(t => t.Player)
                .Include(t => t.AssignedToUser)
                .Include(t => t.Messages)
                .Where(t => t.Title.Contains(searchTerm) || 
                           t.Description.Contains(searchTerm) ||
                           t.TicketId.Contains(searchTerm) ||
                           t.PlayerAccountId.Contains(searchTerm) ||
                           t.Player.Nickname.Contains(searchTerm))
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();

            return tickets.Select(MapToCustomerServiceTicketDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching tickets with term {SearchTerm}", searchTerm);
            throw;
        }
    }

    public async Task<CustomerServiceTicketDto> CreateTicketAsync(CreateCustomerServiceTicketDto createTicketDto)
    {
        try
        {
            var ticket = new CustomerServiceTicket
            {
                TicketId = GenerateTicketId(),
                PlayerId = createTicketDto.PlayerId,
                PlayerAccountId = createTicketDto.PlayerAccountId,
                Title = createTicketDto.Title,
                Description = createTicketDto.Description,
                Status = TicketStatus.Open,
                Priority = createTicketDto.Priority,
                Category = createTicketDto.Category,
                AssignedToUserId = createTicketDto.AssignedToUserId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.CustomerServiceTickets.Add(ticket);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Created ticket {TicketId} for player {PlayerId}", ticket.TicketId, createTicketDto.PlayerId);

            // Return mapped DTO directly
            return MapToCustomerServiceTicketDto(ticket);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating ticket for player {PlayerId}", createTicketDto.PlayerId);
            throw;
        }
    }

    public async Task<CustomerServiceTicketDto> UpdateTicketAsync(int id, UpdateCustomerServiceTicketDto updateTicketDto)
    {
        try
        {
            var ticket = await _context.CustomerServiceTickets.FindAsync(id);
            if (ticket == null)
                throw new ArgumentException($"Ticket with ID {id} not found");

            if (!string.IsNullOrEmpty(updateTicketDto.Title))
                ticket.Title = updateTicketDto.Title;
            
            if (!string.IsNullOrEmpty(updateTicketDto.Description))
                ticket.Description = updateTicketDto.Description;
            
            if (updateTicketDto.Status.HasValue)
                ticket.Status = updateTicketDto.Status.Value;
            
            if (updateTicketDto.Priority.HasValue)
                ticket.Priority = updateTicketDto.Priority.Value;
            
            if (updateTicketDto.Category.HasValue)
                ticket.Category = updateTicketDto.Category.Value;
            
            if (updateTicketDto.AssignedToUserId.HasValue)
                ticket.AssignedToUserId = updateTicketDto.AssignedToUserId.Value;
            
            if (!string.IsNullOrEmpty(updateTicketDto.Resolution))
                ticket.Resolution = updateTicketDto.Resolution;

            ticket.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            var updatedTicket = await GetTicketByIdAsync(id);
            
            _logger.LogInformation("Updated ticket {Id}", id);
            
            return updatedTicket!;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating ticket {Id}", id);
            throw;
        }
    }

    private string GenerateTicketId()
    {
        return $"TK{DateTime.UtcNow:yyyyMMdd}{Random.Shared.Next(1000, 9999)}";
    }

    private CustomerServiceTicketDto MapToCustomerServiceTicketDto(CustomerServiceTicket ticket)
    {
        return new CustomerServiceTicketDto
        {
            Id = ticket.Id,
            TicketId = ticket.TicketId,
            PlayerId = ticket.PlayerId,
            PlayerAccountId = ticket.PlayerAccountId,
            PlayerNickname = ticket.Player?.Nickname ?? "Unknown",
            Title = ticket.Title,
            Description = ticket.Description,
            Status = ticket.Status,
            Priority = ticket.Priority,
            Category = ticket.Category,
            AssignedToUserId = ticket.AssignedToUserId,
            AssignedToUserName = ticket.AssignedToUser?.DisplayName ?? ticket.AssignedToUser?.Username,
            FirstResponseAt = ticket.FirstResponseAt,
            ResolvedAt = ticket.ResolvedAt,
            ClosedAt = ticket.ClosedAt,
            Resolution = ticket.Resolution,
            SatisfactionRating = ticket.SatisfactionRating,
            SatisfactionFeedback = ticket.SatisfactionFeedback,
            CreatedAt = ticket.CreatedAt,
            UpdatedAt = ticket.UpdatedAt ?? ticket.CreatedAt,
            MessageCount = ticket.Messages?.Count ?? 0,
            HasUnreadMessages = false, // TODO: Implement unread message logic
            Messages = ticket.Messages?.Select(MapToTicketMessageDto).ToList() ?? new List<TicketMessageDto>()
        };
    }

    public async Task<bool> AssignTicketAsync(int ticketId, int userId)
    {
        try
        {
            var ticket = await _context.CustomerServiceTickets.FindAsync(ticketId);
            if (ticket == null)
                return false;

            ticket.AssignedToUserId = userId;
            ticket.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Assigned ticket {TicketId} to user {UserId}", ticketId, userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning ticket {TicketId} to user {UserId}", ticketId, userId);
            return false;
        }
    }

    public async Task<bool> UnassignTicketAsync(int ticketId)
    {
        try
        {
            var ticket = await _context.CustomerServiceTickets.FindAsync(ticketId);
            if (ticket == null)
                return false;

            ticket.AssignedToUserId = null;
            ticket.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Unassigned ticket {TicketId}", ticketId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unassigning ticket {TicketId}", ticketId);
            return false;
        }
    }

    public async Task<bool> ChangeTicketStatusAsync(int ticketId, TicketStatus status)
    {
        try
        {
            var ticket = await _context.CustomerServiceTickets.FindAsync(ticketId);
            if (ticket == null)
                return false;

            var oldStatus = ticket.Status;
            ticket.Status = status;
            ticket.UpdatedAt = DateTime.UtcNow;

            // Set timestamps based on status changes
            if (status == TicketStatus.Resolved && oldStatus != TicketStatus.Resolved)
                ticket.ResolvedAt = DateTime.UtcNow;
            else if (status == TicketStatus.Closed && oldStatus != TicketStatus.Closed)
                ticket.ClosedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Changed ticket {TicketId} status from {OldStatus} to {NewStatus}", ticketId, oldStatus, status);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing ticket {TicketId} status to {Status}", ticketId, status);
            return false;
        }
    }

    public async Task<bool> ChangeTicketPriorityAsync(int ticketId, TicketPriority priority)
    {
        try
        {
            var ticket = await _context.CustomerServiceTickets.FindAsync(ticketId);
            if (ticket == null)
                return false;

            ticket.Priority = priority;
            ticket.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Changed ticket {TicketId} priority to {Priority}", ticketId, priority);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing ticket {TicketId} priority to {Priority}", ticketId, priority);
            return false;
        }
    }

    public async Task<bool> ResolveTicketAsync(int ticketId, string resolution)
    {
        try
        {
            var ticket = await _context.CustomerServiceTickets.FindAsync(ticketId);
            if (ticket == null)
                return false;

            ticket.Status = TicketStatus.Resolved;
            ticket.Resolution = resolution;
            ticket.ResolvedAt = DateTime.UtcNow;
            ticket.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Resolved ticket {TicketId}", ticketId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving ticket {TicketId}", ticketId);
            return false;
        }
    }

    public async Task<bool> CloseTicketAsync(int ticketId)
    {
        try
        {
            var ticket = await _context.CustomerServiceTickets.FindAsync(ticketId);
            if (ticket == null)
                return false;

            ticket.Status = TicketStatus.Closed;
            ticket.ClosedAt = DateTime.UtcNow;
            ticket.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Closed ticket {TicketId}", ticketId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error closing ticket {TicketId}", ticketId);
            return false;
        }
    }

    public async Task<bool> ReopenTicketAsync(int ticketId)
    {
        try
        {
            var ticket = await _context.CustomerServiceTickets.FindAsync(ticketId);
            if (ticket == null)
                return false;

            ticket.Status = TicketStatus.Open;
            ticket.ResolvedAt = null;
            ticket.ClosedAt = null;
            ticket.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Reopened ticket {TicketId}", ticketId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reopening ticket {TicketId}", ticketId);
            return false;
        }
    }

    public async Task<CustomerServiceStatsDto> GetCustomerServiceStatsAsync()
    {
        try
        {
            var totalTickets = await _context.CustomerServiceTickets.CountAsync();
            var openTickets = await _context.CustomerServiceTickets.CountAsync(t => t.Status == TicketStatus.Open);
            var inProgressTickets = await _context.CustomerServiceTickets.CountAsync(t => t.Status == TicketStatus.InProgress);
            var resolvedTickets = await _context.CustomerServiceTickets.CountAsync(t => t.Status == TicketStatus.Resolved);
            var closedTickets = await _context.CustomerServiceTickets.CountAsync(t => t.Status == TicketStatus.Closed);
            var criticalTickets = await _context.CustomerServiceTickets.CountAsync(t => t.Priority == TicketPriority.Critical);
            var highPriorityTickets = await _context.CustomerServiceTickets.CountAsync(t => t.Priority == TicketPriority.High);
            var unassignedTickets = await _context.CustomerServiceTickets.CountAsync(t => t.AssignedToUserId == null);

            // Calculate average response and resolution times
            var ticketsWithFirstResponse = await _context.CustomerServiceTickets
                .Where(t => t.FirstResponseAt.HasValue)
                .ToListAsync();

            var averageResponseTime = ticketsWithFirstResponse.Any()
                ? ticketsWithFirstResponse.Average(t => (t.FirstResponseAt!.Value - t.CreatedAt).TotalHours)
                : 0.0;

            var resolvedTicketsWithTime = await _context.CustomerServiceTickets
                .Where(t => t.ResolvedAt.HasValue)
                .ToListAsync();

            var averageResolutionTime = resolvedTicketsWithTime.Any()
                ? resolvedTicketsWithTime.Average(t => (t.ResolvedAt!.Value - t.CreatedAt).TotalHours)
                : 0.0;

            // Calculate satisfaction score
            var ticketsWithRating = await _context.CustomerServiceTickets
                .Where(t => t.SatisfactionRating.HasValue)
                .ToListAsync();

            var satisfactionScore = ticketsWithRating.Any()
                ? ticketsWithRating.Average(t => t.SatisfactionRating!.Value)
                : 0.0;

            // Get statistics by status, priority, and category (using ToListAsync for In-Memory DB compatibility)
            var allTickets = await _context.CustomerServiceTickets.ToListAsync();

            var ticketsByStatus = allTickets
                .GroupBy(t => t.Status)
                .ToDictionary(g => g.Key.ToString(), g => g.Count());

            var ticketsByPriority = allTickets
                .GroupBy(t => t.Priority)
                .ToDictionary(g => g.Key.ToString(), g => g.Count());

            var ticketsByCategory = allTickets
                .GroupBy(t => t.Category)
                .ToDictionary(g => g.Key.ToString(), g => g.Count());

            // Simplified agent performance (to avoid complex LINQ issues with In-Memory DB)
            var agentPerformance = new List<AgentPerformanceDto>();

            return new CustomerServiceStatsDto
            {
                TotalTickets = totalTickets,
                OpenTickets = openTickets,
                InProgressTickets = inProgressTickets,
                ResolvedTickets = resolvedTickets,
                ClosedTickets = closedTickets,
                CriticalTickets = criticalTickets,
                HighPriorityTickets = highPriorityTickets,
                UnassignedTickets = unassignedTickets,
                AverageResponseTime = averageResponseTime,
                AverageResolutionTime = averageResolutionTime,
                SatisfactionScore = satisfactionScore,
                TotalRatings = ticketsWithRating.Count,
                TicketsByStatus = ticketsByStatus,
                TicketsByPriority = ticketsByPriority,
                TicketsByCategory = ticketsByCategory,
                AgentPerformance = agentPerformance
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving customer service stats");
            throw;
        }
    }

    // Message Management
    public async Task<IEnumerable<TicketMessageDto>> GetTicketMessagesAsync(int ticketId)
    {
        try
        {
            var messages = await _context.TicketMessages
                .Include(m => m.User)
                .Where(m => m.TicketId == ticketId)
                .OrderBy(m => m.CreatedAt)
                .ToListAsync();

            return messages.Select(MapToTicketMessageDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving messages for ticket {TicketId}", ticketId);
            throw;
        }
    }

    public async Task<TicketMessageDto> AddTicketMessageAsync(int ticketId, CreateTicketMessageDto createMessageDto)
    {
        try
        {
            var ticket = await _context.CustomerServiceTickets.FindAsync(ticketId);
            if (ticket == null)
                throw new ArgumentException($"Ticket with ID {ticketId} not found");

            var message = new TicketMessage
            {
                TicketId = ticketId,
                UserId = createMessageDto.UserId,
                SenderName = createMessageDto.SenderName,
                Message = createMessageDto.Message,
                IsFromPlayer = createMessageDto.IsFromPlayer,
                IsInternal = createMessageDto.IsInternal,
                Attachments = createMessageDto.Attachments,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.TicketMessages.Add(message);

            // Update ticket's first response time if this is the first response from staff
            if (!createMessageDto.IsFromPlayer && !ticket.FirstResponseAt.HasValue)
            {
                ticket.FirstResponseAt = DateTime.UtcNow;
            }

            // Update ticket status to InProgress if it was Open
            if (ticket.Status == TicketStatus.Open && !createMessageDto.IsFromPlayer)
            {
                ticket.Status = TicketStatus.InProgress;
            }

            ticket.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            var createdMessage = await _context.TicketMessages
                .Include(m => m.User)
                .FirstAsync(m => m.Id == message.Id);

            _logger.LogInformation("Added message to ticket {TicketId}", ticketId);

            return MapToTicketMessageDto(createdMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding message to ticket {TicketId}", ticketId);
            throw;
        }
    }

    public async Task<TicketMessageDto> AddInternalNoteAsync(int ticketId, CreateTicketMessageDto createMessageDto)
    {
        try
        {
            createMessageDto.IsInternal = true;
            createMessageDto.IsFromPlayer = false;

            return await AddTicketMessageAsync(ticketId, createMessageDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding internal note to ticket {TicketId}", ticketId);
            throw;
        }
    }

    public async Task<bool> DeleteTicketMessageAsync(int messageId)
    {
        try
        {
            var message = await _context.TicketMessages.FindAsync(messageId);
            if (message == null)
                return false;

            _context.TicketMessages.Remove(message);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Deleted message {MessageId}", messageId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting message {MessageId}", messageId);
            return false;
        }
    }

    // Customer Dialogue
    public async Task<IEnumerable<CustomerServiceTicketDto>> GetActiveDialoguesAsync(int userId)
    {
        try
        {
            var tickets = await _context.CustomerServiceTickets
                .Include(t => t.Player)
                .Include(t => t.AssignedToUser)
                .Include(t => t.Messages)
                .Where(t => t.AssignedToUserId == userId &&
                           (t.Status == TicketStatus.Open || t.Status == TicketStatus.InProgress))
                .OrderByDescending(t => t.UpdatedAt)
                .ToListAsync();

            return tickets.Select(MapToCustomerServiceTicketDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active dialogues for user {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> StartDialogueAsync(int ticketId, int userId)
    {
        try
        {
            var ticket = await _context.CustomerServiceTickets.FindAsync(ticketId);
            if (ticket == null)
                return false;

            ticket.AssignedToUserId = userId;
            ticket.Status = TicketStatus.InProgress;
            ticket.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Started dialogue for ticket {TicketId} with user {UserId}", ticketId, userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting dialogue for ticket {TicketId}", ticketId);
            return false;
        }
    }

    public async Task<bool> EndDialogueAsync(int ticketId)
    {
        try
        {
            var ticket = await _context.CustomerServiceTickets.FindAsync(ticketId);
            if (ticket == null)
                return false;

            ticket.Status = TicketStatus.Resolved;
            ticket.ResolvedAt = DateTime.UtcNow;
            ticket.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Ended dialogue for ticket {TicketId}", ticketId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ending dialogue for ticket {TicketId}", ticketId);
            return false;
        }
    }

    public async Task<CustomerDialogueStatsDto> GetDialogueStatsAsync(int userId)
    {
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
                throw new ArgumentException($"User with ID {userId} not found");

            var activeDialogues = await _context.CustomerServiceTickets
                .CountAsync(t => t.AssignedToUserId == userId &&
                               (t.Status == TicketStatus.Open || t.Status == TicketStatus.InProgress));

            var totalDialogues = await _context.CustomerServiceTickets
                .CountAsync(t => t.AssignedToUserId == userId);

            var messagesExchanged = await _context.TicketMessages
                .Where(m => m.UserId == userId)
                .CountAsync();

            var userTickets = await _context.CustomerServiceTickets
                .Include(t => t.Messages)
                .Where(t => t.AssignedToUserId == userId && t.FirstResponseAt.HasValue)
                .ToListAsync();

            var averageResponseTime = userTickets.Any()
                ? userTickets.Average(t => (t.FirstResponseAt!.Value - t.CreatedAt).TotalHours)
                : 0.0;

            var lastActivity = await _context.TicketMessages
                .Where(m => m.UserId == userId)
                .OrderByDescending(m => m.CreatedAt)
                .Select(m => (DateTime?)m.CreatedAt)
                .FirstOrDefaultAsync();

            return new CustomerDialogueStatsDto
            {
                UserId = userId,
                UserName = user.DisplayName ?? user.Username,
                ActiveDialogues = activeDialogues,
                TotalDialogues = totalDialogues,
                MessagesExchanged = messagesExchanged,
                AverageResponseTime = averageResponseTime,
                LastActivity = lastActivity
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving dialogue stats for user {UserId}", userId);
            throw;
        }
    }

    // Satisfaction & Feedback
    public async Task<bool> SubmitSatisfactionRatingAsync(int ticketId, int rating, string? feedback)
    {
        try
        {
            if (rating < 1 || rating > 5)
                throw new ArgumentException("Rating must be between 1 and 5");

            var ticket = await _context.CustomerServiceTickets.FindAsync(ticketId);
            if (ticket == null)
                return false;

            ticket.SatisfactionRating = rating;
            ticket.SatisfactionFeedback = feedback;
            ticket.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Submitted satisfaction rating {Rating} for ticket {TicketId}", rating, ticketId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting satisfaction rating for ticket {TicketId}", ticketId);
            return false;
        }
    }

    public async Task<SatisfactionStatsDto> GetSatisfactionStatsAsync()
    {
        try
        {
            var ticketsWithRating = await _context.CustomerServiceTickets
                .Where(t => t.SatisfactionRating.HasValue)
                .ToListAsync();

            var overallScore = ticketsWithRating.Any()
                ? ticketsWithRating.Average(t => t.SatisfactionRating!.Value)
                : 0.0;

            var ratingDistribution = ticketsWithRating
                .GroupBy(t => t.SatisfactionRating!.Value)
                .ToDictionary(g => g.Key, g => g.Count());

            var scoreByCategory = ticketsWithRating
                .GroupBy(t => t.Category)
                .ToDictionary(g => g.Key.ToString(), g => g.Average(t => t.SatisfactionRating!.Value));

            var scoreByAgent = await _context.CustomerServiceTickets
                .Where(t => t.SatisfactionRating.HasValue && t.AssignedToUserId.HasValue)
                .Include(t => t.AssignedToUser)
                .GroupBy(t => new { t.AssignedToUserId, t.AssignedToUser!.Username, t.AssignedToUser.DisplayName })
                .ToDictionaryAsync(
                    g => g.Key.DisplayName ?? g.Key.Username,
                    g => g.Average(t => t.SatisfactionRating!.Value)
                );

            var recentFeedback = ticketsWithRating
                .Where(t => !string.IsNullOrEmpty(t.SatisfactionFeedback))
                .OrderByDescending(t => t.UpdatedAt)
                .Take(10)
                .Select(t => t.SatisfactionFeedback!)
                .ToList();

            return new SatisfactionStatsDto
            {
                OverallScore = overallScore,
                TotalRatings = ticketsWithRating.Count,
                RatingDistribution = ratingDistribution,
                ScoreByCategory = scoreByCategory,
                ScoreByAgent = scoreByAgent,
                RecentFeedback = recentFeedback
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving satisfaction stats");
            throw;
        }
    }

    private TicketMessageDto MapToTicketMessageDto(TicketMessage message)
    {
        return new TicketMessageDto
        {
            Id = message.Id,
            TicketId = message.TicketId,
            UserId = message.UserId,
            SenderName = message.SenderName,
            Message = message.Message,
            IsFromPlayer = message.IsFromPlayer,
            IsInternal = message.IsInternal,
            Attachments = message.Attachments,
            CreatedAt = message.CreatedAt,
            UpdatedAt = message.UpdatedAt ?? message.CreatedAt
        };
    }
}
