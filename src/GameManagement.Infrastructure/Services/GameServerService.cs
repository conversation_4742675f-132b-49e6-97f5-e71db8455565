using GameManagement.Core.Entities;
using GameManagement.Core.Interfaces;
using GameManagement.Infrastructure.Data;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using LogLevel = GameManagement.Shared.Enums.LogLevel;

namespace GameManagement.Infrastructure.Services;

public class GameServerService : IGameServerService
{
    private readonly GameManagementDbContext _context;
    private readonly ILogger<GameServerService> _logger;

    public GameServerService(GameManagementDbContext context, ILogger<GameServerService> logger)
    {
        _context = context;
        _logger = logger;
    }

    // 基础CRUD操作
    public async Task<IEnumerable<ServerStatusDto>> GetAllServersAsync()
    {
        try
        {
            var servers = await _context.GameServers
                .Include(s => s.Players)
                .OrderBy(s => s.Name)
                .ToListAsync();

            return servers.Select(MapToServerStatusDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all servers");
            throw;
        }
    }

    public async Task<ServerStatusDto?> GetServerByIdAsync(int serverId)
    {
        try
        {
            var server = await _context.GameServers
                .Include(s => s.Players)
                .FirstOrDefaultAsync(s => s.Id == serverId);

            return server != null ? MapToServerStatusDto(server) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server by ID {ServerId}", serverId);
            throw;
        }
    }

    public async Task<ServerStatusDto?> GetServerByNameAsync(string name)
    {
        try
        {
            var server = await _context.GameServers
                .Include(s => s.Players)
                .FirstOrDefaultAsync(s => s.Name == name);

            return server != null ? MapToServerStatusDto(server) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server by name {ServerName}", name);
            throw;
        }
    }

    public async Task<ServerStatusDto> CreateServerAsync(CreateServerDto createServerDto)
    {
        try
        {
            var server = new GameServer
            {
                Name = createServerDto.Name,
                Host = createServerDto.Host,
                Port = createServerDto.Port,
                Status = createServerDto.Status,
                Version = createServerDto.Version,
                MaxPlayers = createServerDto.MaxPlayers,
                CurrentPlayers = 0,
                Region = createServerDto.Region,
                CreatedAt = DateTime.UtcNow
            };

            _context.GameServers.Add(server);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Created new server: {ServerName}", server.Name);

            return MapToServerStatusDto(server);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating server");
            throw;
        }
    }

    public async Task<ServerStatusDto?> UpdateServerAsync(int serverId, UpdateServerDto updateServerDto)
    {
        try
        {
            var server = await _context.GameServers.FindAsync(serverId);
            if (server == null)
                return null;

            // 更新非空字段
            if (!string.IsNullOrEmpty(updateServerDto.Name))
                server.Name = updateServerDto.Name;
            
            if (!string.IsNullOrEmpty(updateServerDto.Host))
                server.Host = updateServerDto.Host;
            
            if (updateServerDto.Port.HasValue)
                server.Port = updateServerDto.Port.Value;
            
            if (updateServerDto.Status.HasValue)
                server.Status = updateServerDto.Status.Value;
            
            if (!string.IsNullOrEmpty(updateServerDto.Version))
                server.Version = updateServerDto.Version;
            
            if (updateServerDto.MaxPlayers.HasValue)
                server.MaxPlayers = updateServerDto.MaxPlayers.Value;
            
            if (updateServerDto.CurrentPlayers.HasValue)
                server.CurrentPlayers = updateServerDto.CurrentPlayers.Value;
            
            if (!string.IsNullOrEmpty(updateServerDto.Region))
                server.Region = updateServerDto.Region;

            server.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated server: {ServerName}", server.Name);

            return MapToServerStatusDto(server);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating server {ServerId}", serverId);
            throw;
        }
    }

    public async Task<bool> DeleteServerAsync(int serverId)
    {
        try
        {
            var server = await _context.GameServers.FindAsync(serverId);
            if (server == null)
                return false;

            // 检查是否有关联的玩家
            var hasPlayers = await _context.Players.AnyAsync(p => p.ServerId == serverId);
            if (hasPlayers)
            {
                _logger.LogWarning("Cannot delete server {ServerId} - has associated players", serverId);
                return false;
            }

            _context.GameServers.Remove(server);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Deleted server: {ServerName}", server.Name);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting server {ServerId}", serverId);
            throw;
        }
    }

    // 服务器状态管理
    public async Task<bool> UpdateServerStatusAsync(int serverId, ServerStatus status)
    {
        try
        {
            var server = await _context.GameServers.FindAsync(serverId);
            if (server == null)
                return false;

            server.Status = status;
            server.LastHeartbeat = DateTime.UtcNow;
            server.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated server {ServerId} status to {Status}", serverId, status);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating server status {ServerId}", serverId);
            throw;
        }
    }

    public async Task<bool> UpdatePlayerCountAsync(int serverId, int currentPlayers)
    {
        try
        {
            var server = await _context.GameServers.FindAsync(serverId);
            if (server == null)
                return false;

            server.CurrentPlayers = currentPlayers;
            server.LastHeartbeat = DateTime.UtcNow;
            server.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating player count for server {ServerId}", serverId);
            throw;
        }
    }

    public async Task<IEnumerable<ServerStatusDto>> GetServersByStatusAsync(ServerStatus status)
    {
        try
        {
            var servers = await _context.GameServers
                .Include(s => s.Players)
                .Where(s => s.Status == status)
                .OrderBy(s => s.Name)
                .ToListAsync();

            return servers.Select(MapToServerStatusDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting servers by status {Status}", status);
            throw;
        }
    }

    public async Task<IEnumerable<ServerStatusDto>> GetOnlineServersAsync()
    {
        return await GetServersByStatusAsync(ServerStatus.Online);
    }

    // 服务器操作
    public async Task<bool> RestartServerAsync(int serverId)
    {
        try
        {
            var server = await _context.GameServers.FindAsync(serverId);
            if (server == null)
                return false;

            // 记录重启操作
            var logEntry = new ServerLog
            {
                ServerId = serverId,
                Level = LogLevel.Info,
                Message = "Server restart initiated",
                Source = "GameServerService",
                Timestamp = DateTime.UtcNow
            };

            _context.ServerLogs.Add(logEntry);

            // 更新服务器状态
            server.Status = ServerStatus.Maintenance;
            server.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Initiated restart for server {ServerId}", serverId);

            // 这里应该调用实际的服务器重启API
            // await CallServerRestartAPI(server);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error restarting server {ServerId}", serverId);
            throw;
        }
    }

    public async Task<bool> StartServerAsync(int serverId)
    {
        try
        {
            var server = await _context.GameServers.FindAsync(serverId);
            if (server == null)
                return false;

            server.Status = ServerStatus.Online;
            server.LastHeartbeat = DateTime.UtcNow;
            server.UpdatedAt = DateTime.UtcNow;

            var logEntry = new ServerLog
            {
                ServerId = serverId,
                Level = LogLevel.Info,
                Message = "Server started",
                Source = "GameServerService",
                Timestamp = DateTime.UtcNow
            };

            _context.ServerLogs.Add(logEntry);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Started server {ServerId}", serverId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting server {ServerId}", serverId);
            throw;
        }
    }

    public async Task<bool> StopServerAsync(int serverId)
    {
        try
        {
            var server = await _context.GameServers.FindAsync(serverId);
            if (server == null)
                return false;

            server.Status = ServerStatus.Offline;
            server.CurrentPlayers = 0;
            server.UpdatedAt = DateTime.UtcNow;

            var logEntry = new ServerLog
            {
                ServerId = serverId,
                Level = LogLevel.Info,
                Message = "Server stopped",
                Source = "GameServerService",
                Timestamp = DateTime.UtcNow
            };

            _context.ServerLogs.Add(logEntry);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Stopped server {ServerId}", serverId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping server {ServerId}", serverId);
            throw;
        }
    }

    public async Task<bool> SendServerCommandAsync(int serverId, string command)
    {
        try
        {
            var server = await _context.GameServers.FindAsync(serverId);
            if (server == null)
                return false;

            var logEntry = new ServerLog
            {
                ServerId = serverId,
                Level = LogLevel.Info,
                Message = $"Command executed: {command}",
                Source = "GameServerService",
                Timestamp = DateTime.UtcNow
            };

            _context.ServerLogs.Add(logEntry);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Sent command to server {ServerId}: {Command}", serverId, command);

            // 这里应该调用实际的服务器命令API
            // await CallServerCommandAPI(server, command);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending command to server {ServerId}", serverId);
            throw;
        }
    }

    // 服务器统计
    public async Task<ServerStatsDto> GetServerStatsAsync(int serverId)
    {
        try
        {
            var server = await _context.GameServers
                .Include(s => s.Players)
                .FirstOrDefaultAsync(s => s.Id == serverId);

            if (server == null)
                throw new ArgumentException($"Server {serverId} not found");

            var today = DateTime.UtcNow.Date;
            var totalPlayers = server.Players.Count;
            var activePlayers = server.Players.Count(p => p.LastLoginAt >= today.AddDays(-7));
            var onlinePlayers = server.CurrentPlayers;

            // 计算收入统计
            var totalRevenue = await _context.PaymentRecords
                .Where(p => p.Player.ServerId == serverId && p.Status == PaymentStatus.Completed)
                .SumAsync(p => p.Amount);

            var todayRevenue = await _context.PaymentRecords
                .Where(p => p.Player.ServerId == serverId &&
                           p.Status == PaymentStatus.Completed &&
                           p.CompletedAt >= today)
                .SumAsync(p => p.Amount);

            var newPlayersToday = server.Players.Count(p => p.CreatedAt >= today);

            // 计算平均游戏时长
            var averagePlayTime = server.Players.Any()
                ? server.Players.Average(p => p.TotalPlayTime.TotalHours)
                : 0;

            return new ServerStatsDto
            {
                ServerId = serverId,
                ServerName = server.Name,
                TotalPlayers = totalPlayers,
                ActivePlayers = activePlayers,
                OnlinePlayers = onlinePlayers,
                TotalRevenue = totalRevenue,
                TodayRevenue = todayRevenue,
                NewPlayersToday = newPlayersToday,
                AveragePlayTime = averagePlayTime,
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server stats for {ServerId}", serverId);
            throw;
        }
    }

    public async Task<IEnumerable<ServerStatsDto>> GetAllServerStatsAsync()
    {
        try
        {
            var servers = await _context.GameServers.ToListAsync();
            var stats = new List<ServerStatsDto>();

            foreach (var server in servers)
            {
                var serverStats = await GetServerStatsAsync(server.Id);
                stats.Add(serverStats);
            }

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all server stats");
            throw;
        }
    }

    public async Task<ServerPerformanceDto> GetServerPerformanceAsync(int serverId, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var server = await _context.GameServers.FindAsync(serverId);
            if (server == null)
                throw new ArgumentException($"Server {serverId} not found");

            var start = startDate ?? DateTime.UtcNow.AddDays(-7);
            var end = endDate ?? DateTime.UtcNow;

            var monitoringData = await _context.ServerMonitorings
                .Where(m => m.ServerId == serverId && m.Timestamp >= start && m.Timestamp <= end)
                .OrderBy(m => m.Timestamp)
                .ToListAsync();

            var performance = new ServerPerformanceDto
            {
                ServerId = serverId,
                ServerName = server.Name,
                StartDate = start,
                EndDate = end
            };

            if (monitoringData.Any())
            {
                performance.AverageCpuUsage = monitoringData.Average(m => m.CpuUsage);
                performance.AverageMemoryUsage = monitoringData.Average(m => m.MemoryUsage);
                performance.AverageDiskUsage = monitoringData.Average(m => m.DiskUsage);
                performance.AverageNetworkIn = monitoringData.Average(m => m.NetworkIn);
                performance.AverageNetworkOut = monitoringData.Average(m => m.NetworkOut);
                performance.AverageActiveConnections = (int)monitoringData.Average(m => m.ActiveConnections);
                performance.AverageResponseTime = monitoringData.Average(m => m.ResponseTime);

                var healthyCount = monitoringData.Count(m => m.IsHealthy);
                performance.UptimePercentage = (double)healthyCount / monitoringData.Count * 100;

                performance.DataPoints = monitoringData.Select(m => new ServerPerformanceDataPoint
                {
                    Timestamp = m.Timestamp,
                    CpuUsage = m.CpuUsage,
                    MemoryUsage = m.MemoryUsage,
                    DiskUsage = m.DiskUsage,
                    NetworkIn = m.NetworkIn,
                    NetworkOut = m.NetworkOut,
                    ActiveConnections = m.ActiveConnections,
                    ResponseTime = m.ResponseTime
                }).ToList();
            }

            return performance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server performance for {ServerId}", serverId);
            throw;
        }
    }

    // 服务器日志
    public async Task<IEnumerable<ServerLogDto>> GetServerLogsAsync(int serverId, int page = 1, int pageSize = 50, LogLevel? level = null)
    {
        try
        {
            var query = _context.ServerLogs
                .Where(l => l.ServerId == serverId);

            if (level.HasValue)
                query = query.Where(l => l.Level == level.Value);

            var logs = await query
                .OrderByDescending(l => l.Timestamp)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return logs.Select(l => new ServerLogDto
            {
                Id = l.Id,
                ServerId = l.ServerId,
                Level = l.Level,
                Message = l.Message,
                Exception = l.Exception,
                Source = l.Source,
                Timestamp = l.Timestamp
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server logs for {ServerId}", serverId);
            throw;
        }
    }

    public async Task<ServerLogDto> AddServerLogAsync(CreateServerLogDto createLogDto)
    {
        try
        {
            var log = new ServerLog
            {
                ServerId = createLogDto.ServerId,
                Level = createLogDto.Level,
                Message = createLogDto.Message,
                Exception = createLogDto.Exception,
                Source = createLogDto.Source,
                Timestamp = createLogDto.Timestamp ?? DateTime.UtcNow
            };

            _context.ServerLogs.Add(log);
            await _context.SaveChangesAsync();

            return new ServerLogDto
            {
                Id = log.Id,
                ServerId = log.ServerId,
                Level = log.Level,
                Message = log.Message,
                Exception = log.Exception,
                Source = log.Source,
                Timestamp = log.Timestamp
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding server log");
            throw;
        }
    }

    // 服务器维护
    public async Task<IEnumerable<ServerMaintenanceDto>> GetServerMaintenancesAsync(int serverId)
    {
        try
        {
            var maintenances = await _context.ServerMaintenances
                .Include(m => m.Server)
                .Where(m => m.ServerId == serverId)
                .OrderByDescending(m => m.StartTime)
                .ToListAsync();

            return maintenances.Select(m => new ServerMaintenanceDto
            {
                Id = m.Id,
                ServerId = m.ServerId,
                ServerName = m.Server.Name,
                Title = m.Title,
                Description = m.Description,
                StartTime = m.StartTime,
                EndTime = m.EndTime,
                Type = m.Type,
                Status = m.Status,
                NotifyPlayers = m.NotifyPlayers,
                NotificationMessage = m.NotificationMessage,
                EstimatedDurationMinutes = m.EstimatedDurationMinutes,
                CreatedAt = m.CreatedAt
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server maintenances for {ServerId}", serverId);
            throw;
        }
    }

    public async Task<ServerMaintenanceDto> ScheduleMaintenanceAsync(CreateServerMaintenanceDto createMaintenanceDto)
    {
        try
        {
            var server = await _context.GameServers.FindAsync(createMaintenanceDto.ServerId);
            if (server == null)
                throw new ArgumentException($"Server {createMaintenanceDto.ServerId} not found");

            var maintenance = new ServerMaintenance
            {
                ServerId = createMaintenanceDto.ServerId,
                Title = createMaintenanceDto.Title,
                Description = createMaintenanceDto.Description,
                StartTime = createMaintenanceDto.StartTime,
                EndTime = createMaintenanceDto.EndTime,
                Type = createMaintenanceDto.Type,
                Status = MaintenanceStatus.Scheduled,
                NotifyPlayers = createMaintenanceDto.NotifyPlayers,
                NotificationMessage = createMaintenanceDto.NotificationMessage,
                EstimatedDurationMinutes = createMaintenanceDto.EstimatedDurationMinutes,
                CreatedAt = DateTime.UtcNow
            };

            _context.ServerMaintenances.Add(maintenance);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Scheduled maintenance for server {ServerId}: {Title}",
                createMaintenanceDto.ServerId, createMaintenanceDto.Title);

            return new ServerMaintenanceDto
            {
                Id = maintenance.Id,
                ServerId = maintenance.ServerId,
                ServerName = server.Name,
                Title = maintenance.Title,
                Description = maintenance.Description,
                StartTime = maintenance.StartTime,
                EndTime = maintenance.EndTime,
                Type = maintenance.Type,
                Status = maintenance.Status,
                NotifyPlayers = maintenance.NotifyPlayers,
                NotificationMessage = maintenance.NotificationMessage,
                EstimatedDurationMinutes = maintenance.EstimatedDurationMinutes,
                CreatedAt = maintenance.CreatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling maintenance");
            throw;
        }
    }

    public async Task<bool> StartMaintenanceAsync(int maintenanceId)
    {
        try
        {
            var maintenance = await _context.ServerMaintenances
                .Include(m => m.Server)
                .FirstOrDefaultAsync(m => m.Id == maintenanceId);

            if (maintenance == null)
                return false;

            maintenance.Status = MaintenanceStatus.InProgress;
            maintenance.Server.Status = ServerStatus.Maintenance;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Started maintenance {MaintenanceId} for server {ServerId}",
                maintenanceId, maintenance.ServerId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting maintenance {MaintenanceId}", maintenanceId);
            throw;
        }
    }

    public async Task<bool> CompleteMaintenanceAsync(int maintenanceId)
    {
        try
        {
            var maintenance = await _context.ServerMaintenances
                .Include(m => m.Server)
                .FirstOrDefaultAsync(m => m.Id == maintenanceId);

            if (maintenance == null)
                return false;

            maintenance.Status = MaintenanceStatus.Completed;
            maintenance.EndTime = DateTime.UtcNow;
            maintenance.Server.Status = ServerStatus.Online;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Completed maintenance {MaintenanceId} for server {ServerId}",
                maintenanceId, maintenance.ServerId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing maintenance {MaintenanceId}", maintenanceId);
            throw;
        }
    }

    // 辅助方法
    private static ServerStatusDto MapToServerStatusDto(GameServer server)
    {
        return new ServerStatusDto
        {
            Id = server.Id,
            Name = server.Name,
            Host = server.Host,
            Port = server.Port,
            Status = server.Status,
            Version = server.Version,
            MaxPlayers = server.MaxPlayers,
            CurrentPlayers = server.CurrentPlayers,
            LastHeartbeat = server.LastHeartbeat,
            Region = server.Region,
            CpuUsage = 0, // 这些值需要从监控系统获取
            MemoryUsage = 0,
            DiskUsage = 0
        };
    }
}
