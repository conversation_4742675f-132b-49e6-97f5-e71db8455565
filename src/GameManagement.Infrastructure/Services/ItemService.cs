using GameManagement.Core.Entities;
using GameManagement.Core.Interfaces;
using GameManagement.Infrastructure.Data;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace GameManagement.Infrastructure.Services;

public class ItemService : IItemService
{
    private readonly GameManagementDbContext _context;
    private readonly ILogger<ItemService> _logger;

    public ItemService(GameManagementDbContext context, ILogger<ItemService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<GameItemDto?> GetItemByIdAsync(int id)
    {
        try
        {
            var item = await _context.GameItems
                .FirstOrDefaultAsync(i => i.Id == id);

            return item == null ? null : MapToDto(item);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting item by ID {Id}", id);
            throw;
        }
    }

    public async Task<GameItemDto?> GetItemByItemIdAsync(string itemId)
    {
        try
        {
            var item = await _context.GameItems
                .FirstOrDefaultAsync(i => i.ItemId == itemId);

            return item == null ? null : MapToDto(item);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting item by ItemId {ItemId}", itemId);
            throw;
        }
    }

    public async Task<IEnumerable<GameItemDto>> GetItemsAsync(int page = 1, int pageSize = 50)
    {
        try
        {
            var items = await _context.GameItems
                .OrderBy(i => i.Name)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return items.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting items page {Page}, size {PageSize}", page, pageSize);
            throw;
        }
    }

    public async Task<IEnumerable<GameItemDto>> SearchItemsAsync(string searchTerm)
    {
        try
        {
            var items = await _context.GameItems
                .Where(i => i.Name.Contains(searchTerm) || 
                           i.ItemId.Contains(searchTerm) ||
                           (i.Description != null && i.Description.Contains(searchTerm)))
                .OrderBy(i => i.Name)
                .ToListAsync();

            return items.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching items with term {SearchTerm}", searchTerm);
            throw;
        }
    }

    public async Task<GameItemDto> CreateItemAsync(CreateGameItemDto createItemDto)
    {
        try
        {
            // Check if ItemId already exists
            var existingItem = await _context.GameItems
                .FirstOrDefaultAsync(i => i.ItemId == createItemDto.ItemId);
            
            if (existingItem != null)
            {
                throw new InvalidOperationException($"Item with ItemId '{createItemDto.ItemId}' already exists");
            }

            var item = new GameItem
            {
                ItemId = createItemDto.ItemId,
                Name = createItemDto.Name,
                Description = createItemDto.Description,
                Type = createItemDto.Type,
                Rarity = createItemDto.Rarity,
                Level = createItemDto.Level,
                Price = createItemDto.Price,
                Properties = createItemDto.Properties,
                IsActive = createItemDto.IsActive,
                IconUrl = createItemDto.IconUrl,
                MaxStack = createItemDto.MaxStack,
                IsTradeable = createItemDto.IsTradeable,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.GameItems.Add(item);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Created new item: {ItemId} - {Name}", item.ItemId, item.Name);

            return MapToDto(item);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating item {ItemId}", createItemDto.ItemId);
            throw;
        }
    }

    public async Task<GameItemDto> UpdateItemAsync(int id, UpdateGameItemDto updateItemDto)
    {
        try
        {
            var item = await _context.GameItems.FindAsync(id);
            if (item == null)
            {
                throw new InvalidOperationException($"Item with ID {id} not found");
            }

            // Update only provided fields
            if (!string.IsNullOrEmpty(updateItemDto.Name))
                item.Name = updateItemDto.Name;
            
            if (updateItemDto.Description != null)
                item.Description = updateItemDto.Description;
            
            if (updateItemDto.Type.HasValue)
                item.Type = updateItemDto.Type.Value;
            
            if (updateItemDto.Rarity.HasValue)
                item.Rarity = updateItemDto.Rarity.Value;
            
            if (updateItemDto.Level.HasValue)
                item.Level = updateItemDto.Level.Value;
            
            if (updateItemDto.Price.HasValue)
                item.Price = updateItemDto.Price.Value;
            
            if (updateItemDto.Properties != null)
                item.Properties = updateItemDto.Properties;
            
            if (updateItemDto.IsActive.HasValue)
                item.IsActive = updateItemDto.IsActive.Value;
            
            if (updateItemDto.IconUrl != null)
                item.IconUrl = updateItemDto.IconUrl;
            
            if (updateItemDto.MaxStack.HasValue)
                item.MaxStack = updateItemDto.MaxStack.Value;
            
            if (updateItemDto.IsTradeable.HasValue)
                item.IsTradeable = updateItemDto.IsTradeable.Value;

            item.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated item {Id}", id);

            return MapToDto(item);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating item {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeleteItemAsync(int id)
    {
        try
        {
            var item = await _context.GameItems.FindAsync(id);
            if (item == null)
            {
                return false;
            }

            _context.GameItems.Remove(item);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Deleted item {Id}", id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting item {Id}", id);
            throw;
        }
    }

    public async Task<bool> ActivateItemAsync(int id)
    {
        try
        {
            var item = await _context.GameItems.FindAsync(id);
            if (item == null)
            {
                return false;
            }

            item.IsActive = true;
            item.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Activated item {Id}", id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating item {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeactivateItemAsync(int id)
    {
        try
        {
            var item = await _context.GameItems.FindAsync(id);
            if (item == null)
            {
                return false;
            }

            item.IsActive = false;
            item.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Deactivated item {Id}", id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating item {Id}", id);
            throw;
        }
    }

    public async Task<IEnumerable<GameItemDto>> GetItemsByTypeAsync(ItemType type)
    {
        try
        {
            var items = await _context.GameItems
                .Where(i => i.Type == type)
                .OrderBy(i => i.Name)
                .ToListAsync();

            return items.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting items by type {Type}", type);
            throw;
        }
    }

    public async Task<IEnumerable<GameItemDto>> GetItemsByRarityAsync(ItemRarity rarity)
    {
        try
        {
            var items = await _context.GameItems
                .Where(i => i.Rarity == rarity)
                .OrderBy(i => i.Name)
                .ToListAsync();

            return items.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting items by rarity {Rarity}", rarity);
            throw;
        }
    }

    public async Task<IEnumerable<GameItemDto>> GetItemsByLevelRangeAsync(int minLevel, int maxLevel)
    {
        try
        {
            var items = await _context.GameItems
                .Where(i => i.Level >= minLevel && i.Level <= maxLevel)
                .OrderBy(i => i.Level)
                .ThenBy(i => i.Name)
                .ToListAsync();

            return items.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting items by level range {MinLevel}-{MaxLevel}", minLevel, maxLevel);
            throw;
        }
    }

    public async Task<IEnumerable<GameItemDto>> GetActiveItemsAsync()
    {
        try
        {
            var items = await _context.GameItems
                .Where(i => i.IsActive)
                .OrderBy(i => i.Name)
                .ToListAsync();

            return items.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active items");
            throw;
        }
    }

    public async Task<IEnumerable<GameItemDto>> GetTradeableItemsAsync()
    {
        try
        {
            var items = await _context.GameItems
                .Where(i => i.IsTradeable && i.IsActive)
                .OrderBy(i => i.Name)
                .ToListAsync();

            return items.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tradeable items");
            throw;
        }
    }

    public async Task<bool> UpdateItemPriceAsync(int id, decimal newPrice)
    {
        try
        {
            var item = await _context.GameItems.FindAsync(id);
            if (item == null)
            {
                return false;
            }

            item.Price = newPrice;
            item.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated price for item {Id} to {Price}", id, newPrice);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating price for item {Id}", id);
            throw;
        }
    }

    public async Task<bool> BatchUpdatePricesAsync(Dictionary<int, decimal> priceUpdates)
    {
        try
        {
            var itemIds = priceUpdates.Keys.ToList();
            var items = await _context.GameItems
                .Where(i => itemIds.Contains(i.Id))
                .ToListAsync();

            foreach (var item in items)
            {
                if (priceUpdates.TryGetValue(item.Id, out var newPrice))
                {
                    item.Price = newPrice;
                    item.UpdatedAt = DateTime.UtcNow;
                }
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Batch updated prices for {Count} items", items.Count);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch updating prices");
            throw;
        }
    }

    public async Task<IEnumerable<GameItemDto>> GetItemsByPriceRangeAsync(decimal minPrice, decimal maxPrice)
    {
        try
        {
            var items = await _context.GameItems
                .Where(i => i.Price >= minPrice && i.Price <= maxPrice)
                .OrderBy(i => i.Price)
                .ThenBy(i => i.Name)
                .ToListAsync();

            return items.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting items by price range {MinPrice}-{MaxPrice}", minPrice, maxPrice);
            throw;
        }
    }

    public async Task<ItemStatsDto> GetItemStatsAsync()
    {
        try
        {
            var allItems = await _context.GameItems.ToListAsync();

            var stats = new ItemStatsDto
            {
                TotalItems = allItems.Count,
                ActiveItems = allItems.Count(i => i.IsActive),
                InactiveItems = allItems.Count(i => !i.IsActive),
                ItemsByType = allItems.GroupBy(i => i.Type.ToString())
                    .ToDictionary(g => g.Key, g => g.Count()),
                ItemsByRarity = allItems.GroupBy(i => i.Rarity.ToString())
                    .ToDictionary(g => g.Key, g => g.Count()),
                ItemsByLevel = allItems.GroupBy(i => $"Level {i.Level}")
                    .ToDictionary(g => g.Key, g => g.Count()),
                AveragePrice = allItems.Any() ? allItems.Average(i => i.Price) : 0,
                TotalValue = allItems.Sum(i => i.Price),
                TradeableItems = allItems.Count(i => i.IsTradeable),
                NonTradeableItems = allItems.Count(i => !i.IsTradeable)
            };

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting item stats");
            throw;
        }
    }

    public async Task<IEnumerable<ItemUsageStatsDto>> GetItemUsageStatsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            // For now, return simulated usage stats since we don't have usage tracking tables
            var items = await _context.GameItems
                .Where(i => i.IsActive)
                .Take(20)
                .ToListAsync();

            var random = new Random();
            var usageStats = items.Select(item => new ItemUsageStatsDto
            {
                ItemId = item.Id,
                ItemName = item.Name,
                ItemIdCode = item.ItemId,
                UsageCount = random.Next(10, 1000),
                UniqueUsers = random.Next(5, 500),
                TotalValue = item.Price * random.Next(10, 100),
                LastUsed = DateTime.UtcNow.AddDays(-random.Next(0, 30)),
                PopularityScore = random.NextDouble() * 100
            }).OrderByDescending(s => s.UsageCount);

            return usageStats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting item usage stats");
            throw;
        }
    }

    public async Task<IEnumerable<GameItemDto>> GetPopularItemsAsync(int count = 10)
    {
        try
        {
            // For now, return items sorted by price (as a proxy for popularity)
            var items = await _context.GameItems
                .Where(i => i.IsActive)
                .OrderByDescending(i => i.Price)
                .Take(count)
                .ToListAsync();

            return items.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting popular items");
            throw;
        }
    }

    public async Task<IEnumerable<GameItemDto>> GetTopSellingItemsAsync(int count = 10)
    {
        try
        {
            // For now, return items sorted by rarity and price
            var items = await _context.GameItems
                .Where(i => i.IsActive)
                .OrderByDescending(i => (int)i.Rarity)
                .ThenByDescending(i => i.Price)
                .Take(count)
                .ToListAsync();

            return items.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting top selling items");
            throw;
        }
    }

    public async Task<bool> UpdateItemPropertiesAsync(int id, string properties)
    {
        try
        {
            var item = await _context.GameItems.FindAsync(id);
            if (item == null)
            {
                return false;
            }

            // Validate JSON format if properties is not null or empty
            if (!string.IsNullOrEmpty(properties))
            {
                try
                {
                    JsonDocument.Parse(properties);
                }
                catch (JsonException)
                {
                    throw new ArgumentException("Properties must be valid JSON format");
                }
            }

            item.Properties = properties;
            item.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated properties for item {Id}", id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating properties for item {Id}", id);
            throw;
        }
    }

    public async Task<IEnumerable<GameItemDto>> GetItemsByPropertyAsync(string propertyKey, string propertyValue)
    {
        try
        {
            var items = await _context.GameItems
                .Where(i => i.Properties != null && i.Properties.Contains($"\"{propertyKey}\"") && i.Properties.Contains($"\"{propertyValue}\""))
                .OrderBy(i => i.Name)
                .ToListAsync();

            return items.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting items by property {Key}={Value}", propertyKey, propertyValue);
            throw;
        }
    }

    public async Task<bool> BatchActivateItemsAsync(IEnumerable<int> itemIds)
    {
        try
        {
            var items = await _context.GameItems
                .Where(i => itemIds.Contains(i.Id))
                .ToListAsync();

            foreach (var item in items)
            {
                item.IsActive = true;
                item.UpdatedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Batch activated {Count} items", items.Count);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch activating items");
            throw;
        }
    }

    public async Task<bool> BatchDeactivateItemsAsync(IEnumerable<int> itemIds)
    {
        try
        {
            var items = await _context.GameItems
                .Where(i => itemIds.Contains(i.Id))
                .ToListAsync();

            foreach (var item in items)
            {
                item.IsActive = false;
                item.UpdatedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Batch deactivated {Count} items", items.Count);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch deactivating items");
            throw;
        }
    }

    public async Task<bool> BatchDeleteItemsAsync(IEnumerable<int> itemIds)
    {
        try
        {
            var items = await _context.GameItems
                .Where(i => itemIds.Contains(i.Id))
                .ToListAsync();

            _context.GameItems.RemoveRange(items);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Batch deleted {Count} items", items.Count);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch deleting items");
            throw;
        }
    }

    public async Task<IEnumerable<GameItemDto>> ImportItemsAsync(IEnumerable<CreateGameItemDto> items)
    {
        try
        {
            var itemList = items.ToList();
            var existingItemIds = await _context.GameItems
                .Where(i => itemList.Select(dto => dto.ItemId).Contains(i.ItemId))
                .Select(i => i.ItemId)
                .ToListAsync();

            var newItems = itemList
                .Where(dto => !existingItemIds.Contains(dto.ItemId))
                .Select(dto => new GameItem
                {
                    ItemId = dto.ItemId,
                    Name = dto.Name,
                    Description = dto.Description,
                    Type = dto.Type,
                    Rarity = dto.Rarity,
                    Level = dto.Level,
                    Price = dto.Price,
                    Properties = dto.Properties,
                    IsActive = dto.IsActive,
                    IconUrl = dto.IconUrl,
                    MaxStack = dto.MaxStack,
                    IsTradeable = dto.IsTradeable,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                })
                .ToList();

            _context.GameItems.AddRange(newItems);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Imported {Count} new items, skipped {SkippedCount} existing items",
                newItems.Count, existingItemIds.Count);

            return newItems.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing items");
            throw;
        }
    }

    public async Task<ItemPerformanceDto> GetItemPerformanceAsync(int id, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var item = await _context.GameItems.FindAsync(id);
            if (item == null)
            {
                throw new InvalidOperationException($"Item with ID {id} not found");
            }

            // Simulate performance data since we don't have sales tracking tables
            var random = new Random(id); // Use ID as seed for consistent results
            var performance = new ItemPerformanceDto
            {
                ItemId = item.Id,
                ItemName = item.Name,
                TotalSales = random.Next(50, 1000),
                TotalRevenue = item.Price * random.Next(50, 1000),
                UniqueCustomers = random.Next(20, 500),
                AverageRating = Math.Round(random.NextDouble() * 2 + 3, 1), // 3.0 to 5.0
                ReviewCount = random.Next(10, 200)
            };

            // Generate trend data for the last 30 days
            var endDateValue = endDate ?? DateTime.UtcNow;
            var startDateValue = startDate ?? endDateValue.AddDays(-30);

            for (var date = startDateValue.Date; date <= endDateValue.Date; date = date.AddDays(1))
            {
                var dailySales = random.Next(0, 20);
                var dailyRevenue = item.Price * dailySales;

                performance.SalesTrend[date] = dailySales;
                performance.RevenueTrend[date] = dailyRevenue;
            }

            return performance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting item performance for item {Id}", id);
            throw;
        }
    }

    public async Task<IEnumerable<ItemTrendDto>> GetItemTrendsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var endDateValue = endDate ?? DateTime.UtcNow;
            var startDateValue = startDate ?? endDateValue.AddDays(-30);

            var items = await _context.GameItems
                .Where(i => i.IsActive)
                .Take(10)
                .ToListAsync();

            var trends = new List<ItemTrendDto>();
            var random = new Random();

            for (var date = startDateValue.Date; date <= endDateValue.Date; date = date.AddDays(1))
            {
                foreach (var item in items)
                {
                    var salesCount = random.Next(0, 50);
                    var revenue = item.Price * salesCount;
                    var newCustomers = random.Next(0, salesCount);
                    var returnCustomers = salesCount - newCustomers;

                    trends.Add(new ItemTrendDto
                    {
                        Date = date,
                        ItemId = item.Id,
                        ItemName = item.Name,
                        SalesCount = salesCount,
                        Revenue = revenue,
                        NewCustomers = newCustomers,
                        ReturnCustomers = returnCustomers,
                        TrendScore = random.NextDouble() * 100
                    });
                }
            }

            return trends.OrderByDescending(t => t.Date).ThenByDescending(t => t.TrendScore);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting item trends");
            throw;
        }
    }

    public async Task<ItemInventoryDto> GetItemInventoryAsync()
    {
        try
        {
            var items = await _context.GameItems.ToListAsync();
            var random = new Random();

            var inventory = new ItemInventoryDto
            {
                TotalItems = items.Count,
                LowStockItems = random.Next(5, 20),
                OutOfStockItems = random.Next(0, 10),
                OverstockedItems = random.Next(2, 15),
                TotalInventoryValue = items.Sum(i => i.Price * random.Next(10, 100)),
                InventoryByCategory = items.GroupBy(i => i.Type.ToString())
                    .ToDictionary(g => g.Key, g => g.Count()),
                ValueByCategory = items.GroupBy(i => i.Type.ToString())
                    .ToDictionary(g => g.Key, g => g.Sum(i => i.Price * random.Next(10, 100)))
            };

            // Generate stock alerts
            var alertItems = items.Take(5).ToList();
            var alertTypes = new[] { "LowStock", "OutOfStock", "Overstocked" };
            var severities = new[] { "Low", "Medium", "High", "Critical" };

            inventory.StockAlerts = alertItems.Select(item => new ItemStockAlertDto
            {
                ItemId = item.Id,
                ItemName = item.Name,
                AlertType = alertTypes[random.Next(alertTypes.Length)],
                CurrentStock = random.Next(0, 100),
                RecommendedStock = random.Next(50, 200),
                Severity = severities[random.Next(severities.Length)]
            }).ToList();

            return inventory;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting item inventory");
            throw;
        }
    }

    private static GameItemDto MapToDto(GameItem item)
    {
        return new GameItemDto
        {
            Id = item.Id,
            ItemId = item.ItemId,
            Name = item.Name,
            Description = item.Description,
            Type = item.Type,
            Rarity = item.Rarity,
            Level = item.Level,
            Price = item.Price,
            Properties = item.Properties,
            IsActive = item.IsActive,
            IconUrl = item.IconUrl,
            MaxStack = item.MaxStack,
            IsTradeable = item.IsTradeable,
            CreatedAt = item.CreatedAt,
            UpdatedAt = item.UpdatedAt ?? item.CreatedAt
        };
    }
}
