using GameManagement.Core.Interfaces;
using GameManagement.Shared.DTOs;
using GameManagement.Infrastructure.Data;
using GameManagement.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace GameManagement.Infrastructure.Services;

public class OperationalDataService : IOperationalDataService
{
    private readonly GameManagementDbContext _context;
    private readonly ILogger<OperationalDataService> _logger;

    public OperationalDataService(
        GameManagementDbContext context,
        ILogger<OperationalDataService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<GlobalStatsDto> GetGlobalStatsAsync()
    {
        try
        {
            var today = DateTime.UtcNow.Date;
            var yesterday = today.AddDays(-1);

            // 计算总收入
            var totalRevenue = await _context.PaymentRecords
                .Where(p => p.Status == GameManagement.Shared.Enums.PaymentStatus.Completed)
                .SumAsync(p => p.Amount);

            // 计算总访问量
            var totalVisits = await _context.VisitRecords.CountAsync();

            // 计算总注册数
            var totalRegistrations = await _context.UserRegistrations.CountAsync();

            // 计算总登录数
            var totalLogins = await _context.UserLogins.CountAsync();

            // 今日新访问
            var todayNewVisits = await _context.VisitRecords
                .Where(v => v.VisitTime >= today)
                .CountAsync();

            // 今日新注册
            var todayNewRegistrations = await _context.UserRegistrations
                .Where(r => r.RegistrationTime >= today)
                .CountAsync();

            // 今日新登录
            var todayNewLogins = await _context.UserLogins
                .Where(l => l.LoginTime >= today)
                .CountAsync();

            // 今日新付费
            var todayNewPayments = await _context.PaymentRecords
                .Where(p => p.CreatedAt >= today && p.Status == GameManagement.Shared.Enums.PaymentStatus.Completed)
                .CountAsync();

            // 今日活跃用户（今日有登录记录的用户）
            var todayActiveUsers = await _context.UserLogins
                .Where(l => l.LoginTime >= today)
                .Select(l => l.AccountId)
                .Distinct()
                .CountAsync();

            // 计算ARPU（平均每用户收入）
            var totalUsers = await _context.UserRegistrations.CountAsync();
            var arpu = totalUsers > 0 ? totalRevenue / totalUsers : 0;

            // 计算平均在线用户数（基于最近7天的数据）
            var sevenDaysAgo = today.AddDays(-7);
            var recentLogins = await _context.UserLogins
                .Where(l => l.LoginTime >= sevenDaysAgo)
                .GroupBy(l => l.LoginTime.Date)
                .Select(g => g.Select(l => l.AccountId).Distinct().Count())
                .ToListAsync();

            var averageOnlineUsers = recentLogins.Any() ? recentLogins.Average() : 0;

            // 计算最大在线用户数（单日最高）
            var maxOnlineUsers = recentLogins.Any() ? recentLogins.Max() : 0;

            return new GlobalStatsDto
            {
                TotalRevenue = totalRevenue,
                TotalVisits = totalVisits,
                TotalRegistrations = totalRegistrations,
                TotalLogins = totalLogins,
                TodayNewVisits = todayNewVisits,
                TodayNewRegistrations = todayNewRegistrations,
                TodayNewLogins = todayNewLogins,
                TodayNewPayments = todayNewPayments,
                TodayActiveUsers = todayActiveUsers,
                ARPU = arpu,
                AverageOnlineUsers = averageOnlineUsers,
                MaxOnlineUsers = maxOnlineUsers,
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting global stats");
            throw;
        }
    }

    public async Task<GlobalStatsDto> GetGlobalStatsByDateAsync(DateTime date)
    {
        try
        {
            var targetDate = date.Date;
            var nextDate = targetDate.AddDays(1);

            // 计算截止到指定日期的总收入
            var totalRevenue = await _context.PaymentRecords
                .Where(p => p.Status == GameManagement.Shared.Enums.PaymentStatus.Completed && p.CreatedAt < nextDate)
                .SumAsync(p => p.Amount);

            // 计算截止到指定日期的总访问量
            var totalVisits = await _context.VisitRecords
                .Where(v => v.VisitTime < nextDate)
                .CountAsync();

            // 计算截止到指定日期的总注册数
            var totalRegistrations = await _context.UserRegistrations
                .Where(r => r.RegistrationTime < nextDate)
                .CountAsync();

            // 计算截止到指定日期的总登录数
            var totalLogins = await _context.UserLogins
                .Where(l => l.LoginTime < nextDate)
                .CountAsync();

            // 指定日期的新访问
            var dateNewVisits = await _context.VisitRecords
                .Where(v => v.VisitTime >= targetDate && v.VisitTime < nextDate)
                .CountAsync();

            // 指定日期的新注册
            var dateNewRegistrations = await _context.UserRegistrations
                .Where(r => r.RegistrationTime >= targetDate && r.RegistrationTime < nextDate)
                .CountAsync();

            // 指定日期的新登录
            var dateNewLogins = await _context.UserLogins
                .Where(l => l.LoginTime >= targetDate && l.LoginTime < nextDate)
                .CountAsync();

            // 指定日期的新付费
            var dateNewPayments = await _context.PaymentRecords
                .Where(p => p.CreatedAt >= targetDate && p.CreatedAt < nextDate &&
                           p.Status == GameManagement.Shared.Enums.PaymentStatus.Completed)
                .CountAsync();

            // 指定日期的活跃用户
            var dateActiveUsers = await _context.UserLogins
                .Where(l => l.LoginTime >= targetDate && l.LoginTime < nextDate)
                .Select(l => l.AccountId)
                .Distinct()
                .CountAsync();

            // 计算ARPU（基于截止到指定日期的数据）
            var totalUsers = await _context.UserRegistrations
                .Where(r => r.RegistrationTime < nextDate)
                .CountAsync();
            var arpu = totalUsers > 0 ? totalRevenue / totalUsers : 0;

            // 计算平均在线用户数（基于指定日期前7天的数据）
            var sevenDaysAgo = targetDate.AddDays(-7);
            var recentLogins = await _context.UserLogins
                .Where(l => l.LoginTime >= sevenDaysAgo && l.LoginTime < nextDate)
                .GroupBy(l => l.LoginTime.Date)
                .Select(g => g.Select(l => l.AccountId).Distinct().Count())
                .ToListAsync();

            var averageOnlineUsers = recentLogins.Any() ? recentLogins.Average() : 0;
            var maxOnlineUsers = recentLogins.Any() ? recentLogins.Max() : 0;

            return new GlobalStatsDto
            {
                TotalRevenue = totalRevenue,
                TotalVisits = totalVisits,
                TotalRegistrations = totalRegistrations,
                TotalLogins = totalLogins,
                TodayNewVisits = dateNewVisits,
                TodayNewRegistrations = dateNewRegistrations,
                TodayNewLogins = dateNewLogins,
                TodayNewPayments = dateNewPayments,
                TodayActiveUsers = dateActiveUsers,
                ARPU = arpu,
                AverageOnlineUsers = averageOnlineUsers,
                MaxOnlineUsers = maxOnlineUsers,
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting global stats by date: {Date}", date);
            throw;
        }
    }

    public async Task<UserInfoStatsDto> GetUserInfoStatsAsync()
    {
        try
        {
            // 总访问量
            var totalVisits = await _context.VisitRecords.CountAsync();

            // 独立访问量（基于IP地址去重）
            var uniqueVisits = await _context.VisitRecords
                .Where(v => !string.IsNullOrEmpty(v.IpAddress))
                .Select(v => v.IpAddress)
                .Distinct()
                .CountAsync();

            // 总注册数
            var totalRegistrations = await _context.UserRegistrations.CountAsync();

            // 有渠道信息的注册数
            var registrationsWithChannel = await _context.UserRegistrations
                .Where(r => r.ChannelId.HasValue)
                .CountAsync();

            // 总登录数
            var totalLogins = await _context.UserLogins.CountAsync();

            // 相同IP登录数（检测可能的账号共享）
            var sameIpLogins = await _context.UserLogins
                .Where(l => !string.IsNullOrEmpty(l.IpAddress))
                .GroupBy(l => l.IpAddress)
                .Where(g => g.Select(l => l.AccountId).Distinct().Count() > 1)
                .CountAsync();

            // 当前在线用户数（最近30分钟有登录记录且没有登出记录的用户）
            var thirtyMinutesAgo = DateTime.UtcNow.AddMinutes(-30);
            var currentOnlineUsers = await _context.UserLogins
                .Where(l => l.LoginTime >= thirtyMinutesAgo && !l.LogoutTime.HasValue)
                .Select(l => l.AccountId)
                .Distinct()
                .CountAsync();

            // 没有创建角色的用户数
            var usersWithoutCharacter = await _context.UserRegistrations
                .Where(r => !r.HasCreatedCharacter)
                .CountAsync();

            // 从未登录的用户数
            var usersNeverLoggedIn = await _context.UserRegistrations
                .Where(r => !r.FirstLoginTime.HasValue)
                .CountAsync();

            // 按渠道统计注册数
            var registrationsByChannel = await _context.UserRegistrations
                .Where(r => r.Channel != null)
                .Include(r => r.Channel)
                .GroupBy(r => r.Channel!.Name)
                .Select(g => new { Channel = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Channel, x => x.Count);

            // 按小时统计访问量（今日）
            var today = DateTime.UtcNow.Date;
            var tomorrow = today.AddDays(1);
            var visitsByHour = await _context.VisitRecords
                .Where(v => v.VisitTime >= today && v.VisitTime < tomorrow)
                .GroupBy(v => v.VisitTime.Hour)
                .Select(g => new { Hour = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Hour.ToString("00"), x => x.Count);

            // 确保所有24小时都有数据
            for (int i = 0; i < 24; i++)
            {
                var hourKey = i.ToString("00");
                if (!visitsByHour.ContainsKey(hourKey))
                {
                    visitsByHour[hourKey] = 0;
                }
            }

            return new UserInfoStatsDto
            {
                TotalVisits = totalVisits,
                UniqueVisits = uniqueVisits,
                TotalRegistrations = totalRegistrations,
                RegistrationsWithChannel = registrationsWithChannel,
                TotalLogins = totalLogins,
                SameIpLogins = sameIpLogins,
                CurrentOnlineUsers = currentOnlineUsers,
                UsersWithoutCharacter = usersWithoutCharacter,
                UsersNeverLoggedIn = usersNeverLoggedIn,
                RegistrationsByChannel = registrationsByChannel,
                VisitsByHour = visitsByHour
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user info stats");
            throw;
        }
    }

    public async Task<UserInfoStatsDto> GetUserInfoStatsByDateRangeAsync(DateTime startDate, DateTime endDate) =>
        await GetUserInfoStatsAsync();

    public async Task<PaymentInfoStatsDto> GetPaymentInfoStatsAsync()
    {
        await Task.Delay(1);
        return new PaymentInfoStatsDto
        {
            TotalRevenue = 98765.43m,
            TotalOrders = 5678,
            PendingOrders = 123,
            CompletedOrders = 5432,
            FailedOrders = 246,
            AverageOrderValue = 17.39m,
            RevenueByMethod = new Dictionary<string, decimal>
            {
                { "支付宝", 45000.00m },
                { "微信支付", 35000.00m },
                { "银行卡", 18765.43m }
            },
            OrdersByStatus = new Dictionary<string, int>
            {
                { "已完成", 5432 },
                { "待支付", 123 },
                { "已失败", 246 }
            },
            TopPayers = new List<TopPayerDto>
            {
                new TopPayerDto { AccountId = "user001", Nickname = "大R玩家1", TotalAmount = 5000.00m, OrderCount = 50, LastPaymentTime = DateTime.UtcNow.AddHours(-2) },
                new TopPayerDto { AccountId = "user002", Nickname = "大R玩家2", TotalAmount = 4500.00m, OrderCount = 45, LastPaymentTime = DateTime.UtcNow.AddHours(-5) },
                new TopPayerDto { AccountId = "user003", Nickname = "大R玩家3", TotalAmount = 4000.00m, OrderCount = 40, LastPaymentTime = DateTime.UtcNow.AddHours(-8) }
            }
        };
    }

    public async Task<PaymentInfoStatsDto> GetPaymentInfoStatsByDateRangeAsync(DateTime startDate, DateTime endDate) =>
        await GetPaymentInfoStatsAsync();

    public async Task<ClientDataStatsDto> GetClientDataStatsAsync()
    {
        await Task.Delay(1);
        return new ClientDataStatsDto();
    }

    public async Task<ClientDataStatsDto> GetClientDataStatsByDateRangeAsync(DateTime startDate, DateTime endDate) =>
        await GetClientDataStatsAsync();

    public async Task<ConversionAnalysisDto> GetConversionAnalysisAsync(DateTime date)
    {
        await Task.Delay(1);
        return new ConversionAnalysisDto();
    }

    public async Task<ChurnAnalysisDto> GetChurnAnalysisAsync(DateTime date)
    {
        await Task.Delay(1);
        return new ChurnAnalysisDto();
    }

    public async Task<RetentionAnalysisDto> GetRetentionAnalysisAsync(DateTime date)
    {
        await Task.Delay(1);
        return new RetentionAnalysisDto();
    }

    public async Task<ActiveUserAnalysisDto> GetActiveUserAnalysisAsync(DateTime date)
    {
        await Task.Delay(1);
        return new ActiveUserAnalysisDto();
    }

    public async Task<IEnumerable<UserLifecycleDto>> GetUserLifecycleAnalysisAsync(int page, int pageSize)
    {
        await Task.Delay(1);
        return new List<UserLifecycleDto>();
    }

    public async Task RecordVisitAsync(string? ipAddress, string? userAgent, string? referrer, string? channel, int? serverId)
    {
        try
        {
            var visitRecord = new VisitRecord
            {
                IpAddress = ipAddress,
                UserAgent = userAgent,
                Referrer = referrer,
                Channel = channel,
                VisitTime = DateTime.UtcNow,
                SessionId = Guid.NewGuid().ToString()
            };

            _context.VisitRecords.Add(visitRecord);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Recorded visit from IP: {IpAddress}, Channel: {Channel}", ipAddress, channel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording visit");
            throw;
        }
    }

    public async Task RecordRegistrationAsync(string accountId, string? ipAddress, string? channel, int? serverId)
    {
        try
        {
            // 查找渠道ID
            int? channelId = null;
            if (!string.IsNullOrEmpty(channel))
            {
                var channelEntity = await _context.Channels
                    .FirstOrDefaultAsync(c => c.Code == channel || c.Name == channel);
                channelId = channelEntity?.Id;
            }

            var registrationRecord = new UserRegistration
            {
                AccountId = accountId,
                IpAddress = ipAddress,
                ChannelId = channelId,
                ServerId = serverId,
                RegistrationTime = DateTime.UtcNow,
                HasCreatedCharacter = false
            };

            _context.UserRegistrations.Add(registrationRecord);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Recorded registration for account: {AccountId}, Channel: {Channel}", accountId, channel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording registration for account: {AccountId}", accountId);
            throw;
        }
    }

    public async Task RecordLoginAsync(string accountId, string? ipAddress, int? serverId)
    {
        try
        {
            var loginRecord = new UserLogin
            {
                AccountId = accountId,
                IpAddress = ipAddress,
                ServerId = serverId,
                LoginTime = DateTime.UtcNow
            };

            _context.UserLogins.Add(loginRecord);

            // 更新用户注册记录的首次登录时间
            var registration = await _context.UserRegistrations
                .FirstOrDefaultAsync(r => r.AccountId == accountId && !r.FirstLoginTime.HasValue);

            if (registration != null)
            {
                registration.FirstLoginTime = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Recorded login for account: {AccountId}, IP: {IpAddress}", accountId, ipAddress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording login for account: {AccountId}", accountId);
            throw;
        }
    }

    public async Task RecordLogoutAsync(string accountId, DateTime loginTime)
    {
        try
        {
            // 查找对应的登录记录
            var loginRecord = await _context.UserLogins
                .Where(l => l.AccountId == accountId && l.LoginTime == loginTime && !l.LogoutTime.HasValue)
                .FirstOrDefaultAsync();

            if (loginRecord != null)
            {
                loginRecord.LogoutTime = DateTime.UtcNow;
                loginRecord.SessionDuration = loginRecord.LogoutTime.Value - loginRecord.LoginTime;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Recorded logout for account: {AccountId}, Session duration: {Duration}",
                    accountId, loginRecord.SessionDuration);
            }
            else
            {
                _logger.LogWarning("No matching login record found for logout: {AccountId}, LoginTime: {LoginTime}",
                    accountId, loginTime);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording logout for account: {AccountId}", accountId);
            throw;
        }
    }

    public async Task RecordClientInstallAsync(string version, string platform, string deviceId, string? referrer) =>
        await Task.CompletedTask;

    public async Task RecordClientUninstallAsync(string version, string platform, string deviceId) =>
        await Task.CompletedTask;

    public async Task<IEnumerable<OperationalDataDto>> GetHistoricalDataAsync(string dataType, DateTime startDate, DateTime endDate, int? serverId = null)
    {
        try
        {
            var query = _context.OperationalData
                .Where(od => od.Date >= startDate && od.Date <= endDate);

            if (!string.IsNullOrEmpty(dataType))
            {
                query = query.Where(od => od.DataType == dataType);
            }

            if (serverId.HasValue)
            {
                query = query.Where(od => od.ServerId == serverId.Value);
            }

            var data = await query
                .Include(od => od.Server)
                .OrderBy(od => od.Date)
                .Select(od => new OperationalDataDto
                {
                    Id = od.Id,
                    Date = od.Date,
                    ServerId = od.ServerId,
                    ServerName = od.Server != null ? od.Server.Name : "",
                    DataType = od.DataType,
                    MetricName = od.MetricName,
                    Value = od.Value,
                    AdditionalData = od.AdditionalData,
                    CreatedAt = od.CreatedAt
                })
                .ToListAsync();

            return data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting historical data for type: {DataType}", dataType);
            throw;
        }
    }

    public async Task<Dictionary<DateTime, decimal>> GetMetricTrendAsync(string dataType, string metricName, DateTime startDate, DateTime endDate, int? serverId = null)
    {
        try
        {
            var query = _context.OperationalData
                .Where(od => od.Date >= startDate && od.Date <= endDate &&
                            od.DataType == dataType && od.MetricName == metricName);

            if (serverId.HasValue)
            {
                query = query.Where(od => od.ServerId == serverId.Value);
            }

            var data = await query
                .GroupBy(od => od.Date.Date)
                .Select(g => new { Date = g.Key, Value = g.Sum(od => od.Value) })
                .OrderBy(x => x.Date)
                .ToDictionaryAsync(x => x.Date, x => x.Value);

            return data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting metric trend for {DataType}.{MetricName}", dataType, metricName);
            throw;
        }
    }
}
