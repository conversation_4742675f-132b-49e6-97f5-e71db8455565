using GameManagement.Core.Interfaces;
using GameManagement.Shared.DTOs;
using GameManagement.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace GameManagement.Infrastructure.Services;

public class PlayerService : IPlayerService
{
    private readonly GameManagementDbContext _context;
    private readonly ILogger<PlayerService> _logger;

    public PlayerService(GameManagementDbContext context, ILogger<PlayerService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<PlayerDto?> GetPlayerByIdAsync(int id)
    {
        try
        {
            var player = await _context.Players
                .Include(p => p.Server)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (player == null) return null;

            return new PlayerDto
            {
                Id = player.Id,
                AccountId = player.AccountId,
                Nickname = player.Nickname,
                Level = player.Level,
                Class = player.Class,
                Experience = player.Experience,
                Gold = player.Gold,
                Diamonds = player.Diamonds,
                VipLevel = player.VipLevel,
                ServerId = player.ServerId,
                ServerName = player.Server?.Name ?? "Unknown",
                IsBanned = player.IsBanned,
                BannedUntil = player.BannedUntil,
                BanReason = player.BanReason,
                CreatedAt = player.CreatedAt,
                LastLoginAt = player.LastLoginAt,
                TotalPlayTime = player.TotalPlayTime,
                IpAddress = player.IpAddress
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting player by id {PlayerId}", id);
            throw;
        }
    }

    public async Task<PlayerDto?> GetPlayerByAccountIdAsync(string accountId)
    {
        try
        {
            var player = await _context.Players
                .Include(p => p.Server)
                .FirstOrDefaultAsync(p => p.AccountId == accountId);

            if (player == null) return null;

            return new PlayerDto
            {
                Id = player.Id,
                AccountId = player.AccountId,
                Nickname = player.Nickname,
                Level = player.Level,
                Class = player.Class,
                Experience = player.Experience,
                Gold = player.Gold,
                Diamonds = player.Diamonds,
                VipLevel = player.VipLevel,
                ServerId = player.ServerId,
                ServerName = player.Server?.Name ?? "Unknown",
                IsBanned = player.IsBanned,
                BannedUntil = player.BannedUntil,
                BanReason = player.BanReason,
                CreatedAt = player.CreatedAt,
                LastLoginAt = player.LastLoginAt,
                TotalPlayTime = player.TotalPlayTime,
                IpAddress = player.IpAddress
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting player by account id {AccountId}", accountId);
            throw;
        }
    }

    public async Task<IEnumerable<PlayerDto>> GetPlayersAsync(int page, int pageSize)
    {
        try
        {
            var players = await _context.Players
                .Include(p => p.Server)
                .OrderByDescending(p => p.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(p => new PlayerDto
                {
                    Id = p.Id,
                    AccountId = p.AccountId,
                    Nickname = p.Nickname,
                    Level = p.Level,
                    Class = p.Class,
                    Experience = p.Experience,
                    Gold = p.Gold,
                    Diamonds = p.Diamonds,
                    VipLevel = p.VipLevel,
                    ServerId = p.ServerId,
                    ServerName = p.Server != null ? p.Server.Name : "Unknown",
                    IsBanned = p.IsBanned,
                    BannedUntil = p.BannedUntil,
                    BanReason = p.BanReason,
                    CreatedAt = p.CreatedAt,
                    LastLoginAt = p.LastLoginAt,
                    TotalPlayTime = p.TotalPlayTime,
                    IpAddress = p.IpAddress
                })
                .ToListAsync();

            return players;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting players page {Page} size {PageSize}", page, pageSize);
            throw;
        }
    }

    public async Task<IEnumerable<PlayerDto>> SearchPlayersAsync(string searchTerm)
    {
        try
        {
            var players = await _context.Players
                .Include(p => p.Server)
                .Where(p => p.AccountId.Contains(searchTerm) || 
                           p.Nickname.Contains(searchTerm))
                .OrderByDescending(p => p.Level)
                .Take(50) // 限制搜索结果数量
                .Select(p => new PlayerDto
                {
                    Id = p.Id,
                    AccountId = p.AccountId,
                    Nickname = p.Nickname,
                    Level = p.Level,
                    Class = p.Class,
                    Experience = p.Experience,
                    Gold = p.Gold,
                    Diamonds = p.Diamonds,
                    VipLevel = p.VipLevel,
                    ServerId = p.ServerId,
                    ServerName = p.Server != null ? p.Server.Name : "Unknown",
                    IsBanned = p.IsBanned,
                    BannedUntil = p.BannedUntil,
                    BanReason = p.BanReason,
                    CreatedAt = p.CreatedAt,
                    LastLoginAt = p.LastLoginAt,
                    TotalPlayTime = p.TotalPlayTime,
                    IpAddress = p.IpAddress
                })
                .ToListAsync();

            return players;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching players with term {SearchTerm}", searchTerm);
            throw;
        }
    }

    public async Task<PlayerStatsDto> GetPlayerStatsAsync()
    {
        try
        {
            var totalPlayers = await _context.Players.CountAsync();
            var activePlayers = await _context.Players
                .Where(p => p.LastLoginAt >= DateTime.UtcNow.AddDays(-7))
                .CountAsync();
            var bannedPlayers = await _context.Players
                .Where(p => p.IsBanned)
                .CountAsync();
            var vipPlayers = await _context.Players
                .Where(p => p.VipLevel > 0)
                .CountAsync();

            var averageLevel = totalPlayers > 0
                ? await _context.Players.AverageAsync(p => (double)p.Level)
                : 0.0;

            var maxLevel = totalPlayers > 0
                ? await _context.Players.MaxAsync(p => p.Level)
                : 0;

            var newPlayersToday = await _context.Players
                .Where(p => p.CreatedAt >= DateTime.UtcNow.Date)
                .CountAsync();

            // 获取等级分布
            var levelDistribution = await _context.Players
                .GroupBy(p => p.Level / 10 * 10) // 按10级分组
                .Select(g => new { Level = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => $"{x.Level}-{x.Level + 9}", x => x.Count);

            // 获取职业分布
            var classDistribution = await _context.Players
                .GroupBy(p => p.Class)
                .Select(g => new { Class = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Class, x => x.Count);

            return new PlayerStatsDto
            {
                TotalPlayers = totalPlayers,
                ActivePlayers = activePlayers,
                NewPlayersToday = newPlayersToday,
                VipPlayers = vipPlayers,
                AverageLevel = (decimal)averageLevel,
                LevelDistribution = levelDistribution,
                ClassDistribution = classDistribution
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting player stats");
            throw;
        }
    }

    public async Task<IEnumerable<PlayerDto>> GetTopPlayersByLevelAsync(int count)
    {
        try
        {
            var players = await _context.Players
                .Include(p => p.Server)
                .OrderByDescending(p => p.Level)
                .ThenByDescending(p => p.Experience)
                .Take(count)
                .Select(p => new PlayerDto
                {
                    Id = p.Id,
                    AccountId = p.AccountId,
                    Nickname = p.Nickname,
                    Level = p.Level,
                    Class = p.Class,
                    Experience = p.Experience,
                    Gold = p.Gold,
                    Diamonds = p.Diamonds,
                    VipLevel = p.VipLevel,
                    ServerId = p.ServerId,
                    ServerName = p.Server != null ? p.Server.Name : "Unknown",
                    IsBanned = p.IsBanned,
                    BannedUntil = p.BannedUntil,
                    BanReason = p.BanReason,
                    CreatedAt = p.CreatedAt,
                    LastLoginAt = p.LastLoginAt,
                    TotalPlayTime = p.TotalPlayTime,
                    IpAddress = p.IpAddress
                })
                .ToListAsync();

            return players;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting top players by level, count {Count}", count);
            throw;
        }
    }

    public async Task<IEnumerable<PlayerDto>> GetVipPlayersAsync(int minVipLevel = 1)
    {
        try
        {
            var players = await _context.Players
                .Include(p => p.Server)
                .Where(p => p.VipLevel >= minVipLevel)
                .OrderByDescending(p => p.VipLevel)
                .ThenByDescending(p => p.Gold)
                .Select(p => new PlayerDto
                {
                    Id = p.Id,
                    AccountId = p.AccountId,
                    Nickname = p.Nickname,
                    Level = p.Level,
                    Class = p.Class,
                    Experience = p.Experience,
                    Gold = p.Gold,
                    Diamonds = p.Diamonds,
                    VipLevel = p.VipLevel,
                    ServerId = p.ServerId,
                    ServerName = p.Server != null ? p.Server.Name : "Unknown",
                    IsBanned = p.IsBanned,
                    BannedUntil = p.BannedUntil,
                    BanReason = p.BanReason,
                    CreatedAt = p.CreatedAt,
                    LastLoginAt = p.LastLoginAt,
                    TotalPlayTime = p.TotalPlayTime,
                    IpAddress = p.IpAddress
                })
                .ToListAsync();

            return players;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting VIP players with min level {MinVipLevel}", minVipLevel);
            throw;
        }
    }

    public async Task<bool> BanPlayerAsync(int playerId, DateTime? bannedUntil, string reason)
    {
        try
        {
            var player = await _context.Players.FindAsync(playerId);
            if (player == null)
            {
                _logger.LogWarning("Player {PlayerId} not found for banning", playerId);
                return false;
            }

            player.IsBanned = true;
            player.BannedUntil = bannedUntil;
            player.BanReason = reason;
            player.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Player {PlayerId} banned until {BannedUntil} for reason: {Reason}",
                playerId, bannedUntil, reason);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error banning player {PlayerId}", playerId);
            throw;
        }
    }

    public async Task<bool> UnbanPlayerAsync(int playerId)
    {
        try
        {
            var player = await _context.Players.FindAsync(playerId);
            if (player == null)
            {
                _logger.LogWarning("Player {PlayerId} not found for unbanning", playerId);
                return false;
            }

            player.IsBanned = false;
            player.BannedUntil = null;
            player.BanReason = null;
            player.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Player {PlayerId} unbanned", playerId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unbanning player {PlayerId}", playerId);
            throw;
        }
    }

    public async Task<bool> UpdatePlayerDataAsync(int playerId, UpdatePlayerDto updatePlayerDto)
    {
        try
        {
            var player = await _context.Players.FindAsync(playerId);
            if (player == null)
            {
                _logger.LogWarning("Player {PlayerId} not found for updating", playerId);
                return false;
            }

            // 更新允许修改的字段
            if (!string.IsNullOrEmpty(updatePlayerDto.Nickname))
            {
                player.Nickname = updatePlayerDto.Nickname;
            }

            if (updatePlayerDto.Level.HasValue)
            {
                player.Level = updatePlayerDto.Level.Value;
            }

            if (updatePlayerDto.Experience.HasValue)
            {
                player.Experience = updatePlayerDto.Experience.Value;
            }

            if (updatePlayerDto.Gold.HasValue)
            {
                player.Gold = updatePlayerDto.Gold.Value;
            }

            if (updatePlayerDto.VipLevel.HasValue)
            {
                player.VipLevel = updatePlayerDto.VipLevel.Value;
            }

            player.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Player {PlayerId} data updated", playerId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating player {PlayerId} data", playerId);
            throw;
        }
    }
}
