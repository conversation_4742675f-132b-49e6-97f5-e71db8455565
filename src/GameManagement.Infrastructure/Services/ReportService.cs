using GameManagement.Core.Entities;
using GameManagement.Core.Interfaces;
using GameManagement.Infrastructure.Data;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace GameManagement.Infrastructure.Services;

public class ReportService : IReportService
{
    private readonly GameManagementDbContext _context;
    private readonly ILogger<ReportService> _logger;

    public ReportService(GameManagementDbContext context, ILogger<ReportService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<DashboardStatsDto> GetDashboardStatsAsync()
    {
        try
        {
            var today = DateTime.UtcNow.Date;
            var thisMonth = new DateTime(today.Year, today.Month, 1);

            // Get basic stats
            var totalPlayers = await _context.Players.CountAsync();
            var activePlayersToday = await _context.Players
                .Where(p => p.LastLoginAt >= today)
                .CountAsync();
            
            var onlineServers = await _context.GameServers
                .Where(s => s.Status == ServerStatus.Online)
                .CountAsync();

            // Get revenue stats
            var todayRevenue = await _context.PaymentRecords
                .Where(p => p.CompletedAt >= today && p.Status == PaymentStatus.Completed)
                .SumAsync(p => p.Amount);

            var monthRevenue = await _context.PaymentRecords
                .Where(p => p.CompletedAt >= thisMonth && p.Status == PaymentStatus.Completed)
                .SumAsync(p => p.Amount);

            var newPlayersToday = await _context.Players
                .Where(p => p.CreatedAt >= today)
                .CountAsync();

            var activeActivities = await _context.GameActivities
                .Where(a => a.StartTime <= DateTime.UtcNow && a.EndTime >= DateTime.UtcNow)
                .CountAsync();

            // Get revenue chart data (last 7 days)
            var revenueChart = new List<RevenueChartData>();
            for (int i = 6; i >= 0; i--)
            {
                var date = today.AddDays(-i);
                var revenue = await _context.PaymentRecords
                    .Where(p => p.CompletedAt >= date && p.CompletedAt < date.AddDays(1) && p.Status == PaymentStatus.Completed)
                    .SumAsync(p => p.Amount);
                
                revenueChart.Add(new RevenueChartData
                {
                    Date = date,
                    Revenue = revenue
                });
            }

            // Get player chart data (last 7 days)
            var playerChart = new List<PlayerChartData>();
            for (int i = 6; i >= 0; i--)
            {
                var date = today.AddDays(-i);
                var newPlayers = await _context.Players
                    .Where(p => p.CreatedAt >= date && p.CreatedAt < date.AddDays(1))
                    .CountAsync();
                
                var activePlayers = await _context.Players
                    .Where(p => p.LastLoginAt >= date && p.LastLoginAt < date.AddDays(1))
                    .CountAsync();
                
                playerChart.Add(new PlayerChartData
                {
                    Date = date,
                    NewPlayers = newPlayers,
                    ActivePlayers = activePlayers
                });
            }

            return new DashboardStatsDto
            {
                TotalPlayers = totalPlayers,
                ActivePlayers = activePlayersToday,
                OnlineServers = onlineServers,
                TodayRevenue = todayRevenue,
                MonthRevenue = monthRevenue,
                NewPlayersToday = newPlayersToday,
                ActiveActivities = activeActivities,
                RevenueChart = revenueChart,
                PlayerChart = playerChart
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboard stats");
            throw;
        }
    }

    public async Task<RevenueReportDto> GetRevenueReportAsync(DateTime startDate, DateTime endDate)
    {
        try
        {
            var payments = await _context.PaymentRecords
                .Include(p => p.Player)
                .ThenInclude(p => p.Server)
                .Where(p => p.CompletedAt >= startDate && p.CompletedAt <= endDate && p.Status == PaymentStatus.Completed)
                .ToListAsync();

            var totalRevenue = payments.Sum(p => p.Amount);
            var totalTransactions = payments.Count;
            var averageTransactionValue = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;

            // Revenue by payment method
            var revenueByMethod = payments
                .GroupBy(p => p.PaymentMethod)
                .ToDictionary(g => g.Key, g => g.Sum(p => p.Amount));

            // Revenue by server
            var revenueByServer = payments
                .Where(p => p.Player?.Server != null)
                .GroupBy(p => p.Player!.Server!.Name)
                .ToDictionary(g => g.Key, g => g.Sum(p => p.Amount));

            // Daily revenue
            var dailyRevenue = payments
                .Where(p => p.CompletedAt.HasValue)
                .GroupBy(p => p.CompletedAt!.Value.Date)
                .Select(g => new RevenueChartData
                {
                    Date = g.Key,
                    Revenue = g.Sum(p => p.Amount)
                })
                .OrderBy(r => r.Date)
                .ToList();

            return new RevenueReportDto
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalRevenue = totalRevenue,
                TotalTransactions = totalTransactions,
                AverageTransactionValue = averageTransactionValue,
                RevenueByMethod = revenueByMethod,
                RevenueByServer = revenueByServer,
                DailyRevenue = dailyRevenue
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting revenue report for period {StartDate} to {EndDate}", startDate, endDate);
            throw;
        }
    }

    public async Task<PlayerReportDto> GetPlayerReportAsync(DateTime startDate, DateTime endDate)
    {
        try
        {
            var players = await _context.Players
                .Include(p => p.Server)
                .Where(p => p.CreatedAt >= startDate && p.CreatedAt <= endDate)
                .ToListAsync();

            var newPlayers = players.Count;
            
            // Get active players in the period
            var activePlayers = await _context.Players
                .Where(p => p.LastLoginAt >= startDate && p.LastLoginAt <= endDate)
                .CountAsync();

            // Calculate average session time (simplified - using last login to created time for new players)
            var averageSessionTime = players.Any()
                ? players.Where(p => p.LastLoginAt.HasValue)
                         .Average(p => (p.LastLoginAt!.Value - p.CreatedAt).TotalMinutes)
                : 0.0;

            // Players by level (grouped by 10s)
            var allPlayers = await _context.Players.ToListAsync();
            var playersByLevel = allPlayers
                .GroupBy(p => (p.Level / 10) * 10)
                .ToDictionary(g => $"{g.Key}-{g.Key + 9}", g => g.Count());

            // Players by server
            var playersByServer = allPlayers
                .Where(p => p.Server != null)
                .GroupBy(p => p.Server!.Name)
                .ToDictionary(g => g.Key, g => g.Count());

            // Daily player data
            var dailyPlayers = new List<PlayerChartData>();
            for (var date = startDate.Date; date <= endDate.Date; date = date.AddDays(1))
            {
                var newPlayersOnDate = await _context.Players
                    .Where(p => p.CreatedAt >= date && p.CreatedAt < date.AddDays(1))
                    .CountAsync();
                
                var activePlayersOnDate = await _context.Players
                    .Where(p => p.LastLoginAt >= date && p.LastLoginAt < date.AddDays(1))
                    .CountAsync();
                
                dailyPlayers.Add(new PlayerChartData
                {
                    Date = date,
                    NewPlayers = newPlayersOnDate,
                    ActivePlayers = activePlayersOnDate
                });
            }

            return new PlayerReportDto
            {
                StartDate = startDate,
                EndDate = endDate,
                NewPlayers = newPlayers,
                ActivePlayers = activePlayers,
                AverageSessionTime = averageSessionTime,
                PlayersByLevel = playersByLevel,
                PlayersByServer = playersByServer,
                DailyPlayers = dailyPlayers
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting player report for period {StartDate} to {EndDate}", startDate, endDate);
            throw;
        }
    }

    public async Task<RetentionReportDto> GetRetentionReportAsync(DateTime startDate, DateTime endDate)
    {
        try
        {
            // Get players who registered in the period
            var newPlayers = await _context.Players
                .Where(p => p.CreatedAt >= startDate && p.CreatedAt <= endDate)
                .ToListAsync();

            if (!newPlayers.Any())
            {
                return new RetentionReportDto
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    Day1Retention = 0,
                    Day7Retention = 0,
                    Day30Retention = 0,
                    RetentionByLevel = new Dictionary<string, double>(),
                    RetentionByServer = new Dictionary<string, double>()
                };
            }

            // Calculate retention rates
            var day1RetainedCount = 0;
            var day7RetainedCount = 0;
            var day30RetainedCount = 0;

            foreach (var player in newPlayers)
            {
                var registrationDate = player.CreatedAt.Date;

                // Day 1 retention (logged in the day after registration)
                var day1LoginExists = await _context.Players
                    .AnyAsync(p => p.Id == player.Id &&
                                  p.LastLoginAt >= registrationDate.AddDays(1) &&
                                  p.LastLoginAt < registrationDate.AddDays(2));
                if (day1LoginExists) day1RetainedCount++;

                // Day 7 retention (logged in within 7 days after registration)
                var day7LoginExists = await _context.Players
                    .AnyAsync(p => p.Id == player.Id &&
                                  p.LastLoginAt >= registrationDate.AddDays(1) &&
                                  p.LastLoginAt < registrationDate.AddDays(8));
                if (day7LoginExists) day7RetainedCount++;

                // Day 30 retention (logged in within 30 days after registration)
                var day30LoginExists = await _context.Players
                    .AnyAsync(p => p.Id == player.Id &&
                                  p.LastLoginAt >= registrationDate.AddDays(1) &&
                                  p.LastLoginAt < registrationDate.AddDays(31));
                if (day30LoginExists) day30RetainedCount++;
            }

            var totalNewPlayers = newPlayers.Count;
            var day1Retention = totalNewPlayers > 0 ? (double)day1RetainedCount / totalNewPlayers * 100 : 0;
            var day7Retention = totalNewPlayers > 0 ? (double)day7RetainedCount / totalNewPlayers * 100 : 0;
            var day30Retention = totalNewPlayers > 0 ? (double)day30RetainedCount / totalNewPlayers * 100 : 0;

            // Retention by level (simplified - using current level)
            var retentionByLevel = newPlayers
                .GroupBy(p => (p.Level / 10) * 10)
                .ToDictionary(g => $"{g.Key}-{g.Key + 9}",
                             g => g.Count(p => p.LastLoginAt > p.CreatedAt.AddDays(1)) / (double)g.Count() * 100);

            // Retention by server
            var retentionByServer = newPlayers
                .Where(p => p.Server != null)
                .GroupBy(p => p.Server!.Name)
                .ToDictionary(g => g.Key,
                             g => g.Count(p => p.LastLoginAt > p.CreatedAt.AddDays(1)) / (double)g.Count() * 100);

            return new RetentionReportDto
            {
                StartDate = startDate,
                EndDate = endDate,
                Day1Retention = day1Retention,
                Day7Retention = day7Retention,
                Day30Retention = day30Retention,
                RetentionByLevel = retentionByLevel,
                RetentionByServer = retentionByServer
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting retention report for period {StartDate} to {EndDate}", startDate, endDate);
            throw;
        }
    }

    public async Task<byte[]> ExportReportAsync(string reportType, DateTime startDate, DateTime endDate, string format)
    {
        try
        {
            _logger.LogInformation("Exporting {ReportType} report from {StartDate} to {EndDate} in {Format} format",
                                 reportType, startDate, endDate, format);

            // Get report data based on type
            object reportData = reportType.ToLower() switch
            {
                "revenue" => await GetRevenueReportAsync(startDate, endDate),
                "player" => await GetPlayerReportAsync(startDate, endDate),
                "retention" => await GetRetentionReportAsync(startDate, endDate),
                "dashboard" => await GetDashboardStatsAsync(),
                _ => throw new ArgumentException($"Unknown report type: {reportType}")
            };

            // Export based on format
            return format.ToLower() switch
            {
                "json" => ExportToJson(reportData),
                "csv" => ExportToCsv(reportData, reportType),
                "excel" => ExportToExcel(reportData, reportType),
                _ => throw new ArgumentException($"Unsupported export format: {format}")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting {ReportType} report in {Format} format", reportType, format);
            throw;
        }
    }

    private byte[] ExportToJson(object data)
    {
        var json = JsonSerializer.Serialize(data, new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });
        return System.Text.Encoding.UTF8.GetBytes(json);
    }

    private byte[] ExportToCsv(object data, string reportType)
    {
        // Simplified CSV export - in a real implementation, you'd use a proper CSV library
        var csv = new System.Text.StringBuilder();

        switch (reportType.ToLower())
        {
            case "revenue":
                if (data is RevenueReportDto revenueReport)
                {
                    csv.AppendLine("Date,Revenue");
                    foreach (var item in revenueReport.DailyRevenue)
                    {
                        csv.AppendLine($"{item.Date:yyyy-MM-dd},{item.Revenue}");
                    }
                }
                break;
            case "player":
                if (data is PlayerReportDto playerReport)
                {
                    csv.AppendLine("Date,New Players,Active Players");
                    foreach (var item in playerReport.DailyPlayers)
                    {
                        csv.AppendLine($"{item.Date:yyyy-MM-dd},{item.NewPlayers},{item.ActivePlayers}");
                    }
                }
                break;
            default:
                csv.AppendLine("Export format not implemented for this report type");
                break;
        }

        return System.Text.Encoding.UTF8.GetBytes(csv.ToString());
    }

    private byte[] ExportToExcel(object data, string reportType)
    {
        // Placeholder for Excel export - in a real implementation, you'd use EPPlus or similar
        // For now, return CSV format with Excel-like structure
        _logger.LogWarning("Excel export not fully implemented, returning CSV format");
        return ExportToCsv(data, reportType);
    }
}
