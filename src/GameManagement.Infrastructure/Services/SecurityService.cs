using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using GameManagement.Core.Interfaces;
using GameManagement.Core.Entities;
using GameManagement.Infrastructure.Data;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;

namespace GameManagement.Infrastructure.Services;

public class SecurityService : ISecurityService
{
    private readonly GameManagementDbContext _context;
    private readonly ILogger<SecurityService> _logger;

    public SecurityService(GameManagementDbContext context, ILogger<SecurityService> logger)
    {
        _context = context;
        _logger = logger;
    }

    // Security Events
    public async Task<SecurityEventDto?> GetSecurityEventByIdAsync(int id)
    {
        try
        {
            var securityEvent = await _context.SecurityEvents
                .Include(se => se.HandledByUser)
                .FirstOrDefaultAsync(se => se.Id == id);

            if (securityEvent == null)
                return null;

            return MapToSecurityEventDto(securityEvent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting security event by ID {Id}", id);
            throw;
        }
    }

    public async Task<IEnumerable<SecurityEventDto>> GetSecurityEventsAsync(int page, int pageSize)
    {
        try
        {
            var securityEvents = await _context.SecurityEvents
                .Include(se => se.HandledByUser)
                .OrderByDescending(se => se.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return securityEvents.Select(MapToSecurityEventDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting security events");
            throw;
        }
    }

    public async Task<IEnumerable<SecurityEventDto>> GetSecurityEventsByTypeAsync(SecurityEventType eventType)
    {
        try
        {
            var securityEvents = await _context.SecurityEvents
                .Include(se => se.HandledByUser)
                .Where(se => se.EventType == eventType)
                .OrderByDescending(se => se.CreatedAt)
                .ToListAsync();

            return securityEvents.Select(MapToSecurityEventDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting security events by type {EventType}", eventType);
            throw;
        }
    }

    public async Task<IEnumerable<SecurityEventDto>> GetSecurityEventsByRiskLevelAsync(SecurityRiskLevel riskLevel)
    {
        try
        {
            var securityEvents = await _context.SecurityEvents
                .Include(se => se.HandledByUser)
                .Where(se => se.RiskLevel == riskLevel)
                .OrderByDescending(se => se.CreatedAt)
                .ToListAsync();

            return securityEvents.Select(MapToSecurityEventDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting security events by risk level {RiskLevel}", riskLevel);
            throw;
        }
    }

    public async Task<IEnumerable<SecurityEventDto>> GetPendingSecurityEventsAsync()
    {
        try
        {
            var securityEvents = await _context.SecurityEvents
                .Include(se => se.HandledByUser)
                .Where(se => se.Status == SecurityEventStatus.Pending)
                .OrderByDescending(se => se.CreatedAt)
                .ToListAsync();

            return securityEvents.Select(MapToSecurityEventDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending security events");
            throw;
        }
    }

    public async Task<SecurityEventDto> CreateSecurityEventAsync(CreateSecurityEventDto createSecurityEventDto)
    {
        try
        {
            var securityEvent = new SecurityEvent
            {
                EventId = Guid.NewGuid().ToString(),
                EventType = createSecurityEventDto.EventType,
                PlayerId = createSecurityEventDto.PlayerId,
                IpAddress = createSecurityEventDto.IpAddress,
                Description = createSecurityEventDto.Description,
                RiskLevel = createSecurityEventDto.RiskLevel,
                Evidence = createSecurityEventDto.Evidence,
                IsAutoDetected = createSecurityEventDto.IsAutoDetected,
                Status = SecurityEventStatus.Pending,
                CreatedAt = DateTime.UtcNow
            };

            _context.SecurityEvents.Add(securityEvent);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Created security event {EventId} of type {EventType}", 
                securityEvent.EventId, securityEvent.EventType);

            return MapToSecurityEventDto(securityEvent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating security event");
            throw;
        }
    }

    public async Task<SecurityEventDto> UpdateSecurityEventAsync(int id, UpdateSecurityEventDto updateSecurityEventDto)
    {
        try
        {
            var securityEvent = await _context.SecurityEvents.FindAsync(id);
            if (securityEvent == null)
                throw new ArgumentException($"Security event with ID {id} not found");

            if (updateSecurityEventDto.Status.HasValue)
                securityEvent.Status = updateSecurityEventDto.Status.Value;

            if (!string.IsNullOrEmpty(updateSecurityEventDto.HandlingNotes))
                securityEvent.HandlingNotes = updateSecurityEventDto.HandlingNotes;

            if (!string.IsNullOrEmpty(updateSecurityEventDto.Evidence))
                securityEvent.Evidence = updateSecurityEventDto.Evidence;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated security event {Id}", id);

            return MapToSecurityEventDto(securityEvent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating security event {Id}", id);
            throw;
        }
    }

    public async Task<bool> HandleSecurityEventAsync(int id, int handledByUserId, string handlingNotes)
    {
        try
        {
            var securityEvent = await _context.SecurityEvents.FindAsync(id);
            if (securityEvent == null)
                return false;

            securityEvent.Status = SecurityEventStatus.Investigating;
            securityEvent.HandledByUserId = handledByUserId;
            securityEvent.HandledAt = DateTime.UtcNow;
            securityEvent.HandlingNotes = handlingNotes;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Security event {Id} handled by user {UserId}", id, handledByUserId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling security event {Id}", id);
            return false;
        }
    }

    public async Task<bool> MarkSecurityEventAsResolvedAsync(int id)
    {
        try
        {
            var securityEvent = await _context.SecurityEvents.FindAsync(id);
            if (securityEvent == null)
                return false;

            securityEvent.Status = SecurityEventStatus.Resolved;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Security event {Id} marked as resolved", id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking security event {Id} as resolved", id);
            return false;
        }
    }

    public async Task<bool> MarkSecurityEventAsFalsePositiveAsync(int id)
    {
        try
        {
            var securityEvent = await _context.SecurityEvents.FindAsync(id);
            if (securityEvent == null)
                return false;

            securityEvent.Status = SecurityEventStatus.FalsePositive;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Security event {Id} marked as false positive", id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking security event {Id} as false positive", id);
            return false;
        }
    }

    public async Task<SecurityStatsDto> GetSecurityStatsAsync()
    {
        try
        {
            var totalEvents = await _context.SecurityEvents.CountAsync();
            var pendingEvents = await _context.SecurityEvents
                .Where(se => se.Status == SecurityEventStatus.Pending)
                .CountAsync();
            var criticalEvents = await _context.SecurityEvents
                .Where(se => se.RiskLevel == SecurityRiskLevel.Critical)
                .CountAsync();
            var highRiskEvents = await _context.SecurityEvents
                .Where(se => se.RiskLevel == SecurityRiskLevel.High)
                .CountAsync();
            var resolvedEvents = await _context.SecurityEvents
                .Where(se => se.Status == SecurityEventStatus.Resolved)
                .CountAsync();
            var falsePositiveEvents = await _context.SecurityEvents
                .Where(se => se.Status == SecurityEventStatus.FalsePositive)
                .CountAsync();
            var activeRules = await _context.SecurityRules
                .Where(sr => sr.IsActive)
                .CountAsync();

            // Event distribution by type
            var eventsByType = await _context.SecurityEvents
                .GroupBy(se => se.EventType)
                .Select(g => new { Type = g.Key.ToString(), Count = g.Count() })
                .ToDictionaryAsync(x => x.Type, x => x.Count);

            // Event distribution by risk level
            var eventsByRiskLevel = await _context.SecurityEvents
                .GroupBy(se => se.RiskLevel)
                .Select(g => new { Level = g.Key.ToString(), Count = g.Count() })
                .ToDictionaryAsync(x => x.Level, x => x.Count);

            // Event distribution by status
            var eventsByStatus = await _context.SecurityEvents
                .GroupBy(se => se.Status)
                .Select(g => new { Status = g.Key.ToString(), Count = g.Count() })
                .ToDictionaryAsync(x => x.Status, x => x.Count);

            return new SecurityStatsDto
            {
                TotalEvents = totalEvents,
                PendingEvents = pendingEvents,
                CriticalEvents = criticalEvents,
                HighRiskEvents = highRiskEvents,
                ResolvedEvents = resolvedEvents,
                FalsePositiveEvents = falsePositiveEvents,
                ActiveRules = activeRules,
                EventsByType = eventsByType,
                EventsByRiskLevel = eventsByRiskLevel,
                EventsByStatus = eventsByStatus
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting security stats");
            throw;
        }
    }

    // Security Rules
    public async Task<SecurityRuleDto?> GetSecurityRuleByIdAsync(int id)
    {
        try
        {
            var securityRule = await _context.SecurityRules.FindAsync(id);
            return securityRule == null ? null : MapToSecurityRuleDto(securityRule);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting security rule by ID {Id}", id);
            throw;
        }
    }

    public async Task<IEnumerable<SecurityRuleDto>> GetSecurityRulesAsync()
    {
        try
        {
            var securityRules = await _context.SecurityRules
                .OrderBy(sr => sr.Priority)
                .ThenBy(sr => sr.Name)
                .ToListAsync();

            return securityRules.Select(MapToSecurityRuleDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting security rules");
            throw;
        }
    }

    public async Task<IEnumerable<SecurityRuleDto>> GetActiveSecurityRulesAsync()
    {
        try
        {
            var securityRules = await _context.SecurityRules
                .Where(sr => sr.IsActive)
                .OrderBy(sr => sr.Priority)
                .ThenBy(sr => sr.Name)
                .ToListAsync();

            return securityRules.Select(MapToSecurityRuleDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active security rules");
            throw;
        }
    }

    public async Task<SecurityRuleDto> CreateSecurityRuleAsync(CreateSecurityRuleDto createSecurityRuleDto)
    {
        try
        {
            var securityRule = new SecurityRule
            {
                Name = createSecurityRuleDto.Name,
                Description = createSecurityRuleDto.Description,
                EventType = createSecurityRuleDto.EventType,
                Conditions = createSecurityRuleDto.Conditions,
                Actions = createSecurityRuleDto.Actions,
                Priority = createSecurityRuleDto.Priority,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            _context.SecurityRules.Add(securityRule);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Created security rule {Name}", securityRule.Name);

            return MapToSecurityRuleDto(securityRule);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating security rule");
            throw;
        }
    }

    public async Task<SecurityRuleDto> UpdateSecurityRuleAsync(int id, UpdateSecurityRuleDto updateSecurityRuleDto)
    {
        try
        {
            var securityRule = await _context.SecurityRules.FindAsync(id);
            if (securityRule == null)
                throw new ArgumentException($"Security rule with ID {id} not found");

            if (!string.IsNullOrEmpty(updateSecurityRuleDto.Name))
                securityRule.Name = updateSecurityRuleDto.Name;

            if (!string.IsNullOrEmpty(updateSecurityRuleDto.Description))
                securityRule.Description = updateSecurityRuleDto.Description;

            if (!string.IsNullOrEmpty(updateSecurityRuleDto.Conditions))
                securityRule.Conditions = updateSecurityRuleDto.Conditions;

            if (!string.IsNullOrEmpty(updateSecurityRuleDto.Actions))
                securityRule.Actions = updateSecurityRuleDto.Actions;

            if (updateSecurityRuleDto.IsActive.HasValue)
                securityRule.IsActive = updateSecurityRuleDto.IsActive.Value;

            if (updateSecurityRuleDto.Priority.HasValue)
                securityRule.Priority = updateSecurityRuleDto.Priority.Value;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated security rule {Id}", id);

            return MapToSecurityRuleDto(securityRule);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating security rule {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeleteSecurityRuleAsync(int id)
    {
        try
        {
            var securityRule = await _context.SecurityRules.FindAsync(id);
            if (securityRule == null)
                return false;

            _context.SecurityRules.Remove(securityRule);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Deleted security rule {Id}", id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting security rule {Id}", id);
            return false;
        }
    }

    public async Task<bool> ActivateSecurityRuleAsync(int id)
    {
        try
        {
            var securityRule = await _context.SecurityRules.FindAsync(id);
            if (securityRule == null)
                return false;

            securityRule.IsActive = true;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Activated security rule {Id}", id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating security rule {Id}", id);
            return false;
        }
    }

    public async Task<bool> DeactivateSecurityRuleAsync(int id)
    {
        try
        {
            var securityRule = await _context.SecurityRules.FindAsync(id);
            if (securityRule == null)
                return false;

            securityRule.IsActive = false;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Deactivated security rule {Id}", id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating security rule {Id}", id);
            return false;
        }
    }

    private SecurityEventDto MapToSecurityEventDto(SecurityEvent securityEvent)
    {
        return new SecurityEventDto
        {
            Id = securityEvent.Id,
            EventId = securityEvent.EventId,
            EventType = securityEvent.EventType,
            PlayerId = securityEvent.PlayerId,
            IpAddress = securityEvent.IpAddress,
            Description = securityEvent.Description,
            RiskLevel = securityEvent.RiskLevel,
            Status = securityEvent.Status,
            HandledByUserId = securityEvent.HandledByUserId,
            HandledByUserName = securityEvent.HandledByUser?.Username,
            HandledAt = securityEvent.HandledAt,
            HandlingNotes = securityEvent.HandlingNotes,
            Evidence = securityEvent.Evidence,
            IsAutoDetected = securityEvent.IsAutoDetected,
            CreatedAt = securityEvent.CreatedAt
        };
    }

    // Risk Detection
    public async Task<RiskAssessmentDto> AssessPlayerRiskAsync(string playerId)
    {
        try
        {
            // Get player information
            var player = await _context.Players.FirstOrDefaultAsync(p => p.AccountId == playerId);
            if (player == null)
                throw new ArgumentException($"Player with ID {playerId} not found");

            // Get recent security events for this player
            var recentEvents = await _context.SecurityEvents
                .Include(se => se.HandledByUser)
                .Where(se => se.PlayerId == playerId && se.CreatedAt >= DateTime.UtcNow.AddDays(-30))
                .OrderByDescending(se => se.CreatedAt)
                .Take(10)
                .ToListAsync();

            // Calculate risk factors
            var riskFactors = new List<RiskFactorDto>();
            double totalRiskScore = 0;

            // Factor 1: Recent security events
            if (recentEvents.Any())
            {
                var criticalEvents = recentEvents.Count(e => e.RiskLevel == SecurityRiskLevel.Critical);
                var highEvents = recentEvents.Count(e => e.RiskLevel == SecurityRiskLevel.High);

                var eventScore = (criticalEvents * 10) + (highEvents * 5) + (recentEvents.Count * 2);
                totalRiskScore += eventScore;

                riskFactors.Add(new RiskFactorDto
                {
                    Name = "Recent Security Events",
                    Description = $"{recentEvents.Count} events in last 30 days ({criticalEvents} critical, {highEvents} high)",
                    Score = eventScore,
                    Level = eventScore > 20 ? SecurityRiskLevel.Critical :
                           eventScore > 10 ? SecurityRiskLevel.High :
                           eventScore > 5 ? SecurityRiskLevel.Medium : SecurityRiskLevel.Low
                });
            }

            // Factor 2: Account age (newer accounts are riskier)
            var accountAge = DateTime.UtcNow - player.CreatedAt;
            var ageScore = accountAge.TotalDays < 7 ? 15 :
                          accountAge.TotalDays < 30 ? 10 :
                          accountAge.TotalDays < 90 ? 5 : 0;
            totalRiskScore += ageScore;

            if (ageScore > 0)
            {
                riskFactors.Add(new RiskFactorDto
                {
                    Name = "Account Age",
                    Description = $"Account created {accountAge.TotalDays:F0} days ago",
                    Score = ageScore,
                    Level = ageScore >= 15 ? SecurityRiskLevel.High :
                           ageScore >= 10 ? SecurityRiskLevel.Medium : SecurityRiskLevel.Low
                });
            }

            // Factor 3: Ban history
            if (player.IsBanned || player.BannedUntil.HasValue)
            {
                var banScore = 20;
                totalRiskScore += banScore;

                riskFactors.Add(new RiskFactorDto
                {
                    Name = "Ban History",
                    Description = player.IsBanned ? "Currently banned" : "Previously banned",
                    Score = banScore,
                    Level = SecurityRiskLevel.Critical
                });
            }

            // Determine overall risk level
            var overallRiskLevel = totalRiskScore >= 50 ? SecurityRiskLevel.Critical :
                                  totalRiskScore >= 30 ? SecurityRiskLevel.High :
                                  totalRiskScore >= 15 ? SecurityRiskLevel.Medium : SecurityRiskLevel.Low;

            return new RiskAssessmentDto
            {
                PlayerId = playerId,
                PlayerName = player.Nickname,
                OverallRiskLevel = overallRiskLevel,
                RiskScore = totalRiskScore,
                RiskFactors = riskFactors,
                RecentEvents = recentEvents.Select(MapToSecurityEventDto).ToList(),
                AssessmentDate = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assessing player risk for {PlayerId}", playerId);
            throw;
        }
    }

    public async Task<IEnumerable<SecurityEventDto>> DetectSuspiciousActivitiesAsync()
    {
        try
        {
            var suspiciousEvents = new List<SecurityEvent>();

            // Detection 1: Multiple failed logins from same IP
            var recentFailedLogins = await _context.SecurityEvents
                .Where(se => se.EventType == SecurityEventType.MultipleFailedLogins &&
                           se.CreatedAt >= DateTime.UtcNow.AddHours(-1))
                .GroupBy(se => se.IpAddress)
                .Where(g => g.Count() >= 5)
                .ToListAsync();

            foreach (var group in recentFailedLogins)
            {
                var existingEvent = await _context.SecurityEvents
                    .FirstOrDefaultAsync(se => se.IpAddress == group.Key &&
                                             se.EventType == SecurityEventType.MultipleFailedLogins &&
                                             se.CreatedAt >= DateTime.UtcNow.AddMinutes(-30));

                if (existingEvent == null)
                {
                    suspiciousEvents.Add(new SecurityEvent
                    {
                        EventId = Guid.NewGuid().ToString(),
                        EventType = SecurityEventType.MultipleFailedLogins,
                        IpAddress = group.Key,
                        Description = $"Multiple failed login attempts from IP {group.Key}",
                        RiskLevel = SecurityRiskLevel.High,
                        Status = SecurityEventStatus.Pending,
                        IsAutoDetected = true,
                        CreatedAt = DateTime.UtcNow
                    });
                }
            }

            // Detection 2: Unusual payment patterns (placeholder - would need payment data)
            // This would analyze payment amounts, frequencies, etc.

            // Save detected events
            if (suspiciousEvents.Any())
            {
                _context.SecurityEvents.AddRange(suspiciousEvents);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Detected {Count} suspicious activities", suspiciousEvents.Count);
            }

            return suspiciousEvents.Select(MapToSecurityEventDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting suspicious activities");
            throw;
        }
    }

    public async Task<bool> TriggerSecurityRuleAsync(int ruleId, string playerId, string evidence)
    {
        try
        {
            var rule = await _context.SecurityRules.FindAsync(ruleId);
            if (rule == null || !rule.IsActive)
                return false;

            // Create security event based on rule
            var securityEvent = new SecurityEvent
            {
                EventId = Guid.NewGuid().ToString(),
                EventType = rule.EventType,
                PlayerId = playerId,
                Description = $"Security rule '{rule.Name}' triggered",
                RiskLevel = SecurityRiskLevel.Medium, // Default, could be determined by rule
                Status = SecurityEventStatus.Pending,
                Evidence = evidence,
                IsAutoDetected = true,
                CreatedAt = DateTime.UtcNow
            };

            _context.SecurityEvents.Add(securityEvent);

            // Update rule statistics
            rule.LastTriggeredAt = DateTime.UtcNow;
            rule.TriggerCount++;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Security rule {RuleId} triggered for player {PlayerId}", ruleId, playerId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error triggering security rule {RuleId}", ruleId);
            return false;
        }
    }

    private SecurityRuleDto MapToSecurityRuleDto(SecurityRule securityRule)
    {
        return new SecurityRuleDto
        {
            Id = securityRule.Id,
            Name = securityRule.Name,
            Description = securityRule.Description,
            EventType = securityRule.EventType,
            Conditions = securityRule.Conditions,
            Actions = securityRule.Actions,
            IsActive = securityRule.IsActive,
            Priority = securityRule.Priority,
            LastTriggeredAt = securityRule.LastTriggeredAt,
            TriggerCount = securityRule.TriggerCount,
            CreatedAt = securityRule.CreatedAt
        };
    }
}
