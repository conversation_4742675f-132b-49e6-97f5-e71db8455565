using GameManagement.Core.Interfaces;
using GameManagement.Core.Entities;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using GameManagement.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace GameManagement.Infrastructure.Services;

public partial class ServerMonitoringService : IServerMonitoringService
{
    private readonly GameManagementDbContext _context;
    private readonly ILogger<ServerMonitoringService> _logger;

    public ServerMonitoringService(
        GameManagementDbContext context,
        ILogger<ServerMonitoringService> logger)
    {
        _context = context;
        _logger = logger;
    }

    // 基础监控
    public async Task<IEnumerable<ServerStatusDto>> GetAllServerStatusAsync()
    {
        try
        {
            var servers = await _context.GameServers
                .Include(s => s.ServerMonitorings.OrderByDescending(m => m.Timestamp).Take(1))
                .ToListAsync();

            return servers.Select(s => new ServerStatusDto
            {
                Id = s.Id,
                Name = s.Name,
                Host = s.Host,
                Port = s.Port,
                Status = s.Status,
                MaxPlayers = s.MaxPlayers,
                CurrentPlayers = s.CurrentPlayers,
                Version = s.Version,
                LastPing = s.ServerMonitorings.FirstOrDefault()?.Timestamp,
                ResponseTime = s.ServerMonitorings.FirstOrDefault()?.ResponseTime ?? 0
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all server status");
            throw;
        }
    }

    public async Task<ServerStatusDto?> GetServerStatusAsync(int serverId)
    {
        try
        {
            var server = await _context.GameServers
                .Include(s => s.ServerMonitorings.OrderByDescending(m => m.Timestamp).Take(1))
                .FirstOrDefaultAsync(s => s.Id == serverId);

            if (server == null) return null;

            return new ServerStatusDto
            {
                Id = server.Id,
                Name = server.Name,
                Host = server.Host,
                Port = server.Port,
                Status = server.Status,
                MaxPlayers = server.MaxPlayers,
                CurrentPlayers = server.CurrentPlayers,
                Version = server.Version,
                LastPing = server.ServerMonitorings.FirstOrDefault()?.Timestamp,
                ResponseTime = server.ServerMonitorings.FirstOrDefault()?.ResponseTime ?? 0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server status for server {ServerId}", serverId);
            throw;
        }
    }

    public async Task<bool> UpdateServerStatusAsync(int serverId, ServerStatus status)
    {
        try
        {
            var server = await _context.GameServers.FindAsync(serverId);
            if (server == null) return false;

            server.Status = status;
            server.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating server status for server {ServerId}", serverId);
            return false;
        }
    }

    public async Task<ServerHealthDto> GetServerHealthAsync(int serverId)
    {
        try
        {
            var server = await _context.GameServers
                .Include(s => s.ServerMonitorings.OrderByDescending(m => m.Timestamp).Take(10))
                .Include(s => s.HealthChecks.OrderByDescending(h => h.CheckTime).Take(5))
                .FirstOrDefaultAsync(s => s.Id == serverId);

            if (server == null)
                throw new ArgumentException($"Server with ID {serverId} not found");

            var latestMonitoring = server.ServerMonitorings.FirstOrDefault();
            var latestHealthCheck = server.HealthChecks.FirstOrDefault();

            return new ServerHealthDto
            {
                ServerId = server.Id,
                ServerName = server.Name,
                IsHealthy = latestMonitoring?.IsHealthy ?? false,
                Status = server.Status.ToString(),
                CpuUsage = latestMonitoring?.CpuUsage ?? 0,
                MemoryUsage = latestMonitoring?.MemoryUsage ?? 0,
                DiskUsage = latestMonitoring?.DiskUsage ?? 0,
                ResponseTime = latestMonitoring?.ResponseTime ?? 0,
                ActiveConnections = latestMonitoring?.ActiveConnections ?? 0,
                LastCheckTime = latestHealthCheck?.CheckTime ?? DateTime.MinValue,
                Issues = server.ServerMonitorings
                    .Where(m => !m.IsHealthy)
                    .Select(m => m.AlertMessage ?? "Unknown issue")
                    .ToList()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server health for server {ServerId}", serverId);
            throw;
        }
    }

    public async Task<IEnumerable<ServerHealthDto>> GetAllServerHealthAsync()
    {
        try
        {
            var servers = await _context.GameServers
                .Include(s => s.ServerMonitorings.OrderByDescending(m => m.Timestamp).Take(1))
                .Include(s => s.HealthChecks.OrderByDescending(h => h.CheckTime).Take(1))
                .ToListAsync();

            return servers.Select(server =>
            {
                var latestMonitoring = server.ServerMonitorings.FirstOrDefault();
                var latestHealthCheck = server.HealthChecks.FirstOrDefault();

                return new ServerHealthDto
                {
                    ServerId = server.Id,
                    ServerName = server.Name,
                    IsHealthy = latestMonitoring?.IsHealthy ?? false,
                    Status = server.Status.ToString(),
                    CpuUsage = latestMonitoring?.CpuUsage ?? 0,
                    MemoryUsage = latestMonitoring?.MemoryUsage ?? 0,
                    DiskUsage = latestMonitoring?.DiskUsage ?? 0,
                    ResponseTime = latestMonitoring?.ResponseTime ?? 0,
                    ActiveConnections = latestMonitoring?.ActiveConnections ?? 0,
                    LastCheckTime = latestHealthCheck?.CheckTime ?? DateTime.MinValue,
                    Issues = new List<string>()
                };
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all server health");
            throw;
        }
    }

    public async Task<IEnumerable<ServerLogDto>> GetServerLogsAsync(int serverId, int page, int pageSize)
    {
        try
        {
            var logs = await _context.ServerLogs
                .Where(l => l.ServerId == serverId)
                .OrderByDescending(l => l.Timestamp)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(l => new ServerLogDto
                {
                    Id = l.Id,
                    ServerId = l.ServerId,
                    Level = l.Level,
                    Message = l.Message,
                    Exception = l.Exception,
                    Source = l.Source,
                    Timestamp = l.Timestamp
                })
                .ToListAsync();

            return logs;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server logs for server {ServerId}", serverId);
            throw;
        }
    }

    public async Task<bool> RestartServerAsync(int serverId)
    {
        try
        {
            var server = await _context.GameServers.FindAsync(serverId);
            if (server == null) return false;

            // 记录重启操作
            var log = new ServerLog
            {
                ServerId = serverId,
                Level = GameManagement.Shared.Enums.LogLevel.Info,
                Message = "Server restart initiated",
                Source = "ServerMonitoringService",
                Timestamp = DateTime.UtcNow
            };

            _context.ServerLogs.Add(log);

            // 更新服务器状态
            server.Status = ServerStatus.Maintenance;
            server.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            // 这里应该调用实际的服务器重启逻辑
            // 暂时模拟重启过程
            await Task.Delay(1000);

            server.Status = ServerStatus.Online;
            await _context.SaveChangesAsync();

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error restarting server {ServerId}", serverId);
            return false;
        }
    }

    public async Task<bool> SendServerCommandAsync(int serverId, string command)
    {
        try
        {
            var server = await _context.GameServers.FindAsync(serverId);
            if (server == null) return false;

            // 记录命令执行
            var log = new ServerLog
            {
                ServerId = serverId,
                Level = GameManagement.Shared.Enums.LogLevel.Info,
                Message = $"Command executed: {command}",
                Source = "ServerMonitoringService",
                Timestamp = DateTime.UtcNow
            };

            _context.ServerLogs.Add(log);
            await _context.SaveChangesAsync();

            // 这里应该调用实际的命令执行逻辑
            _logger.LogInformation("Command '{Command}' sent to server {ServerId}", command, serverId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending command to server {ServerId}", serverId);
            return false;
        }
    }

    // 性能监控
    public async Task<ServerMonitoringDto> AddMonitoringDataAsync(CreateServerMonitoringDto createMonitoringDto)
    {
        try
        {
            var server = await _context.GameServers.FindAsync(createMonitoringDto.ServerId);
            if (server == null)
                throw new ArgumentException($"Server with ID {createMonitoringDto.ServerId} not found");

            var monitoring = new ServerMonitoring
            {
                ServerId = createMonitoringDto.ServerId,
                Timestamp = DateTime.UtcNow,
                CpuUsage = createMonitoringDto.CpuUsage,
                MemoryUsage = createMonitoringDto.MemoryUsage,
                DiskUsage = createMonitoringDto.DiskUsage,
                NetworkIn = createMonitoringDto.NetworkIn,
                NetworkOut = createMonitoringDto.NetworkOut,
                ActiveConnections = createMonitoringDto.ActiveConnections,
                ResponseTime = createMonitoringDto.ResponseTime,
                IsHealthy = createMonitoringDto.IsHealthy,
                AlertMessage = createMonitoringDto.AlertMessage
            };

            _context.ServerMonitorings.Add(monitoring);
            await _context.SaveChangesAsync();

            return new ServerMonitoringDto
            {
                Id = monitoring.Id,
                ServerId = monitoring.ServerId,
                ServerName = server.Name,
                Timestamp = monitoring.Timestamp,
                CpuUsage = monitoring.CpuUsage,
                MemoryUsage = monitoring.MemoryUsage,
                DiskUsage = monitoring.DiskUsage,
                NetworkIn = monitoring.NetworkIn,
                NetworkOut = monitoring.NetworkOut,
                ActiveConnections = monitoring.ActiveConnections,
                ResponseTime = monitoring.ResponseTime,
                IsHealthy = monitoring.IsHealthy,
                AlertMessage = monitoring.AlertMessage
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding monitoring data for server {ServerId}", createMonitoringDto.ServerId);
            throw;
        }
    }

    public async Task<IEnumerable<ServerMonitoringDto>> GetMonitoringDataAsync(int serverId, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var query = _context.ServerMonitorings
                .Include(m => m.Server)
                .Where(m => m.ServerId == serverId);

            if (startDate.HasValue)
                query = query.Where(m => m.Timestamp >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(m => m.Timestamp <= endDate.Value);

            var monitoringData = await query
                .OrderByDescending(m => m.Timestamp)
                .Take(1000) // 限制返回数量
                .ToListAsync();

            return monitoringData.Select(m => new ServerMonitoringDto
            {
                Id = m.Id,
                ServerId = m.ServerId,
                ServerName = m.Server.Name,
                Timestamp = m.Timestamp,
                CpuUsage = m.CpuUsage,
                MemoryUsage = m.MemoryUsage,
                DiskUsage = m.DiskUsage,
                NetworkIn = m.NetworkIn,
                NetworkOut = m.NetworkOut,
                ActiveConnections = m.ActiveConnections,
                ResponseTime = m.ResponseTime,
                IsHealthy = m.IsHealthy,
                AlertMessage = m.AlertMessage
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting monitoring data for server {ServerId}", serverId);
            throw;
        }
    }

    public async Task<ServerPerformanceDto> GetServerPerformanceAsync(int serverId, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var server = await _context.GameServers.FindAsync(serverId);
            if (server == null)
                throw new ArgumentException($"Server with ID {serverId} not found");

            var start = startDate ?? DateTime.UtcNow.AddDays(-7);
            var end = endDate ?? DateTime.UtcNow;

            var monitoringData = await _context.ServerMonitorings
                .Where(m => m.ServerId == serverId && m.Timestamp >= start && m.Timestamp <= end)
                .ToListAsync();

            if (!monitoringData.Any())
            {
                return new ServerPerformanceDto
                {
                    ServerId = serverId,
                    ServerName = server.Name,
                    StartDate = start,
                    EndDate = end
                };
            }

            var alertCount = await _context.ServerAlerts
                .Where(a => a.ServerId == serverId && a.CreatedAt >= start && a.CreatedAt <= end)
                .CountAsync();

            var criticalAlertCount = await _context.ServerAlerts
                .Where(a => a.ServerId == serverId && a.CreatedAt >= start && a.CreatedAt <= end && a.Severity == AlertSeverity.Critical)
                .CountAsync();

            return new ServerPerformanceDto
            {
                ServerId = serverId,
                ServerName = server.Name,
                StartDate = start,
                EndDate = end,
                AverageCpuUsage = monitoringData.Average(m => m.CpuUsage),
                MaxCpuUsage = monitoringData.Max(m => m.CpuUsage),
                AverageMemoryUsage = monitoringData.Average(m => m.MemoryUsage),
                MaxMemoryUsage = monitoringData.Max(m => m.MemoryUsage),
                AverageDiskUsage = monitoringData.Average(m => m.DiskUsage),
                MaxDiskUsage = monitoringData.Max(m => m.DiskUsage),
                AverageResponseTime = monitoringData.Average(m => m.ResponseTime),
                MaxResponseTime = monitoringData.Max(m => m.ResponseTime),
                TotalConnections = monitoringData.Sum(m => m.ActiveConnections),
                MaxConcurrentConnections = monitoringData.Max(m => m.ActiveConnections),
                UptimePercentage = monitoringData.Count(m => m.IsHealthy) * 100.0 / monitoringData.Count,
                AlertCount = alertCount,
                CriticalAlertCount = criticalAlertCount
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server performance for server {ServerId}", serverId);
            throw;
        }
    }

    public async Task<IEnumerable<ServerPerformanceDto>> GetAllServerPerformanceAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var servers = await _context.GameServers.ToListAsync();
            var performances = new List<ServerPerformanceDto>();

            foreach (var server in servers)
            {
                var performance = await GetServerPerformanceAsync(server.Id, startDate, endDate);
                performances.Add(performance);
            }

            return performances;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all server performance");
            throw;
        }
    }

    // 告警管理
    public async Task<IEnumerable<ServerAlertDto>> GetActiveAlertsAsync()
    {
        try
        {
            var alerts = await _context.ServerAlerts
                .Include(a => a.Server)
                .Where(a => a.Status == AlertStatus.Active || a.Status == AlertStatus.Acknowledged)
                .OrderByDescending(a => a.CreatedAt)
                .ToListAsync();

            return alerts.Select(a => new ServerAlertDto
            {
                Id = a.Id,
                ServerId = a.ServerId,
                ServerName = a.Server.Name,
                AlertType = a.AlertType,
                Title = a.Title,
                Message = a.Message,
                Severity = a.Severity,
                Status = a.Status,
                CreatedAt = a.CreatedAt,
                AcknowledgedAt = a.AcknowledgedAt,
                AcknowledgedBy = a.AcknowledgedBy,
                ResolvedAt = a.ResolvedAt,
                ResolvedBy = a.ResolvedBy,
                Resolution = a.Resolution,
                Metadata = string.IsNullOrEmpty(a.Metadata) ? new Dictionary<string, object>() :
                    System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(a.Metadata) ?? new Dictionary<string, object>()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active alerts");
            throw;
        }
    }

    public async Task<IEnumerable<ServerAlertDto>> GetServerAlertsAsync(int serverId)
    {
        try
        {
            var alerts = await _context.ServerAlerts
                .Include(a => a.Server)
                .Where(a => a.ServerId == serverId)
                .OrderByDescending(a => a.CreatedAt)
                .Take(100)
                .ToListAsync();

            return alerts.Select(a => new ServerAlertDto
            {
                Id = a.Id,
                ServerId = a.ServerId,
                ServerName = a.Server.Name,
                AlertType = a.AlertType,
                Title = a.Title,
                Message = a.Message,
                Severity = a.Severity,
                Status = a.Status,
                CreatedAt = a.CreatedAt,
                AcknowledgedAt = a.AcknowledgedAt,
                AcknowledgedBy = a.AcknowledgedBy,
                ResolvedAt = a.ResolvedAt,
                ResolvedBy = a.ResolvedBy,
                Resolution = a.Resolution,
                Metadata = string.IsNullOrEmpty(a.Metadata) ? new Dictionary<string, object>() :
                    System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(a.Metadata) ?? new Dictionary<string, object>()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting alerts for server {ServerId}", serverId);
            throw;
        }
    }

    public async Task<ServerAlertDto> CreateAlertAsync(CreateServerAlertDto createAlertDto)
    {
        try
        {
            var server = await _context.GameServers.FindAsync(createAlertDto.ServerId);
            if (server == null)
                throw new ArgumentException($"Server with ID {createAlertDto.ServerId} not found");

            var alert = new ServerAlert
            {
                ServerId = createAlertDto.ServerId,
                AlertType = createAlertDto.AlertType,
                Title = createAlertDto.Title,
                Message = createAlertDto.Message,
                Severity = createAlertDto.Severity,
                Status = AlertStatus.Active,
                Metadata = System.Text.Json.JsonSerializer.Serialize(createAlertDto.Metadata),
                CreatedAt = DateTime.UtcNow
            };

            _context.ServerAlerts.Add(alert);
            await _context.SaveChangesAsync();

            return new ServerAlertDto
            {
                Id = alert.Id,
                ServerId = alert.ServerId,
                ServerName = server.Name,
                AlertType = alert.AlertType,
                Title = alert.Title,
                Message = alert.Message,
                Severity = alert.Severity,
                Status = alert.Status,
                CreatedAt = alert.CreatedAt,
                Metadata = createAlertDto.Metadata
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating alert for server {ServerId}", createAlertDto.ServerId);
            throw;
        }
    }

    public async Task<bool> AcknowledgeAlertAsync(int alertId, string acknowledgedBy)
    {
        try
        {
            var alert = await _context.ServerAlerts.FindAsync(alertId);
            if (alert == null) return false;

            alert.Status = AlertStatus.Acknowledged;
            alert.AcknowledgedAt = DateTime.UtcNow;
            alert.AcknowledgedBy = acknowledgedBy;
            alert.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error acknowledging alert {AlertId}", alertId);
            return false;
        }
    }

    public async Task<bool> ResolveAlertAsync(int alertId, string resolvedBy, string resolution)
    {
        try
        {
            var alert = await _context.ServerAlerts.FindAsync(alertId);
            if (alert == null) return false;

            alert.Status = AlertStatus.Resolved;
            alert.ResolvedAt = DateTime.UtcNow;
            alert.ResolvedBy = resolvedBy;
            alert.Resolution = resolution;
            alert.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving alert {AlertId}", alertId);
            return false;
        }
    }

    public async Task<bool> DeleteAlertAsync(int alertId)
    {
        try
        {
            var alert = await _context.ServerAlerts.FindAsync(alertId);
            if (alert == null) return false;

            _context.ServerAlerts.Remove(alert);
            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting alert {AlertId}", alertId);
            return false;
        }
    }

    // 健康检查
    public async Task<bool> PerformHealthCheckAsync(int serverId)
    {
        try
        {
            var server = await _context.GameServers.FindAsync(serverId);
            if (server == null) return false;

            // 模拟健康检查
            var isHealthy = server.Status == ServerStatus.Online;
            var responseTime = Random.Shared.NextDouble() * 100; // 模拟响应时间

            var healthCheck = new HealthCheck
            {
                ServerId = serverId,
                CheckType = "General",
                IsHealthy = isHealthy,
                Status = isHealthy ? "Healthy" : "Unhealthy",
                Message = isHealthy ? "Server is running normally" : "Server is not responding",
                ResponseTime = responseTime,
                CheckTime = DateTime.UtcNow,
                Details = System.Text.Json.JsonSerializer.Serialize(new {
                    ServerStatus = server.Status.ToString(),
                    CheckedAt = DateTime.UtcNow
                })
            };

            _context.HealthChecks.Add(healthCheck);
            await _context.SaveChangesAsync();

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing health check for server {ServerId}", serverId);
            return false;
        }
    }

    public async Task<IEnumerable<HealthCheckResultDto>> PerformAllHealthChecksAsync()
    {
        try
        {
            var servers = await _context.GameServers.ToListAsync();
            var results = new List<HealthCheckResultDto>();

            foreach (var server in servers)
            {
                await PerformHealthCheckAsync(server.Id);

                var latestCheck = await _context.HealthChecks
                    .Where(h => h.ServerId == server.Id)
                    .OrderByDescending(h => h.CheckTime)
                    .FirstOrDefaultAsync();

                if (latestCheck != null)
                {
                    results.Add(new HealthCheckResultDto
                    {
                        Id = latestCheck.Id,
                        ServerId = latestCheck.ServerId,
                        ServerName = server.Name,
                        CheckType = latestCheck.CheckType,
                        IsHealthy = latestCheck.IsHealthy,
                        Status = latestCheck.Status,
                        Message = latestCheck.Message,
                        ResponseTime = latestCheck.ResponseTime,
                        CheckTime = latestCheck.CheckTime,
                        Details = string.IsNullOrEmpty(latestCheck.Details) ? new Dictionary<string, object>() :
                            System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(latestCheck.Details) ?? new Dictionary<string, object>()
                    });
                }
            }

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing all health checks");
            throw;
        }
    }

    public async Task<HealthCheckResultDto> GetLatestHealthCheckAsync(int serverId)
    {
        try
        {
            var server = await _context.GameServers.FindAsync(serverId);
            if (server == null)
                throw new ArgumentException($"Server with ID {serverId} not found");

            var latestCheck = await _context.HealthChecks
                .Where(h => h.ServerId == serverId)
                .OrderByDescending(h => h.CheckTime)
                .FirstOrDefaultAsync();

            if (latestCheck == null)
            {
                // 如果没有健康检查记录，执行一次检查
                await PerformHealthCheckAsync(serverId);
                latestCheck = await _context.HealthChecks
                    .Where(h => h.ServerId == serverId)
                    .OrderByDescending(h => h.CheckTime)
                    .FirstOrDefaultAsync();
            }

            return new HealthCheckResultDto
            {
                Id = latestCheck!.Id,
                ServerId = latestCheck.ServerId,
                ServerName = server.Name,
                CheckType = latestCheck.CheckType,
                IsHealthy = latestCheck.IsHealthy,
                Status = latestCheck.Status,
                Message = latestCheck.Message,
                ResponseTime = latestCheck.ResponseTime,
                CheckTime = latestCheck.CheckTime,
                Details = string.IsNullOrEmpty(latestCheck.Details) ? new Dictionary<string, object>() :
                    System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(latestCheck.Details) ?? new Dictionary<string, object>()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting latest health check for server {ServerId}", serverId);
            throw;
        }
    }

    // 监控统计
    public async Task<MonitoringStatsDto> GetMonitoringStatsAsync()
    {
        try
        {
            var totalServers = await _context.GameServers.CountAsync();
            var healthyServers = await _context.GameServers.CountAsync(s => s.Status == ServerStatus.Online);
            var unhealthyServers = totalServers - healthyServers;

            var activeAlerts = await _context.ServerAlerts
                .CountAsync(a => a.Status == AlertStatus.Active || a.Status == AlertStatus.Acknowledged);

            var criticalAlerts = await _context.ServerAlerts
                .CountAsync(a => (a.Status == AlertStatus.Active || a.Status == AlertStatus.Acknowledged) && a.Severity == AlertSeverity.Critical);

            // 获取最近的监控数据来计算平均值
            var recentMonitoring = await _context.ServerMonitorings
                .Where(m => m.Timestamp >= DateTime.UtcNow.AddHours(-1))
                .ToListAsync();

            var avgCpu = recentMonitoring.Any() ? recentMonitoring.Average(m => m.CpuUsage) : 0;
            var avgMemory = recentMonitoring.Any() ? recentMonitoring.Average(m => m.MemoryUsage) : 0;
            var avgDisk = recentMonitoring.Any() ? recentMonitoring.Average(m => m.DiskUsage) : 0;

            return new MonitoringStatsDto
            {
                TotalServers = totalServers,
                HealthyServers = healthyServers,
                UnhealthyServers = unhealthyServers,
                ActiveAlerts = activeAlerts,
                CriticalAlerts = criticalAlerts,
                AverageCpuUsage = avgCpu,
                AverageMemoryUsage = avgMemory,
                AverageDiskUsage = avgDisk,
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting monitoring stats");
            throw;
        }
    }

    public async Task<Dictionary<string, object>> GetServerMetricsAsync(int serverId, string metricType, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var start = startDate ?? DateTime.UtcNow.AddDays(-1);
            var end = endDate ?? DateTime.UtcNow;

            var monitoringData = await _context.ServerMonitorings
                .Where(m => m.ServerId == serverId && m.Timestamp >= start && m.Timestamp <= end)
                .OrderBy(m => m.Timestamp)
                .ToListAsync();

            var metrics = new Dictionary<string, object>();

            switch (metricType.ToLower())
            {
                case "cpu":
                    metrics["values"] = monitoringData.Select(m => new { timestamp = m.Timestamp, value = m.CpuUsage }).ToList();
                    metrics["average"] = monitoringData.Any() ? monitoringData.Average(m => m.CpuUsage) : 0;
                    metrics["max"] = monitoringData.Any() ? monitoringData.Max(m => m.CpuUsage) : 0;
                    break;
                case "memory":
                    metrics["values"] = monitoringData.Select(m => new { timestamp = m.Timestamp, value = m.MemoryUsage }).ToList();
                    metrics["average"] = monitoringData.Any() ? monitoringData.Average(m => m.MemoryUsage) : 0;
                    metrics["max"] = monitoringData.Any() ? monitoringData.Max(m => m.MemoryUsage) : 0;
                    break;
                case "disk":
                    metrics["values"] = monitoringData.Select(m => new { timestamp = m.Timestamp, value = m.DiskUsage }).ToList();
                    metrics["average"] = monitoringData.Any() ? monitoringData.Average(m => m.DiskUsage) : 0;
                    metrics["max"] = monitoringData.Any() ? monitoringData.Max(m => m.DiskUsage) : 0;
                    break;
                case "network":
                    metrics["values"] = monitoringData.Select(m => new {
                        timestamp = m.Timestamp,
                        networkIn = m.NetworkIn,
                        networkOut = m.NetworkOut
                    }).ToList();
                    metrics["averageIn"] = monitoringData.Any() ? monitoringData.Average(m => m.NetworkIn) : 0;
                    metrics["averageOut"] = monitoringData.Any() ? monitoringData.Average(m => m.NetworkOut) : 0;
                    break;
                case "connections":
                    metrics["values"] = monitoringData.Select(m => new { timestamp = m.Timestamp, value = m.ActiveConnections }).ToList();
                    metrics["average"] = monitoringData.Any() ? monitoringData.Average(m => m.ActiveConnections) : 0;
                    metrics["max"] = monitoringData.Any() ? monitoringData.Max(m => m.ActiveConnections) : 0;
                    break;
                case "response":
                    metrics["values"] = monitoringData.Select(m => new { timestamp = m.Timestamp, value = m.ResponseTime }).ToList();
                    metrics["average"] = monitoringData.Any() ? monitoringData.Average(m => m.ResponseTime) : 0;
                    metrics["max"] = monitoringData.Any() ? monitoringData.Max(m => m.ResponseTime) : 0;
                    break;
                default:
                    throw new ArgumentException($"Unknown metric type: {metricType}");
            }

            metrics["startDate"] = start;
            metrics["endDate"] = end;
            metrics["dataPoints"] = monitoringData.Count;

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server metrics for server {ServerId}, metric {MetricType}", serverId, metricType);
            throw;
        }
    }
}
