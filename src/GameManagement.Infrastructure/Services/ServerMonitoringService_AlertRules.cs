using GameManagement.Core.Interfaces;
using GameManagement.Core.Entities;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using GameManagement.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace GameManagement.Infrastructure.Services;

public partial class ServerMonitoringService
{
    // 告警规则管理
    public async Task<IEnumerable<AlertRuleDto>> GetAlertRulesAsync()
    {
        try
        {
            var rules = await _context.AlertRules
                .Include(r => r.Server)
                .OrderBy(r => r.Name)
                .ToListAsync();

            return rules.Select(r => new AlertRuleDto
            {
                Id = r.Id,
                Name = r.Name,
                Description = r.Description,
                MetricType = r.MetricType,
                Condition = r.Condition,
                Threshold = r.Threshold,
                Severity = r.Severity,
                IsEnabled = r.IsEnabled,
                ServerId = r.ServerId,
                ServerName = r.Server?.Name,
                CreatedAt = r.CreatedAt,
                UpdatedAt = r.UpdatedAt
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting alert rules");
            throw;
        }
    }

    public async Task<AlertRuleDto?> GetAlertRuleByIdAsync(int ruleId)
    {
        try
        {
            var rule = await _context.AlertRules
                .Include(r => r.Server)
                .FirstOrDefaultAsync(r => r.Id == ruleId);

            if (rule == null) return null;

            return new AlertRuleDto
            {
                Id = rule.Id,
                Name = rule.Name,
                Description = rule.Description,
                MetricType = rule.MetricType,
                Condition = rule.Condition,
                Threshold = rule.Threshold,
                Severity = rule.Severity,
                IsEnabled = rule.IsEnabled,
                ServerId = rule.ServerId,
                ServerName = rule.Server?.Name,
                CreatedAt = rule.CreatedAt,
                UpdatedAt = rule.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting alert rule {RuleId}", ruleId);
            throw;
        }
    }

    public async Task<AlertRuleDto> CreateAlertRuleAsync(CreateAlertRuleDto createRuleDto)
    {
        try
        {
            // 验证服务器存在（如果指定了服务器）
            if (createRuleDto.ServerId.HasValue)
            {
                var existingServer = await _context.GameServers.FindAsync(createRuleDto.ServerId.Value);
                if (existingServer == null)
                    throw new ArgumentException($"Server with ID {createRuleDto.ServerId.Value} not found");
            }

            var rule = new AlertRule
            {
                Name = createRuleDto.Name,
                Description = createRuleDto.Description,
                MetricType = createRuleDto.MetricType,
                Condition = createRuleDto.Condition,
                Threshold = createRuleDto.Threshold,
                Severity = createRuleDto.Severity,
                IsEnabled = createRuleDto.IsEnabled,
                ServerId = createRuleDto.ServerId,
                CreatedAt = DateTime.UtcNow
            };

            _context.AlertRules.Add(rule);
            await _context.SaveChangesAsync();

            var server = createRuleDto.ServerId.HasValue ?
                await _context.GameServers.FindAsync(createRuleDto.ServerId.Value) : null;

            return new AlertRuleDto
            {
                Id = rule.Id,
                Name = rule.Name,
                Description = rule.Description,
                MetricType = rule.MetricType,
                Condition = rule.Condition,
                Threshold = rule.Threshold,
                Severity = rule.Severity,
                IsEnabled = rule.IsEnabled,
                ServerId = rule.ServerId,
                ServerName = server?.Name,
                CreatedAt = rule.CreatedAt,
                UpdatedAt = rule.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating alert rule");
            throw;
        }
    }

    public async Task<AlertRuleDto?> UpdateAlertRuleAsync(int ruleId, UpdateAlertRuleDto updateRuleDto)
    {
        try
        {
            var rule = await _context.AlertRules.FindAsync(ruleId);
            if (rule == null) return null;

            // 验证服务器存在（如果指定了服务器）
            if (updateRuleDto.ServerId.HasValue)
            {
                var existingServer = await _context.GameServers.FindAsync(updateRuleDto.ServerId.Value);
                if (existingServer == null)
                    throw new ArgumentException($"Server with ID {updateRuleDto.ServerId.Value} not found");
            }

            // 更新字段
            if (!string.IsNullOrEmpty(updateRuleDto.Name))
                rule.Name = updateRuleDto.Name;
            
            if (updateRuleDto.Description != null)
                rule.Description = updateRuleDto.Description;
            
            if (!string.IsNullOrEmpty(updateRuleDto.MetricType))
                rule.MetricType = updateRuleDto.MetricType;
            
            if (!string.IsNullOrEmpty(updateRuleDto.Condition))
                rule.Condition = updateRuleDto.Condition;
            
            if (updateRuleDto.Threshold.HasValue)
                rule.Threshold = updateRuleDto.Threshold.Value;
            
            if (updateRuleDto.Severity.HasValue)
                rule.Severity = updateRuleDto.Severity.Value;
            
            if (updateRuleDto.IsEnabled.HasValue)
                rule.IsEnabled = updateRuleDto.IsEnabled.Value;
            
            if (updateRuleDto.ServerId.HasValue)
                rule.ServerId = updateRuleDto.ServerId.Value;

            rule.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            var server = rule.ServerId.HasValue ? 
                await _context.GameServers.FindAsync(rule.ServerId.Value) : null;

            return new AlertRuleDto
            {
                Id = rule.Id,
                Name = rule.Name,
                Description = rule.Description,
                MetricType = rule.MetricType,
                Condition = rule.Condition,
                Threshold = rule.Threshold,
                Severity = rule.Severity,
                IsEnabled = rule.IsEnabled,
                ServerId = rule.ServerId,
                ServerName = server?.Name,
                CreatedAt = rule.CreatedAt,
                UpdatedAt = rule.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating alert rule {RuleId}", ruleId);
            throw;
        }
    }

    public async Task<bool> DeleteAlertRuleAsync(int ruleId)
    {
        try
        {
            var rule = await _context.AlertRules.FindAsync(ruleId);
            if (rule == null) return false;

            _context.AlertRules.Remove(rule);
            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting alert rule {RuleId}", ruleId);
            return false;
        }
    }

    public async Task<bool> ToggleAlertRuleAsync(int ruleId, bool isEnabled)
    {
        try
        {
            var rule = await _context.AlertRules.FindAsync(ruleId);
            if (rule == null) return false;

            rule.IsEnabled = isEnabled;
            rule.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling alert rule {RuleId}", ruleId);
            return false;
        }
    }
}
