{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"GameManagement.Infrastructure/1.0.0": {"dependencies": {"GameManagement.Core": "1.0.0", "GameManagement.Shared": "1.0.0"}, "runtime": {"GameManagement.Infrastructure.dll": {}}}, "GameManagement.Core/1.0.0": {"dependencies": {"GameManagement.Shared": "1.0.0"}, "runtime": {"GameManagement.Core.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "GameManagement.Shared/1.0.0": {"runtime": {"GameManagement.Shared.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"GameManagement.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "GameManagement.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "GameManagement.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}