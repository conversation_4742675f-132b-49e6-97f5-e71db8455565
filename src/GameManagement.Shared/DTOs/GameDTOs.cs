using System.ComponentModel.DataAnnotations;
using GameManagement.Shared.Enums;

namespace GameManagement.Shared.DTOs;

// Player DTOs
public class PlayerDto
{
    public int Id { get; set; }
    public string AccountId { get; set; } = string.Empty;
    public string Nickname { get; set; } = string.Empty;
    public int Level { get; set; }
    public string Class { get; set; } = string.Empty;
    public long Experience { get; set; }
    public decimal Gold { get; set; }
    public int Diamonds { get; set; }
    public int VipLevel { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public TimeSpan TotalPlayTime { get; set; }
    public string? IpAddress { get; set; }
    public int ServerId { get; set; }
    public string ServerName { get; set; } = string.Empty;
    public bool IsBanned { get; set; }
    public DateTime? BannedUntil { get; set; }
    public string? BanReason { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class UpdatePlayerDto
{
    public string? Nickname { get; set; }
    public int? Level { get; set; }
    public long? Experience { get; set; }
    public decimal? Gold { get; set; }
    public int? Diamonds { get; set; }
    public int? VipLevel { get; set; }
}

public class PlayerStatsDto
{
    public int TotalPlayers { get; set; }
    public int ActivePlayers { get; set; }
    public int NewPlayersToday { get; set; }
    public int VipPlayers { get; set; }
    public decimal AverageLevel { get; set; }
    public Dictionary<string, int> LevelDistribution { get; set; } = new();
    public Dictionary<string, int> ClassDistribution { get; set; } = new();
}

// Payment DTOs
public class PaymentDto
{
    public int Id { get; set; }
    public int PlayerId { get; set; }
    public string PlayerNickname { get; set; } = string.Empty;
    public string OrderId { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public int DiamondsPurchased { get; set; }
    public PaymentStatus Status { get; set; }
    public string PaymentMethod { get; set; } = string.Empty;
    public string? TransactionId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? FailureReason { get; set; }
}

public class PaymentStatsDto
{
    public decimal TotalRevenue { get; set; }
    public decimal TodayRevenue { get; set; }
    public int TotalTransactions { get; set; }
    public int TodayTransactions { get; set; }
    public decimal AverageTransactionValue { get; set; }
    public Dictionary<string, decimal> RevenueByMethod { get; set; } = new();
    public Dictionary<string, int> TransactionsByStatus { get; set; } = new();
}

// Server DTOs
public class ServerStatusDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Host { get; set; } = string.Empty;
    public int Port { get; set; }
    public ServerStatus Status { get; set; }
    public string? Version { get; set; }
    public int MaxPlayers { get; set; }
    public int CurrentPlayers { get; set; }
    public DateTime? LastHeartbeat { get; set; }
    public string? Region { get; set; }
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public double DiskUsage { get; set; }
}

public class ServerHealthDto
{
    public int ServerId { get; set; }
    public string ServerName { get; set; } = string.Empty;
    public bool IsHealthy { get; set; }
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public double DiskUsage { get; set; }
    public double NetworkIn { get; set; }
    public double NetworkOut { get; set; }
    public int ActiveConnections { get; set; }
    public TimeSpan Uptime { get; set; }
    public DateTime LastCheck { get; set; }
}

public class ServerLogDto
{
    public int Id { get; set; }
    public int ServerId { get; set; }
    public LogLevel Level { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? Exception { get; set; }
    public string? Source { get; set; }
    public DateTime Timestamp { get; set; }
}

// Activity DTOs
public class ActivityDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ActivityStatus Status { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string? Conditions { get; set; }
    public string? Rewards { get; set; }
    public int ParticipantCount { get; set; }
    public int CompletionCount { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class CreateActivityDto
{
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [StringLength(500)]
    public string Description { get; set; } = string.Empty;
    
    [Required]
    public DateTime StartTime { get; set; }
    
    [Required]
    public DateTime EndTime { get; set; }
    
    public string? Conditions { get; set; }
    public string? Rewards { get; set; }
}

public class UpdateActivityDto
{
    [StringLength(100)]
    public string? Name { get; set; }
    
    [StringLength(500)]
    public string? Description { get; set; }
    
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public string? Conditions { get; set; }
    public string? Rewards { get; set; }
}

public class ActivityStatsDto
{
    public int ActivityId { get; set; }
    public string ActivityName { get; set; } = string.Empty;
    public int TotalParticipants { get; set; }
    public int CompletedParticipants { get; set; }
    public double CompletionRate { get; set; }
    public Dictionary<string, int> ParticipationByLevel { get; set; } = new();
}

// Announcement DTOs
public class AnnouncementDto
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public bool IsActive { get; set; }
    public string? TargetServers { get; set; }
    public string? TargetPlayers { get; set; }
    public int Priority { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class CreateAnnouncementDto
{
    [Required]
    [StringLength(200)]
    public string Title { get; set; } = string.Empty;
    
    [Required]
    public string Content { get; set; } = string.Empty;
    
    [Required]
    [StringLength(50)]
    public string Type { get; set; } = string.Empty;
    
    [Required]
    public DateTime StartTime { get; set; }
    
    public DateTime? EndTime { get; set; }
    public string? TargetServers { get; set; }
    public string? TargetPlayers { get; set; }
    public int Priority { get; set; } = 0;
}

public class UpdateAnnouncementDto
{
    [StringLength(200)]
    public string? Title { get; set; }
    
    public string? Content { get; set; }
    
    [StringLength(50)]
    public string? Type { get; set; }
    
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public string? TargetServers { get; set; }
    public string? TargetPlayers { get; set; }
    public int? Priority { get; set; }
}

// Report DTOs
public class DashboardStatsDto
{
    public int TotalPlayers { get; set; }
    public int ActivePlayers { get; set; }
    public int OnlineServers { get; set; }
    public decimal TodayRevenue { get; set; }
    public decimal MonthRevenue { get; set; }
    public int NewPlayersToday { get; set; }
    public int ActiveActivities { get; set; }
    public List<RevenueChartData> RevenueChart { get; set; } = new();
    public List<PlayerChartData> PlayerChart { get; set; } = new();
}

public class RevenueChartData
{
    public DateTime Date { get; set; }
    public decimal Revenue { get; set; }
}

public class PlayerChartData
{
    public DateTime Date { get; set; }
    public int NewPlayers { get; set; }
    public int ActivePlayers { get; set; }
}

public class RevenueReportDto
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public decimal TotalRevenue { get; set; }
    public int TotalTransactions { get; set; }
    public decimal AverageTransactionValue { get; set; }
    public Dictionary<string, decimal> RevenueByMethod { get; set; } = new();
    public Dictionary<string, decimal> RevenueByServer { get; set; } = new();
    public List<RevenueChartData> DailyRevenue { get; set; } = new();
}

public class PlayerReportDto
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int NewPlayers { get; set; }
    public int ActivePlayers { get; set; }
    public double AverageSessionTime { get; set; }
    public Dictionary<string, int> PlayersByLevel { get; set; } = new();
    public Dictionary<string, int> PlayersByServer { get; set; } = new();
    public List<PlayerChartData> DailyPlayers { get; set; } = new();
}

public class RetentionReportDto
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public double Day1Retention { get; set; }
    public double Day7Retention { get; set; }
    public double Day30Retention { get; set; }
    public Dictionary<string, double> RetentionByLevel { get; set; } = new();
    public Dictionary<string, double> RetentionByServer { get; set; } = new();
}

public class AuditLogDto
{
    public int Id { get; set; }
    public int? UserId { get; set; }
    public string? Username { get; set; }
    public string Action { get; set; } = string.Empty;
    public string EntityName { get; set; } = string.Empty;
    public string? EntityId { get; set; }
    public string? OldValues { get; set; }
    public string? NewValues { get; set; }
    public string? IpAddress { get; set; }
    public DateTime CreatedAt { get; set; }
}

// 运营数据相关DTOs
public class OperationalDataDto
{
    public int Id { get; set; }
    public DateTime Date { get; set; }
    public int? ServerId { get; set; }
    public string ServerName { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string? AdditionalData { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class GlobalStatsDto
{
    public decimal TotalRevenue { get; set; }
    public int TotalVisits { get; set; }
    public int TotalRegistrations { get; set; }
    public int TotalLogins { get; set; }
    public int TodayNewVisits { get; set; }
    public int TodayNewRegistrations { get; set; }
    public int TodayNewLogins { get; set; }
    public int TodayNewPayments { get; set; }
    public int TodayActiveUsers { get; set; }
    public decimal ARPU { get; set; } // 平均每用户收入
    public double AverageOnlineUsers { get; set; }
    public int MaxOnlineUsers { get; set; }
    public DateTime LastUpdated { get; set; }
}

public class UserInfoStatsDto
{
    public int TotalVisits { get; set; }
    public int UniqueVisits { get; set; }
    public int TotalRegistrations { get; set; }
    public int RegistrationsWithChannel { get; set; }
    public int TotalLogins { get; set; }
    public int SameIpLogins { get; set; }
    public int CurrentOnlineUsers { get; set; }
    public int UsersWithoutCharacter { get; set; }
    public int UsersNeverLoggedIn { get; set; }
    public Dictionary<string, int> RegistrationsByChannel { get; set; } = new();
    public Dictionary<string, int> VisitsByHour { get; set; } = new();
}

public class PaymentInfoStatsDto
{
    public decimal TotalRevenue { get; set; }
    public int TotalOrders { get; set; }
    public int PendingOrders { get; set; }
    public int CompletedOrders { get; set; }
    public int FailedOrders { get; set; }
    public decimal AverageOrderValue { get; set; }
    public Dictionary<string, decimal> RevenueByMethod { get; set; } = new();
    public Dictionary<string, int> OrdersByStatus { get; set; } = new();
    public List<TopPayerDto> TopPayers { get; set; } = new();
}

public class TopPayerDto
{
    public string AccountId { get; set; } = string.Empty;
    public string Nickname { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
    public int OrderCount { get; set; }
    public DateTime LastPaymentTime { get; set; }
}

public class ClientDataStatsDto
{
    public int TotalInstalls { get; set; }
    public int TotalUninstalls { get; set; }
    public int ActiveInstalls { get; set; }
    public Dictionary<string, int> InstallsByVersion { get; set; } = new();
    public Dictionary<string, int> InstallsByPlatform { get; set; } = new();
    public Dictionary<string, double> UsageByVersion { get; set; } = new();
}

public class ConversionAnalysisDto
{
    public double VisitToRegistrationRate { get; set; }
    public double RegistrationToPaymentRate { get; set; }
    public double RegistrationToCharacterCreationRate { get; set; }
    public double CharacterCreationToFirstLoginRate { get; set; }
    public DateTime AnalysisDate { get; set; }
    public Dictionary<string, double> ConversionByChannel { get; set; } = new();
}

public class ChurnAnalysisDto
{
    public double ThreeDayChurnRate { get; set; }
    public double SevenDayChurnRate { get; set; }
    public double MonthlyChurnRate { get; set; }
    public DateTime AnalysisDate { get; set; }
    public Dictionary<string, double> ChurnByLevel { get; set; } = new();
    public Dictionary<string, double> ChurnByServer { get; set; } = new();
}

public class RetentionAnalysisDto
{
    public double NextDayRetentionRate { get; set; }
    public double SevenDayRetentionRate { get; set; }
    public double MonthlyRetentionRate { get; set; }
    public DateTime AnalysisDate { get; set; }
    public Dictionary<string, double> RetentionByChannel { get; set; } = new();
    public Dictionary<string, double> RetentionByLevel { get; set; } = new();
}

public class UserLifecycleDto
{
    public string AccountId { get; set; } = string.Empty;
    public DateTime RegistrationDate { get; set; }
    public DateTime? FirstLoginDate { get; set; }
    public DateTime? LastLoginDate { get; set; }
    public DateTime? FirstPaymentDate { get; set; }
    public DateTime? LastPaymentDate { get; set; }
    public int TotalLoginDays { get; set; }
    public decimal TotalSpent { get; set; }
    public string LifecycleStage { get; set; } = string.Empty; // 新用户、活跃用户、流失用户等
    public int DaysSinceLastLogin { get; set; }
}

public class ActiveUserAnalysisDto
{
    public int DAU { get; set; } // 日活跃用户
    public int WAU { get; set; } // 周活跃用户
    public int MAU { get; set; } // 月活跃用户
    public double AverageSessionTime { get; set; }
    public Dictionary<string, int> ActiveUsersByHour { get; set; } = new();
    public Dictionary<string, int> ActiveUsersByServer { get; set; } = new();
    public DateTime AnalysisDate { get; set; }
}

public enum DataExportFormat
{
    Excel = 1,
    CSV = 2,
    PDF = 3,
    JSON = 4
}

// Security DTOs
public class SecurityEventDto
{
    public int Id { get; set; }
    public string EventId { get; set; } = string.Empty;
    public SecurityEventType EventType { get; set; }
    public string? PlayerId { get; set; }
    public string? PlayerName { get; set; }
    public string? IpAddress { get; set; }
    public string Description { get; set; } = string.Empty;
    public SecurityRiskLevel RiskLevel { get; set; }
    public SecurityEventStatus Status { get; set; }
    public int? HandledByUserId { get; set; }
    public string? HandledByUserName { get; set; }
    public DateTime? HandledAt { get; set; }
    public string? HandlingNotes { get; set; }
    public string? Evidence { get; set; }
    public bool IsAutoDetected { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class CreateSecurityEventDto
{
    public SecurityEventType EventType { get; set; }
    public string? PlayerId { get; set; }
    public string? IpAddress { get; set; }
    public string Description { get; set; } = string.Empty;
    public SecurityRiskLevel RiskLevel { get; set; }
    public string? Evidence { get; set; }
    public bool IsAutoDetected { get; set; } = true;
}

public class UpdateSecurityEventDto
{
    public SecurityEventStatus? Status { get; set; }
    public string? HandlingNotes { get; set; }
    public string? Evidence { get; set; }
}

public class SecurityRuleDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public SecurityEventType EventType { get; set; }
    public string Conditions { get; set; } = string.Empty;
    public string Actions { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public int Priority { get; set; }
    public DateTime? LastTriggeredAt { get; set; }
    public int TriggerCount { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class CreateSecurityRuleDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public SecurityEventType EventType { get; set; }
    public string Conditions { get; set; } = string.Empty;
    public string Actions { get; set; } = string.Empty;
    public int Priority { get; set; } = 0;
}

public class UpdateSecurityRuleDto
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? Conditions { get; set; }
    public string? Actions { get; set; }
    public bool? IsActive { get; set; }
    public int? Priority { get; set; }
}

public class SecurityStatsDto
{
    public int TotalEvents { get; set; }
    public int PendingEvents { get; set; }
    public int CriticalEvents { get; set; }
    public int HighRiskEvents { get; set; }
    public int ResolvedEvents { get; set; }
    public int FalsePositiveEvents { get; set; }
    public int ActiveRules { get; set; }
    public Dictionary<string, int> EventsByType { get; set; } = new();
    public Dictionary<string, int> EventsByRiskLevel { get; set; } = new();
    public Dictionary<string, int> EventsByStatus { get; set; } = new();
}

public class RiskAssessmentDto
{
    public string PlayerId { get; set; } = string.Empty;
    public string PlayerName { get; set; } = string.Empty;
    public SecurityRiskLevel OverallRiskLevel { get; set; }
    public double RiskScore { get; set; }
    public List<RiskFactorDto> RiskFactors { get; set; } = new();
    public List<SecurityEventDto> RecentEvents { get; set; } = new();
    public DateTime AssessmentDate { get; set; }
}

public class RiskFactorDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public double Score { get; set; }
    public SecurityRiskLevel Level { get; set; }
}

// Customer Service DTOs
public class CustomerServiceTicketDto
{
    public int Id { get; set; }
    public string TicketId { get; set; } = string.Empty;
    public int PlayerId { get; set; }
    public string PlayerAccountId { get; set; } = string.Empty;
    public string PlayerNickname { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TicketStatus Status { get; set; }
    public TicketPriority Priority { get; set; }
    public TicketCategory Category { get; set; }
    public int? AssignedToUserId { get; set; }
    public string? AssignedToUserName { get; set; }
    public DateTime? FirstResponseAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public DateTime? ClosedAt { get; set; }
    public string? Resolution { get; set; }
    public int? SatisfactionRating { get; set; }
    public string? SatisfactionFeedback { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int MessageCount { get; set; }
    public bool HasUnreadMessages { get; set; }
    public List<TicketMessageDto> Messages { get; set; } = new();
}

public class CreateCustomerServiceTicketDto
{
    public int PlayerId { get; set; }
    public string PlayerAccountId { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TicketPriority Priority { get; set; } = TicketPriority.Medium;
    public TicketCategory Category { get; set; }
    public int? AssignedToUserId { get; set; }
}

public class UpdateCustomerServiceTicketDto
{
    public string? Title { get; set; }
    public string? Description { get; set; }
    public TicketStatus? Status { get; set; }
    public TicketPriority? Priority { get; set; }
    public TicketCategory? Category { get; set; }
    public int? AssignedToUserId { get; set; }
    public string? Resolution { get; set; }
}

public class TicketMessageDto
{
    public int Id { get; set; }
    public int TicketId { get; set; }
    public int? UserId { get; set; }
    public string SenderName { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public bool IsFromPlayer { get; set; }
    public bool IsInternal { get; set; }
    public string? Attachments { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class CreateTicketMessageDto
{
    public int? UserId { get; set; }
    public string SenderName { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public bool IsFromPlayer { get; set; }
    public bool IsInternal { get; set; } = false;
    public string? Attachments { get; set; }
}

public class CustomerServiceStatsDto
{
    public int TotalTickets { get; set; }
    public int OpenTickets { get; set; }
    public int InProgressTickets { get; set; }
    public int ResolvedTickets { get; set; }
    public int ClosedTickets { get; set; }
    public int CriticalTickets { get; set; }
    public int HighPriorityTickets { get; set; }
    public int UnassignedTickets { get; set; }
    public double AverageResponseTime { get; set; } // in hours
    public double AverageResolutionTime { get; set; } // in hours
    public double SatisfactionScore { get; set; } // average rating
    public int TotalRatings { get; set; }
    public Dictionary<string, int> TicketsByStatus { get; set; } = new();
    public Dictionary<string, int> TicketsByPriority { get; set; } = new();
    public Dictionary<string, int> TicketsByCategory { get; set; } = new();
    public List<AgentPerformanceDto> AgentPerformance { get; set; } = new();
}

public class AgentPerformanceDto
{
    public int UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public int AssignedTickets { get; set; }
    public int ResolvedTickets { get; set; }
    public double AverageResolutionTime { get; set; }
    public double SatisfactionScore { get; set; }
    public int TotalRatings { get; set; }
}

public class CustomerDialogueStatsDto
{
    public int UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public int ActiveDialogues { get; set; }
    public int TotalDialogues { get; set; }
    public int MessagesExchanged { get; set; }
    public double AverageResponseTime { get; set; }
    public DateTime? LastActivity { get; set; }
}

public class SatisfactionStatsDto
{
    public double OverallScore { get; set; }
    public int TotalRatings { get; set; }
    public Dictionary<int, int> RatingDistribution { get; set; } = new(); // rating -> count
    public Dictionary<string, double> ScoreByCategory { get; set; } = new();
    public Dictionary<string, double> ScoreByAgent { get; set; } = new();
    public List<string> RecentFeedback { get; set; } = new();
}
