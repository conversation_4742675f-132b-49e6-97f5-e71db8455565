namespace GameManagement.Shared.Enums;

public enum UserRole
{
    SystemAdmin = 1,
    ProductManager = 2,
    ProductSpecialist = 3,
    PartnerManager = 4,
    PartnerSpecialist = 5,
    CustomerServiceManager = 6,
    CustomerServiceSpecialist = 7,
    Viewer = 8
}

public enum ServerStatus
{
    Online = 1,
    Offline = 2,
    Maintenance = 3,
    Error = 4
}

public enum PaymentStatus
{
    Pending = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4,
    Refunded = 5
}

public enum ActivityStatus
{
    Draft = 1,
    Active = 2,
    Paused = 3,
    Ended = 4,
    Cancelled = 5
}

public enum LogLevel
{
    Debug = 1,
    Info = 2,
    Warning = 3,
    Error = 4,
    Critical = 5
}

public enum NotificationType
{
    Email = 1,
    SMS = 2,
    Push = 3,
    InApp = 4
}

public enum ReportType
{
    Daily = 1,
    Weekly = 2,
    Monthly = 3,
    Custom = 4
}

public enum DataExportFormat
{
    Excel = 1,
    CSV = 2,
    PDF = 3,
    JSON = 4
}

// 客服管理相关枚举
public enum TicketStatus
{
    Open = 1,
    InProgress = 2,
    Resolved = 3,
    Closed = 4,
    Cancelled = 5
}

public enum TicketPriority
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

public enum TicketCategory
{
    Technical = 1,
    Payment = 2,
    Account = 3,
    Gameplay = 4,
    Bug = 5,
    Suggestion = 6,
    Other = 7
}

// 安全管理相关枚举
public enum SecurityEventType
{
    SuspiciousLogin = 1,
    MultipleFailedLogins = 2,
    UnusualPayment = 3,
    CheatDetection = 4,
    AccountSharing = 5,
    DataBreach = 6,
    Other = 7
}

public enum SecurityRiskLevel
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

public enum SecurityEventStatus
{
    Pending = 1,
    Investigating = 2,
    Resolved = 3,
    FalsePositive = 4,
    Ignored = 5
}

// 游戏内容相关枚举
public enum ItemType
{
    Weapon = 1,
    Armor = 2,
    Accessory = 3,
    Consumable = 4,
    Material = 5,
    Currency = 6,
    Other = 7
}

public enum ItemRarity
{
    Common = 1,
    Uncommon = 2,
    Rare = 3,
    Epic = 4,
    Legendary = 5,
    Mythic = 6
}

public enum AnnouncementType
{
    System = 1,
    Maintenance = 2,
    Event = 3,
    Update = 4,
    Promotion = 5,
    News = 6
}

// 支付相关枚举
public enum PaymentMethod
{
    CreditCard = 1,
    PayPal = 2,
    Alipay = 3,
    WeChat = 4,
    BankTransfer = 5,
    GooglePay = 6,
    ApplePay = 7,
    Other = 8
}

// 报表相关枚举
public enum ReportStatus
{
    Pending = 1,
    Generating = 2,
    Completed = 3,
    Failed = 4
}

// 服务器管理相关枚举
public enum MaintenanceType
{
    Scheduled = 1,
    Emergency = 2,
    Update = 3,
    Hotfix = 4
}

public enum MaintenanceStatus
{
    Scheduled = 1,
    InProgress = 2,
    Completed = 3,
    Cancelled = 4
}

// 告警相关枚举
public enum AlertSeverity
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

public enum AlertStatus
{
    Active = 1,
    Acknowledged = 2,
    Resolved = 3,
    Closed = 4
}

// 数据库备份相关枚举
public enum BackupType
{
    Full = 1,
    Incremental = 2,
    Differential = 3,
    Transaction = 4
}

public enum BackupStatus
{
    Pending = 1,
    InProgress = 2,
    Completed = 3,
    Failed = 4,
    Cancelled = 5
}
