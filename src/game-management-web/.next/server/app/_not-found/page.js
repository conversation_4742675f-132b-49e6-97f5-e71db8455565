/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQW1IIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvYXBwL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQW1IIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvYXBwL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzE5Y2IwZmMzZjYzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ConfigProvider!=!antd */ \"(ssr)/./node_modules/antd/es/config-provider/index.js\");\n/* harmony import */ var antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! antd/locale/zh_CN */ \"(ssr)/./node_modules/antd/lib/locale/zh_CN.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// 创建 QueryClient 实例\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient({\n    defaultOptions: {\n        queries: {\n            staleTime: 5 * 60 * 1000,\n            retry: 1\n        }\n    }\n});\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"游戏管理系统\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"游戏运营管理后台系统\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n                    client: queryClient,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            locale: antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_8__.ReactQueryDevtools, {\n                            initialIsOpen: false\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   ROLES: () => (/* binding */ ROLES),\n/* harmony export */   checkPermission: () => (/* binding */ checkPermission),\n/* harmony export */   hasCustomerServicePermission: () => (/* binding */ hasCustomerServicePermission),\n/* harmony export */   hasPartnerPermission: () => (/* binding */ hasPartnerPermission),\n/* harmony export */   hasProductPermission: () => (/* binding */ hasProductPermission),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(ssr)/./node_modules/antd/es/message/index.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,ROLES,checkPermission,isAdmin,hasCustomerServicePermission,hasProductPermission,hasPartnerPermission auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // 检查本地存储中的用户信息\n            if (false) {}\n            setIsLoading(false);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (username, password)=>{\n        try {\n            setIsLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.login({\n                username,\n                password\n            });\n            const { token, user: userData } = response.data;\n            // 存储认证信息\n            if (false) {}\n            setUser(userData);\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('登录成功');\n            return true;\n        } catch (error) {\n            console.error('Login error:', error);\n            const errorMessage = error.response?.data?.message || '登录失败，请检查用户名和密码';\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(errorMessage);\n            return false;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            if (false) {}\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            // 清除本地存储\n            if (false) {}\n            setUser(null);\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('已退出登录');\n            // 重定向到登录页\n            window.location.href = '/login';\n        }\n    };\n    const hasRole = (role)=>{\n        return user?.roles?.includes(role) || false;\n    };\n    const hasAnyRole = (roles)=>{\n        return roles.some((role)=>hasRole(role));\n    };\n    const isAuthenticated = !!user;\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        login,\n        logout,\n        hasRole,\n        hasAnyRole\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/contexts/AuthContext.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n// 角色常量\nconst ROLES = {\n    SYSTEM_ADMIN: 'SystemAdmin',\n    PRODUCT_MANAGER: 'ProductManager',\n    PRODUCT_SPECIALIST: 'ProductSpecialist',\n    PARTNER_MANAGER: 'PartnerManager',\n    PARTNER_SPECIALIST: 'PartnerSpecialist',\n    CUSTOMER_SERVICE_MANAGER: 'CustomerServiceManager',\n    CUSTOMER_SERVICE_SPECIALIST: 'CustomerServiceSpecialist',\n    VIEWER: 'Viewer'\n};\n// 权限检查工具函数\nconst checkPermission = (userRoles, requiredRoles)=>{\n    return requiredRoles.some((role)=>userRoles.includes(role));\n};\n// 管理员角色检查\nconst isAdmin = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER\n    ]);\n};\n// 客服权限检查\nconst hasCustomerServicePermission = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER,\n        ROLES.CUSTOMER_SERVICE_MANAGER,\n        ROLES.CUSTOMER_SERVICE_SPECIALIST\n    ]);\n};\n// 产品管理权限检查\nconst hasProductPermission = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER,\n        ROLES.PRODUCT_SPECIALIST\n    ]);\n};\n// 渠道管理权限检查\nconst hasPartnerPermission = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER,\n        ROLES.PARTNER_MANAGER,\n        ROLES.PARTNER_SPECIALIST\n    ]);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   operationalDataApi: () => (/* binding */ operationalDataApi),\n/* harmony export */   playersApi: () => (/* binding */ playersApi),\n/* harmony export */   usersApi: () => (/* binding */ usersApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// API 基础配置\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5108/api';\n// 创建 axios 实例\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 10000,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// 请求拦截器 - 添加认证令牌\napiClient.interceptors.request.use((config)=>{\n    // 检查是否在浏览器环境中\n    if (false) {}\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器 - 处理认证错误\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, async (error)=>{\n    const originalRequest = error.config;\n    if (error.response?.status === 401 && !originalRequest._retry && \"undefined\" !== 'undefined') {}\n    return Promise.reject(error);\n});\n// API 方法\nconst authApi = {\n    login: (data)=>apiClient.post('/auth/login', data),\n    logout: (refreshToken)=>apiClient.post('/auth/logout', refreshToken),\n    register: (data)=>apiClient.post('/auth/register', data),\n    refreshToken: (refreshToken)=>apiClient.post('/auth/refresh', refreshToken),\n    changePassword: (currentPassword, newPassword)=>apiClient.post('/auth/change-password', {\n            currentPassword,\n            newPassword\n        }),\n    resetPassword: (email)=>apiClient.post('/auth/reset-password', {\n            email\n        })\n};\nconst usersApi = {\n    getUsers: ()=>apiClient.get('/users'),\n    getUser: (id)=>apiClient.get(`/users/${id}`),\n    getUserByUsername: (username)=>apiClient.get(`/users/by-username/${username}`),\n    createUser: (data)=>apiClient.post('/users', data),\n    updateUser: (id, data)=>apiClient.put(`/users/${id}`, data),\n    deleteUser: (id)=>apiClient.delete(`/users/${id}`),\n    activateUser: (id)=>apiClient.post(`/users/${id}/activate`),\n    deactivateUser: (id)=>apiClient.post(`/users/${id}/deactivate`),\n    getUsersByRole: (role)=>apiClient.get(`/users/by-role/${role}`)\n};\nconst playersApi = {\n    getPlayers: (page = 1, pageSize = 20)=>apiClient.get(`/players?page=${page}&pageSize=${pageSize}`),\n    getPlayer: (id)=>apiClient.get(`/players/${id}`),\n    getPlayerByAccountId: (accountId)=>apiClient.get(`/players/by-account/${accountId}`),\n    searchPlayers: (searchTerm)=>apiClient.get(`/players/search?searchTerm=${encodeURIComponent(searchTerm)}`),\n    getPlayerStats: ()=>apiClient.get('/players/stats'),\n    getTopPlayersByLevel: (count = 10)=>apiClient.get(`/players/top-by-level?count=${count}`),\n    getVipPlayers: (minVipLevel = 1)=>apiClient.get(`/players/vip?minVipLevel=${minVipLevel}`),\n    updatePlayer: (id, data)=>apiClient.put(`/players/${id}`, data),\n    banPlayer: (id, bannedUntil, reason)=>apiClient.post(`/players/${id}/ban`, {\n            bannedUntil,\n            reason\n        }),\n    unbanPlayer: (id)=>apiClient.post(`/players/${id}/unban`)\n};\n// 运营数据API方法\nconst operationalDataApi = {\n    // 全局统计\n    getGlobalStats: ()=>apiClient.get('/operational-data/global-stats'),\n    getGlobalStatsByDate: (date)=>apiClient.get(`/operational-data/global-stats/${date}`),\n    // 用户信息统计\n    getUserInfoStats: ()=>apiClient.get('/operational-data/user-info-stats'),\n    getUserInfoStatsByDateRange: (startDate, endDate)=>apiClient.get(`/operational-data/user-info-stats/${startDate}/${endDate}`),\n    // 付费信息统计\n    getPaymentInfoStats: ()=>apiClient.get('/operational-data/payment-info-stats'),\n    getPaymentInfoStatsByDateRange: (startDate, endDate)=>apiClient.get(`/operational-data/payment-info-stats/${startDate}/${endDate}`),\n    // 数据分析\n    getConversionAnalysis: (date)=>apiClient.get(`/operational-data/conversion-analysis/${date}`),\n    getRetentionAnalysis: (date)=>apiClient.get(`/operational-data/retention-analysis/${date}`),\n    getActiveUserAnalysis: (date)=>apiClient.get(`/operational-data/active-user-analysis/${date}`),\n    // 记录数据\n    recordVisit: (data)=>apiClient.post('/operational-data/record-visit', data)\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tanstack","vendor-chunks/next","vendor-chunks/antd","vendor-chunks/@ant-design","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/rc-util","vendor-chunks/rc-motion","vendor-chunks/rc-notification","vendor-chunks/@babel","vendor-chunks/rc-pagination","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/stylis","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/@emotion","vendor-chunks/rc-picker","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/classnames","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();