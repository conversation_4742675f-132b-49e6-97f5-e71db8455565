globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/app/layout.tsx":{"*":{"id":"(ssr)/./src/app/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(ssr)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/operational-data/page.tsx":{"*":{"id":"(ssr)/./src/app/operational-data/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/payments/page.tsx":{"*":{"id":"(ssr)/./src/app/payments/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/players/page.tsx":{"*":{"id":"(ssr)/./src/app/players/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/games/activities/page.tsx":{"*":{"id":"(ssr)/./src/app/games/activities/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/games/announcements/page.tsx":{"*":{"id":"(ssr)/./src/app/games/announcements/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/games/items/page.tsx":{"*":{"id":"(ssr)/./src/app/games/items/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/servers/status/page.tsx":{"*":{"id":"(ssr)/./src/app/servers/status/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/servers/monitoring/page.tsx":{"*":{"id":"(ssr)/./src/app/servers/monitoring/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer-service/page.tsx":{"*":{"id":"(ssr)/./src/app/customer-service/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx":{"id":"(app-pages-browser)/./src/app/layout.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/login/page.tsx":{"id":"(app-pages-browser)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/dashboard/page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.tsx","name":"*","chunks":["app/dashboard/page","static/chunks/app/dashboard/page.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx":{"id":"(app-pages-browser)/./src/app/operational-data/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/payments/page.tsx":{"id":"(app-pages-browser)/./src/app/payments/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/players/page.tsx":{"id":"(app-pages-browser)/./src/app/players/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx":{"id":"(app-pages-browser)/./src/app/games/activities/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/announcements/page.tsx":{"id":"(app-pages-browser)/./src/app/games/announcements/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/items/page.tsx":{"id":"(app-pages-browser)/./src/app/games/items/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx":{"id":"(app-pages-browser)/./src/app/servers/status/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx":{"id":"(app-pages-browser)/./src/app/servers/monitoring/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/customer-service/page.tsx":{"id":"(app-pages-browser)/./src/app/customer-service/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/":[],"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/dashboard/page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/layout.tsx":{"*":{"id":"(rsc)/./src/app/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(rsc)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/operational-data/page.tsx":{"*":{"id":"(rsc)/./src/app/operational-data/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/payments/page.tsx":{"*":{"id":"(rsc)/./src/app/payments/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/players/page.tsx":{"*":{"id":"(rsc)/./src/app/players/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/games/activities/page.tsx":{"*":{"id":"(rsc)/./src/app/games/activities/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/games/announcements/page.tsx":{"*":{"id":"(rsc)/./src/app/games/announcements/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/games/items/page.tsx":{"*":{"id":"(rsc)/./src/app/games/items/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/servers/status/page.tsx":{"*":{"id":"(rsc)/./src/app/servers/status/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/servers/monitoring/page.tsx":{"*":{"id":"(rsc)/./src/app/servers/monitoring/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer-service/page.tsx":{"*":{"id":"(rsc)/./src/app/customer-service/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}