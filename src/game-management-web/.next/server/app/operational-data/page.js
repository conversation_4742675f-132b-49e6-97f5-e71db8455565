/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/operational-data/page";
exports.ids = ["app/operational-data/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Foperational-data%2Fpage&page=%2Foperational-data%2Fpage&appPaths=%2Foperational-data%2Fpage&pagePath=private-next-app-dir%2Foperational-data%2Fpage.tsx&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Foperational-data%2Fpage&page=%2Foperational-data%2Fpage&appPaths=%2Foperational-data%2Fpage&pagePath=private-next-app-dir%2Foperational-data%2Fpage.tsx&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/operational-data/page.tsx */ \"(rsc)/./src/app/operational-data/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'operational-data',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/operational-data/page\",\n        pathname: \"/operational-data\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Foperational-data%2Fpage&page=%2Foperational-data%2Fpage&appPaths=%2Foperational-data%2Fpage&pagePath=private-next-app-dir%2Foperational-data%2Fpage.tsx&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZjYWZlJTJGRG9jdW1lbnRzJTJGZ2FtZW1hbmFnZXdlYiUyRnNyYyUyRmdhbWUtbWFuYWdlbWVudC13ZWIlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZjbGllbnQtc2VnbWVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmNhZmUlMkZEb2N1bWVudHMlMkZnYW1lbWFuYWdld2ViJTJGc3JjJTJGZ2FtZS1tYW5hZ2VtZW50LXdlYiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGaHR0cC1hY2Nlc3MtZmFsbGJhY2slMkZlcnJvci1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmNhZmUlMkZEb2N1bWVudHMlMkZnYW1lbWFuYWdld2ViJTJGc3JjJTJGZ2FtZS1tYW5hZ2VtZW50LXdlYiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmxheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZjYWZlJTJGRG9jdW1lbnRzJTJGZ2FtZW1hbmFnZXdlYiUyRnNyYyUyRmdhbWUtbWFuYWdlbWVudC13ZWIlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZtZXRhZGF0YSUyRmFzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbWV0YWRhdGElMkZtZXRhZGF0YS1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmNhZmUlMkZEb2N1bWVudHMlMkZnYW1lbWFuYWdld2ViJTJGc3JjJTJGZ2FtZS1tYW5hZ2VtZW50LXdlYiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRnJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9PQUF3SjtBQUN4SjtBQUNBLDBPQUEySjtBQUMzSjtBQUNBLDBPQUEySjtBQUMzSjtBQUNBLG9SQUFnTDtBQUNoTDtBQUNBLHdPQUEwSjtBQUMxSjtBQUNBLDRQQUFvSztBQUNwSztBQUNBLGtRQUF1SztBQUN2SztBQUNBLHNRQUF5SyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2NhZmUvRG9jdW1lbnRzL2dhbWVtYW5hZ2V3ZWIvc3JjL2dhbWUtbWFuYWdlbWVudC13ZWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2NhZmUvRG9jdW1lbnRzL2dhbWVtYW5hZ2V3ZWIvc3JjL2dhbWUtbWFuYWdlbWVudC13ZWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2NhZmUvRG9jdW1lbnRzL2dhbWVtYW5hZ2V3ZWIvc3JjL2dhbWUtbWFuYWdlbWVudC13ZWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2NhZmUvRG9jdW1lbnRzL2dhbWVtYW5hZ2V3ZWIvc3JjL2dhbWUtbWFuYWdlbWVudC13ZWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9odHRwLWFjY2Vzcy1mYWxsYmFjay9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2NhZmUvRG9jdW1lbnRzL2dhbWVtYW5hZ2V3ZWIvc3JjL2dhbWUtbWFuYWdlbWVudC13ZWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9sYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL21ldGFkYXRhL2FzeW5jLW1ldGFkYXRhLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL21ldGFkYXRhL21ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQW1IIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvYXBwL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Foperational-data%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Foperational-data%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/operational-data/page.tsx */ \"(rsc)/./src/app/operational-data/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGc3JjJTJGYXBwJTJGb3BlcmF0aW9uYWwtZGF0YSUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrTEFBa0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9jYWZlL0RvY3VtZW50cy9nYW1lbWFuYWdld2ViL3NyYy9nYW1lLW1hbmFnZW1lbnQtd2ViL3NyYy9hcHAvb3BlcmF0aW9uYWwtZGF0YS9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Foperational-data%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy9jYWZlL0RvY3VtZW50cy9nYW1lbWFuYWdld2ViL3NyYy9nYW1lLW1hbmFnZW1lbnQtd2ViL3NyYy9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/operational-data/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/operational-data/page.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQW1IIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvYXBwL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Foperational-data%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Foperational-data%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/operational-data/page.tsx */ \"(ssr)/./src/app/operational-data/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGc3JjJTJGYXBwJTJGb3BlcmF0aW9uYWwtZGF0YSUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrTEFBa0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9jYWZlL0RvY3VtZW50cy9nYW1lbWFuYWdld2ViL3NyYy9nYW1lLW1hbmFnZW1lbnQtd2ViL3NyYy9hcHAvb3BlcmF0aW9uYWwtZGF0YS9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Foperational-data%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzE5Y2IwZmMzZjYzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ConfigProvider!=!antd */ \"(ssr)/./node_modules/antd/es/config-provider/index.js\");\n/* harmony import */ var antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! antd/locale/zh_CN */ \"(ssr)/./node_modules/antd/lib/locale/zh_CN.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// 创建 QueryClient 实例\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient({\n    defaultOptions: {\n        queries: {\n            staleTime: 5 * 60 * 1000,\n            retry: 1\n        }\n    }\n});\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"游戏管理系统\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"游戏运营管理后台系统\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n                    client: queryClient,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            locale: antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_8__.ReactQueryDevtools, {\n                            initialIsOpen: false\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/operational-data/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/operational-data/page.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OperationalDataPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(ssr)/./node_modules/antd/es/date-picker/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(ssr)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(ssr)/./node_modules/antd/es/tabs/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(ssr)/./node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(ssr)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(ssr)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(ssr)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(ssr)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(ssr)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(ssr)/./node_modules/antd/es/progress/index.js\");\n/* harmony import */ var _barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DollarOutlined,EyeOutlined,LoginOutlined,ShoppingCartOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DollarOutlined,EyeOutlined,LoginOutlined,ShoppingCartOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=DollarOutlined,EyeOutlined,LoginOutlined,ShoppingCartOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DollarOutlined,EyeOutlined,LoginOutlined,ShoppingCartOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/LoginOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DollarOutlined,EyeOutlined,LoginOutlined,ShoppingCartOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/TeamOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=DollarOutlined,EyeOutlined,LoginOutlined,ShoppingCartOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DollarOutlined,EyeOutlined,LoginOutlined,ShoppingCartOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/TrophyOutlined.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Auth/ProtectedRoute */ \"(ssr)/./src/components/Auth/ProtectedRoute.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(ssr)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst { RangePicker } = _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst { Option } = _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nconst { TabPane } = _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\nfunction OperationalDataPage() {\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(dayjs__WEBPACK_IMPORTED_MODULE_4___default()().format('YYYY-MM-DD'));\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        dayjs__WEBPACK_IMPORTED_MODULE_4___default()().subtract(7, 'day').format('YYYY-MM-DD'),\n        dayjs__WEBPACK_IMPORTED_MODULE_4___default()().format('YYYY-MM-DD')\n    ]);\n    // 全局统计数据\n    const { data: globalStats, isLoading: globalStatsLoading, error: globalStatsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.useQuery)({\n        queryKey: [\n            'globalStats'\n        ],\n        queryFn: {\n            \"OperationalDataPage.useQuery\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_2__.operationalDataApi.getGlobalStats()\n        }[\"OperationalDataPage.useQuery\"]\n    });\n    // 用户信息统计\n    const { data: userInfoStats, isLoading: userInfoLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.useQuery)({\n        queryKey: [\n            'userInfoStats',\n            dateRange\n        ],\n        queryFn: {\n            \"OperationalDataPage.useQuery\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_2__.operationalDataApi.getUserInfoStatsByDateRange(dateRange[0], dateRange[1])\n        }[\"OperationalDataPage.useQuery\"]\n    });\n    // 付费信息统计\n    const { data: paymentInfoStats, isLoading: paymentInfoLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.useQuery)({\n        queryKey: [\n            'paymentInfoStats',\n            dateRange\n        ],\n        queryFn: {\n            \"OperationalDataPage.useQuery\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_2__.operationalDataApi.getPaymentInfoStatsByDateRange(dateRange[0], dateRange[1])\n        }[\"OperationalDataPage.useQuery\"]\n    });\n    // 转化率分析\n    const { data: conversionAnalysis, isLoading: conversionLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.useQuery)({\n        queryKey: [\n            'conversionAnalysis',\n            selectedDate\n        ],\n        queryFn: {\n            \"OperationalDataPage.useQuery\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_2__.operationalDataApi.getConversionAnalysis(selectedDate)\n        }[\"OperationalDataPage.useQuery\"]\n    });\n    // 留存率分析\n    const { data: retentionAnalysis, isLoading: retentionLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.useQuery)({\n        queryKey: [\n            'retentionAnalysis',\n            selectedDate\n        ],\n        queryFn: {\n            \"OperationalDataPage.useQuery\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_2__.operationalDataApi.getRetentionAnalysis(selectedDate)\n        }[\"OperationalDataPage.useQuery\"]\n    });\n    // 活跃用户分析\n    const { data: activeUserAnalysis, isLoading: activeUserLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.useQuery)({\n        queryKey: [\n            'activeUserAnalysis',\n            selectedDate\n        ],\n        queryFn: {\n            \"OperationalDataPage.useQuery\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_2__.operationalDataApi.getActiveUserAnalysis(selectedDate)\n        }[\"OperationalDataPage.useQuery\"]\n    });\n    const formatNumber = (num)=>{\n        if (num >= 1000000) {\n            return (num / 1000000).toFixed(1) + 'M';\n        }\n        if (num >= 1000) {\n            return (num / 1000).toFixed(1) + 'K';\n        }\n        return num.toString();\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('zh-CN', {\n            style: 'currency',\n            currency: 'CNY'\n        }).format(amount);\n    };\n    const formatPercentage = (rate)=>{\n        return (rate * 100).toFixed(2) + '%';\n    };\n    if (globalStatsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            requiredRoles: [\n                'SystemAdmin',\n                'ProductManager',\n                'ProductSpecialist'\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                message: \"加载失败\",\n                description: \"无法加载运营数据，请稍后重试\",\n                type: \"error\",\n                showIcon: true\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        requiredRoles: [\n            'SystemAdmin',\n            'ProductManager',\n            'ProductSpecialist'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                padding: '24px'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        marginBottom: '24px',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            children: \"运营数据\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '16px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    value: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(selectedDate),\n                                    onChange: (date)=>setSelectedDate(date?.format('YYYY-MM-DD') || dayjs__WEBPACK_IMPORTED_MODULE_4___default()().format('YYYY-MM-DD')),\n                                    placeholder: \"选择分析日期\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RangePicker, {\n                                    value: [\n                                        dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange[0]),\n                                        dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange[1])\n                                    ],\n                                    onChange: (dates)=>{\n                                        if (dates && dates[0] && dates[1]) {\n                                            setDateRange([\n                                                dates[0].format('YYYY-MM-DD'),\n                                                dates[1].format('YYYY-MM-DD')\n                                            ]);\n                                        }\n                                    },\n                                    placeholder: [\n                                        '开始日期',\n                                        '结束日期'\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    defaultActiveKey: \"overview\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPane, {\n                            tab: \"数据概览\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    gutter: [\n                                        16,\n                                        16\n                                    ],\n                                    style: {\n                                        marginBottom: '24px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            md: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"总营收\",\n                                                    value: globalStats?.totalRevenue || 0,\n                                                    formatter: (value)=>formatCurrency(Number(value)),\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    loading: globalStatsLoading\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            md: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"总访问量\",\n                                                    value: globalStats?.totalVisits || 0,\n                                                    formatter: (value)=>formatNumber(Number(value)),\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    loading: globalStatsLoading\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            md: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"总注册数\",\n                                                    value: globalStats?.totalRegistrations || 0,\n                                                    formatter: (value)=>formatNumber(Number(value)),\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    loading: globalStatsLoading\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            md: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"总登录数\",\n                                                    value: globalStats?.totalLogins || 0,\n                                                    formatter: (value)=>formatNumber(Number(value)),\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    loading: globalStatsLoading\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    title: \"今日数据\",\n                                    style: {\n                                        marginBottom: '24px'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        gutter: [\n                                            16,\n                                            16\n                                        ],\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                xs: 24,\n                                                sm: 12,\n                                                md: 6,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"新增访问\",\n                                                    value: globalStats?.todayNewVisits || 0,\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    loading: globalStatsLoading\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                xs: 24,\n                                                sm: 12,\n                                                md: 6,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"新增注册\",\n                                                    value: globalStats?.todayNewRegistrations || 0,\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    loading: globalStatsLoading\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                xs: 24,\n                                                sm: 12,\n                                                md: 6,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"新增登录\",\n                                                    value: globalStats?.todayNewLogins || 0,\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    loading: globalStatsLoading\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                xs: 24,\n                                                sm: 12,\n                                                md: 6,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"活跃用户\",\n                                                    value: globalStats?.todayActiveUsers || 0,\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    loading: globalStatsLoading\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    gutter: [\n                                        16,\n                                        16\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                title: \"收入指标\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"ARPU (平均每用户收入)\",\n                                                    value: globalStats?.arpu || 0,\n                                                    formatter: (value)=>formatCurrency(Number(value)),\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    loading: globalStatsLoading\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                title: \"在线指标\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    gutter: 16,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            span: 12,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                title: \"平均在线\",\n                                                                value: globalStats?.averageOnlineUsers || 0,\n                                                                formatter: (value)=>formatNumber(Number(value)),\n                                                                loading: globalStatsLoading\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            span: 12,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                title: \"最高在线\",\n                                                                value: globalStats?.maxOnlineUsers || 0,\n                                                                formatter: (value)=>formatNumber(Number(value)),\n                                                                loading: globalStatsLoading\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, \"overview\", true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPane, {\n                            tab: \"用户分析\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                gutter: [\n                                    16,\n                                    16\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        xs: 24,\n                                        lg: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            title: \"用户统计\",\n                                            loading: userInfoLoading,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                gutter: [\n                                                    16,\n                                                    16\n                                                ],\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        span: 12,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            title: \"总访问量\",\n                                                            value: userInfoStats?.totalVisits || 0,\n                                                            formatter: (value)=>formatNumber(Number(value))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        span: 12,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            title: \"独立访问\",\n                                                            value: userInfoStats?.uniqueVisits || 0,\n                                                            formatter: (value)=>formatNumber(Number(value))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        span: 12,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            title: \"总注册数\",\n                                                            value: userInfoStats?.totalRegistrations || 0,\n                                                            formatter: (value)=>formatNumber(Number(value))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        span: 12,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            title: \"当前在线\",\n                                                            value: userInfoStats?.currentOnlineUsers || 0,\n                                                            formatter: (value)=>formatNumber(Number(value))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        xs: 24,\n                                        lg: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            title: \"用户状态\",\n                                            loading: userInfoLoading,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                gutter: [\n                                                    16,\n                                                    16\n                                                ],\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        span: 12,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            title: \"未创角用户\",\n                                                            value: userInfoStats?.usersWithoutCharacter || 0,\n                                                            formatter: (value)=>formatNumber(Number(value))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        span: 12,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            title: \"未登录用户\",\n                                                            value: userInfoStats?.usersNeverLoggedIn || 0,\n                                                            formatter: (value)=>formatNumber(Number(value))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        span: 12,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            title: \"同IP登录\",\n                                                            value: userInfoStats?.sameIpLogins || 0,\n                                                            formatter: (value)=>formatNumber(Number(value))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        span: 12,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            title: \"渠道注册\",\n                                                            value: userInfoStats?.registrationsWithChannel || 0,\n                                                            formatter: (value)=>formatNumber(Number(value))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this)\n                        }, \"user-analysis\", false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPane, {\n                            tab: \"付费分析\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    gutter: [\n                                        16,\n                                        16\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            lg: 16,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                title: \"付费统计\",\n                                                loading: paymentInfoLoading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    gutter: [\n                                                        16,\n                                                        16\n                                                    ],\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            xs: 24,\n                                                            sm: 12,\n                                                            md: 6,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                title: \"总营收\",\n                                                                value: paymentInfoStats?.totalRevenue || 0,\n                                                                formatter: (value)=>formatCurrency(Number(value)),\n                                                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 33\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            xs: 24,\n                                                            sm: 12,\n                                                            md: 6,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                title: \"总订单\",\n                                                                value: paymentInfoStats?.totalOrders || 0,\n                                                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 33\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            xs: 24,\n                                                            sm: 12,\n                                                            md: 6,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                title: \"成功订单\",\n                                                                value: paymentInfoStats?.completedOrders || 0,\n                                                                valueStyle: {\n                                                                    color: '#3f8600'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            xs: 24,\n                                                            sm: 12,\n                                                            md: 6,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                title: \"失败订单\",\n                                                                value: paymentInfoStats?.failedOrders || 0,\n                                                                valueStyle: {\n                                                                    color: '#cf1322'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                title: \"平均订单价值\",\n                                                loading: paymentInfoLoading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    value: paymentInfoStats?.averageOrderValue || 0,\n                                                    formatter: (value)=>formatCurrency(Number(value)),\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 29\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    title: \"付费排行榜\",\n                                    style: {\n                                        marginTop: '16px'\n                                    },\n                                    loading: paymentInfoLoading,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        dataSource: paymentInfoStats?.topPayers || [],\n                                        pagination: false,\n                                        size: \"small\",\n                                        columns: [\n                                            {\n                                                title: '排名',\n                                                key: 'rank',\n                                                render: (_, __, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: [\n                                                            index + 1 <= 3 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                style: {\n                                                                    color: [\n                                                                        '#FFD700',\n                                                                        '#C0C0C0',\n                                                                        '#CD7F32'\n                                                                    ][index]\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 43\n                                                            }, void 0) : null,\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                width: 80\n                                            },\n                                            {\n                                                title: '账号ID',\n                                                dataIndex: 'accountId',\n                                                key: 'accountId'\n                                            },\n                                            {\n                                                title: '昵称',\n                                                dataIndex: 'nickname',\n                                                key: 'nickname'\n                                            },\n                                            {\n                                                title: '总金额',\n                                                dataIndex: 'totalAmount',\n                                                key: 'totalAmount',\n                                                render: (amount)=>formatCurrency(amount)\n                                            },\n                                            {\n                                                title: '订单数',\n                                                dataIndex: 'orderCount',\n                                                key: 'orderCount'\n                                            },\n                                            {\n                                                title: '最后付费时间',\n                                                dataIndex: 'lastPaymentTime',\n                                                key: 'lastPaymentTime',\n                                                render: (time)=>dayjs__WEBPACK_IMPORTED_MODULE_4___default()(time).format('YYYY-MM-DD HH:mm')\n                                            }\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, \"payment-analysis\", true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPane, {\n                            tab: \"转化分析\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    gutter: [\n                                        16,\n                                        16\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                title: \"转化率分析\",\n                                                loading: conversionLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    justifyContent: 'space-between',\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"访问-注册转化率\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatPercentage(conversionAnalysis?.visitToRegistrationRate || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                percent: (conversionAnalysis?.visitToRegistrationRate || 0) * 100,\n                                                                showInfo: false\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    justifyContent: 'space-between',\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"注册-付费转化率\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatPercentage(conversionAnalysis?.registrationToPaymentRate || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 435,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                percent: (conversionAnalysis?.registrationToPaymentRate || 0) * 100,\n                                                                showInfo: false\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    justifyContent: 'space-between',\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"注册-创角转化率\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatPercentage(conversionAnalysis?.registrationToCharacterCreationRate || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                percent: (conversionAnalysis?.registrationToCharacterCreationRate || 0) * 100,\n                                                                showInfo: false\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                title: \"留存率分析\",\n                                                loading: retentionLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    justifyContent: 'space-between',\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"次日留存率\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 452,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatPercentage(retentionAnalysis?.nextDayRetentionRate || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                percent: (retentionAnalysis?.nextDayRetentionRate || 0) * 100,\n                                                                showInfo: false,\n                                                                strokeColor: \"#52c41a\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    justifyContent: 'space-between',\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"七日留存率\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatPercentage(retentionAnalysis?.sevenDayRetentionRate || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 460,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                percent: (retentionAnalysis?.sevenDayRetentionRate || 0) * 100,\n                                                                showInfo: false,\n                                                                strokeColor: \"#1890ff\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    justifyContent: 'space-between',\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"月留存率\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 466,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatPercentage(retentionAnalysis?.monthlyRetentionRate || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 467,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                percent: (retentionAnalysis?.monthlyRetentionRate || 0) * 100,\n                                                                showInfo: false,\n                                                                strokeColor: \"#722ed1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    title: \"活跃用户分析\",\n                                    style: {\n                                        marginTop: '16px'\n                                    },\n                                    loading: activeUserLoading,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        gutter: [\n                                            16,\n                                            16\n                                        ],\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                xs: 24,\n                                                sm: 8,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"日活跃用户 (DAU)\",\n                                                    value: activeUserAnalysis?.dau || 0,\n                                                    formatter: (value)=>formatNumber(Number(value)),\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 29\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                xs: 24,\n                                                sm: 8,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"周活跃用户 (WAU)\",\n                                                    value: activeUserAnalysis?.wau || 0,\n                                                    formatter: (value)=>formatNumber(Number(value)),\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 29\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                xs: 24,\n                                                sm: 8,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"月活跃用户 (MAU)\",\n                                                    value: activeUserAnalysis?.mau || 0,\n                                                    formatter: (value)=>formatNumber(Number(value)),\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 29\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, \"conversion-analysis\", true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/operational-data/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/Auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Spin_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Spin!=!antd */ \"(ssr)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(ssr)/./src/components/Layout/MainLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst ProtectedRoute = ({ children, requiredRoles = [] })=>{\n    const { isAuthenticated, isLoading, hasAnyRole } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            if (!isLoading) {\n                if (!isAuthenticated) {\n                    // 未登录，重定向到登录页\n                    router.push('/login');\n                    return;\n                }\n                if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {\n                    // 没有权限，重定向到仪表板或显示无权限页面\n                    router.push('/dashboard');\n                    return;\n                }\n            }\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        hasAnyRole,\n        requiredRoles,\n        router\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                justifyContent: 'center',\n                alignItems: 'center',\n                minHeight: '100vh'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Spin_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Auth/ProtectedRoute.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Auth/ProtectedRoute.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!isAuthenticated) {\n        return null; // 将重定向到登录页\n    }\n    if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {\n        return null; // 将重定向到仪表板\n    }\n    // 如果是登录页面，不使用主布局\n    if (pathname === '/login') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    // 使用主布局包装受保护的页面\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Auth/ProtectedRoute.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProtectedRoute);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/MainLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/Layout/MainLayout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/theme/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/menu/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/dropdown/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/TeamOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/AppstoreOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/CloudServerOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/BellOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/LinkOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/SettingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/LogoutOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/MenuUnfoldOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/MenuFoldOutlined.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst { Header, Sider, Content } = _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Text } = _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst MainLayout = ({ children })=>{\n    const [collapsed, setCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, logout, hasRole, hasAnyRole } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { token: { colorBgContainer, borderRadiusLG } } = _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useToken();\n    // 菜单项配置\n    const menuItems = [\n        {\n            key: '/dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 44,\n                columnNumber: 13\n            }, undefined),\n            label: '仪表板',\n            roles: []\n        },\n        {\n            key: '/operational-data',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 50,\n                columnNumber: 13\n            }, undefined),\n            label: '运营数据',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n            ]\n        },\n        {\n            key: '/players',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 56,\n                columnNumber: 13\n            }, undefined),\n            label: '玩家管理',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.CUSTOMER_SERVICE_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.CUSTOMER_SERVICE_SPECIALIST\n            ]\n        },\n        {\n            key: '/payments',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 62,\n                columnNumber: 13\n            }, undefined),\n            label: '支付管理',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n            ]\n        },\n        {\n            key: '/games',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 68,\n                columnNumber: 13\n            }, undefined),\n            label: '游戏数据',\n            children: [\n                {\n                    key: '/games/activities',\n                    label: '活动管理',\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n                    ]\n                },\n                {\n                    key: '/games/announcements',\n                    label: '公告管理',\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n                    ]\n                },\n                {\n                    key: '/games/items',\n                    label: '道具管理',\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n                    ]\n                }\n            ]\n        },\n        {\n            key: '/servers',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 90,\n                columnNumber: 13\n            }, undefined),\n            label: '服务器管理',\n            children: [\n                {\n                    key: '/servers/status',\n                    label: '服务器状态',\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER\n                    ]\n                },\n                {\n                    key: '/servers/monitoring',\n                    label: '监控告警',\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER\n                    ]\n                },\n                {\n                    key: '/servers/logs',\n                    label: '日志管理',\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER\n                    ]\n                }\n            ]\n        },\n        {\n            key: '/customer-service',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 112,\n                columnNumber: 13\n            }, undefined),\n            label: '客服管理',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.CUSTOMER_SERVICE_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.CUSTOMER_SERVICE_SPECIALIST\n            ]\n        },\n        {\n            key: '/security',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 118,\n                columnNumber: 13\n            }, undefined),\n            label: '安全管理',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN\n            ]\n        },\n        {\n            key: '/reports',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 124,\n                columnNumber: 13\n            }, undefined),\n            label: '数据报表',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n            ]\n        },\n        {\n            key: '/channels',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 130,\n                columnNumber: 13\n            }, undefined),\n            label: '渠道管理',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER\n            ]\n        }\n    ];\n    // 过滤菜单项基于用户角色\n    const filterMenuItems = (items)=>{\n        return items.filter((item)=>{\n            if (item.roles && item.roles.length > 0) {\n                if (!hasAnyRole(item.roles)) {\n                    return false;\n                }\n            }\n            if (item.children) {\n                item.children = filterMenuItems(item.children);\n                return item.children.length > 0;\n            }\n            return true;\n        });\n    };\n    const filteredMenuItems = filterMenuItems(menuItems);\n    // 用户下拉菜单\n    const userMenuItems = [\n        {\n            key: 'profile',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 160,\n                columnNumber: 13\n            }, undefined),\n            label: '个人资料',\n            onClick: ()=>router.push('/profile')\n        },\n        {\n            key: 'settings',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 166,\n                columnNumber: 13\n            }, undefined),\n            label: '账户设置',\n            onClick: ()=>router.push('/account-settings')\n        },\n        {\n            type: 'divider'\n        },\n        {\n            key: 'logout',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 175,\n                columnNumber: 13\n            }, undefined),\n            label: '退出登录',\n            onClick: logout\n        }\n    ];\n    const handleMenuClick = ({ key })=>{\n        router.push(key);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        style: {\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Sider, {\n                trigger: null,\n                collapsible: true,\n                collapsed: collapsed,\n                theme: \"dark\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            height: 64,\n                            margin: 16,\n                            background: 'rgba(255, 255, 255, 0.2)',\n                            borderRadius: 6,\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            color: 'white',\n                            fontWeight: 'bold',\n                            fontSize: collapsed ? 14 : 16\n                        },\n                        children: collapsed ? 'GM' : '游戏管理系统'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        theme: \"dark\",\n                        mode: \"inline\",\n                        selectedKeys: [\n                            pathname\n                        ],\n                        items: filteredMenuItems,\n                        onClick: handleMenuClick\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                        style: {\n                            padding: '0 16px',\n                            background: colorBgContainer,\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            borderBottom: '1px solid #f0f0f0'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                type: \"text\",\n                                icon: collapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 31\n                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 56\n                                }, void 0),\n                                onClick: ()=>setCollapsed(!collapsed),\n                                style: {\n                                    fontSize: '16px',\n                                    width: 64,\n                                    height: 64\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                        type: \"secondary\",\n                                        children: \"欢迎回来，\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        menu: {\n                                            items: userMenuItems\n                                        },\n                                        placement: \"bottomRight\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            style: {\n                                                cursor: 'pointer'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 31\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    strong: true,\n                                                    children: user?.displayName || user?.username\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                        style: {\n                            margin: '16px',\n                            padding: 24,\n                            minHeight: 280,\n                            background: colorBgContainer,\n                            borderRadius: borderRadiusLG\n                        },\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MainLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/MainLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   ROLES: () => (/* binding */ ROLES),\n/* harmony export */   checkPermission: () => (/* binding */ checkPermission),\n/* harmony export */   hasCustomerServicePermission: () => (/* binding */ hasCustomerServicePermission),\n/* harmony export */   hasPartnerPermission: () => (/* binding */ hasPartnerPermission),\n/* harmony export */   hasProductPermission: () => (/* binding */ hasProductPermission),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(ssr)/./node_modules/antd/es/message/index.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,ROLES,checkPermission,isAdmin,hasCustomerServicePermission,hasProductPermission,hasPartnerPermission auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // 检查本地存储中的用户信息\n            if (false) {}\n            setIsLoading(false);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (username, password)=>{\n        try {\n            setIsLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.login({\n                username,\n                password\n            });\n            const { token, user: userData } = response.data;\n            // 存储认证信息\n            if (false) {}\n            setUser(userData);\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('登录成功');\n            return true;\n        } catch (error) {\n            console.error('Login error:', error);\n            const errorMessage = error.response?.data?.message || '登录失败，请检查用户名和密码';\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(errorMessage);\n            return false;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            if (false) {}\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            // 清除本地存储\n            if (false) {}\n            setUser(null);\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('已退出登录');\n            // 重定向到登录页\n            window.location.href = '/login';\n        }\n    };\n    const hasRole = (role)=>{\n        return user?.roles?.includes(role) || false;\n    };\n    const hasAnyRole = (roles)=>{\n        return roles.some((role)=>hasRole(role));\n    };\n    const isAuthenticated = !!user;\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        login,\n        logout,\n        hasRole,\n        hasAnyRole\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/contexts/AuthContext.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n// 角色常量\nconst ROLES = {\n    SYSTEM_ADMIN: 'SystemAdmin',\n    PRODUCT_MANAGER: 'ProductManager',\n    PRODUCT_SPECIALIST: 'ProductSpecialist',\n    PARTNER_MANAGER: 'PartnerManager',\n    PARTNER_SPECIALIST: 'PartnerSpecialist',\n    CUSTOMER_SERVICE_MANAGER: 'CustomerServiceManager',\n    CUSTOMER_SERVICE_SPECIALIST: 'CustomerServiceSpecialist',\n    VIEWER: 'Viewer'\n};\n// 权限检查工具函数\nconst checkPermission = (userRoles, requiredRoles)=>{\n    return requiredRoles.some((role)=>userRoles.includes(role));\n};\n// 管理员角色检查\nconst isAdmin = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER\n    ]);\n};\n// 客服权限检查\nconst hasCustomerServicePermission = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER,\n        ROLES.CUSTOMER_SERVICE_MANAGER,\n        ROLES.CUSTOMER_SERVICE_SPECIALIST\n    ]);\n};\n// 产品管理权限检查\nconst hasProductPermission = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER,\n        ROLES.PRODUCT_SPECIALIST\n    ]);\n};\n// 渠道管理权限检查\nconst hasPartnerPermission = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER,\n        ROLES.PARTNER_MANAGER,\n        ROLES.PARTNER_SPECIALIST\n    ]);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   operationalDataApi: () => (/* binding */ operationalDataApi),\n/* harmony export */   playersApi: () => (/* binding */ playersApi),\n/* harmony export */   usersApi: () => (/* binding */ usersApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// API 基础配置\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5108/api';\n// 创建 axios 实例\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 10000,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// 请求拦截器 - 添加认证令牌\napiClient.interceptors.request.use((config)=>{\n    const token = localStorage.getItem('accessToken');\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器 - 处理认证错误\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, async (error)=>{\n    const originalRequest = error.config;\n    if (error.response?.status === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n            const refreshToken = localStorage.getItem('refreshToken');\n            if (refreshToken) {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${API_BASE_URL}/auth/refresh`, refreshToken);\n                const { token } = response.data;\n                localStorage.setItem('accessToken', token);\n                originalRequest.headers.Authorization = `Bearer ${token}`;\n                return apiClient(originalRequest);\n            }\n        } catch (refreshError) {\n            // 刷新令牌失败，清除本地存储并重定向到登录页\n            localStorage.removeItem('accessToken');\n            localStorage.removeItem('refreshToken');\n            localStorage.removeItem('user');\n            window.location.href = '/login';\n        }\n    }\n    return Promise.reject(error);\n});\n// API 方法\nconst authApi = {\n    login: (data)=>apiClient.post('/auth/login', data),\n    logout: (refreshToken)=>apiClient.post('/auth/logout', refreshToken),\n    register: (data)=>apiClient.post('/auth/register', data),\n    refreshToken: (refreshToken)=>apiClient.post('/auth/refresh', refreshToken),\n    changePassword: (currentPassword, newPassword)=>apiClient.post('/auth/change-password', {\n            currentPassword,\n            newPassword\n        }),\n    resetPassword: (email)=>apiClient.post('/auth/reset-password', {\n            email\n        })\n};\nconst usersApi = {\n    getUsers: ()=>apiClient.get('/users'),\n    getUser: (id)=>apiClient.get(`/users/${id}`),\n    getUserByUsername: (username)=>apiClient.get(`/users/by-username/${username}`),\n    createUser: (data)=>apiClient.post('/users', data),\n    updateUser: (id, data)=>apiClient.put(`/users/${id}`, data),\n    deleteUser: (id)=>apiClient.delete(`/users/${id}`),\n    activateUser: (id)=>apiClient.post(`/users/${id}/activate`),\n    deactivateUser: (id)=>apiClient.post(`/users/${id}/deactivate`),\n    getUsersByRole: (role)=>apiClient.get(`/users/by-role/${role}`)\n};\nconst playersApi = {\n    getPlayers: (page = 1, pageSize = 20)=>apiClient.get(`/players?page=${page}&pageSize=${pageSize}`),\n    getPlayer: (id)=>apiClient.get(`/players/${id}`),\n    getPlayerByAccountId: (accountId)=>apiClient.get(`/players/by-account/${accountId}`),\n    searchPlayers: (searchTerm)=>apiClient.get(`/players/search?searchTerm=${encodeURIComponent(searchTerm)}`),\n    getPlayerStats: ()=>apiClient.get('/players/stats'),\n    getTopPlayersByLevel: (count = 10)=>apiClient.get(`/players/top-by-level?count=${count}`),\n    getVipPlayers: (minVipLevel = 1)=>apiClient.get(`/players/vip?minVipLevel=${minVipLevel}`),\n    updatePlayer: (id, data)=>apiClient.put(`/players/${id}`, data),\n    banPlayer: (id, bannedUntil, reason)=>apiClient.post(`/players/${id}/ban`, {\n            bannedUntil,\n            reason\n        }),\n    unbanPlayer: (id)=>apiClient.post(`/players/${id}/unban`)\n};\n// 运营数据API方法\nconst operationalDataApi = {\n    // 全局统计\n    getGlobalStats: ()=>apiClient.get('/operational-data/global-stats'),\n    getGlobalStatsByDate: (date)=>apiClient.get(`/operational-data/global-stats/${date}`),\n    // 用户信息统计\n    getUserInfoStats: ()=>apiClient.get('/operational-data/user-info-stats'),\n    getUserInfoStatsByDateRange: (startDate, endDate)=>apiClient.get(`/operational-data/user-info-stats/${startDate}/${endDate}`),\n    // 付费信息统计\n    getPaymentInfoStats: ()=>apiClient.get('/operational-data/payment-info-stats'),\n    getPaymentInfoStatsByDateRange: (startDate, endDate)=>apiClient.get(`/operational-data/payment-info-stats/${startDate}/${endDate}`),\n    // 数据分析\n    getConversionAnalysis: (date)=>apiClient.get(`/operational-data/conversion-analysis/${date}`),\n    getRetentionAnalysis: (date)=>apiClient.get(`/operational-data/retention-analysis/${date}`),\n    getActiveUserAnalysis: (date)=>apiClient.get(`/operational-data/active-user-analysis/${date}`),\n    // 记录数据\n    recordVisit: (data)=>apiClient.post('/operational-data/record-visit', data)\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tanstack","vendor-chunks/next","vendor-chunks/antd","vendor-chunks/rc-picker","vendor-chunks/@ant-design","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/rc-util","vendor-chunks/rc-motion","vendor-chunks/rc-notification","vendor-chunks/@babel","vendor-chunks/rc-pagination","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/stylis","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/@emotion","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/classnames","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/throttle-debounce","vendor-chunks/@rc-component","vendor-chunks/rc-field-form","vendor-chunks/rc-menu","vendor-chunks/rc-tabs","vendor-chunks/resize-observer-polyfill","vendor-chunks/rc-textarea","vendor-chunks/rc-input","vendor-chunks/rc-overflow","vendor-chunks/rc-collapse","vendor-chunks/rc-resize-observer","vendor-chunks/rc-dropdown","vendor-chunks/rc-tooltip","vendor-chunks/copy-to-clipboard","vendor-chunks/toggle-selection","vendor-chunks/rc-table","vendor-chunks/rc-select","vendor-chunks/rc-tree","vendor-chunks/rc-virtual-list","vendor-chunks/rc-checkbox","vendor-chunks/dayjs","vendor-chunks/rc-progress"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Foperational-data%2Fpage&page=%2Foperational-data%2Fpage&appPaths=%2Foperational-data%2Fpage&pagePath=private-next-app-dir%2Foperational-data%2Fpage.tsx&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();