/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/security/page";
exports.ids = ["app/security/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsecurity%2Fpage&page=%2Fsecurity%2Fpage&appPaths=%2Fsecurity%2Fpage&pagePath=private-next-app-dir%2Fsecurity%2Fpage.tsx&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsecurity%2Fpage&page=%2Fsecurity%2Fpage&appPaths=%2Fsecurity%2Fpage&pagePath=private-next-app-dir%2Fsecurity%2Fpage.tsx&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/security/page.tsx */ \"(rsc)/./src/app/security/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'security',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/security/page\",\n        pathname: \"/security\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZzZWN1cml0eSUyRnBhZ2UmcGFnZT0lMkZzZWN1cml0eSUyRnBhZ2UmYXBwUGF0aHM9JTJGc2VjdXJpdHklMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGc2VjdXJpdHklMkZwYWdlLnRzeCZhcHBEaXI9JTJGVXNlcnMlMkZjYWZlJTJGRG9jdW1lbnRzJTJGZ2FtZW1hbmFnZXdlYiUyRnNyYyUyRmdhbWUtbWFuYWdlbWVudC13ZWIlMkZzcmMlMkZhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPSUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxzQkFBc0Isb0pBQW1IO0FBQ3pJLHNCQUFzQiwwTkFBZ0Y7QUFDdEcsc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsZ09BQW1GO0FBQ3pHLG9CQUFvQixrS0FBMEg7QUFHNUk7QUFHQTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0Msc2ZBQTJRO0FBQy9TO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxzZkFBMlE7QUFDL1M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFHckI7QUFDRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFHRTtBQUNGO0FBQ08sd0JBQXdCLHVHQUFrQjtBQUNqRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBtb2R1bGUwID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvYXBwL2xheW91dC50c3hcIik7XG5jb25zdCBtb2R1bGUxID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlMiA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2ZvcmJpZGRlbi1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTMgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy91bmF1dGhvcml6ZWQtZXJyb3JcIik7XG5jb25zdCBwYWdlNCA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2NhZmUvRG9jdW1lbnRzL2dhbWVtYW5hZ2V3ZWIvc3JjL2dhbWUtbWFuYWdlbWVudC13ZWIvc3JjL2FwcC9zZWN1cml0eS9wYWdlLnRzeFwiKTtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc3NyJ1xufTtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnc2VjdXJpdHknLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbcGFnZTQsIFwiL1VzZXJzL2NhZmUvRG9jdW1lbnRzL2dhbWVtYW5hZ2V3ZWIvc3JjL2dhbWUtbWFuYWdlbWVudC13ZWIvc3JjL2FwcC9zZWN1cml0eS9wYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyEvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fXCIpKS5kZWZhdWx0KHByb3BzKSldLFxuICAgIGFwcGxlOiBbXSxcbiAgICBvcGVuR3JhcGg6IFtdLFxuICAgIHR3aXR0ZXI6IFtdLFxuICAgIG1hbmlmZXN0OiB1bmRlZmluZWRcbiAgfVxuICAgICAgfVxuICAgICAgXVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogW21vZHVsZTAsIFwiL1VzZXJzL2NhZmUvRG9jdW1lbnRzL2dhbWVtYW5hZ2V3ZWIvc3JjL2dhbWUtbWFuYWdlbWVudC13ZWIvc3JjL2FwcC9sYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFttb2R1bGUxLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4nZm9yYmlkZGVuJzogW21vZHVsZTIsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2ZvcmJpZGRlbi1lcnJvclwiXSxcbid1bmF1dGhvcml6ZWQnOiBbbW9kdWxlMywgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCJdLFxuICAgICAgICBtZXRhZGF0YToge1xuICAgIGljb246IFsoYXN5bmMgKHByb3BzKSA9PiAoYXdhaXQgaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlcj90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhL1VzZXJzL2NhZmUvRG9jdW1lbnRzL2dhbWVtYW5hZ2V3ZWIvc3JjL2dhbWUtbWFuYWdlbWVudC13ZWIvc3JjL2FwcC9mYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfX1wiKSkuZGVmYXVsdChwcm9wcykpXSxcbiAgICBhcHBsZTogW10sXG4gICAgb3BlbkdyYXBoOiBbXSxcbiAgICB0d2l0dGVyOiBbXSxcbiAgICBtYW5pZmVzdDogdW5kZWZpbmVkXG4gIH1cbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIi9Vc2Vycy9jYWZlL0RvY3VtZW50cy9nYW1lbWFuYWdld2ViL3NyYy9nYW1lLW1hbmFnZW1lbnQtd2ViL3NyYy9hcHAvc2VjdXJpdHkvcGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL3NlY3VyaXR5L3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL3NlY3VyaXR5XCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogJycsXG4gICAgICAgIGZpbGVuYW1lOiAnJyxcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfVxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1wYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsecurity%2Fpage&page=%2Fsecurity%2Fpage&appPaths=%2Fsecurity%2Fpage&pagePath=private-next-app-dir%2Fsecurity%2Fpage.tsx&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQW1IIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvYXBwL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Fsecurity%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Fsecurity%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/security/page.tsx */ \"(rsc)/./src/app/security/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGc3JjJTJGYXBwJTJGc2VjdXJpdHklMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQTBIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvYXBwL3NlY3VyaXR5L3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Fsecurity%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy9jYWZlL0RvY3VtZW50cy9nYW1lbWFuYWdld2ViL3NyYy9nYW1lLW1hbmFnZW1lbnQtd2ViL3NyYy9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/security/page.tsx":
/*!***********************************!*\
  !*** ./src/app/security/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQW1IIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvYXBwL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Fsecurity%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Fsecurity%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/security/page.tsx */ \"(ssr)/./src/app/security/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGc3JjJTJGYXBwJTJGc2VjdXJpdHklMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQTBIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvYXBwL3NlY3VyaXR5L3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Fsecurity%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzE5Y2IwZmMzZjYzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ConfigProvider!=!antd */ \"(ssr)/./node_modules/antd/es/config-provider/index.js\");\n/* harmony import */ var antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! antd/locale/zh_CN */ \"(ssr)/./node_modules/antd/lib/locale/zh_CN.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// 创建 QueryClient 实例\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient({\n    defaultOptions: {\n        queries: {\n            staleTime: 5 * 60 * 1000,\n            retry: 1\n        }\n    }\n});\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"游戏管理系统\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"游戏运营管理后台系统\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n                    client: queryClient,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            locale: antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_8__.ReactQueryDevtools, {\n                            initialIsOpen: false\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/security/page.tsx":
/*!***********************************!*\
  !*** ./src/app/security/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Auth/ProtectedRoute */ \"(ssr)/./src/components/Auth/ProtectedRoute.tsx\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,DatePicker,Input,Modal,Row,Select,Space,Statistic,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,DatePicker,Input,Modal,Row,Select,Space,Statistic,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/date-picker/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,DatePicker,Input,Modal,Row,Select,Space,Statistic,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,DatePicker,Input,Modal,Row,Select,Space,Statistic,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,DatePicker,Input,Modal,Row,Select,Space,Statistic,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,DatePicker,Input,Modal,Row,Select,Space,Statistic,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,DatePicker,Input,Modal,Row,Select,Space,Statistic,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,DatePicker,Input,Modal,Row,Select,Space,Statistic,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,DatePicker,Input,Modal,Row,Select,Space,Statistic,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,DatePicker,Input,Modal,Row,Select,Space,Statistic,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,DatePicker,Input,Modal,Row,Select,Space,Statistic,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,DatePicker,Input,Modal,Row,Select,Space,Statistic,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,DatePicker,Input,Modal,Row,Select,Space,Statistic,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,DatePicker,Input,Modal,Row,Select,Space,Statistic,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,DatePicker,Input,Modal,Row,Select,Space,Statistic,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_EyeOutlined_SafetyOutlined_SearchOutlined_SecurityScanOutlined_StopOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=EyeOutlined,SafetyOutlined,SearchOutlined,SecurityScanOutlined,StopOutlined,WarningOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/WarningOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_EyeOutlined_SafetyOutlined_SearchOutlined_SecurityScanOutlined_StopOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=EyeOutlined,SafetyOutlined,SearchOutlined,SecurityScanOutlined,StopOutlined,WarningOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_EyeOutlined_SafetyOutlined_SearchOutlined_SecurityScanOutlined_StopOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=EyeOutlined,SafetyOutlined,SearchOutlined,SecurityScanOutlined,StopOutlined,WarningOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/SecurityScanOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_EyeOutlined_SafetyOutlined_SearchOutlined_SecurityScanOutlined_StopOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=EyeOutlined,SafetyOutlined,SearchOutlined,SecurityScanOutlined,StopOutlined,WarningOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/StopOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_EyeOutlined_SafetyOutlined_SearchOutlined_SecurityScanOutlined_StopOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=EyeOutlined,SafetyOutlined,SearchOutlined,SecurityScanOutlined,StopOutlined,WarningOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_EyeOutlined_SafetyOutlined_SearchOutlined_SecurityScanOutlined_StopOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=EyeOutlined,SafetyOutlined,SearchOutlined,SecurityScanOutlined,StopOutlined,WarningOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/SearchOutlined.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst { Title } = _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { RangePicker } = _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst SecurityPage = ()=>{\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchText, setSearchText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [riskLevel, setRiskLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [eventType, setEventType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    // 模拟安全事件数据\n    const securityEventsData = [\n        {\n            key: '1',\n            id: 7001,\n            type: 'suspicious_login',\n            title: '异常登录检测',\n            description: '玩家从异常地理位置登录',\n            riskLevel: 'high',\n            playerId: 12345,\n            playerName: '玩家ABC',\n            ip: '************',\n            location: '美国纽约',\n            deviceInfo: 'iPhone 13 Pro',\n            timestamp: '2024-06-25 14:30:25',\n            status: 'investigating',\n            actionTaken: null,\n            handledBy: '安全专员A'\n        },\n        {\n            key: '2',\n            id: 7002,\n            type: 'cheat_detection',\n            title: '外挂检测',\n            description: '检测到玩家使用速度外挂',\n            riskLevel: 'critical',\n            playerId: 67890,\n            playerName: '玩家XYZ',\n            ip: '*************',\n            location: '中国上海',\n            deviceInfo: 'Windows 10',\n            timestamp: '2024-06-25 13:45:12',\n            status: 'resolved',\n            actionTaken: '账号封禁7天',\n            handledBy: '安全专员B'\n        },\n        {\n            key: '3',\n            id: 7003,\n            type: 'payment_fraud',\n            title: '支付欺诈',\n            description: '检测到可疑的充值行为',\n            riskLevel: 'high',\n            playerId: 11111,\n            playerName: '玩家DEF',\n            ip: '*************',\n            location: '中国北京',\n            deviceInfo: 'Android 12',\n            timestamp: '2024-06-25 12:20:30',\n            status: 'pending',\n            actionTaken: null,\n            handledBy: null\n        },\n        {\n            key: '4',\n            id: 7004,\n            type: 'account_sharing',\n            title: '账号共享',\n            description: '检测到账号在多个设备同时登录',\n            riskLevel: 'medium',\n            playerId: 22222,\n            playerName: '玩家GHI',\n            ip: '************',\n            location: '中国广州',\n            deviceInfo: 'Multiple devices',\n            timestamp: '2024-06-25 11:15:45',\n            status: 'resolved',\n            actionTaken: '发送安全提醒',\n            handledBy: '安全专员A'\n        }\n    ];\n    const getRiskLevelTag = (level)=>{\n        switch(level){\n            case 'critical':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    color: \"red\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeOutlined_SafetyOutlined_SearchOutlined_SecurityScanOutlined_StopOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 39\n                    }, void 0),\n                    children: \"严重\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 16\n                }, undefined);\n            case 'high':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    color: \"orange\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeOutlined_SafetyOutlined_SearchOutlined_SecurityScanOutlined_StopOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 42\n                    }, void 0),\n                    children: \"高\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 16\n                }, undefined);\n            case 'medium':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    color: \"yellow\",\n                    children: \"中\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 16\n                }, undefined);\n            case 'low':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    color: \"green\",\n                    children: \"低\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    color: \"default\",\n                    children: \"未知\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getStatusTag = (status)=>{\n        switch(status){\n            case 'pending':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    color: \"red\",\n                    children: \"待处理\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 16\n                }, undefined);\n            case 'investigating':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    color: \"processing\",\n                    children: \"调查中\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 16\n                }, undefined);\n            case 'resolved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    color: \"success\",\n                    children: \"已处理\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    color: \"default\",\n                    children: \"未知\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getEventTypeTag = (type)=>{\n        const typeMap = {\n            suspicious_login: {\n                color: 'orange',\n                text: '异常登录'\n            },\n            cheat_detection: {\n                color: 'red',\n                text: '外挂检测'\n            },\n            payment_fraud: {\n                color: 'purple',\n                text: '支付欺诈'\n            },\n            account_sharing: {\n                color: 'blue',\n                text: '账号共享'\n            },\n            data_breach: {\n                color: 'red',\n                text: '数据泄露'\n            }\n        };\n        const config = typeMap[type] || {\n            color: 'default',\n            text: type\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            color: config.color,\n            children: config.text\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n            lineNumber: 151,\n            columnNumber: 12\n        }, undefined);\n    };\n    const columns = [\n        {\n            title: 'ID',\n            dataIndex: 'id',\n            key: 'id',\n            width: 80\n        },\n        {\n            title: '事件类型',\n            key: 'type',\n            width: 120,\n            render: (record)=>getEventTypeTag(record.type)\n        },\n        {\n            title: '事件标题',\n            key: 'title',\n            width: 200,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontWeight: 500,\n                                marginBottom: 4\n                            },\n                            children: record.title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '12px',\n                                color: '#666'\n                            },\n                            children: record.description\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: '风险等级',\n            key: 'riskLevel',\n            width: 100,\n            render: (record)=>getRiskLevelTag(record.riskLevel)\n        },\n        {\n            title: '涉及玩家',\n            key: 'player',\n            width: 150,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontWeight: 500\n                            },\n                            children: record.playerName\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '12px',\n                                color: '#666'\n                            },\n                            children: [\n                                \"ID: \",\n                                record.playerId\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: 'IP地址',\n            key: 'ip',\n            width: 120,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontFamily: 'monospace',\n                                fontSize: '12px'\n                            },\n                            children: record.ip\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '11px',\n                                color: '#666'\n                            },\n                            children: record.location\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: '状态',\n            key: 'status',\n            width: 100,\n            render: (record)=>getStatusTag(record.status)\n        },\n        {\n            title: '处理措施',\n            dataIndex: 'actionTaken',\n            key: 'actionTaken',\n            width: 150,\n            render: (action)=>action ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        color: '#52c41a'\n                    },\n                    children: action\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        color: '#999'\n                    },\n                    children: \"未处理\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 11\n                }, undefined)\n        },\n        {\n            title: '处理人',\n            dataIndex: 'handledBy',\n            key: 'handledBy',\n            width: 120,\n            render: (handler)=>handler ? handler : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        color: '#999'\n                    },\n                    children: \"未分配\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 29\n                }, undefined)\n        },\n        {\n            title: '发生时间',\n            dataIndex: 'timestamp',\n            key: 'timestamp',\n            width: 160\n        },\n        {\n            title: '操作',\n            key: 'action',\n            width: 200,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    size: \"small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeOutlined_SafetyOutlined_SearchOutlined_SecurityScanOutlined_StopOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>handleViewDetail(record),\n                            children: \"详情\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, undefined),\n                        record.status === 'pending' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeOutlined_SafetyOutlined_SearchOutlined_SecurityScanOutlined_StopOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 21\n                            }, void 0),\n                            onClick: ()=>handleProcess(record),\n                            children: \"处理\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            type: \"link\",\n                            size: \"small\",\n                            danger: true,\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeOutlined_SafetyOutlined_SearchOutlined_SecurityScanOutlined_StopOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>handleBanPlayer(record),\n                            children: \"封禁\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ];\n    const handleViewDetail = (record)=>{\n        _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"].info({\n            title: '安全事件详情',\n            width: 700,\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: 16\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: [\n                                getEventTypeTag(record.type),\n                                getRiskLevelTag(record.riskLevel),\n                                getStatusTag(record.status)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: 16\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            gutter: 16,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    span: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"事件标题：\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" \",\n                                        record.title\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    span: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"发生时间：\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" \",\n                                        record.timestamp\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: 16\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"事件描述：\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, undefined),\n                            \" \",\n                            record.description\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: 16\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            gutter: 16,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    span: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"涉及玩家：\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" \",\n                                        record.playerName,\n                                        \" (ID: \",\n                                        record.playerId,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    span: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"设备信息：\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" \",\n                                        record.deviceInfo\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: 16\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            gutter: 16,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    span: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"IP地址：\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" \",\n                                        record.ip\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    span: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"地理位置：\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" \",\n                                        record.location\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: 16\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            gutter: 16,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    span: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"处理状态：\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" \",\n                                        getStatusTag(record.status)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    span: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"处理人：\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" \",\n                                        record.handledBy || '未分配'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 11\n                    }, undefined),\n                    record.actionTaken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: 16\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"处理措施：\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: 8,\n                                    padding: 12,\n                                    backgroundColor: '#f6ffed',\n                                    borderRadius: 4,\n                                    border: '1px solid #b7eb8f'\n                                },\n                                children: record.actionTaken\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                lineNumber: 289,\n                columnNumber: 9\n            }, undefined)\n        });\n    };\n    const handleProcess = (record)=>{\n        _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"].confirm({\n            title: '处理安全事件',\n            content: `确定要处理安全事件 \"${record.title}\" 吗？`,\n            onOk () {\n                _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].success(`已开始处理安全事件: ${record.title}`);\n            }\n        });\n    };\n    const handleBanPlayer = (record)=>{\n        _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"].confirm({\n            title: '封禁玩家',\n            content: `确定要封禁玩家 \"${record.playerName}\" 吗？`,\n            onOk () {\n                _barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].success(`已封禁玩家: ${record.playerName}`);\n            }\n        });\n    };\n    const handleSearch = (value)=>{\n        setSearchText(value);\n    };\n    // 计算统计数据\n    const totalEvents = securityEventsData.length;\n    const pendingEvents = securityEventsData.filter((e)=>e.status === 'pending').length;\n    const criticalEvents = securityEventsData.filter((e)=>e.riskLevel === 'critical').length;\n    const highRiskEvents = securityEventsData.filter((e)=>e.riskLevel === 'high').length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        requiredRoles: [\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.ROLES.SYSTEM_ADMIN\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                    level: 2,\n                    children: \"安全管理\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 392,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    gutter: [\n                        16,\n                        16\n                    ],\n                    style: {\n                        marginBottom: 24\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    title: \"总安全事件\",\n                                    value: totalEvents,\n                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeOutlined_SafetyOutlined_SearchOutlined_SecurityScanOutlined_StopOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    valueStyle: {\n                                        color: '#1890ff'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    title: \"待处理事件\",\n                                    value: pendingEvents,\n                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeOutlined_SafetyOutlined_SearchOutlined_SecurityScanOutlined_StopOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    valueStyle: {\n                                        color: '#faad14'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    title: \"严重风险\",\n                                    value: criticalEvents,\n                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeOutlined_SafetyOutlined_SearchOutlined_SecurityScanOutlined_StopOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    valueStyle: {\n                                        color: '#ff4d4f'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    title: \"高风险事件\",\n                                    value: highRiskEvents,\n                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeOutlined_SafetyOutlined_SearchOutlined_SecurityScanOutlined_StopOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    valueStyle: {\n                                        color: '#fa8c16'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 9\n                }, undefined),\n                (pendingEvents > 0 || criticalEvents > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    message: \"安全警告\",\n                    description: `当前有 ${pendingEvents} 个待处理安全事件，其中 ${criticalEvents} 个为严重风险事件，请及时处理。`,\n                    type: \"warning\",\n                    showIcon: true,\n                    style: {\n                        marginBottom: 16\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 440,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    style: {\n                        marginBottom: 16\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        gutter: [\n                            16,\n                            16\n                        ],\n                        align: \"middle\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                xs: 24,\n                                sm: 8,\n                                lg: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].Search, {\n                                    placeholder: \"搜索事件标题\",\n                                    allowClear: true,\n                                    onSearch: handleSearch,\n                                    style: {\n                                        width: '100%'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                xs: 24,\n                                sm: 6,\n                                lg: 4,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    placeholder: \"事件类型\",\n                                    style: {\n                                        width: '100%'\n                                    },\n                                    value: eventType,\n                                    onChange: setEventType,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Option, {\n                                            value: \"all\",\n                                            children: \"全部类型\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Option, {\n                                            value: \"suspicious_login\",\n                                            children: \"异常登录\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Option, {\n                                            value: \"cheat_detection\",\n                                            children: \"外挂检测\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Option, {\n                                            value: \"payment_fraud\",\n                                            children: \"支付欺诈\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Option, {\n                                            value: \"account_sharing\",\n                                            children: \"账号共享\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                xs: 24,\n                                sm: 6,\n                                lg: 4,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    placeholder: \"风险等级\",\n                                    style: {\n                                        width: '100%'\n                                    },\n                                    value: riskLevel,\n                                    onChange: setRiskLevel,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Option, {\n                                            value: \"all\",\n                                            children: \"全部等级\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Option, {\n                                            value: \"critical\",\n                                            children: \"严重\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Option, {\n                                            value: \"high\",\n                                            children: \"高\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Option, {\n                                            value: \"medium\",\n                                            children: \"中\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Option, {\n                                            value: \"low\",\n                                            children: \"低\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                xs: 24,\n                                sm: 8,\n                                lg: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RangePicker, {\n                                    style: {\n                                        width: '100%'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                xs: 24,\n                                sm: 6,\n                                lg: 4,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    type: \"primary\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeOutlined_SafetyOutlined_SearchOutlined_SecurityScanOutlined_StopOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 44\n                                    }, void 0),\n                                    block: true,\n                                    children: \"搜索\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_DatePicker_Input_Modal_Row_Select_Space_Statistic_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                        columns: columns,\n                        dataSource: securityEventsData,\n                        loading: loading,\n                        pagination: {\n                            total: 89,\n                            pageSize: 20,\n                            showSizeChanger: true,\n                            showQuickJumper: true,\n                            showTotal: (total, range)=>`第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n                        },\n                        scroll: {\n                            x: 1600\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                        lineNumber: 501,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n                    lineNumber: 500,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n            lineNumber: 391,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx\",\n        lineNumber: 390,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SecurityPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/security/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/Auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Spin_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Spin!=!antd */ \"(ssr)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(ssr)/./src/components/Layout/MainLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst ProtectedRoute = ({ children, requiredRoles = [] })=>{\n    const { isAuthenticated, isLoading, hasAnyRole } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            if (!isLoading) {\n                if (!isAuthenticated) {\n                    // 未登录，重定向到登录页\n                    router.push('/login');\n                    return;\n                }\n                if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {\n                    // 没有权限，重定向到仪表板或显示无权限页面\n                    router.push('/dashboard');\n                    return;\n                }\n            }\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        hasAnyRole,\n        requiredRoles,\n        router\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                justifyContent: 'center',\n                alignItems: 'center',\n                minHeight: '100vh'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Spin_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Auth/ProtectedRoute.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Auth/ProtectedRoute.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!isAuthenticated) {\n        return null; // 将重定向到登录页\n    }\n    if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {\n        return null; // 将重定向到仪表板\n    }\n    // 如果是登录页面，不使用主布局\n    if (pathname === '/login') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    // 使用主布局包装受保护的页面\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Auth/ProtectedRoute.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProtectedRoute);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9BdXRoL1Byb3RlY3RlZFJvdXRlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRXlDO0FBQ1E7QUFDUTtBQUM3QjtBQUM0QjtBQU94RCxNQUFNTyxpQkFBZ0QsQ0FBQyxFQUNyREMsUUFBUSxFQUNSQyxnQkFBZ0IsRUFBRSxFQUNuQjtJQUNDLE1BQU0sRUFBRUMsZUFBZSxFQUFFQyxTQUFTLEVBQUVDLFVBQVUsRUFBRSxHQUFHViw4REFBT0E7SUFDMUQsTUFBTVcsU0FBU1YsMERBQVNBO0lBQ3hCLE1BQU1XLFdBQVdWLDREQUFXQTtJQUU1QkgsZ0RBQVNBO29DQUFDO1lBQ1IsSUFBSSxDQUFDVSxXQUFXO2dCQUNkLElBQUksQ0FBQ0QsaUJBQWlCO29CQUNwQixjQUFjO29CQUNkRyxPQUFPRSxJQUFJLENBQUM7b0JBQ1o7Z0JBQ0Y7Z0JBRUEsSUFBSU4sY0FBY08sTUFBTSxHQUFHLEtBQUssQ0FBQ0osV0FBV0gsZ0JBQWdCO29CQUMxRCx1QkFBdUI7b0JBQ3ZCSSxPQUFPRSxJQUFJLENBQUM7b0JBQ1o7Z0JBQ0Y7WUFDRjtRQUNGO21DQUFHO1FBQUNMO1FBQWlCQztRQUFXQztRQUFZSDtRQUFlSTtLQUFPO0lBRWxFLElBQUlGLFdBQVc7UUFDYixxQkFDRSw4REFBQ007WUFBSUMsT0FBTztnQkFDVkMsU0FBUztnQkFDVEMsZ0JBQWdCO2dCQUNoQkMsWUFBWTtnQkFDWkMsV0FBVztZQUNiO3NCQUNFLDRFQUFDakIsd0VBQUlBO2dCQUFDa0IsTUFBSzs7Ozs7Ozs7Ozs7SUFHakI7SUFFQSxJQUFJLENBQUNiLGlCQUFpQjtRQUNwQixPQUFPLE1BQU0sV0FBVztJQUMxQjtJQUVBLElBQUlELGNBQWNPLE1BQU0sR0FBRyxLQUFLLENBQUNKLFdBQVdILGdCQUFnQjtRQUMxRCxPQUFPLE1BQU0sV0FBVztJQUMxQjtJQUVBLGlCQUFpQjtJQUNqQixJQUFJSyxhQUFhLFVBQVU7UUFDekIscUJBQU87c0JBQUdOOztJQUNaO0lBRUEsZ0JBQWdCO0lBQ2hCLHFCQUNFLDhEQUFDRixxRUFBVUE7a0JBQ1JFOzs7Ozs7QUFHUDtBQUVBLGlFQUFlRCxjQUFjQSxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvY29tcG9uZW50cy9BdXRoL1Byb3RlY3RlZFJvdXRlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIsIHVzZVBhdGhuYW1lIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IFNwaW4gfSBmcm9tICdhbnRkJztcbmltcG9ydCBNYWluTGF5b3V0IGZyb20gJ0AvY29tcG9uZW50cy9MYXlvdXQvTWFpbkxheW91dCc7XG5cbmludGVyZmFjZSBQcm90ZWN0ZWRSb3V0ZVByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgcmVxdWlyZWRSb2xlcz86IHN0cmluZ1tdO1xufVxuXG5jb25zdCBQcm90ZWN0ZWRSb3V0ZTogUmVhY3QuRkM8UHJvdGVjdGVkUm91dGVQcm9wcz4gPSAoeyBcbiAgY2hpbGRyZW4sIFxuICByZXF1aXJlZFJvbGVzID0gW10gXG59KSA9PiB7XG4gIGNvbnN0IHsgaXNBdXRoZW50aWNhdGVkLCBpc0xvYWRpbmcsIGhhc0FueVJvbGUgfSA9IHVzZUF1dGgoKTtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghaXNMb2FkaW5nKSB7XG4gICAgICBpZiAoIWlzQXV0aGVudGljYXRlZCkge1xuICAgICAgICAvLyDmnKrnmbvlvZXvvIzph43lrprlkJHliLDnmbvlvZXpobVcbiAgICAgICAgcm91dGVyLnB1c2goJy9sb2dpbicpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGlmIChyZXF1aXJlZFJvbGVzLmxlbmd0aCA+IDAgJiYgIWhhc0FueVJvbGUocmVxdWlyZWRSb2xlcykpIHtcbiAgICAgICAgLy8g5rKh5pyJ5p2D6ZmQ77yM6YeN5a6a5ZCR5Yiw5Luq6KGo5p2/5oiW5pi+56S65peg5p2D6ZmQ6aG16Z2iXG4gICAgICAgIHJvdXRlci5wdXNoKCcvZGFzaGJvYXJkJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFtpc0F1dGhlbnRpY2F0ZWQsIGlzTG9hZGluZywgaGFzQW55Um9sZSwgcmVxdWlyZWRSb2xlcywgcm91dGVyXSk7XG5cbiAgaWYgKGlzTG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgbWluSGVpZ2h0OiAnMTAwdmgnLFxuICAgICAgfX0+XG4gICAgICAgIDxTcGluIHNpemU9XCJsYXJnZVwiIC8+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgaWYgKCFpc0F1dGhlbnRpY2F0ZWQpIHtcbiAgICByZXR1cm4gbnVsbDsgLy8g5bCG6YeN5a6a5ZCR5Yiw55m75b2V6aG1XG4gIH1cblxuICBpZiAocmVxdWlyZWRSb2xlcy5sZW5ndGggPiAwICYmICFoYXNBbnlSb2xlKHJlcXVpcmVkUm9sZXMpKSB7XG4gICAgcmV0dXJuIG51bGw7IC8vIOWwhumHjeWumuWQkeWIsOS7quihqOadv1xuICB9XG5cbiAgLy8g5aaC5p6c5piv55m75b2V6aG16Z2i77yM5LiN5L2/55So5Li75biD5bGAXG4gIGlmIChwYXRobmFtZSA9PT0gJy9sb2dpbicpIHtcbiAgICByZXR1cm4gPD57Y2hpbGRyZW59PC8+O1xuICB9XG5cbiAgLy8g5L2/55So5Li75biD5bGA5YyF6KOF5Y+X5L+d5oqk55qE6aG16Z2iXG4gIHJldHVybiAoXG4gICAgPE1haW5MYXlvdXQ+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9NYWluTGF5b3V0PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgUHJvdGVjdGVkUm91dGU7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VFZmZlY3QiLCJ1c2VBdXRoIiwidXNlUm91dGVyIiwidXNlUGF0aG5hbWUiLCJTcGluIiwiTWFpbkxheW91dCIsIlByb3RlY3RlZFJvdXRlIiwiY2hpbGRyZW4iLCJyZXF1aXJlZFJvbGVzIiwiaXNBdXRoZW50aWNhdGVkIiwiaXNMb2FkaW5nIiwiaGFzQW55Um9sZSIsInJvdXRlciIsInBhdGhuYW1lIiwicHVzaCIsImxlbmd0aCIsImRpdiIsInN0eWxlIiwiZGlzcGxheSIsImp1c3RpZnlDb250ZW50IiwiYWxpZ25JdGVtcyIsIm1pbkhlaWdodCIsInNpemUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/MainLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/Layout/MainLayout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/theme/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/menu/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/dropdown/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/TeamOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/AppstoreOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/CloudServerOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/BellOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/LinkOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/SettingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/LogoutOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/MenuUnfoldOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/MenuFoldOutlined.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst { Header, Sider, Content } = _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Text } = _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst MainLayout = ({ children })=>{\n    const [collapsed, setCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, logout, hasRole, hasAnyRole } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { token: { colorBgContainer, borderRadiusLG } } = _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useToken();\n    // 菜单项配置\n    const menuItems = [\n        {\n            key: '/dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 44,\n                columnNumber: 13\n            }, undefined),\n            label: '仪表板',\n            roles: []\n        },\n        {\n            key: '/operational-data',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 50,\n                columnNumber: 13\n            }, undefined),\n            label: '运营数据',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n            ]\n        },\n        {\n            key: '/players',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 56,\n                columnNumber: 13\n            }, undefined),\n            label: '玩家管理',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.CUSTOMER_SERVICE_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.CUSTOMER_SERVICE_SPECIALIST\n            ]\n        },\n        {\n            key: '/payments',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 62,\n                columnNumber: 13\n            }, undefined),\n            label: '支付管理',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n            ]\n        },\n        {\n            key: '/games',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 68,\n                columnNumber: 13\n            }, undefined),\n            label: '游戏数据',\n            children: [\n                {\n                    key: '/games/activities',\n                    label: '活动管理',\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n                    ]\n                },\n                {\n                    key: '/games/announcements',\n                    label: '公告管理',\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n                    ]\n                },\n                {\n                    key: '/games/items',\n                    label: '道具管理',\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n                    ]\n                }\n            ]\n        },\n        {\n            key: '/servers',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 90,\n                columnNumber: 13\n            }, undefined),\n            label: '服务器管理',\n            children: [\n                {\n                    key: '/servers/status',\n                    label: '服务器状态',\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER\n                    ]\n                },\n                {\n                    key: '/servers/monitoring',\n                    label: '监控告警',\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER\n                    ]\n                },\n                {\n                    key: '/servers/logs',\n                    label: '日志管理',\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER\n                    ]\n                }\n            ]\n        },\n        {\n            key: '/customer-service',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 112,\n                columnNumber: 13\n            }, undefined),\n            label: '客服管理',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.CUSTOMER_SERVICE_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.CUSTOMER_SERVICE_SPECIALIST\n            ]\n        },\n        {\n            key: '/security',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 118,\n                columnNumber: 13\n            }, undefined),\n            label: '安全管理',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN\n            ]\n        },\n        {\n            key: '/reports',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 124,\n                columnNumber: 13\n            }, undefined),\n            label: '数据报表',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n            ]\n        },\n        {\n            key: '/channels',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 130,\n                columnNumber: 13\n            }, undefined),\n            label: '渠道管理',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER\n            ]\n        }\n    ];\n    // 过滤菜单项基于用户角色\n    const filterMenuItems = (items)=>{\n        return items.filter((item)=>{\n            if (item.roles && item.roles.length > 0) {\n                if (!hasAnyRole(item.roles)) {\n                    return false;\n                }\n            }\n            if (item.children) {\n                item.children = filterMenuItems(item.children);\n                return item.children.length > 0;\n            }\n            return true;\n        });\n    };\n    const filteredMenuItems = filterMenuItems(menuItems);\n    // 用户下拉菜单\n    const userMenuItems = [\n        {\n            key: 'profile',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 160,\n                columnNumber: 13\n            }, undefined),\n            label: '个人资料',\n            onClick: ()=>router.push('/profile')\n        },\n        {\n            key: 'settings',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 166,\n                columnNumber: 13\n            }, undefined),\n            label: '账户设置',\n            onClick: ()=>router.push('/account-settings')\n        },\n        {\n            type: 'divider'\n        },\n        {\n            key: 'logout',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 175,\n                columnNumber: 13\n            }, undefined),\n            label: '退出登录',\n            onClick: logout\n        }\n    ];\n    const handleMenuClick = ({ key })=>{\n        router.push(key);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        style: {\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Sider, {\n                trigger: null,\n                collapsible: true,\n                collapsed: collapsed,\n                theme: \"dark\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            height: 64,\n                            margin: 16,\n                            background: 'rgba(255, 255, 255, 0.2)',\n                            borderRadius: 6,\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            color: 'white',\n                            fontWeight: 'bold',\n                            fontSize: collapsed ? 14 : 16\n                        },\n                        children: collapsed ? 'GM' : '游戏管理系统'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        theme: \"dark\",\n                        mode: \"inline\",\n                        selectedKeys: [\n                            pathname\n                        ],\n                        items: filteredMenuItems,\n                        onClick: handleMenuClick\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                        style: {\n                            padding: '0 16px',\n                            background: colorBgContainer,\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            borderBottom: '1px solid #f0f0f0'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                type: \"text\",\n                                icon: collapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 31\n                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 56\n                                }, void 0),\n                                onClick: ()=>setCollapsed(!collapsed),\n                                style: {\n                                    fontSize: '16px',\n                                    width: 64,\n                                    height: 64\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                        type: \"secondary\",\n                                        children: \"欢迎回来，\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        menu: {\n                                            items: userMenuItems\n                                        },\n                                        placement: \"bottomRight\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            style: {\n                                                cursor: 'pointer'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 31\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    strong: true,\n                                                    children: user?.displayName || user?.username\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                        style: {\n                            margin: '16px',\n                            padding: 24,\n                            minHeight: 280,\n                            background: colorBgContainer,\n                            borderRadius: borderRadiusLG\n                        },\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MainLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/MainLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   ROLES: () => (/* binding */ ROLES),\n/* harmony export */   checkPermission: () => (/* binding */ checkPermission),\n/* harmony export */   hasCustomerServicePermission: () => (/* binding */ hasCustomerServicePermission),\n/* harmony export */   hasPartnerPermission: () => (/* binding */ hasPartnerPermission),\n/* harmony export */   hasProductPermission: () => (/* binding */ hasProductPermission),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(ssr)/./node_modules/antd/es/message/index.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,ROLES,checkPermission,isAdmin,hasCustomerServicePermission,hasProductPermission,hasPartnerPermission auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // 检查本地存储中的用户信息\n            if (false) {}\n            setIsLoading(false);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (username, password)=>{\n        try {\n            setIsLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.login({\n                username,\n                password\n            });\n            const { token, user: userData } = response.data;\n            // 存储认证信息\n            if (false) {}\n            setUser(userData);\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('登录成功');\n            return true;\n        } catch (error) {\n            console.error('Login error:', error);\n            const errorMessage = error.response?.data?.message || '登录失败，请检查用户名和密码';\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(errorMessage);\n            return false;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            if (false) {}\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            // 清除本地存储\n            if (false) {}\n            setUser(null);\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('已退出登录');\n            // 重定向到登录页\n            window.location.href = '/login';\n        }\n    };\n    const hasRole = (role)=>{\n        return user?.roles?.includes(role) || false;\n    };\n    const hasAnyRole = (roles)=>{\n        return roles.some((role)=>hasRole(role));\n    };\n    const isAuthenticated = !!user;\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        login,\n        logout,\n        hasRole,\n        hasAnyRole\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/contexts/AuthContext.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n// 角色常量\nconst ROLES = {\n    SYSTEM_ADMIN: 'SystemAdmin',\n    PRODUCT_MANAGER: 'ProductManager',\n    PRODUCT_SPECIALIST: 'ProductSpecialist',\n    PARTNER_MANAGER: 'PartnerManager',\n    PARTNER_SPECIALIST: 'PartnerSpecialist',\n    CUSTOMER_SERVICE_MANAGER: 'CustomerServiceManager',\n    CUSTOMER_SERVICE_SPECIALIST: 'CustomerServiceSpecialist',\n    VIEWER: 'Viewer'\n};\n// 权限检查工具函数\nconst checkPermission = (userRoles, requiredRoles)=>{\n    return requiredRoles.some((role)=>userRoles.includes(role));\n};\n// 管理员角色检查\nconst isAdmin = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER\n    ]);\n};\n// 客服权限检查\nconst hasCustomerServicePermission = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER,\n        ROLES.CUSTOMER_SERVICE_MANAGER,\n        ROLES.CUSTOMER_SERVICE_SPECIALIST\n    ]);\n};\n// 产品管理权限检查\nconst hasProductPermission = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER,\n        ROLES.PRODUCT_SPECIALIST\n    ]);\n};\n// 渠道管理权限检查\nconst hasPartnerPermission = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER,\n        ROLES.PARTNER_MANAGER,\n        ROLES.PARTNER_SPECIALIST\n    ]);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   operationalDataApi: () => (/* binding */ operationalDataApi),\n/* harmony export */   playersApi: () => (/* binding */ playersApi),\n/* harmony export */   usersApi: () => (/* binding */ usersApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// API 基础配置\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5108/api';\n// 创建 axios 实例\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 10000,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// 请求拦截器 - 添加认证令牌\napiClient.interceptors.request.use((config)=>{\n    const token = localStorage.getItem('accessToken');\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器 - 处理认证错误\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, async (error)=>{\n    const originalRequest = error.config;\n    if (error.response?.status === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n            const refreshToken = localStorage.getItem('refreshToken');\n            if (refreshToken) {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${API_BASE_URL}/auth/refresh`, refreshToken);\n                const { token } = response.data;\n                localStorage.setItem('accessToken', token);\n                originalRequest.headers.Authorization = `Bearer ${token}`;\n                return apiClient(originalRequest);\n            }\n        } catch (refreshError) {\n            // 刷新令牌失败，清除本地存储并重定向到登录页\n            localStorage.removeItem('accessToken');\n            localStorage.removeItem('refreshToken');\n            localStorage.removeItem('user');\n            window.location.href = '/login';\n        }\n    }\n    return Promise.reject(error);\n});\n// API 方法\nconst authApi = {\n    login: (data)=>apiClient.post('/auth/login', data),\n    logout: (refreshToken)=>apiClient.post('/auth/logout', refreshToken),\n    register: (data)=>apiClient.post('/auth/register', data),\n    refreshToken: (refreshToken)=>apiClient.post('/auth/refresh', refreshToken),\n    changePassword: (currentPassword, newPassword)=>apiClient.post('/auth/change-password', {\n            currentPassword,\n            newPassword\n        }),\n    resetPassword: (email)=>apiClient.post('/auth/reset-password', {\n            email\n        })\n};\nconst usersApi = {\n    getUsers: ()=>apiClient.get('/users'),\n    getUser: (id)=>apiClient.get(`/users/${id}`),\n    getUserByUsername: (username)=>apiClient.get(`/users/by-username/${username}`),\n    createUser: (data)=>apiClient.post('/users', data),\n    updateUser: (id, data)=>apiClient.put(`/users/${id}`, data),\n    deleteUser: (id)=>apiClient.delete(`/users/${id}`),\n    activateUser: (id)=>apiClient.post(`/users/${id}/activate`),\n    deactivateUser: (id)=>apiClient.post(`/users/${id}/deactivate`),\n    getUsersByRole: (role)=>apiClient.get(`/users/by-role/${role}`)\n};\nconst playersApi = {\n    getPlayers: (page = 1, pageSize = 20)=>apiClient.get(`/players?page=${page}&pageSize=${pageSize}`),\n    getPlayer: (id)=>apiClient.get(`/players/${id}`),\n    getPlayerByAccountId: (accountId)=>apiClient.get(`/players/by-account/${accountId}`),\n    searchPlayers: (searchTerm)=>apiClient.get(`/players/search?searchTerm=${encodeURIComponent(searchTerm)}`),\n    getPlayerStats: ()=>apiClient.get('/players/stats'),\n    getTopPlayersByLevel: (count = 10)=>apiClient.get(`/players/top-by-level?count=${count}`),\n    getVipPlayers: (minVipLevel = 1)=>apiClient.get(`/players/vip?minVipLevel=${minVipLevel}`),\n    updatePlayer: (id, data)=>apiClient.put(`/players/${id}`, data),\n    banPlayer: (id, bannedUntil, reason)=>apiClient.post(`/players/${id}/ban`, {\n            bannedUntil,\n            reason\n        }),\n    unbanPlayer: (id)=>apiClient.post(`/players/${id}/unban`)\n};\n// 运营数据API方法\nconst operationalDataApi = {\n    // 全局统计\n    getGlobalStats: ()=>apiClient.get('/operational-data/global-stats'),\n    getGlobalStatsByDate: (date)=>apiClient.get(`/operational-data/global-stats/${date}`),\n    // 用户信息统计\n    getUserInfoStats: ()=>apiClient.get('/operational-data/user-info-stats'),\n    getUserInfoStatsByDateRange: (startDate, endDate)=>apiClient.get(`/operational-data/user-info-stats/${startDate}/${endDate}`),\n    // 付费信息统计\n    getPaymentInfoStats: ()=>apiClient.get('/operational-data/payment-info-stats'),\n    getPaymentInfoStatsByDateRange: (startDate, endDate)=>apiClient.get(`/operational-data/payment-info-stats/${startDate}/${endDate}`),\n    // 数据分析\n    getConversionAnalysis: (date)=>apiClient.get(`/operational-data/conversion-analysis/${date}`),\n    getRetentionAnalysis: (date)=>apiClient.get(`/operational-data/retention-analysis/${date}`),\n    getActiveUserAnalysis: (date)=>apiClient.get(`/operational-data/active-user-analysis/${date}`),\n    // 记录数据\n    recordVisit: (data)=>apiClient.post('/operational-data/record-visit', data)\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tanstack","vendor-chunks/antd","vendor-chunks/next","vendor-chunks/@ant-design","vendor-chunks/rc-picker","vendor-chunks/@rc-component","vendor-chunks/mime-db","vendor-chunks/rc-table","vendor-chunks/axios","vendor-chunks/rc-select","vendor-chunks/rc-tree","vendor-chunks/rc-field-form","vendor-chunks/rc-menu","vendor-chunks/rc-tabs","vendor-chunks/rc-util","vendor-chunks/rc-virtual-list","vendor-chunks/resize-observer-polyfill","vendor-chunks/rc-motion","vendor-chunks/rc-notification","vendor-chunks/@babel","vendor-chunks/rc-pagination","vendor-chunks/rc-textarea","vendor-chunks/rc-input","vendor-chunks/follow-redirects","vendor-chunks/rc-overflow","vendor-chunks/debug","vendor-chunks/stylis","vendor-chunks/form-data","vendor-chunks/rc-collapse","vendor-chunks/get-intrinsic","vendor-chunks/rc-resize-observer","vendor-chunks/rc-dropdown","vendor-chunks/asynckit","vendor-chunks/rc-tooltip","vendor-chunks/throttle-debounce","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/rc-checkbox","vendor-chunks/copy-to-clipboard","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/@emotion","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/classnames","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/toggle-selection","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/dayjs","vendor-chunks/rc-dialog"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsecurity%2Fpage&page=%2Fsecurity%2Fpage&appPaths=%2Fsecurity%2Fpage&pagePath=private-next-app-dir%2Fsecurity%2Fpage.tsx&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();