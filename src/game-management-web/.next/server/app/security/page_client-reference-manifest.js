globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/security/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/layout.tsx":{"*":{"id":"(ssr)/./src/app/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/operational-data/page.tsx":{"*":{"id":"(ssr)/./src/app/operational-data/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/players/page.tsx":{"*":{"id":"(ssr)/./src/app/players/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/security/page.tsx":{"*":{"id":"(ssr)/./src/app/security/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/payments/page.tsx":{"*":{"id":"(ssr)/./src/app/payments/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/dashboard/page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx":{"id":"(app-pages-browser)/./src/app/layout.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx":{"id":"(app-pages-browser)/./src/app/operational-data/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/players/page.tsx":{"id":"(app-pages-browser)/./src/app/players/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page.tsx":{"id":"(app-pages-browser)/./src/app/security/page.tsx","name":"*","chunks":["app/security/page","static/chunks/app/security/page.js"],"async":false},"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/payments/page.tsx":{"id":"(app-pages-browser)/./src/app/payments/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/":[],"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout":["static/css/app/layout.css"],"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/security/page":[]}}