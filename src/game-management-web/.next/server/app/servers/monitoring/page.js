/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/servers/monitoring/page";
exports.ids = ["app/servers/monitoring/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fservers%2Fmonitoring%2Fpage&page=%2Fservers%2Fmonitoring%2Fpage&appPaths=%2Fservers%2Fmonitoring%2Fpage&pagePath=private-next-app-dir%2Fservers%2Fmonitoring%2Fpage.tsx&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fservers%2Fmonitoring%2Fpage&page=%2Fservers%2Fmonitoring%2Fpage&appPaths=%2Fservers%2Fmonitoring%2Fpage&pagePath=private-next-app-dir%2Fservers%2Fmonitoring%2Fpage.tsx&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'servers',\n        {\n        children: [\n        'monitoring',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/servers/monitoring/page.tsx */ \"(rsc)/./src/app/servers/monitoring/page.tsx\")), \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/servers/monitoring/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/servers/monitoring/page\",\n        pathname: \"/servers/monitoring\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fservers%2Fmonitoring%2Fpage&page=%2Fservers%2Fmonitoring%2Fpage&appPaths=%2Fservers%2Fmonitoring%2Fpage&pagePath=private-next-app-dir%2Fservers%2Fmonitoring%2Fpage.tsx&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQW1IIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ2FtZS1tYW5hZ2VtZW50LXdlYi8/ZWUxYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9jYWZlL0RvY3VtZW50cy9nYW1lbWFuYWdld2ViL3NyYy9nYW1lLW1hbmFnZW1lbnQtd2ViL3NyYy9hcHAvbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Fservers%2Fmonitoring%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Fservers%2Fmonitoring%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/servers/monitoring/page.tsx */ \"(ssr)/./src/app/servers/monitoring/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGc3JjJTJGYXBwJTJGc2VydmVycyUyRm1vbml0b3JpbmclMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0xBQW9JIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ2FtZS1tYW5hZ2VtZW50LXdlYi8/MDJjOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9jYWZlL0RvY3VtZW50cy9nYW1lbWFuYWdld2ViL3NyYy9nYW1lLW1hbmFnZW1lbnQtd2ViL3NyYy9hcHAvc2VydmVycy9tb25pdG9yaW5nL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Fservers%2Fmonitoring%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// 创建 QueryClient 实例\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient({\n    defaultOptions: {\n        queries: {\n            staleTime: 5 * 60 * 1000,\n            retry: 1\n        }\n    }\n});\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"游戏管理系统\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"游戏运营管理后台系统\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n                    client: queryClient,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_6__.ReactQueryDevtools, {\n                            initialIsOpen: false\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/servers/monitoring/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/servers/monitoring/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Auth/ProtectedRoute */ \"(ssr)/./src/components/Auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(ssr)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(ssr)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/progress */ \"(ssr)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,Bell,CheckCircle,Clock,Cpu,HardDrive,Play,RotateCcw,Server,Settings,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,Bell,CheckCircle,Clock,Cpu,HardDrive,Play,RotateCcw,Server,Settings,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,Bell,CheckCircle,Clock,Cpu,HardDrive,Play,RotateCcw,Server,Settings,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,Bell,CheckCircle,Clock,Cpu,HardDrive,Play,RotateCcw,Server,Settings,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,Bell,CheckCircle,Clock,Cpu,HardDrive,Play,RotateCcw,Server,Settings,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,Bell,CheckCircle,Clock,Cpu,HardDrive,Play,RotateCcw,Server,Settings,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,Bell,CheckCircle,Clock,Cpu,HardDrive,Play,RotateCcw,Server,Settings,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,Bell,CheckCircle,Clock,Cpu,HardDrive,Play,RotateCcw,Server,Settings,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,Bell,CheckCircle,Clock,Cpu,HardDrive,Play,RotateCcw,Server,Settings,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,Bell,CheckCircle,Clock,Cpu,HardDrive,Play,RotateCcw,Server,Settings,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,Bell,CheckCircle,Clock,Cpu,HardDrive,Play,RotateCcw,Server,Settings,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,Bell,CheckCircle,Clock,Cpu,HardDrive,Play,RotateCcw,Server,Settings,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,Bell,CheckCircle,Clock,Cpu,HardDrive,Play,RotateCcw,Server,Settings,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,Bell,CheckCircle,Clock,Cpu,HardDrive,Play,RotateCcw,Server,Settings,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n// Server status mapping\nconst ServerStatusMap = {\n    0: {\n        label: \"离线\",\n        color: \"bg-gray-100 text-gray-800\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    1: {\n        label: \"在线\",\n        color: \"bg-green-100 text-green-800\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    2: {\n        label: \"维护\",\n        color: \"bg-yellow-100 text-yellow-800\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    3: {\n        label: \"错误\",\n        color: \"bg-red-100 text-red-800\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    }\n};\n// API functions\nconst monitoringApi = {\n    async getServerStatus () {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(\"http://localhost:5109/api/ServerMonitoring/servers/status\", {\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) throw new Error(\"Failed to fetch server status\");\n        return response.json();\n    },\n    async getMonitoringStats () {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(\"http://localhost:5109/api/ServerMonitoring/stats\", {\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) throw new Error(\"Failed to fetch monitoring stats\");\n        return response.json();\n    },\n    async getServerMonitoring (serverId) {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(`http://localhost:5109/api/ServerMonitoring/servers/${serverId}/monitoring`, {\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) throw new Error(\"Failed to fetch server monitoring data\");\n        return response.json();\n    },\n    async restartServer (serverId) {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(`http://localhost:5109/api/ServerMonitoring/servers/${serverId}/restart`, {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": `Bearer ${token}`\n            }\n        });\n        if (!response.ok) throw new Error(\"Failed to restart server\");\n    },\n    async performHealthCheck (serverId) {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(`http://localhost:5109/api/ServerMonitoring/servers/${serverId}/health-check`, {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": `Bearer ${token}`\n            }\n        });\n        if (!response.ok) throw new Error(\"Failed to perform health check\");\n    }\n};\nconst MonitoringPage = ()=>{\n    // State management\n    const [servers, setServers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedServer, setSelectedServer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Data loading\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const [serverData, statsData] = await Promise.all([\n                monitoringApi.getServerStatus(),\n                monitoringApi.getMonitoringStats()\n            ]);\n            setServers(serverData);\n            setStats(statsData);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"加载监控数据失败\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadData();\n    }, []);\n    // Auto refresh every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!autoRefresh) return;\n        const interval = setInterval(()=>{\n            loadData();\n        }, 30000);\n        return ()=>clearInterval(interval);\n    }, [\n        autoRefresh\n    ]);\n    // Event handlers\n    const handleRefresh = ()=>{\n        loadData();\n    };\n    const handleRestartServer = async (serverId)=>{\n        if (!confirm(\"确定要重启这个服务器吗？\")) return;\n        try {\n            setLoading(true);\n            await monitoringApi.restartServer(serverId);\n            await loadData();\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"重启服务器失败\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleHealthCheck = async (serverId)=>{\n        try {\n            setLoading(true);\n            await monitoringApi.performHealthCheck(serverId);\n            await loadData();\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"健康检查失败\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Helper functions\n    const getUsageColor = (usage)=>{\n        if (usage >= 90) return \"text-red-600\";\n        if (usage >= 70) return \"text-yellow-600\";\n        return \"text-green-600\";\n    };\n    const getUsageBarColor = (usage)=>{\n        if (usage >= 90) return \"bg-red-500\";\n        if (usage >= 70) return \"bg-yellow-500\";\n        return \"bg-green-500\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        requiredRoles: [\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_10__.ROLES.SYSTEM_ADMIN,\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_10__.ROLES.TECHNICAL_SUPPORT\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"服务器监控\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: autoRefresh ? \"default\" : \"outline\",\n                                        onClick: ()=>setAutoRefresh(!autoRefresh),\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            autoRefresh ? \"自动刷新\" : \"手动刷新\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleRefresh,\n                                        disabled: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: `h-4 w-4 mr-2 ${loading ? \"animate-spin\" : \"\"}`\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"刷新\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, undefined),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"总服务器\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: stats?.totalServers || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: [\n                                                    \"在线: \",\n                                                    stats?.healthyServers || 0,\n                                                    \" | 离线: \",\n                                                    stats?.unhealthyServers || 0\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"平均CPU使用率\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `text-2xl font-bold ${getUsageColor(stats?.averageCpuUsage || 0)}`,\n                                                children: [\n                                                    (stats?.averageCpuUsage || 0).toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_9__.Progress, {\n                                                value: stats?.averageCpuUsage || 0,\n                                                className: \"mt-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"平均内存使用率\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-4 w-4 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `text-2xl font-bold ${getUsageColor(stats?.averageMemoryUsage || 0)}`,\n                                                children: [\n                                                    (stats?.averageMemoryUsage || 0).toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_9__.Progress, {\n                                                value: stats?.averageMemoryUsage || 0,\n                                                className: \"mt-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"活跃告警\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4 text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-red-600\",\n                                                children: stats?.activeAlerts || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: [\n                                                    \"严重: \",\n                                                    stats?.criticalAlerts || 0\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"服务器状态\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: loading && servers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-gray-500\",\n                                            children: \"加载中...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 17\n                                }, undefined) : servers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"暂无服务器数据\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        children: \"服务器信息\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        children: \"状态\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        children: \"玩家数\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        children: \"CPU使用率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        children: \"内存使用率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        children: \"磁盘使用率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        children: \"响应时间\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        children: \"操作\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                            children: servers.map((server)=>{\n                                                const statusInfo = ServerStatusMap[server.status];\n                                                const StatusIcon = statusInfo?.icon || _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"];\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-blue-100 rounded flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                            lineNumber: 367,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                        lineNumber: 366,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: server.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                                lineNumber: 370,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    server.host,\n                                                                                    \":\",\n                                                                                    server.port\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                                lineNumber: 371,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            server.version && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-400\",\n                                                                                children: [\n                                                                                    \"版本: \",\n                                                                                    server.version\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                                lineNumber: 375,\n                                                                                columnNumber: 35\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                        lineNumber: 369,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                className: statusInfo?.color || \"bg-gray-100 text-gray-800\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                        lineNumber: 384,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    statusInfo?.label || \"未知\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-1 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                            lineNumber: 390,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: server.currentPlayers\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                            lineNumber: 391,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: [\n                                                                                \"/\",\n                                                                                server.maxPlayers\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                            lineNumber: 392,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full bg-gray-200 rounded-full h-1.5 mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-blue-600 h-1.5 rounded-full\",\n                                                                        style: {\n                                                                            width: `${server.currentPlayers / server.maxPlayers * 100}%`\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `font-medium ${getUsageColor(server.cpuUsage)}`,\n                                                                    children: [\n                                                                        server.cpuUsage.toFixed(1),\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full bg-gray-200 rounded-full h-1.5 mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: `h-1.5 rounded-full ${getUsageBarColor(server.cpuUsage)}`,\n                                                                        style: {\n                                                                            width: `${server.cpuUsage}%`\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `font-medium ${getUsageColor(server.memoryUsage)}`,\n                                                                    children: [\n                                                                        server.memoryUsage.toFixed(1),\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                    lineNumber: 413,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full bg-gray-200 rounded-full h-1.5 mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: `h-1.5 rounded-full ${getUsageBarColor(server.memoryUsage)}`,\n                                                                        style: {\n                                                                            width: `${server.memoryUsage}%`\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                        lineNumber: 417,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `font-medium ${getUsageColor(server.diskUsage)}`,\n                                                                    children: [\n                                                                        server.diskUsage.toFixed(1),\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full bg-gray-200 rounded-full h-1.5 mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: `h-1.5 rounded-full ${getUsageBarColor(server.diskUsage)}`,\n                                                                        style: {\n                                                                            width: `${server.diskUsage}%`\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                        lineNumber: 436,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            server.responseTime.toFixed(0),\n                                                                            \"ms\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                        lineNumber: 437,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>handleHealthCheck(server.id),\n                                                                        disabled: loading,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                            lineNumber: 448,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    server.status === 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>handleRestartServer(server.id),\n                                                                        disabled: loading,\n                                                                        className: \"text-orange-600 hover:text-orange-700\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                            lineNumber: 459,\n                                                                            columnNumber: 35\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                        lineNumber: 452,\n                                                                        columnNumber: 33\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        disabled: true,\n                                                                        className: \"text-gray-400\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_Bell_CheckCircle_Clock_Cpu_HardDrive_Play_RotateCcw_Server_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                            lineNumber: 468,\n                                                                            columnNumber: 35\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                        lineNumber: 462,\n                                                                        columnNumber: 33\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, server.id, true, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 25\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 11\n                    }, undefined),\n                    stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 text-center text-sm text-gray-500\",\n                        children: [\n                            \"最后更新: \",\n                            new Date(stats.lastUpdated).toLocaleString(\"zh-CN\"),\n                            autoRefresh && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2\",\n                                children: \"• 自动刷新已启用\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 31\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                        lineNumber: 484,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n                lineNumber: 240,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n            lineNumber: 239,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx\",\n        lineNumber: 238,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MonitoringPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/servers/monitoring/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/Auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst ProtectedRoute = ({ children, requiredRoles = [] })=>{\n    const { isAuthenticated, isLoading, hasAnyRole } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading) {\n            if (!isAuthenticated) {\n                // 未登录，重定向到登录页\n                router.push(\"/login\");\n                return;\n            }\n            if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {\n                // 没有权限，重定向到仪表板或显示无权限页面\n                router.push(\"/dashboard\");\n                return;\n            }\n        }\n    }, [\n        isAuthenticated,\n        isLoading,\n        hasAnyRole,\n        requiredRoles,\n        router\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Auth/ProtectedRoute.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Auth/ProtectedRoute.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!isAuthenticated) {\n        return null; // 将重定向到登录页\n    }\n    if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {\n        return null; // 将重定向到仪表板\n    }\n    // 认证通过，返回子组件\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProtectedRoute);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/MainLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/Layout/MainLayout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,ChevronRight,DollarSign,Gamepad2,LayoutDashboard,Link,LogOut,Menu,Server,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,ChevronRight,DollarSign,Gamepad2,LayoutDashboard,Link,LogOut,Menu,Server,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,ChevronRight,DollarSign,Gamepad2,LayoutDashboard,Link,LogOut,Menu,Server,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,ChevronRight,DollarSign,Gamepad2,LayoutDashboard,Link,LogOut,Menu,Server,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,ChevronRight,DollarSign,Gamepad2,LayoutDashboard,Link,LogOut,Menu,Server,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/gamepad-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,ChevronRight,DollarSign,Gamepad2,LayoutDashboard,Link,LogOut,Menu,Server,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,ChevronRight,DollarSign,Gamepad2,LayoutDashboard,Link,LogOut,Menu,Server,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,ChevronRight,DollarSign,Gamepad2,LayoutDashboard,Link,LogOut,Menu,Server,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,ChevronRight,DollarSign,Gamepad2,LayoutDashboard,Link,LogOut,Menu,Server,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,ChevronRight,DollarSign,Gamepad2,LayoutDashboard,Link,LogOut,Menu,Server,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,ChevronRight,DollarSign,Gamepad2,LayoutDashboard,Link,LogOut,Menu,Server,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,ChevronRight,DollarSign,Gamepad2,LayoutDashboard,Link,LogOut,Menu,Server,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,ChevronRight,DollarSign,Gamepad2,LayoutDashboard,Link,LogOut,Menu,Server,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,ChevronRight,DollarSign,Gamepad2,LayoutDashboard,Link,LogOut,Menu,Server,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst MainLayout = ({ children })=>{\n    const [collapsed, setCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedMenus, setExpandedMenus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { user, logout, hasRole, hasAnyRole } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // 菜单项配置\n    const menuItems = [\n        {\n            key: \"/dashboard\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 42,\n                columnNumber: 13\n            }, undefined),\n            label: \"仪表板\",\n            roles: []\n        },\n        {\n            key: \"/operational-data\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 48,\n                columnNumber: 13\n            }, undefined),\n            label: \"运营数据\",\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n            ]\n        },\n        {\n            key: \"/players\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 54,\n                columnNumber: 13\n            }, undefined),\n            label: \"玩家管理\",\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.CUSTOMER_SERVICE_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.CUSTOMER_SERVICE_SPECIALIST\n            ]\n        },\n        {\n            key: \"/payments\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 60,\n                columnNumber: 13\n            }, undefined),\n            label: \"支付管理\",\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n            ]\n        },\n        {\n            key: \"/games\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 66,\n                columnNumber: 13\n            }, undefined),\n            label: \"游戏数据\",\n            children: [\n                {\n                    key: \"/games/activities\",\n                    label: \"活动管理\",\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n                    ]\n                },\n                {\n                    key: \"/games/announcements\",\n                    label: \"公告管理\",\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n                    ]\n                },\n                {\n                    key: \"/games/items\",\n                    label: \"道具管理\",\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n                    ]\n                }\n            ]\n        },\n        {\n            key: \"/servers\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 88,\n                columnNumber: 13\n            }, undefined),\n            label: \"服务器管理\",\n            children: [\n                {\n                    key: \"/servers/status\",\n                    label: \"服务器状态\",\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER\n                    ]\n                },\n                {\n                    key: \"/servers/monitoring\",\n                    label: \"监控告警\",\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER\n                    ]\n                },\n                {\n                    key: \"/servers/logs\",\n                    label: \"日志管理\",\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER\n                    ]\n                }\n            ]\n        },\n        {\n            key: \"/customer-service\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 110,\n                columnNumber: 13\n            }, undefined),\n            label: \"客服管理\",\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.CUSTOMER_SERVICE_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.CUSTOMER_SERVICE_SPECIALIST\n            ]\n        },\n        {\n            key: \"/security\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 116,\n                columnNumber: 13\n            }, undefined),\n            label: \"安全管理\",\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN\n            ]\n        },\n        {\n            key: \"/reports\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 122,\n                columnNumber: 13\n            }, undefined),\n            label: \"数据报表\",\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n            ]\n        },\n        {\n            key: \"/channels\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 128,\n                columnNumber: 13\n            }, undefined),\n            label: \"渠道管理\",\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER\n            ]\n        }\n    ];\n    // 过滤菜单项基于用户角色\n    const filterMenuItems = (items)=>{\n        return items.filter((item)=>{\n            if (item.roles && item.roles.length > 0) {\n                if (!hasAnyRole(item.roles)) {\n                    return false;\n                }\n            }\n            if (item.children) {\n                item.children = filterMenuItems(item.children);\n                return item.children.length > 0;\n            }\n            return true;\n        });\n    };\n    const filteredMenuItems = filterMenuItems(menuItems);\n    const handleMenuClick = (key)=>{\n        router.push(key);\n    };\n    const toggleSubmenu = (key)=>{\n        setExpandedMenus((prev)=>prev.includes(key) ? prev.filter((k)=>k !== key) : [\n                ...prev,\n                key\n            ]);\n    };\n    const renderMenuItem = (item, level = 0)=>{\n        const isActive = pathname === item.key;\n        const hasChildren = item.children && item.children.length > 0;\n        const isExpanded = expandedMenus.includes(item.key);\n        if (hasChildren) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>toggleSubmenu(item.key),\n                        className: `w-full flex items-center justify-between px-3 py-2 text-sm rounded-md transition-colors ${collapsed ? \"px-2\" : \"px-3\"} hover:bg-gray-700 text-gray-300`,\n                        style: {\n                            paddingLeft: `${12 + level * 16}px`\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    item.icon,\n                                    !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: item.label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 30\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, undefined),\n                            !collapsed && (isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 28\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 66\n                            }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, undefined),\n                    !collapsed && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-4\",\n                        children: item.children.map((child)=>renderMenuItem(child, level + 1))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, item.key, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 173,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: ()=>handleMenuClick(item.key),\n            className: `w-full flex items-center space-x-2 px-3 py-2 text-sm rounded-md transition-colors mb-1 ${collapsed ? \"px-2\" : \"px-3\"} ${isActive ? \"bg-blue-600 text-white\" : \"text-gray-300 hover:bg-gray-700\"}`,\n            style: {\n                paddingLeft: `${12 + level * 16}px`\n            },\n            children: [\n                item.icon,\n                !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: item.label\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 24\n                }, undefined)\n            ]\n        }, item.key, true, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n            lineNumber: 199,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `bg-gray-900 text-white transition-all duration-300 ${collapsed ? \"w-16\" : \"w-64\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-16 flex items-center justify-center border-b border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-white font-bold text-lg\",\n                            children: collapsed ? \"GM\" : \"游戏管理系统\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"mt-4 px-2\",\n                        children: filteredMenuItems.map((item)=>renderMenuItem(item))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setCollapsed(!collapsed),\n                                className: \"p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"欢迎回来，\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: user?.displayName || user?.username\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: logout,\n                                                className: \"text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_ChevronRight_DollarSign_Gamepad2_LayoutDashboard_Link_LogOut_Menu_Server_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"退出\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n        lineNumber: 218,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MainLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/MainLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/alert.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/alert.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\", {\n    variants: {\n        variant: {\n            default: \"bg-card text-card-foreground\",\n            destructive: \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Alert({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"alert\",\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/alert.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\nfunction AlertTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"alert-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/alert.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\nfunction AlertDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"alert-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/alert.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\",\n            success: \"border-transparent bg-green-500 text-white hover:bg-green-600\",\n            warning: \"border-transparent bg-yellow-500 text-white hover:bg-yellow-600\",\n            info: \"border-transparent bg-blue-500 text-white hover:bg-blue-600\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/badge.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/progress.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/progress.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Progress: () => (/* binding */ Progress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-progress */ \"(ssr)/./node_modules/@radix-ui/react-progress/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Progress auto */ \n\n\n\nfunction Progress({ className, value, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"progress\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Indicator, {\n            \"data-slot\": \"progress-indicator\",\n            className: \"bg-primary h-full w-full flex-1 transition-all\",\n            style: {\n                transform: `translateX(-${100 - (value || 0)}%)`\n            }\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/progress.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/progress.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9wcm9ncmVzcy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFOEI7QUFDK0I7QUFFN0I7QUFFaEMsU0FBU0csU0FBUyxFQUNoQkMsU0FBUyxFQUNUQyxLQUFLLEVBQ0wsR0FBR0MsT0FDaUQ7SUFDcEQscUJBQ0UsOERBQUNMLDBEQUFzQjtRQUNyQk8sYUFBVTtRQUNWSixXQUFXRiw4Q0FBRUEsQ0FDWCxrRUFDQUU7UUFFRCxHQUFHRSxLQUFLO2tCQUVULDRFQUFDTCwrREFBMkI7WUFDMUJPLGFBQVU7WUFDVkosV0FBVTtZQUNWTSxPQUFPO2dCQUFFQyxXQUFXLENBQUMsWUFBWSxFQUFFLE1BQU9OLENBQUFBLFNBQVMsR0FBRyxFQUFFLENBQUM7WUFBQzs7Ozs7Ozs7Ozs7QUFJbEU7QUFFbUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9nYW1lLW1hbmFnZW1lbnQtd2ViLy4vc3JjL2NvbXBvbmVudHMvdWkvcHJvZ3Jlc3MudHN4Pzk3NTIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCAqIGFzIFByb2dyZXNzUHJpbWl0aXZlIGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtcHJvZ3Jlc3NcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmZ1bmN0aW9uIFByb2dyZXNzKHtcbiAgY2xhc3NOYW1lLFxuICB2YWx1ZSxcbiAgLi4ucHJvcHNcbn06IFJlYWN0LkNvbXBvbmVudFByb3BzPHR5cGVvZiBQcm9ncmVzc1ByaW1pdGl2ZS5Sb290Pikge1xuICByZXR1cm4gKFxuICAgIDxQcm9ncmVzc1ByaW1pdGl2ZS5Sb290XG4gICAgICBkYXRhLXNsb3Q9XCJwcm9ncmVzc1wiXG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcImJnLXByaW1hcnkvMjAgcmVsYXRpdmUgaC0yIHctZnVsbCBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC1mdWxsXCIsXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICA+XG4gICAgICA8UHJvZ3Jlc3NQcmltaXRpdmUuSW5kaWNhdG9yXG4gICAgICAgIGRhdGEtc2xvdD1cInByb2dyZXNzLWluZGljYXRvclwiXG4gICAgICAgIGNsYXNzTmFtZT1cImJnLXByaW1hcnkgaC1mdWxsIHctZnVsbCBmbGV4LTEgdHJhbnNpdGlvbi1hbGxcIlxuICAgICAgICBzdHlsZT17eyB0cmFuc2Zvcm06IGB0cmFuc2xhdGVYKC0kezEwMCAtICh2YWx1ZSB8fCAwKX0lKWAgfX1cbiAgICAgIC8+XG4gICAgPC9Qcm9ncmVzc1ByaW1pdGl2ZS5Sb290PlxuICApXG59XG5cbmV4cG9ydCB7IFByb2dyZXNzIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlByb2dyZXNzUHJpbWl0aXZlIiwiY24iLCJQcm9ncmVzcyIsImNsYXNzTmFtZSIsInZhbHVlIiwicHJvcHMiLCJSb290IiwiZGF0YS1zbG90IiwiSW5kaWNhdG9yIiwic3R5bGUiLCJ0cmFuc2Zvcm0iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/progress.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/table.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/table.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Table: () => (/* binding */ Table),\n/* harmony export */   TableBody: () => (/* binding */ TableBody),\n/* harmony export */   TableCaption: () => (/* binding */ TableCaption),\n/* harmony export */   TableCell: () => (/* binding */ TableCell),\n/* harmony export */   TableFooter: () => (/* binding */ TableFooter),\n/* harmony export */   TableHead: () => (/* binding */ TableHead),\n/* harmony export */   TableHeader: () => (/* binding */ TableHeader),\n/* harmony export */   TableRow: () => (/* binding */ TableRow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Table,TableHeader,TableBody,TableFooter,TableHead,TableRow,TableCell,TableCaption auto */ \n\n\nfunction Table({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"table-container\",\n        className: \"relative w-full overflow-x-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n            \"data-slot\": \"table\",\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-full caption-bottom text-sm\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/table.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/table.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\nfunction TableHeader({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n        \"data-slot\": \"table-header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"[&_tr]:border-b\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/table.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\nfunction TableBody({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n        \"data-slot\": \"table-body\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"[&_tr:last-child]:border-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/table.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\nfunction TableFooter({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tfoot\", {\n        \"data-slot\": \"table-footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/table.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\nfunction TableRow({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n        \"data-slot\": \"table-row\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/table.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\nfunction TableHead({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n        \"data-slot\": \"table-head\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/table.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\nfunction TableCell({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n        \"data-slot\": \"table-cell\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/table.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\nfunction TableCaption({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"caption\", {\n        \"data-slot\": \"table-caption\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground mt-4 text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/ui/table.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/table.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   ROLES: () => (/* binding */ ROLES),\n/* harmony export */   checkPermission: () => (/* binding */ checkPermission),\n/* harmony export */   hasCustomerServicePermission: () => (/* binding */ hasCustomerServicePermission),\n/* harmony export */   hasPartnerPermission: () => (/* binding */ hasPartnerPermission),\n/* harmony export */   hasProductPermission: () => (/* binding */ hasProductPermission),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,ROLES,checkPermission,isAdmin,hasCustomerServicePermission,hasProductPermission,hasPartnerPermission auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 检查本地存储中的用户信息\n        if (false) {}\n        setIsLoading(false);\n    }, []);\n    const login = async (username, password)=>{\n        try {\n            setIsLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.login({\n                username,\n                password\n            });\n            const { token, user: userData } = response.data;\n            // 存储认证信息\n            if (false) {}\n            setUser(userData);\n            console.log(\"登录成功\");\n            return true;\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            const errorMessage = error.response?.data?.message || \"登录失败，请检查用户名和密码\";\n            console.error(errorMessage);\n            return false;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            if (false) {}\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            // 清除本地存储\n            if (false) {}\n            setUser(null);\n            console.log(\"已退出登录\");\n            // 重定向到登录页\n            window.location.href = \"/login\";\n        }\n    };\n    const hasRole = (role)=>{\n        return user?.roles?.includes(role) || false;\n    };\n    const hasAnyRole = (roles)=>{\n        return roles.some((role)=>hasRole(role));\n    };\n    const isAuthenticated = !!user;\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        login,\n        logout,\n        hasRole,\n        hasAnyRole\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/contexts/AuthContext.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n// 角色常量\nconst ROLES = {\n    SYSTEM_ADMIN: \"SystemAdmin\",\n    PRODUCT_MANAGER: \"ProductManager\",\n    PRODUCT_SPECIALIST: \"ProductSpecialist\",\n    PARTNER_MANAGER: \"PartnerManager\",\n    PARTNER_SPECIALIST: \"PartnerSpecialist\",\n    CUSTOMER_SERVICE_MANAGER: \"CustomerServiceManager\",\n    CUSTOMER_SERVICE_SPECIALIST: \"CustomerServiceSpecialist\",\n    VIEWER: \"Viewer\"\n};\n// 权限检查工具函数\nconst checkPermission = (userRoles, requiredRoles)=>{\n    return requiredRoles.some((role)=>userRoles.includes(role));\n};\n// 管理员角色检查\nconst isAdmin = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER\n    ]);\n};\n// 客服权限检查\nconst hasCustomerServicePermission = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER,\n        ROLES.CUSTOMER_SERVICE_MANAGER,\n        ROLES.CUSTOMER_SERVICE_SPECIALIST\n    ]);\n};\n// 产品管理权限检查\nconst hasProductPermission = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER,\n        ROLES.PRODUCT_SPECIALIST\n    ]);\n};\n// 渠道管理权限检查\nconst hasPartnerPermission = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER,\n        ROLES.PARTNER_MANAGER,\n        ROLES.PARTNER_SPECIALIST\n    ]);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   operationalDataApi: () => (/* binding */ operationalDataApi),\n/* harmony export */   playersApi: () => (/* binding */ playersApi),\n/* harmony export */   usersApi: () => (/* binding */ usersApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// API 基础配置\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:5108/api\";\n// 创建 axios 实例\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 10000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// 请求拦截器 - 添加认证令牌\napiClient.interceptors.request.use((config)=>{\n    // 检查是否在浏览器环境中\n    if (false) {}\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器 - 处理认证错误\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, async (error)=>{\n    const originalRequest = error.config;\n    if (error.response?.status === 401 && !originalRequest._retry && \"undefined\" !== \"undefined\") {}\n    return Promise.reject(error);\n});\n// API 方法\nconst authApi = {\n    login: (data)=>apiClient.post(\"/auth/login\", data),\n    logout: (refreshToken)=>apiClient.post(\"/auth/logout\", refreshToken),\n    register: (data)=>apiClient.post(\"/auth/register\", data),\n    refreshToken: (refreshToken)=>apiClient.post(\"/auth/refresh\", refreshToken),\n    changePassword: (currentPassword, newPassword)=>apiClient.post(\"/auth/change-password\", {\n            currentPassword,\n            newPassword\n        }),\n    resetPassword: (email)=>apiClient.post(\"/auth/reset-password\", {\n            email\n        })\n};\nconst usersApi = {\n    getUsers: ()=>apiClient.get(\"/users\"),\n    getUser: (id)=>apiClient.get(`/users/${id}`),\n    getUserByUsername: (username)=>apiClient.get(`/users/by-username/${username}`),\n    createUser: (data)=>apiClient.post(\"/users\", data),\n    updateUser: (id, data)=>apiClient.put(`/users/${id}`, data),\n    deleteUser: (id)=>apiClient.delete(`/users/${id}`),\n    activateUser: (id)=>apiClient.post(`/users/${id}/activate`),\n    deactivateUser: (id)=>apiClient.post(`/users/${id}/deactivate`),\n    getUsersByRole: (role)=>apiClient.get(`/users/by-role/${role}`)\n};\nconst playersApi = {\n    getPlayers: (page = 1, pageSize = 20)=>apiClient.get(`/players?page=${page}&pageSize=${pageSize}`),\n    getPlayer: (id)=>apiClient.get(`/players/${id}`),\n    getPlayerByAccountId: (accountId)=>apiClient.get(`/players/by-account/${accountId}`),\n    searchPlayers: (searchTerm)=>apiClient.get(`/players/search?searchTerm=${encodeURIComponent(searchTerm)}`),\n    getPlayerStats: ()=>apiClient.get(\"/players/stats\"),\n    getTopPlayersByLevel: (count = 10)=>apiClient.get(`/players/top-by-level?count=${count}`),\n    getVipPlayers: (minVipLevel = 1)=>apiClient.get(`/players/vip?minVipLevel=${minVipLevel}`),\n    updatePlayer: (id, data)=>apiClient.put(`/players/${id}`, data),\n    banPlayer: (id, bannedUntil, reason)=>apiClient.post(`/players/${id}/ban`, {\n            bannedUntil,\n            reason\n        }),\n    unbanPlayer: (id)=>apiClient.post(`/players/${id}/unban`)\n};\n// 运营数据API方法\nconst operationalDataApi = {\n    // 全局统计\n    getGlobalStats: ()=>apiClient.get(\"/operational-data/global-stats\"),\n    getGlobalStatsByDate: (date)=>apiClient.get(`/operational-data/global-stats/${date}`),\n    // 用户信息统计\n    getUserInfoStats: ()=>apiClient.get(\"/operational-data/user-info-stats\"),\n    getUserInfoStatsByDateRange: (startDate, endDate)=>apiClient.get(`/operational-data/user-info-stats/${startDate}/${endDate}`),\n    // 付费信息统计\n    getPaymentInfoStats: ()=>apiClient.get(\"/operational-data/payment-info-stats\"),\n    getPaymentInfoStatsByDateRange: (startDate, endDate)=>apiClient.get(`/operational-data/payment-info-stats/${startDate}/${endDate}`),\n    // 数据分析\n    getConversionAnalysis: (date)=>apiClient.get(`/operational-data/conversion-analysis/${date}`),\n    getRetentionAnalysis: (date)=>apiClient.get(`/operational-data/retention-analysis/${date}`),\n    getActiveUserAnalysis: (date)=>apiClient.get(`/operational-data/active-user-analysis/${date}`),\n    // 记录数据\n    recordVisit: (data)=>apiClient.post(\"/operational-data/record-visit\", data)\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL2dhbWUtbWFuYWdlbWVudC13ZWIvLi9zcmMvbGliL3V0aWxzLnRzPzdjMWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xzeCwgdHlwZSBDbGFzc1ZhbHVlIH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d186d8c17185\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ2FtZS1tYW5hZ2VtZW50LXdlYi8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YTI0MSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImQxODZkOGMxNzE4NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/servers/monitoring/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/servers/monitoring/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/monitoring/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9nYW1lLW1hbmFnZW1lbnQtd2ViLy4vc3JjL2FwcC9mYXZpY29uLmljbz9lZDFiIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@tanstack","vendor-chunks/@radix-ui","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/class-variance-authority","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/clsx","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fservers%2Fmonitoring%2Fpage&page=%2Fservers%2Fmonitoring%2Fpage&appPaths=%2Fservers%2Fmonitoring%2Fpage&pagePath=private-next-app-dir%2Fservers%2Fmonitoring%2Fpage.tsx&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();