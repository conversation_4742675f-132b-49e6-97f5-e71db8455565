/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/servers/status/page";
exports.ids = ["app/servers/status/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fservers%2Fstatus%2Fpage&page=%2Fservers%2Fstatus%2Fpage&appPaths=%2Fservers%2Fstatus%2Fpage&pagePath=private-next-app-dir%2Fservers%2Fstatus%2Fpage.tsx&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fservers%2Fstatus%2Fpage&page=%2Fservers%2Fstatus%2Fpage&appPaths=%2Fservers%2Fstatus%2Fpage&pagePath=private-next-app-dir%2Fservers%2Fstatus%2Fpage.tsx&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/servers/status/page.tsx */ \"(rsc)/./src/app/servers/status/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'servers',\n        {\n        children: [\n        'status',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/servers/status/page\",\n        pathname: \"/servers/status\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fservers%2Fstatus%2Fpage&page=%2Fservers%2Fstatus%2Fpage&appPaths=%2Fservers%2Fstatus%2Fpage&pagePath=private-next-app-dir%2Fservers%2Fstatus%2Fpage.tsx&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQW1IIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvYXBwL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Fservers%2Fstatus%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Fservers%2Fstatus%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/servers/status/page.tsx */ \"(rsc)/./src/app/servers/status/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGc3JjJTJGYXBwJTJGc2VydmVycyUyRnN0YXR1cyUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4S0FBZ0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9jYWZlL0RvY3VtZW50cy9nYW1lbWFuYWdld2ViL3NyYy9nYW1lLW1hbmFnZW1lbnQtd2ViL3NyYy9hcHAvc2VydmVycy9zdGF0dXMvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Fservers%2Fstatus%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy9jYWZlL0RvY3VtZW50cy9nYW1lbWFuYWdld2ViL3NyYy9nYW1lLW1hbmFnZW1lbnQtd2ViL3NyYy9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/servers/status/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/servers/status/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZjYWZlJTJGRG9jdW1lbnRzJTJGZ2FtZW1hbmFnZXdlYiUyRnNyYyUyRmdhbWUtbWFuYWdlbWVudC13ZWIlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZjbGllbnQtc2VnbWVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmNhZmUlMkZEb2N1bWVudHMlMkZnYW1lbWFuYWdld2ViJTJGc3JjJTJGZ2FtZS1tYW5hZ2VtZW50LXdlYiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGaHR0cC1hY2Nlc3MtZmFsbGJhY2slMkZlcnJvci1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmNhZmUlMkZEb2N1bWVudHMlMkZnYW1lbWFuYWdld2ViJTJGc3JjJTJGZ2FtZS1tYW5hZ2VtZW50LXdlYiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmxheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZjYWZlJTJGRG9jdW1lbnRzJTJGZ2FtZW1hbmFnZXdlYiUyRnNyYyUyRmdhbWUtbWFuYWdlbWVudC13ZWIlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZtZXRhZGF0YSUyRmFzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbWV0YWRhdGElMkZtZXRhZGF0YS1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmNhZmUlMkZEb2N1bWVudHMlMkZnYW1lbWFuYWdld2ViJTJGc3JjJTJGZ2FtZS1tYW5hZ2VtZW50LXdlYiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRnJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9PQUF3SjtBQUN4SjtBQUNBLDBPQUEySjtBQUMzSjtBQUNBLDBPQUEySjtBQUMzSjtBQUNBLG9SQUFnTDtBQUNoTDtBQUNBLHdPQUEwSjtBQUMxSjtBQUNBLDRQQUFvSztBQUNwSztBQUNBLGtRQUF1SztBQUN2SztBQUNBLHNRQUF5SyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2NhZmUvRG9jdW1lbnRzL2dhbWVtYW5hZ2V3ZWIvc3JjL2dhbWUtbWFuYWdlbWVudC13ZWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2NhZmUvRG9jdW1lbnRzL2dhbWVtYW5hZ2V3ZWIvc3JjL2dhbWUtbWFuYWdlbWVudC13ZWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2NhZmUvRG9jdW1lbnRzL2dhbWVtYW5hZ2V3ZWIvc3JjL2dhbWUtbWFuYWdlbWVudC13ZWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2NhZmUvRG9jdW1lbnRzL2dhbWVtYW5hZ2V3ZWIvc3JjL2dhbWUtbWFuYWdlbWVudC13ZWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9odHRwLWFjY2Vzcy1mYWxsYmFjay9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2NhZmUvRG9jdW1lbnRzL2dhbWVtYW5hZ2V3ZWIvc3JjL2dhbWUtbWFuYWdlbWVudC13ZWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9sYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL21ldGFkYXRhL2FzeW5jLW1ldGFkYXRhLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL21ldGFkYXRhL21ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQW1IIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvYXBwL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Fservers%2Fstatus%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Fservers%2Fstatus%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/servers/status/page.tsx */ \"(ssr)/./src/app/servers/status/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGY2FmZSUyRkRvY3VtZW50cyUyRmdhbWVtYW5hZ2V3ZWIlMkZzcmMlMkZnYW1lLW1hbmFnZW1lbnQtd2ViJTJGc3JjJTJGYXBwJTJGc2VydmVycyUyRnN0YXR1cyUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4S0FBZ0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9jYWZlL0RvY3VtZW50cy9nYW1lbWFuYWdld2ViL3NyYy9nYW1lLW1hbmFnZW1lbnQtd2ViL3NyYy9hcHAvc2VydmVycy9zdGF0dXMvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Fservers%2Fstatus%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzE5Y2IwZmMzZjYzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ConfigProvider!=!antd */ \"(ssr)/./node_modules/antd/es/config-provider/index.js\");\n/* harmony import */ var antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! antd/locale/zh_CN */ \"(ssr)/./node_modules/antd/lib/locale/zh_CN.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// 创建 QueryClient 实例\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient({\n    defaultOptions: {\n        queries: {\n            staleTime: 5 * 60 * 1000,\n            retry: 1\n        }\n    }\n});\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"游戏管理系统\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"游戏运营管理后台系统\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n                    client: queryClient,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            locale: antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_8__.ReactQueryDevtools, {\n                            initialIsOpen: false\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/servers/status/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/servers/status/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Auth/ProtectedRoute */ \"(ssr)/./src/components/Auth/ProtectedRoute.tsx\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Col,Modal,Progress,Row,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Col,Modal,Progress,Row,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Col,Modal,Progress,Row,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/progress/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Col,Modal,Progress,Row,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Col,Modal,Progress,Row,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Col,Modal,Progress,Row,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Col,Modal,Progress,Row,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Col,Modal,Progress,Row,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Col,Modal,Progress,Row,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Col,Modal,Progress,Row,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Col,Modal,Progress,Row,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Col,Modal,Progress,Row,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Col,Modal,Progress,Row,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,CloseCircleOutlined,CloudServerOutlined,ExclamationCircleOutlined,EyeOutlined,ReloadOutlined,SettingOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,CloseCircleOutlined,CloudServerOutlined,ExclamationCircleOutlined,EyeOutlined,ReloadOutlined,SettingOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/ExclamationCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,CloseCircleOutlined,CloudServerOutlined,ExclamationCircleOutlined,EyeOutlined,ReloadOutlined,SettingOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/SettingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,CloseCircleOutlined,CloudServerOutlined,ExclamationCircleOutlined,EyeOutlined,ReloadOutlined,SettingOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/CloseCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,CloseCircleOutlined,CloudServerOutlined,ExclamationCircleOutlined,EyeOutlined,ReloadOutlined,SettingOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/CloudServerOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,CloseCircleOutlined,CloudServerOutlined,ExclamationCircleOutlined,EyeOutlined,ReloadOutlined,SettingOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,CloseCircleOutlined,CloudServerOutlined,ExclamationCircleOutlined,EyeOutlined,ReloadOutlined,SettingOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst { Title } = _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst ServerStatusPage = ()=>{\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 模拟服务器状态数据\n    const serversData = [\n        {\n            key: '1',\n            id: 1,\n            name: '服务器1',\n            region: '华东',\n            ip: '*************',\n            port: 8080,\n            status: 'online',\n            playerCount: 1256,\n            maxPlayers: 2000,\n            cpuUsage: 45.2,\n            memoryUsage: 68.5,\n            diskUsage: 32.1,\n            networkIn: 125.6,\n            networkOut: 89.3,\n            uptime: '15天3小时25分钟',\n            version: '2.5.1',\n            lastUpdate: '2024-06-25 14:30:25',\n            maintenance: false\n        },\n        {\n            key: '2',\n            id: 2,\n            name: '服务器2',\n            region: '华南',\n            ip: '*************',\n            port: 8080,\n            status: 'warning',\n            playerCount: 1890,\n            maxPlayers: 2000,\n            cpuUsage: 85.7,\n            memoryUsage: 92.3,\n            diskUsage: 78.9,\n            networkIn: 256.8,\n            networkOut: 198.7,\n            uptime: '8天12小时15分钟',\n            version: '2.5.1',\n            lastUpdate: '2024-06-25 14:28:12',\n            maintenance: false\n        },\n        {\n            key: '3',\n            id: 3,\n            name: '服务器3',\n            region: '华北',\n            ip: '*************',\n            port: 8080,\n            status: 'maintenance',\n            playerCount: 0,\n            maxPlayers: 2000,\n            cpuUsage: 0,\n            memoryUsage: 0,\n            diskUsage: 45.6,\n            networkIn: 0,\n            networkOut: 0,\n            uptime: '维护中',\n            version: '2.5.0',\n            lastUpdate: '2024-06-25 02:00:00',\n            maintenance: true\n        },\n        {\n            key: '4',\n            id: 4,\n            name: '服务器4',\n            region: '华西',\n            ip: '*************',\n            port: 8080,\n            status: 'offline',\n            playerCount: 0,\n            maxPlayers: 2000,\n            cpuUsage: 0,\n            memoryUsage: 0,\n            diskUsage: 0,\n            networkIn: 0,\n            networkOut: 0,\n            uptime: '离线',\n            version: '2.4.8',\n            lastUpdate: '2024-06-24 18:45:30',\n            maintenance: false\n        }\n    ];\n    const getStatusTag = (status)=>{\n        switch(status){\n            case 'online':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    color: \"success\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 43\n                    }, void 0),\n                    children: \"在线\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 16\n                }, undefined);\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    color: \"warning\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 43\n                    }, void 0),\n                    children: \"警告\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 16\n                }, undefined);\n            case 'maintenance':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    color: \"processing\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 46\n                    }, void 0),\n                    children: \"维护中\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 16\n                }, undefined);\n            case 'offline':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    color: \"error\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 41\n                    }, void 0),\n                    children: \"离线\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    color: \"default\",\n                    children: \"未知\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getUsageColor = (usage)=>{\n        if (usage >= 90) return '#ff4d4f';\n        if (usage >= 70) return '#faad14';\n        return '#52c41a';\n    };\n    const columns = [\n        {\n            title: '服务器',\n            key: 'server',\n            width: 200,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontWeight: 500,\n                                fontSize: '14px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    style: {\n                                        marginRight: 8\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, undefined),\n                                record.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '12px',\n                                color: '#666',\n                                marginTop: 4\n                            },\n                            children: [\n                                record.region,\n                                \" | \",\n                                record.ip,\n                                \":\",\n                                record.port\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: '状态',\n            key: 'status',\n            width: 120,\n            render: (record)=>getStatusTag(record.status)\n        },\n        {\n            title: '在线玩家',\n            key: 'players',\n            width: 150,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '14px',\n                                fontWeight: 500\n                            },\n                            children: [\n                                record.playerCount.toLocaleString(),\n                                \" / \",\n                                record.maxPlayers.toLocaleString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            percent: Math.round(record.playerCount / record.maxPlayers * 100),\n                            size: \"small\",\n                            showInfo: false,\n                            strokeColor: record.playerCount / record.maxPlayers > 0.9 ? '#ff4d4f' : '#1890ff'\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: 'CPU使用率',\n            key: 'cpu',\n            width: 120,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '12px',\n                                marginBottom: 4\n                            },\n                            children: [\n                                record.cpuUsage,\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            percent: record.cpuUsage,\n                            size: \"small\",\n                            showInfo: false,\n                            strokeColor: getUsageColor(record.cpuUsage)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: '内存使用率',\n            key: 'memory',\n            width: 120,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '12px',\n                                marginBottom: 4\n                            },\n                            children: [\n                                record.memoryUsage,\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            percent: record.memoryUsage,\n                            size: \"small\",\n                            showInfo: false,\n                            strokeColor: getUsageColor(record.memoryUsage)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: '磁盘使用率',\n            key: 'disk',\n            width: 120,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '12px',\n                                marginBottom: 4\n                            },\n                            children: [\n                                record.diskUsage,\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            percent: record.diskUsage,\n                            size: \"small\",\n                            showInfo: false,\n                            strokeColor: getUsageColor(record.diskUsage)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: '网络流量',\n            key: 'network',\n            width: 120,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        fontSize: '12px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"↓ \",\n                                record.networkIn,\n                                \" MB/s\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"↑ \",\n                                record.networkOut,\n                                \" MB/s\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: '运行时间',\n            dataIndex: 'uptime',\n            key: 'uptime',\n            width: 150\n        },\n        {\n            title: '版本',\n            dataIndex: 'version',\n            key: 'version',\n            width: 100\n        },\n        {\n            title: '操作',\n            key: 'action',\n            width: 200,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    size: \"small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            title: \"查看详情\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                type: \"link\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: ()=>handleViewDetail(record)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            title: \"重启服务器\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                type: \"link\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: ()=>handleRestart(record),\n                                disabled: record.status === 'offline'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            title: \"服务器设置\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                type: \"link\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: ()=>handleSettings(record)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ];\n    const handleViewDetail = (record)=>{\n        _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"].info({\n            title: `${record.name} 详细信息`,\n            width: 600,\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    gutter: [\n                        16,\n                        16\n                    ],\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            span: 12,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                size: \"small\",\n                                title: \"基本信息\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"服务器名称：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"地区：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.region\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"IP地址：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.ip,\n                                            \":\",\n                                            record.port\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"版本：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.version\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"状态：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            getStatusTag(record.status)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            span: 12,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                size: \"small\",\n                                title: \"性能指标\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"CPU使用率：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.cpuUsage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"内存使用率：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.memoryUsage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"磁盘使用率：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.diskUsage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"运行时间：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.uptime\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            span: 12,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                size: \"small\",\n                                title: \"玩家信息\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"在线玩家：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.playerCount\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"最大容量：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.maxPlayers\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"负载率：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            Math.round(record.playerCount / record.maxPlayers * 100),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            span: 12,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                size: \"small\",\n                                title: \"网络流量\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"入站流量：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.networkIn,\n                                            \" MB/s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"出站流量：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.networkOut,\n                                            \" MB/s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"最后更新：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.lastUpdate\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                lineNumber: 295,\n                columnNumber: 9\n            }, undefined)\n        });\n    };\n    const handleRestart = (record)=>{\n        _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"].confirm({\n            title: '确认重启',\n            content: `确定要重启 \"${record.name}\" 吗？这将断开所有在线玩家的连接。`,\n            onOk () {\n                _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].success(`正在重启 ${record.name}...`);\n            }\n        });\n    };\n    const handleSettings = (record)=>{\n        _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].info(`打开 ${record.name} 的设置页面`);\n    };\n    const handleRefresh = async ()=>{\n        setRefreshing(true);\n        // 模拟刷新延迟\n        setTimeout(()=>{\n            setRefreshing(false);\n            _barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].success('服务器状态已刷新');\n        }, 1000);\n    };\n    // 计算统计数据\n    const onlineServers = serversData.filter((s)=>s.status === 'online').length;\n    const warningServers = serversData.filter((s)=>s.status === 'warning').length;\n    const offlineServers = serversData.filter((s)=>s.status === 'offline').length;\n    const maintenanceServers = serversData.filter((s)=>s.status === 'maintenance').length;\n    const totalPlayers = serversData.reduce((sum, s)=>sum + s.playerCount, 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        requiredRoles: [\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.ROLES.SYSTEM_ADMIN,\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.ROLES.PRODUCT_MANAGER\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        marginBottom: 24\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                            level: 2,\n                            children: \"服务器状态\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            type: \"primary\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 19\n                            }, void 0),\n                            loading: refreshing,\n                            onClick: handleRefresh,\n                            children: \"刷新状态\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    gutter: [\n                        16,\n                        16\n                    ],\n                    style: {\n                        marginBottom: 24\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    title: \"在线服务器\",\n                                    value: onlineServers,\n                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    valueStyle: {\n                                        color: '#3f8600'\n                                    },\n                                    suffix: `/ ${serversData.length}`\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    title: \"警告服务器\",\n                                    value: warningServers,\n                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    valueStyle: {\n                                        color: '#faad14'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    title: \"离线服务器\",\n                                    value: offlineServers,\n                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    valueStyle: {\n                                        color: '#cf1322'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    title: \"总在线玩家\",\n                                    value: totalPlayers,\n                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_CloudServerOutlined_ExclamationCircleOutlined_EyeOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    valueStyle: {\n                                        color: '#1890ff'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Col_Modal_Progress_Row_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        columns: columns,\n                        dataSource: serversData,\n                        loading: loading,\n                        pagination: false,\n                        scroll: {\n                            x: 1200\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 425,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n            lineNumber: 366,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n        lineNumber: 365,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ServerStatusPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/servers/status/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/Auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Spin_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Spin!=!antd */ \"(ssr)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(ssr)/./src/components/Layout/MainLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst ProtectedRoute = ({ children, requiredRoles = [] })=>{\n    const { isAuthenticated, isLoading, hasAnyRole } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            if (!isLoading) {\n                if (!isAuthenticated) {\n                    // 未登录，重定向到登录页\n                    router.push('/login');\n                    return;\n                }\n                if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {\n                    // 没有权限，重定向到仪表板或显示无权限页面\n                    router.push('/dashboard');\n                    return;\n                }\n            }\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        hasAnyRole,\n        requiredRoles,\n        router\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                justifyContent: 'center',\n                alignItems: 'center',\n                minHeight: '100vh'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Spin_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Auth/ProtectedRoute.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Auth/ProtectedRoute.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!isAuthenticated) {\n        return null; // 将重定向到登录页\n    }\n    if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {\n        return null; // 将重定向到仪表板\n    }\n    // 如果是登录页面，不使用主布局\n    if (pathname === '/login') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    // 使用主布局包装受保护的页面\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Auth/ProtectedRoute.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProtectedRoute);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/MainLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/Layout/MainLayout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/theme/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/menu/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/dropdown/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Dropdown,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/TeamOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/AppstoreOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/CloudServerOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/BellOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/LinkOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/SettingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/LogoutOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/MenuUnfoldOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AppstoreOutlined,BarChartOutlined,BellOutlined,CloudServerOutlined,DashboardOutlined,DollarOutlined,LinkOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/MenuFoldOutlined.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst { Header, Sider, Content } = _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Text } = _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst MainLayout = ({ children })=>{\n    const [collapsed, setCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, logout, hasRole, hasAnyRole } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { token: { colorBgContainer, borderRadiusLG } } = _barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useToken();\n    // 菜单项配置\n    const menuItems = [\n        {\n            key: '/dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 44,\n                columnNumber: 13\n            }, undefined),\n            label: '仪表板',\n            roles: []\n        },\n        {\n            key: '/operational-data',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 50,\n                columnNumber: 13\n            }, undefined),\n            label: '运营数据',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n            ]\n        },\n        {\n            key: '/players',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 56,\n                columnNumber: 13\n            }, undefined),\n            label: '玩家管理',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.CUSTOMER_SERVICE_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.CUSTOMER_SERVICE_SPECIALIST\n            ]\n        },\n        {\n            key: '/payments',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 62,\n                columnNumber: 13\n            }, undefined),\n            label: '支付管理',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n            ]\n        },\n        {\n            key: '/games',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 68,\n                columnNumber: 13\n            }, undefined),\n            label: '游戏数据',\n            children: [\n                {\n                    key: '/games/activities',\n                    label: '活动管理',\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n                    ]\n                },\n                {\n                    key: '/games/announcements',\n                    label: '公告管理',\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n                    ]\n                },\n                {\n                    key: '/games/items',\n                    label: '道具管理',\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n                    ]\n                }\n            ]\n        },\n        {\n            key: '/servers',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 90,\n                columnNumber: 13\n            }, undefined),\n            label: '服务器管理',\n            children: [\n                {\n                    key: '/servers/status',\n                    label: '服务器状态',\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER\n                    ]\n                },\n                {\n                    key: '/servers/monitoring',\n                    label: '监控告警',\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER\n                    ]\n                },\n                {\n                    key: '/servers/logs',\n                    label: '日志管理',\n                    roles: [\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER\n                    ]\n                }\n            ]\n        },\n        {\n            key: '/customer-service',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 112,\n                columnNumber: 13\n            }, undefined),\n            label: '客服管理',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.CUSTOMER_SERVICE_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.CUSTOMER_SERVICE_SPECIALIST\n            ]\n        },\n        {\n            key: '/security',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 118,\n                columnNumber: 13\n            }, undefined),\n            label: '安全管理',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN\n            ]\n        },\n        {\n            key: '/reports',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 124,\n                columnNumber: 13\n            }, undefined),\n            label: '数据报表',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_SPECIALIST\n            ]\n        },\n        {\n            key: '/channels',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 130,\n                columnNumber: 13\n            }, undefined),\n            label: '渠道管理',\n            roles: [\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.SYSTEM_ADMIN,\n                _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.ROLES.PRODUCT_MANAGER\n            ]\n        }\n    ];\n    // 过滤菜单项基于用户角色\n    const filterMenuItems = (items)=>{\n        return items.filter((item)=>{\n            if (item.roles && item.roles.length > 0) {\n                if (!hasAnyRole(item.roles)) {\n                    return false;\n                }\n            }\n            if (item.children) {\n                item.children = filterMenuItems(item.children);\n                return item.children.length > 0;\n            }\n            return true;\n        });\n    };\n    const filteredMenuItems = filterMenuItems(menuItems);\n    // 用户下拉菜单\n    const userMenuItems = [\n        {\n            key: 'profile',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 160,\n                columnNumber: 13\n            }, undefined),\n            label: '个人资料',\n            onClick: ()=>router.push('/profile')\n        },\n        {\n            key: 'settings',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 166,\n                columnNumber: 13\n            }, undefined),\n            label: '账户设置',\n            onClick: ()=>router.push('/account-settings')\n        },\n        {\n            type: 'divider'\n        },\n        {\n            key: 'logout',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 175,\n                columnNumber: 13\n            }, undefined),\n            label: '退出登录',\n            onClick: logout\n        }\n    ];\n    const handleMenuClick = ({ key })=>{\n        router.push(key);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        style: {\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Sider, {\n                trigger: null,\n                collapsible: true,\n                collapsed: collapsed,\n                theme: \"dark\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            height: 64,\n                            margin: 16,\n                            background: 'rgba(255, 255, 255, 0.2)',\n                            borderRadius: 6,\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            color: 'white',\n                            fontWeight: 'bold',\n                            fontSize: collapsed ? 14 : 16\n                        },\n                        children: collapsed ? 'GM' : '游戏管理系统'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        theme: \"dark\",\n                        mode: \"inline\",\n                        selectedKeys: [\n                            pathname\n                        ],\n                        items: filteredMenuItems,\n                        onClick: handleMenuClick\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                        style: {\n                            padding: '0 16px',\n                            background: colorBgContainer,\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            borderBottom: '1px solid #f0f0f0'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                type: \"text\",\n                                icon: collapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 31\n                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 56\n                                }, void 0),\n                                onClick: ()=>setCollapsed(!collapsed),\n                                style: {\n                                    fontSize: '16px',\n                                    width: 64,\n                                    height: 64\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                        type: \"secondary\",\n                                        children: \"欢迎回来，\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        menu: {\n                                            items: userMenuItems\n                                        },\n                                        placement: \"bottomRight\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            style: {\n                                                cursor: 'pointer'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Dropdown_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppstoreOutlined_BarChartOutlined_BellOutlined_CloudServerOutlined_DashboardOutlined_DollarOutlined_LinkOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 31\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    strong: true,\n                                                    children: user?.displayName || user?.username\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                        style: {\n                            margin: '16px',\n                            padding: 24,\n                            minHeight: 280,\n                            background: colorBgContainer,\n                            borderRadius: borderRadiusLG\n                        },\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MainLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/MainLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   ROLES: () => (/* binding */ ROLES),\n/* harmony export */   checkPermission: () => (/* binding */ checkPermission),\n/* harmony export */   hasCustomerServicePermission: () => (/* binding */ hasCustomerServicePermission),\n/* harmony export */   hasPartnerPermission: () => (/* binding */ hasPartnerPermission),\n/* harmony export */   hasProductPermission: () => (/* binding */ hasProductPermission),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(ssr)/./node_modules/antd/es/message/index.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,ROLES,checkPermission,isAdmin,hasCustomerServicePermission,hasProductPermission,hasPartnerPermission auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // 检查本地存储中的用户信息\n            if (false) {}\n            setIsLoading(false);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (username, password)=>{\n        try {\n            setIsLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.login({\n                username,\n                password\n            });\n            const { token, user: userData } = response.data;\n            // 存储认证信息\n            if (false) {}\n            setUser(userData);\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('登录成功');\n            return true;\n        } catch (error) {\n            console.error('Login error:', error);\n            const errorMessage = error.response?.data?.message || '登录失败，请检查用户名和密码';\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(errorMessage);\n            return false;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            if (false) {}\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            // 清除本地存储\n            if (false) {}\n            setUser(null);\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('已退出登录');\n            // 重定向到登录页\n            window.location.href = '/login';\n        }\n    };\n    const hasRole = (role)=>{\n        return user?.roles?.includes(role) || false;\n    };\n    const hasAnyRole = (roles)=>{\n        return roles.some((role)=>hasRole(role));\n    };\n    const isAuthenticated = !!user;\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        login,\n        logout,\n        hasRole,\n        hasAnyRole\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/contexts/AuthContext.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n// 角色常量\nconst ROLES = {\n    SYSTEM_ADMIN: 'SystemAdmin',\n    PRODUCT_MANAGER: 'ProductManager',\n    PRODUCT_SPECIALIST: 'ProductSpecialist',\n    PARTNER_MANAGER: 'PartnerManager',\n    PARTNER_SPECIALIST: 'PartnerSpecialist',\n    CUSTOMER_SERVICE_MANAGER: 'CustomerServiceManager',\n    CUSTOMER_SERVICE_SPECIALIST: 'CustomerServiceSpecialist',\n    VIEWER: 'Viewer'\n};\n// 权限检查工具函数\nconst checkPermission = (userRoles, requiredRoles)=>{\n    return requiredRoles.some((role)=>userRoles.includes(role));\n};\n// 管理员角色检查\nconst isAdmin = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER\n    ]);\n};\n// 客服权限检查\nconst hasCustomerServicePermission = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER,\n        ROLES.CUSTOMER_SERVICE_MANAGER,\n        ROLES.CUSTOMER_SERVICE_SPECIALIST\n    ]);\n};\n// 产品管理权限检查\nconst hasProductPermission = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER,\n        ROLES.PRODUCT_SPECIALIST\n    ]);\n};\n// 渠道管理权限检查\nconst hasPartnerPermission = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER,\n        ROLES.PARTNER_MANAGER,\n        ROLES.PARTNER_SPECIALIST\n    ]);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   operationalDataApi: () => (/* binding */ operationalDataApi),\n/* harmony export */   playersApi: () => (/* binding */ playersApi),\n/* harmony export */   usersApi: () => (/* binding */ usersApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// API 基础配置\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5108/api';\n// 创建 axios 实例\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 10000,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// 请求拦截器 - 添加认证令牌\napiClient.interceptors.request.use((config)=>{\n    const token = localStorage.getItem('accessToken');\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器 - 处理认证错误\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, async (error)=>{\n    const originalRequest = error.config;\n    if (error.response?.status === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n            const refreshToken = localStorage.getItem('refreshToken');\n            if (refreshToken) {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${API_BASE_URL}/auth/refresh`, refreshToken);\n                const { token } = response.data;\n                localStorage.setItem('accessToken', token);\n                originalRequest.headers.Authorization = `Bearer ${token}`;\n                return apiClient(originalRequest);\n            }\n        } catch (refreshError) {\n            // 刷新令牌失败，清除本地存储并重定向到登录页\n            localStorage.removeItem('accessToken');\n            localStorage.removeItem('refreshToken');\n            localStorage.removeItem('user');\n            window.location.href = '/login';\n        }\n    }\n    return Promise.reject(error);\n});\n// API 方法\nconst authApi = {\n    login: (data)=>apiClient.post('/auth/login', data),\n    logout: (refreshToken)=>apiClient.post('/auth/logout', refreshToken),\n    register: (data)=>apiClient.post('/auth/register', data),\n    refreshToken: (refreshToken)=>apiClient.post('/auth/refresh', refreshToken),\n    changePassword: (currentPassword, newPassword)=>apiClient.post('/auth/change-password', {\n            currentPassword,\n            newPassword\n        }),\n    resetPassword: (email)=>apiClient.post('/auth/reset-password', {\n            email\n        })\n};\nconst usersApi = {\n    getUsers: ()=>apiClient.get('/users'),\n    getUser: (id)=>apiClient.get(`/users/${id}`),\n    getUserByUsername: (username)=>apiClient.get(`/users/by-username/${username}`),\n    createUser: (data)=>apiClient.post('/users', data),\n    updateUser: (id, data)=>apiClient.put(`/users/${id}`, data),\n    deleteUser: (id)=>apiClient.delete(`/users/${id}`),\n    activateUser: (id)=>apiClient.post(`/users/${id}/activate`),\n    deactivateUser: (id)=>apiClient.post(`/users/${id}/deactivate`),\n    getUsersByRole: (role)=>apiClient.get(`/users/by-role/${role}`)\n};\nconst playersApi = {\n    getPlayers: (page = 1, pageSize = 20)=>apiClient.get(`/players?page=${page}&pageSize=${pageSize}`),\n    getPlayer: (id)=>apiClient.get(`/players/${id}`),\n    getPlayerByAccountId: (accountId)=>apiClient.get(`/players/by-account/${accountId}`),\n    searchPlayers: (searchTerm)=>apiClient.get(`/players/search?searchTerm=${encodeURIComponent(searchTerm)}`),\n    getPlayerStats: ()=>apiClient.get('/players/stats'),\n    getTopPlayersByLevel: (count = 10)=>apiClient.get(`/players/top-by-level?count=${count}`),\n    getVipPlayers: (minVipLevel = 1)=>apiClient.get(`/players/vip?minVipLevel=${minVipLevel}`),\n    updatePlayer: (id, data)=>apiClient.put(`/players/${id}`, data),\n    banPlayer: (id, bannedUntil, reason)=>apiClient.post(`/players/${id}/ban`, {\n            bannedUntil,\n            reason\n        }),\n    unbanPlayer: (id)=>apiClient.post(`/players/${id}/unban`)\n};\n// 运营数据API方法\nconst operationalDataApi = {\n    // 全局统计\n    getGlobalStats: ()=>apiClient.get('/operational-data/global-stats'),\n    getGlobalStatsByDate: (date)=>apiClient.get(`/operational-data/global-stats/${date}`),\n    // 用户信息统计\n    getUserInfoStats: ()=>apiClient.get('/operational-data/user-info-stats'),\n    getUserInfoStatsByDateRange: (startDate, endDate)=>apiClient.get(`/operational-data/user-info-stats/${startDate}/${endDate}`),\n    // 付费信息统计\n    getPaymentInfoStats: ()=>apiClient.get('/operational-data/payment-info-stats'),\n    getPaymentInfoStatsByDateRange: (startDate, endDate)=>apiClient.get(`/operational-data/payment-info-stats/${startDate}/${endDate}`),\n    // 数据分析\n    getConversionAnalysis: (date)=>apiClient.get(`/operational-data/conversion-analysis/${date}`),\n    getRetentionAnalysis: (date)=>apiClient.get(`/operational-data/retention-analysis/${date}`),\n    getActiveUserAnalysis: (date)=>apiClient.get(`/operational-data/active-user-analysis/${date}`),\n    // 记录数据\n    recordVisit: (data)=>apiClient.post('/operational-data/record-visit', data)\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tanstack","vendor-chunks/@ant-design","vendor-chunks/antd","vendor-chunks/next","vendor-chunks/rc-picker","vendor-chunks/@rc-component","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/rc-field-form","vendor-chunks/rc-menu","vendor-chunks/rc-util","vendor-chunks/rc-tabs","vendor-chunks/resize-observer-polyfill","vendor-chunks/rc-motion","vendor-chunks/rc-notification","vendor-chunks/@babel","vendor-chunks/rc-pagination","vendor-chunks/rc-textarea","vendor-chunks/rc-input","vendor-chunks/follow-redirects","vendor-chunks/rc-overflow","vendor-chunks/debug","vendor-chunks/stylis","vendor-chunks/form-data","vendor-chunks/rc-collapse","vendor-chunks/get-intrinsic","vendor-chunks/rc-resize-observer","vendor-chunks/rc-dropdown","vendor-chunks/asynckit","vendor-chunks/rc-tooltip","vendor-chunks/throttle-debounce","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/copy-to-clipboard","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/@emotion","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/classnames","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/toggle-selection","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/rc-table","vendor-chunks/rc-select","vendor-chunks/rc-tree","vendor-chunks/rc-virtual-list","vendor-chunks/rc-checkbox","vendor-chunks/rc-dialog","vendor-chunks/rc-progress"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fservers%2Fstatus%2Fpage&page=%2Fservers%2Fstatus%2Fpage&appPaths=%2Fservers%2Fstatus%2Fpage&pagePath=private-next-app-dir%2Fservers%2Fstatus%2Fpage.tsx&appDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();