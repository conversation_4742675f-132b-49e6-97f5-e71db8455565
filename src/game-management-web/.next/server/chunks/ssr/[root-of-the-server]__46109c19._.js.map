{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Form, Input, Button, Card, Typography, Space, Alert, Spin } from 'antd';\nimport { UserOutlined, LockOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\n\nconst { Title, Text } = Typography;\n\ninterface LoginFormValues {\n  username: string;\n  password: string;\n}\n\nconst LoginPage: React.FC = () => {\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const { login, isAuthenticated, isLoading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    // 如果已经登录，重定向到仪表板\n    if (isAuthenticated && !isLoading) {\n      router.push('/dashboard');\n    }\n  }, [isAuthenticated, isLoading, router]);\n\n  const handleSubmit = async (values: LoginFormValues) => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      const success = await login(values.username, values.password);\n      if (success) {\n        router.push('/dashboard');\n      }\n    } catch (err: any) {\n      setError(err.message || '登录失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 临时注释掉加载状态检查，用于调试\n  // if (isLoading) {\n  //   return (\n  //     <div style={{\n  //       display: 'flex',\n  //       justifyContent: 'center',\n  //       alignItems: 'center',\n  //       minHeight: '100vh',\n  //       background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n  //     }}>\n  //       <Spin size=\"large\" />\n  //     </div>\n  //   );\n  // }\n\n  if (isAuthenticated) {\n    return null; // 将重定向到仪表板\n  }\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      padding: '20px',\n    }}>\n      <Card\n        style={{\n          width: '100%',\n          maxWidth: 400,\n          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',\n          borderRadius: 12,\n        }}\n        styles={{\n          body: { padding: '40px 32px' },\n        }}\n      >\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%', textAlign: 'center' }}>\n          {/* Logo 和标题 */}\n          <Space direction=\"vertical\" size=\"small\">\n            <PlayCircleOutlined style={{ fontSize: 48, color: '#667eea' }} />\n            <Title level={2} style={{ margin: 0, color: '#333' }}>\n              游戏管理系统\n            </Title>\n            <Text type=\"secondary\">请登录您的账户</Text>\n          </Space>\n\n          {/* 错误提示 */}\n          {error && (\n            <Alert\n              message={error}\n              type=\"error\"\n              showIcon\n              style={{ textAlign: 'left' }}\n            />\n          )}\n\n          {/* 登录表单 */}\n          <Form\n            form={form}\n            name=\"login\"\n            onFinish={handleSubmit}\n            autoComplete=\"off\"\n            size=\"large\"\n            style={{ width: '100%' }}\n          >\n            <Form.Item\n              name=\"username\"\n              rules={[\n                { required: true, message: '请输入用户名' },\n                { min: 3, message: '用户名至少3个字符' },\n              ]}\n            >\n              <Input\n                prefix={<UserOutlined />}\n                placeholder=\"用户名\"\n                autoComplete=\"username\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"password\"\n              rules={[\n                { required: true, message: '请输入密码' },\n                { min: 6, message: '密码至少6个字符' },\n              ]}\n            >\n              <Input.Password\n                prefix={<LockOutlined />}\n                placeholder=\"密码\"\n                autoComplete=\"current-password\"\n              />\n            </Form.Item>\n\n            <Form.Item style={{ marginBottom: 0 }}>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={loading}\n                style={{\n                  width: '100%',\n                  height: 48,\n                  fontSize: 16,\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  border: 'none',\n                }}\n              >\n                {loading ? '登录中...' : '登录'}\n              </Button>\n            </Form.Item>\n          </Form>\n\n          {/* 测试账号提示 */}\n          <div style={{\n            marginTop: 16,\n            padding: 12,\n            background: '#f0f2f5',\n            borderRadius: 6,\n            border: '1px dashed #d9d9d9'\n          }}>\n            <Text type=\"secondary\" style={{ fontSize: 12 }}>\n              <strong>测试账号：</strong>用户名: 111, 密码: 111111\n            </Text>\n          </div>\n\n          {/* 底部信息 */}\n          <Space direction=\"vertical\" size=\"small\" style={{ marginTop: 24 }}>\n            <Text type=\"secondary\" style={{ fontSize: 12 }}>\n              忘记密码？请联系系统管理员\n            </Text>\n            <Text type=\"secondary\" style={{ fontSize: 12 }}>\n              © 2024 游戏管理系统. 保留所有权利.\n            </Text>\n          </Space>\n        </Space>\n      </Card>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAOlC,MAAM,YAAsB;IAC1B,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAI,CAAC,OAAO;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACpD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iBAAiB;QACjB,IAAI,mBAAmB,CAAC,WAAW;YACjC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,MAAM,eAAe,OAAO;QAC1B,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,UAAU,MAAM,MAAM,OAAO,QAAQ,EAAE,OAAO,QAAQ;YAC5D,IAAI,SAAS;gBACX,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,mBAAmB;IACnB,mBAAmB;IACnB,aAAa;IACb,oBAAoB;IACpB,yBAAyB;IACzB,kCAAkC;IAClC,8BAA8B;IAC9B,4BAA4B;IAC5B,yEAAyE;IACzE,UAAU;IACV,8BAA8B;IAC9B,aAAa;IACb,OAAO;IACP,IAAI;IAEJ,IAAI,iBAAiB;QACnB,OAAO,MAAM,WAAW;IAC1B;IAEA,qBACE,8OAAC;QAAI,OAAO;YACV,WAAW;YACX,YAAY;YACZ,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,SAAS;QACX;kBACE,cAAA,8OAAC,8KAAA,CAAA,OAAI;YACH,OAAO;gBACL,OAAO;gBACP,UAAU;gBACV,WAAW;gBACX,cAAc;YAChB;YACA,QAAQ;gBACN,MAAM;oBAAE,SAAS;gBAAY;YAC/B;sBAEA,cAAA,8OAAC,gMAAA,CAAA,QAAK;gBAAC,WAAU;gBAAW,MAAK;gBAAQ,OAAO;oBAAE,OAAO;oBAAQ,WAAW;gBAAS;;kCAEnF,8OAAC,gMAAA,CAAA,QAAK;wBAAC,WAAU;wBAAW,MAAK;;0CAC/B,8OAAC,8NAAA,CAAA,qBAAkB;gCAAC,OAAO;oCAAE,UAAU;oCAAI,OAAO;gCAAU;;;;;;0CAC5D,8OAAC;gCAAM,OAAO;gCAAG,OAAO;oCAAE,QAAQ;oCAAG,OAAO;gCAAO;0CAAG;;;;;;0CAGtD,8OAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;oBAIxB,uBACC,8OAAC,gLAAA,CAAA,QAAK;wBACJ,SAAS;wBACT,MAAK;wBACL,QAAQ;wBACR,OAAO;4BAAE,WAAW;wBAAO;;;;;;kCAK/B,8OAAC,8KAAA,CAAA,OAAI;wBACH,MAAM;wBACN,MAAK;wBACL,UAAU;wBACV,cAAa;wBACb,MAAK;wBACL,OAAO;4BAAE,OAAO;wBAAO;;0CAEvB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gCACR,MAAK;gCACL,OAAO;oCACL;wCAAE,UAAU;wCAAM,SAAS;oCAAS;oCACpC;wCAAE,KAAK;wCAAG,SAAS;oCAAY;iCAChC;0CAED,cAAA,8OAAC,gLAAA,CAAA,QAAK;oCACJ,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;oCACrB,aAAY;oCACZ,cAAa;;;;;;;;;;;0CAIjB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gCACR,MAAK;gCACL,OAAO;oCACL;wCAAE,UAAU;wCAAM,SAAS;oCAAQ;oCACnC;wCAAE,KAAK;wCAAG,SAAS;oCAAW;iCAC/B;0CAED,cAAA,8OAAC,gLAAA,CAAA,QAAK,CAAC,QAAQ;oCACb,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;oCACrB,aAAY;oCACZ,cAAa;;;;;;;;;;;0CAIjB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gCAAC,OAAO;oCAAE,cAAc;gCAAE;0CAClC,cAAA,8OAAC,kMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAS;oCACT,SAAS;oCACT,OAAO;wCACL,OAAO;wCACP,QAAQ;wCACR,UAAU;wCACV,YAAY;wCACZ,QAAQ;oCACV;8CAEC,UAAU,WAAW;;;;;;;;;;;;;;;;;kCAM5B,8OAAC;wBAAI,OAAO;4BACV,WAAW;4BACX,SAAS;4BACT,YAAY;4BACZ,cAAc;4BACd,QAAQ;wBACV;kCACE,cAAA,8OAAC;4BAAK,MAAK;4BAAY,OAAO;gCAAE,UAAU;4BAAG;;8CAC3C,8OAAC;8CAAO;;;;;;gCAAc;;;;;;;;;;;;kCAK1B,8OAAC,gMAAA,CAAA,QAAK;wBAAC,WAAU;wBAAW,MAAK;wBAAQ,OAAO;4BAAE,WAAW;wBAAG;;0CAC9D,8OAAC;gCAAK,MAAK;gCAAY,OAAO;oCAAE,UAAU;gCAAG;0CAAG;;;;;;0CAGhD,8OAAC;gCAAK,MAAK;gCAAY,OAAO;oCAAE,UAAU;gCAAG;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5D;uCAEe", "debugId": null}}]}