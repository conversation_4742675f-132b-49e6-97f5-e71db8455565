{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Layout, Menu, Avatar, Dropdown, Button, theme, Space, Typography } from 'antd';\nimport {\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  DashboardOutlined,\n  UserOutlined,\n  TeamOutlined,\n  AppstoreOutlined,\n  DollarOutlined,\n  CloudServerOutlined,\n  BellOutlined,\n  Bar<PERSON>hartOutlined,\n  SettingOutlined,\n  LogoutOutlined,\n  SafetyOutlined,\n  LinkOutlined,\n} from '@ant-design/icons';\nimport { useAuth, ROLES } from '@/contexts/AuthContext';\nimport { useRouter, usePathname } from 'next/navigation';\n\nconst { Header, Sider, Content } = Layout;\nconst { Text } = Typography;\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout: React.FC<MainLayoutProps> = ({ children }) => {\n  const [collapsed, setCollapsed] = useState(false);\n  const { user, logout, hasRole, hasAnyRole } = useAuth();\n  const router = useRouter();\n  const pathname = usePathname();\n  const {\n    token: { colorBgContainer, borderRadiusLG },\n  } = theme.useToken();\n\n  // 菜单项配置\n  const menuItems = [\n    {\n      key: '/dashboard',\n      icon: <DashboardOutlined />,\n      label: '仪表板',\n      roles: [], // 所有角色都可以访问\n    },\n    {\n      key: '/operational-data',\n      icon: <BarChartOutlined />,\n      label: '运营数据',\n      roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],\n    },\n    {\n      key: '/players',\n      icon: <TeamOutlined />,\n      label: '玩家管理',\n      roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST, ROLES.CUSTOMER_SERVICE_MANAGER, ROLES.CUSTOMER_SERVICE_SPECIALIST],\n    },\n    {\n      key: '/payments',\n      icon: <DollarOutlined />,\n      label: '支付管理',\n      roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],\n    },\n    {\n      key: '/games',\n      icon: <AppstoreOutlined />,\n      label: '游戏数据',\n      children: [\n        {\n          key: '/games/activities',\n          label: '活动管理',\n          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],\n        },\n        {\n          key: '/games/announcements',\n          label: '公告管理',\n          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],\n        },\n        {\n          key: '/games/items',\n          label: '道具管理',\n          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],\n        },\n      ],\n    },\n    {\n      key: '/servers',\n      icon: <CloudServerOutlined />,\n      label: '服务器管理',\n      children: [\n        {\n          key: '/servers/status',\n          label: '服务器状态',\n          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER],\n        },\n        {\n          key: '/servers/monitoring',\n          label: '监控告警',\n          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER],\n        },\n        {\n          key: '/servers/logs',\n          label: '日志管理',\n          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER],\n        },\n      ],\n    },\n    {\n      key: '/customer-service',\n      icon: <BellOutlined />,\n      label: '客服管理',\n      roles: [ROLES.SYSTEM_ADMIN, ROLES.CUSTOMER_SERVICE_MANAGER, ROLES.CUSTOMER_SERVICE_SPECIALIST],\n    },\n    {\n      key: '/security',\n      icon: <SafetyOutlined />,\n      label: '安全管理',\n      roles: [ROLES.SYSTEM_ADMIN],\n    },\n    {\n      key: '/reports',\n      icon: <BarChartOutlined />,\n      label: '数据报表',\n      roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],\n    },\n    {\n      key: '/channels',\n      icon: <LinkOutlined />,\n      label: '渠道管理',\n      roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER],\n    },\n  ];\n\n  // 过滤菜单项基于用户角色\n  const filterMenuItems = (items: any[]): any[] => {\n    return items.filter(item => {\n      if (item.roles && item.roles.length > 0) {\n        if (!hasAnyRole(item.roles)) {\n          return false;\n        }\n      }\n      \n      if (item.children) {\n        item.children = filterMenuItems(item.children);\n        return item.children.length > 0;\n      }\n      \n      return true;\n    });\n  };\n\n  const filteredMenuItems = filterMenuItems(menuItems);\n\n  // 用户下拉菜单\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人资料',\n      onClick: () => router.push('/profile'),\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '账户设置',\n      onClick: () => router.push('/account-settings'),\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      onClick: logout,\n    },\n  ];\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    router.push(key);\n  };\n\n  return (\n    <Layout style={{ minHeight: '100vh' }}>\n      <Sider trigger={null} collapsible collapsed={collapsed} theme=\"dark\">\n        <div style={{ \n          height: 64, \n          margin: 16, \n          background: 'rgba(255, 255, 255, 0.2)', \n          borderRadius: 6,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontWeight: 'bold',\n          fontSize: collapsed ? 14 : 16,\n        }}>\n          {collapsed ? 'GM' : '游戏管理系统'}\n        </div>\n        <Menu\n          theme=\"dark\"\n          mode=\"inline\"\n          selectedKeys={[pathname]}\n          items={filteredMenuItems}\n          onClick={handleMenuClick}\n        />\n      </Sider>\n      <Layout>\n        <Header style={{ \n          padding: '0 16px', \n          background: colorBgContainer,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          borderBottom: '1px solid #f0f0f0',\n        }}>\n          <Button\n            type=\"text\"\n            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n            onClick={() => setCollapsed(!collapsed)}\n            style={{\n              fontSize: '16px',\n              width: 64,\n              height: 64,\n            }}\n          />\n          \n          <Space>\n            <Text type=\"secondary\">欢迎回来，</Text>\n            <Dropdown menu={{ items: userMenuItems }} placement=\"bottomRight\">\n              <Space style={{ cursor: 'pointer' }}>\n                <Avatar icon={<UserOutlined />} />\n                <Text strong>{user?.displayName || user?.username}</Text>\n              </Space>\n            </Dropdown>\n          </Space>\n        </Header>\n        <Content\n          style={{\n            margin: '16px',\n            padding: 24,\n            minHeight: 280,\n            background: colorBgContainer,\n            borderRadius: borderRadiusLG,\n          }}\n        >\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default MainLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AArBA;;;;;;;AAuBA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,kLAAA,CAAA,SAAM;AACzC,MAAM,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAM3B,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACpD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EACJ,OAAO,EAAE,gBAAgB,EAAE,cAAc,EAAE,EAC5C,GAAG,gLAAA,CAAA,QAAK,CAAC,QAAQ;IAElB,QAAQ;IACR,MAAM,YAAY;QAChB;YACE,KAAK;YACL,oBAAM,8OAAC,4NAAA,CAAA,oBAAiB;;;;;YACxB,OAAO;YACP,OAAO,EAAE;QACX;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;YACP,OAAO;gBAAC,+HAAA,CAAA,QAAK,CAAC,YAAY;gBAAE,+HAAA,CAAA,QAAK,CAAC,eAAe;gBAAE,+HAAA,CAAA,QAAK,CAAC,kBAAkB;aAAC;QAC9E;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,OAAO;gBAAC,+HAAA,CAAA,QAAK,CAAC,YAAY;gBAAE,+HAAA,CAAA,QAAK,CAAC,eAAe;gBAAE,+HAAA,CAAA,QAAK,CAAC,kBAAkB;gBAAE,+HAAA,CAAA,QAAK,CAAC,wBAAwB;gBAAE,+HAAA,CAAA,QAAK,CAAC,2BAA2B;aAAC;QACjJ;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,OAAO;gBAAC,+HAAA,CAAA,QAAK,CAAC,YAAY;gBAAE,+HAAA,CAAA,QAAK,CAAC,eAAe;gBAAE,+HAAA,CAAA,QAAK,CAAC,kBAAkB;aAAC;QAC9E;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,OAAO;oBACP,OAAO;wBAAC,+HAAA,CAAA,QAAK,CAAC,YAAY;wBAAE,+HAAA,CAAA,QAAK,CAAC,eAAe;wBAAE,+HAAA,CAAA,QAAK,CAAC,kBAAkB;qBAAC;gBAC9E;gBACA;oBACE,KAAK;oBACL,OAAO;oBACP,OAAO;wBAAC,+HAAA,CAAA,QAAK,CAAC,YAAY;wBAAE,+HAAA,CAAA,QAAK,CAAC,eAAe;wBAAE,+HAAA,CAAA,QAAK,CAAC,kBAAkB;qBAAC;gBAC9E;gBACA;oBACE,KAAK;oBACL,OAAO;oBACP,OAAO;wBAAC,+HAAA,CAAA,QAAK,CAAC,YAAY;wBAAE,+HAAA,CAAA,QAAK,CAAC,eAAe;wBAAE,+HAAA,CAAA,QAAK,CAAC,kBAAkB;qBAAC;gBAC9E;aACD;QACH;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,gOAAA,CAAA,sBAAmB;;;;;YAC1B,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,OAAO;oBACP,OAAO;wBAAC,+HAAA,CAAA,QAAK,CAAC,YAAY;wBAAE,+HAAA,CAAA,QAAK,CAAC,eAAe;qBAAC;gBACpD;gBACA;oBACE,KAAK;oBACL,OAAO;oBACP,OAAO;wBAAC,+HAAA,CAAA,QAAK,CAAC,YAAY;wBAAE,+HAAA,CAAA,QAAK,CAAC,eAAe;qBAAC;gBACpD;gBACA;oBACE,KAAK;oBACL,OAAO;oBACP,OAAO;wBAAC,+HAAA,CAAA,QAAK,CAAC,YAAY;wBAAE,+HAAA,CAAA,QAAK,CAAC,eAAe;qBAAC;gBACpD;aACD;QACH;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,OAAO;gBAAC,+HAAA,CAAA,QAAK,CAAC,YAAY;gBAAE,+HAAA,CAAA,QAAK,CAAC,wBAAwB;gBAAE,+HAAA,CAAA,QAAK,CAAC,2BAA2B;aAAC;QAChG;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,OAAO;gBAAC,+HAAA,CAAA,QAAK,CAAC,YAAY;aAAC;QAC7B;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;YACP,OAAO;gBAAC,+HAAA,CAAA,QAAK,CAAC,YAAY;gBAAE,+HAAA,CAAA,QAAK,CAAC,eAAe;gBAAE,+HAAA,CAAA,QAAK,CAAC,kBAAkB;aAAC;QAC9E;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,OAAO;gBAAC,+HAAA,CAAA,QAAK,CAAC,YAAY;gBAAE,+HAAA,CAAA,QAAK,CAAC,eAAe;aAAC;QACpD;KACD;IAED,cAAc;IACd,MAAM,kBAAkB,CAAC;QACvB,OAAO,MAAM,MAAM,CAAC,CAAA;YAClB,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;gBACvC,IAAI,CAAC,WAAW,KAAK,KAAK,GAAG;oBAC3B,OAAO;gBACT;YACF;YAEA,IAAI,KAAK,QAAQ,EAAE;gBACjB,KAAK,QAAQ,GAAG,gBAAgB,KAAK,QAAQ;gBAC7C,OAAO,KAAK,QAAQ,CAAC,MAAM,GAAG;YAChC;YAEA,OAAO;QACT;IACF;IAEA,MAAM,oBAAoB,gBAAgB;IAE1C,SAAS;IACT,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,SAAS,IAAM,OAAO,IAAI,CAAC;QAC7B;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;YACtB,OAAO;YACP,SAAS,IAAM,OAAO,IAAI,CAAC;QAC7B;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,SAAS;QACX;KACD;IAED,MAAM,kBAAkB,CAAC,EAAE,GAAG,EAAmB;QAC/C,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC,kLAAA,CAAA,SAAM;QAAC,OAAO;YAAE,WAAW;QAAQ;;0BAClC,8OAAC;gBAAM,SAAS;gBAAM,WAAW;gBAAC,WAAW;gBAAW,OAAM;;kCAC5D,8OAAC;wBAAI,OAAO;4BACV,QAAQ;4BACR,QAAQ;4BACR,YAAY;4BACZ,cAAc;4BACd,SAAS;4BACT,YAAY;4BACZ,gBAAgB;4BAChB,OAAO;4BACP,YAAY;4BACZ,UAAU,YAAY,KAAK;wBAC7B;kCACG,YAAY,OAAO;;;;;;kCAEtB,8OAAC,8KAAA,CAAA,OAAI;wBACH,OAAM;wBACN,MAAK;wBACL,cAAc;4BAAC;yBAAS;wBACxB,OAAO;wBACP,SAAS;;;;;;;;;;;;0BAGb,8OAAC,kLAAA,CAAA,SAAM;;kCACL,8OAAC;wBAAO,OAAO;4BACb,SAAS;4BACT,YAAY;4BACZ,SAAS;4BACT,YAAY;4BACZ,gBAAgB;4BAChB,cAAc;wBAChB;;0CACE,8OAAC,kMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,MAAM,0BAAY,8OAAC,8NAAA,CAAA,qBAAkB;;;;2DAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gCAC5D,SAAS,IAAM,aAAa,CAAC;gCAC7B,OAAO;oCACL,UAAU;oCACV,OAAO;oCACP,QAAQ;gCACV;;;;;;0CAGF,8OAAC,gMAAA,CAAA,QAAK;;kDACJ,8OAAC;wCAAK,MAAK;kDAAY;;;;;;kDACvB,8OAAC,sLAAA,CAAA,WAAQ;wCAAC,MAAM;4CAAE,OAAO;wCAAc;wCAAG,WAAU;kDAClD,cAAA,8OAAC,gMAAA,CAAA,QAAK;4CAAC,OAAO;gDAAE,QAAQ;4CAAU;;8DAChC,8OAAC,kLAAA,CAAA,SAAM;oDAAC,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;8DAC3B,8OAAC;oDAAK,MAAM;8DAAE,MAAM,eAAe,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKjD,8OAAC;wBACC,OAAO;4BACL,QAAQ;4BACR,SAAS;4BACT,WAAW;4BACX,YAAY;4BACZ,cAAc;wBAChB;kCAEC;;;;;;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { Spin } from 'antd';\nimport MainLayout from '@/components/Layout/MainLayout';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requiredRoles?: string[];\n}\n\nconst ProtectedRoute: React.FC<ProtectedRouteProps> = ({ \n  children, \n  requiredRoles = [] \n}) => {\n  const { isAuthenticated, isLoading, hasAnyRole } = useAuth();\n  const router = useRouter();\n  const pathname = usePathname();\n\n  useEffect(() => {\n    if (!isLoading) {\n      if (!isAuthenticated) {\n        // 未登录，重定向到登录页\n        router.push('/login');\n        return;\n      }\n\n      if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {\n        // 没有权限，重定向到仪表板或显示无权限页面\n        router.push('/dashboard');\n        return;\n      }\n    }\n  }, [isAuthenticated, isLoading, hasAnyRole, requiredRoles, router]);\n\n  if (isLoading) {\n    return (\n      <div style={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '100vh',\n      }}>\n        <Spin size=\"large\" />\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null; // 将重定向到登录页\n  }\n\n  if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {\n    return null; // 将重定向到仪表板\n  }\n\n  // 如果是登录页面，不使用主布局\n  if (pathname === '/login') {\n    return <>{children}</>;\n  }\n\n  // 使用主布局包装受保护的页面\n  return (\n    <MainLayout>\n      {children}\n    </MainLayout>\n  );\n};\n\nexport default ProtectedRoute;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAaA,MAAM,iBAAgD,CAAC,EACrD,QAAQ,EACR,gBAAgB,EAAE,EACnB;IACC,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;YACd,IAAI,CAAC,iBAAiB;gBACpB,cAAc;gBACd,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,cAAc,MAAM,GAAG,KAAK,CAAC,WAAW,gBAAgB;gBAC1D,uBAAuB;gBACvB,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;IACF,GAAG;QAAC;QAAiB;QAAW;QAAY;QAAe;KAAO;IAElE,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,OAAO;gBACV,SAAS;gBACT,gBAAgB;gBAChB,YAAY;gBACZ,WAAW;YACb;sBACE,cAAA,8OAAC,8KAAA,CAAA,OAAI;gBAAC,MAAK;;;;;;;;;;;IAGjB;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO,MAAM,WAAW;IAC1B;IAEA,IAAI,cAAc,MAAM,GAAG,KAAK,CAAC,WAAW,gBAAgB;QAC1D,OAAO,MAAM,WAAW;IAC1B;IAEA,iBAAiB;IACjB,IAAI,aAAa,UAAU;QACzB,qBAAO;sBAAG;;IACZ;IAEA,gBAAgB;IAChB,qBACE,8OAAC,0IAAA,CAAA,UAAU;kBACR;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport ProtectedRoute from '@/components/Auth/ProtectedRoute';\nimport { Row, Col, Card, Statistic, Typography, Space, Progress, Table, Tag } from 'antd';\nimport {\n  UserOutlined,\n  DollarOutlined,\n  CloudServerOutlined,\n  TrophyOutlined,\n  ArrowUpOutlined,\n  ArrowDownOutlined,\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\n\nconst DashboardContent: React.FC = () => {\n  // 模拟数据\n  const stats = {\n    totalPlayers: 125430,\n    activePlayers: 8567,\n    todayRevenue: 45678.90,\n    monthRevenue: 1234567.89,\n    onlineServers: 12,\n    totalServers: 15,\n  };\n\n  const recentPlayers = [\n    {\n      key: '1',\n      nickname: '龙战士',\n      level: 85,\n      class: '战士',\n      server: '服务器1',\n      lastLogin: '2024-06-25 14:30',\n      status: 'online',\n    },\n    {\n      key: '2',\n      nickname: '法师小明',\n      level: 72,\n      class: '法师',\n      server: '服务器2',\n      lastLogin: '2024-06-25 14:25',\n      status: 'online',\n    },\n    {\n      key: '3',\n      nickname: '弓箭手',\n      level: 68,\n      class: '弓箭手',\n      server: '服务器1',\n      lastLogin: '2024-06-25 14:20',\n      status: 'offline',\n    },\n  ];\n\n  const serverStatus = [\n    {\n      key: '1',\n      name: '服务器1',\n      status: 'online',\n      players: 1250,\n      maxPlayers: 2000,\n      cpu: 65,\n      memory: 78,\n    },\n    {\n      key: '2',\n      name: '服务器2',\n      status: 'online',\n      players: 980,\n      maxPlayers: 2000,\n      cpu: 45,\n      memory: 62,\n    },\n    {\n      key: '3',\n      name: '服务器3',\n      status: 'maintenance',\n      players: 0,\n      maxPlayers: 2000,\n      cpu: 0,\n      memory: 0,\n    },\n  ];\n\n  const playerColumns = [\n    {\n      title: '昵称',\n      dataIndex: 'nickname',\n      key: 'nickname',\n    },\n    {\n      title: '等级',\n      dataIndex: 'level',\n      key: 'level',\n    },\n    {\n      title: '职业',\n      dataIndex: 'class',\n      key: 'class',\n    },\n    {\n      title: '服务器',\n      dataIndex: 'server',\n      key: 'server',\n    },\n    {\n      title: '最后登录',\n      dataIndex: 'lastLogin',\n      key: 'lastLogin',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status: string) => (\n        <Tag color={status === 'online' ? 'green' : 'default'}>\n          {status === 'online' ? '在线' : '离线'}\n        </Tag>\n      ),\n    },\n  ];\n\n  const serverColumns = [\n    {\n      title: '服务器名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status: string) => {\n        const color = status === 'online' ? 'green' : status === 'maintenance' ? 'orange' : 'red';\n        const text = status === 'online' ? '在线' : status === 'maintenance' ? '维护中' : '离线';\n        return <Tag color={color}>{text}</Tag>;\n      },\n    },\n    {\n      title: '在线玩家',\n      key: 'playerCount',\n      render: (record: any) => `${record.players}/${record.maxPlayers}`,\n    },\n    {\n      title: 'CPU使用率',\n      dataIndex: 'cpu',\n      key: 'cpu',\n      render: (cpu: number) => (\n        <Progress \n          percent={cpu} \n          size=\"small\" \n          status={cpu > 80 ? 'exception' : cpu > 60 ? 'active' : 'success'}\n        />\n      ),\n    },\n    {\n      title: '内存使用率',\n      dataIndex: 'memory',\n      key: 'memory',\n      render: (memory: number) => (\n        <Progress \n          percent={memory} \n          size=\"small\" \n          status={memory > 80 ? 'exception' : memory > 60 ? 'active' : 'success'}\n        />\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Title level={2}>仪表板</Title>\n      \n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"总玩家数\"\n              value={stats.totalPlayers}\n              prefix={<UserOutlined />}\n              valueStyle={{ color: '#3f8600' }}\n              suffix={\n                <Space>\n                  <ArrowUpOutlined />\n                  <Text type=\"success\">12%</Text>\n                </Space>\n              }\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"活跃玩家\"\n              value={stats.activePlayers}\n              prefix={<TrophyOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n              suffix={\n                <Space>\n                  <ArrowUpOutlined />\n                  <Text style={{ color: '#1890ff' }}>8%</Text>\n                </Space>\n              }\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"今日收入\"\n              value={stats.todayRevenue}\n              precision={2}\n              prefix={<DollarOutlined />}\n              valueStyle={{ color: '#cf1322' }}\n              suffix={\n                <Space>\n                  <ArrowDownOutlined />\n                  <Text type=\"danger\">3%</Text>\n                </Space>\n              }\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"在线服务器\"\n              value={`${stats.onlineServers}/${stats.totalServers}`}\n              prefix={<CloudServerOutlined />}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 图表和表格 */}\n      <Row gutter={[16, 16]}>\n        <Col xs={24} lg={12}>\n          <Card title=\"最近登录玩家\" size=\"small\">\n            <Table\n              columns={playerColumns}\n              dataSource={recentPlayers}\n              pagination={false}\n              size=\"small\"\n            />\n          </Card>\n        </Col>\n        <Col xs={24} lg={12}>\n          <Card title=\"服务器状态\" size=\"small\">\n            <Table\n              columns={serverColumns}\n              dataSource={serverStatus}\n              pagination={false}\n              size=\"small\"\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 快速操作 */}\n      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>\n        <Col span={24}>\n          <Card title=\"快速操作\" size=\"small\">\n            <Space wrap>\n              <Card.Grid style={{ width: '25%', textAlign: 'center' }}>\n                <UserOutlined style={{ fontSize: 24, color: '#1890ff' }} />\n                <div style={{ marginTop: 8 }}>玩家管理</div>\n              </Card.Grid>\n              <Card.Grid style={{ width: '25%', textAlign: 'center' }}>\n                <DollarOutlined style={{ fontSize: 24, color: '#52c41a' }} />\n                <div style={{ marginTop: 8 }}>支付管理</div>\n              </Card.Grid>\n              <Card.Grid style={{ width: '25%', textAlign: 'center' }}>\n                <CloudServerOutlined style={{ fontSize: 24, color: '#722ed1' }} />\n                <div style={{ marginTop: 8 }}>服务器管理</div>\n              </Card.Grid>\n              <Card.Grid style={{ width: '25%', textAlign: 'center' }}>\n                <TrophyOutlined style={{ fontSize: 24, color: '#fa8c16' }} />\n                <div style={{ marginTop: 8 }}>活动管理</div>\n              </Card.Grid>\n            </Space>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nconst DashboardPage: React.FC = () => {\n  return (\n    <ProtectedRoute>\n      <DashboardContent />\n    </ProtectedRoute>\n  );\n};\n\nexport default DashboardPage;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAcA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAElC,MAAM,mBAA6B;IACjC,OAAO;IACP,MAAM,QAAQ;QACZ,cAAc;QACd,eAAe;QACf,cAAc;QACd,cAAc;QACd,eAAe;QACf,cAAc;IAChB;IAEA,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,UAAU;YACV,OAAO;YACP,OAAO;YACP,QAAQ;YACR,WAAW;YACX,QAAQ;QACV;QACA;YACE,KAAK;YACL,UAAU;YACV,OAAO;YACP,OAAO;YACP,QAAQ;YACR,WAAW;YACX,QAAQ;QACV;QACA;YACE,KAAK;YACL,UAAU;YACV,OAAO;YACP,OAAO;YACP,QAAQ;YACR,WAAW;YACX,QAAQ;QACV;KACD;IAED,MAAM,eAAe;QACnB;YACE,KAAK;YACL,MAAM;YACN,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,KAAK;YACL,QAAQ;QACV;QACA;YACE,KAAK;YACL,MAAM;YACN,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,KAAK;YACL,QAAQ;QACV;QACA;YACE,KAAK;YACL,MAAM;YACN,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,KAAK;YACL,QAAQ;QACV;KACD;IAED,MAAM,gBAAgB;QACpB;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,uBACP,8OAAC,4KAAA,CAAA,MAAG;oBAAC,OAAO,WAAW,WAAW,UAAU;8BACzC,WAAW,WAAW,OAAO;;;;;;QAGpC;KACD;IAED,MAAM,gBAAgB;QACpB;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC;gBACP,MAAM,QAAQ,WAAW,WAAW,UAAU,WAAW,gBAAgB,WAAW;gBACpF,MAAM,OAAO,WAAW,WAAW,OAAO,WAAW,gBAAgB,QAAQ;gBAC7E,qBAAO,8OAAC,4KAAA,CAAA,MAAG;oBAAC,OAAO;8BAAQ;;;;;;YAC7B;QACF;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,SAAgB,GAAG,OAAO,OAAO,CAAC,CAAC,EAAE,OAAO,UAAU,EAAE;QACnE;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,oBACP,8OAAC,sLAAA,CAAA,WAAQ;oBACP,SAAS;oBACT,MAAK;oBACL,QAAQ,MAAM,KAAK,cAAc,MAAM,KAAK,WAAW;;;;;;QAG7D;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,uBACP,8OAAC,sLAAA,CAAA,WAAQ;oBACP,SAAS;oBACT,MAAK;oBACL,QAAQ,SAAS,KAAK,cAAc,SAAS,KAAK,WAAW;;;;;;QAGnE;KACD;IAED,qBACE,8OAAC;;0BACC,8OAAC;gBAAM,OAAO;0BAAG;;;;;;0BAGjB,8OAAC,4KAAA,CAAA,MAAG;gBAAC,QAAQ;oBAAC;oBAAI;iBAAG;gBAAE,OAAO;oBAAE,cAAc;gBAAG;;kCAC/C,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,MAAM,YAAY;gCACzB,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;gCACrB,YAAY;oCAAE,OAAO;gCAAU;gCAC/B,sBACE,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC,wNAAA,CAAA,kBAAe;;;;;sDAChB,8OAAC;4CAAK,MAAK;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM/B,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,MAAM,aAAa;gCAC1B,sBAAQ,8OAAC,sNAAA,CAAA,iBAAc;;;;;gCACvB,YAAY;oCAAE,OAAO;gCAAU;gCAC/B,sBACE,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC,wNAAA,CAAA,kBAAe;;;;;sDAChB,8OAAC;4CAAK,OAAO;gDAAE,OAAO;4CAAU;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,MAAM,YAAY;gCACzB,WAAW;gCACX,sBAAQ,8OAAC,sNAAA,CAAA,iBAAc;;;;;gCACvB,YAAY;oCAAE,OAAO;gCAAU;gCAC/B,sBACE,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC,4NAAA,CAAA,oBAAiB;;;;;sDAClB,8OAAC;4CAAK,MAAK;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM9B,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,GAAG,MAAM,aAAa,CAAC,CAAC,EAAE,MAAM,YAAY,EAAE;gCACrD,sBAAQ,8OAAC,gOAAA,CAAA,sBAAmB;;;;;gCAC5B,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC,4KAAA,CAAA,MAAG;gBAAC,QAAQ;oBAAC;oBAAI;iBAAG;;kCACnB,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,8OAAC,8KAAA,CAAA,OAAI;4BAAC,OAAM;4BAAS,MAAK;sCACxB,cAAA,8OAAC,gLAAA,CAAA,QAAK;gCACJ,SAAS;gCACT,YAAY;gCACZ,YAAY;gCACZ,MAAK;;;;;;;;;;;;;;;;kCAIX,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,8OAAC,8KAAA,CAAA,OAAI;4BAAC,OAAM;4BAAQ,MAAK;sCACvB,cAAA,8OAAC,gLAAA,CAAA,QAAK;gCACJ,SAAS;gCACT,YAAY;gCACZ,YAAY;gCACZ,MAAK;;;;;;;;;;;;;;;;;;;;;;0BAOb,8OAAC,4KAAA,CAAA,MAAG;gBAAC,QAAQ;oBAAC;oBAAI;iBAAG;gBAAE,OAAO;oBAAE,WAAW;gBAAG;0BAC5C,cAAA,8OAAC,4KAAA,CAAA,MAAG;oBAAC,MAAM;8BACT,cAAA,8OAAC,8KAAA,CAAA,OAAI;wBAAC,OAAM;wBAAO,MAAK;kCACtB,cAAA,8OAAC,gMAAA,CAAA,QAAK;4BAAC,IAAI;;8CACT,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,OAAO;wCAAE,OAAO;wCAAO,WAAW;oCAAS;;sDACpD,8OAAC,kNAAA,CAAA,eAAY;4CAAC,OAAO;gDAAE,UAAU;gDAAI,OAAO;4CAAU;;;;;;sDACtD,8OAAC;4CAAI,OAAO;gDAAE,WAAW;4CAAE;sDAAG;;;;;;;;;;;;8CAEhC,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,OAAO;wCAAE,OAAO;wCAAO,WAAW;oCAAS;;sDACpD,8OAAC,sNAAA,CAAA,iBAAc;4CAAC,OAAO;gDAAE,UAAU;gDAAI,OAAO;4CAAU;;;;;;sDACxD,8OAAC;4CAAI,OAAO;gDAAE,WAAW;4CAAE;sDAAG;;;;;;;;;;;;8CAEhC,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,OAAO;wCAAE,OAAO;wCAAO,WAAW;oCAAS;;sDACpD,8OAAC,gOAAA,CAAA,sBAAmB;4CAAC,OAAO;gDAAE,UAAU;gDAAI,OAAO;4CAAU;;;;;;sDAC7D,8OAAC;4CAAI,OAAO;gDAAE,WAAW;4CAAE;sDAAG;;;;;;;;;;;;8CAEhC,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,OAAO;wCAAE,OAAO;wCAAO,WAAW;oCAAS;;sDACpD,8OAAC,sNAAA,CAAA,iBAAc;4CAAC,OAAO;gDAAE,UAAU;gDAAI,OAAO;4CAAU;;;;;;sDACxD,8OAAC;4CAAI,OAAO;gDAAE,WAAW;4CAAE;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C;AAEA,MAAM,gBAA0B;IAC9B,qBACE,8OAAC,4IAAA,CAAA,UAAc;kBACb,cAAA,8OAAC;;;;;;;;;;AAGP;uCAEe", "debugId": null}}]}