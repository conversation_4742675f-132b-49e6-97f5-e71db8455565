{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Spin } from 'antd';\n\nexport default function Home() {\n  const { isAuthenticated, isLoading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!isLoading) {\n      if (isAuthenticated) {\n        router.push('/dashboard');\n      } else {\n        router.push('/login');\n      }\n    }\n  }, [isAuthenticated, isLoading, router]);\n\n  return (\n    <div style={{\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      minHeight: '100vh',\n    }}>\n      <Spin size=\"large\" />\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;YACd,IAAI,iBAAiB;gBACnB,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF;IACF,GAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,qBACE,8OAAC;QAAI,OAAO;YACV,SAAS;YACT,gBAAgB;YAChB,YAAY;YACZ,WAAW;QACb;kBACE,cAAA,8OAAC,8KAAA,CAAA,OAAI;YAAC,MAAK;;;;;;;;;;;AAGjB", "debugId": null}}]}