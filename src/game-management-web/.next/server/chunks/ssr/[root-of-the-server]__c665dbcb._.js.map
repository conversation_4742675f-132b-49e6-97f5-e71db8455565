{"version": 3, "sources": [], "sections": [{"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\n\n// API 基础配置\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5108/api';\n\n// 创建 axios 实例\nconst apiClient: AxiosInstance = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器 - 添加认证令牌\napiClient.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('accessToken');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器 - 处理认证错误\napiClient.interceptors.response.use(\n  (response: AxiosResponse) => {\n    return response;\n  },\n  async (error) => {\n    const originalRequest = error.config;\n\n    if (error.response?.status === 401 && !originalRequest._retry) {\n      originalRequest._retry = true;\n\n      try {\n        const refreshToken = localStorage.getItem('refreshToken');\n        if (refreshToken) {\n          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, refreshToken);\n          const { token } = response.data;\n          \n          localStorage.setItem('accessToken', token);\n          originalRequest.headers.Authorization = `Bearer ${token}`;\n          \n          return apiClient(originalRequest);\n        }\n      } catch (refreshError) {\n        // 刷新令牌失败，清除本地存储并重定向到登录页\n        localStorage.removeItem('accessToken');\n        localStorage.removeItem('refreshToken');\n        localStorage.removeItem('user');\n        window.location.href = '/login';\n      }\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// API 接口类型定义\nexport interface LoginRequest {\n  username: string;\n  password: string;\n}\n\nexport interface LoginResponse {\n  token: string;\n  expiresAt: string;\n  user: User;\n}\n\nexport interface User {\n  id: number;\n  username: string;\n  email: string;\n  displayName?: string;\n  createdAt: string;\n  lastLoginAt?: string;\n  isActive: boolean;\n  roles: string[];\n}\n\nexport interface CreateUserRequest {\n  username: string;\n  email: string;\n  password: string;\n  displayName?: string;\n  roles: string[];\n}\n\nexport interface UpdateUserRequest {\n  username?: string;\n  email?: string;\n  displayName?: string;\n  isActive?: boolean;\n  roles?: string[];\n}\n\nexport interface Player {\n  id: number;\n  accountId: string;\n  nickname: string;\n  level: number;\n  class: string;\n  experience: number;\n  gold: number;\n  diamonds: number;\n  vipLevel: number;\n  lastLoginAt?: string;\n  totalPlayTime: string;\n  ipAddress?: string;\n  serverId: number;\n  serverName: string;\n  isBanned: boolean;\n  bannedUntil?: string;\n  banReason?: string;\n  createdAt: string;\n}\n\nexport interface PlayerStats {\n  totalPlayers: number;\n  activePlayers: number;\n  newPlayersToday: number;\n  vipPlayers: number;\n  averageLevel: number;\n  levelDistribution: Record<string, number>;\n  classDistribution: Record<string, number>;\n}\n\n// API 方法\nexport const authApi = {\n  login: (data: LoginRequest) => \n    apiClient.post<LoginResponse>('/auth/login', data),\n  \n  logout: (refreshToken: string) => \n    apiClient.post('/auth/logout', refreshToken),\n  \n  register: (data: CreateUserRequest) => \n    apiClient.post<User>('/auth/register', data),\n  \n  refreshToken: (refreshToken: string) => \n    apiClient.post<LoginResponse>('/auth/refresh', refreshToken),\n  \n  changePassword: (currentPassword: string, newPassword: string) => \n    apiClient.post('/auth/change-password', { currentPassword, newPassword }),\n  \n  resetPassword: (email: string) => \n    apiClient.post('/auth/reset-password', { email }),\n};\n\nexport const usersApi = {\n  getUsers: () => \n    apiClient.get<User[]>('/users'),\n  \n  getUser: (id: number) => \n    apiClient.get<User>(`/users/${id}`),\n  \n  getUserByUsername: (username: string) => \n    apiClient.get<User>(`/users/by-username/${username}`),\n  \n  createUser: (data: CreateUserRequest) => \n    apiClient.post<User>('/users', data),\n  \n  updateUser: (id: number, data: UpdateUserRequest) => \n    apiClient.put<User>(`/users/${id}`, data),\n  \n  deleteUser: (id: number) => \n    apiClient.delete(`/users/${id}`),\n  \n  activateUser: (id: number) => \n    apiClient.post(`/users/${id}/activate`),\n  \n  deactivateUser: (id: number) => \n    apiClient.post(`/users/${id}/deactivate`),\n  \n  getUsersByRole: (role: string) => \n    apiClient.get<User[]>(`/users/by-role/${role}`),\n};\n\nexport const playersApi = {\n  getPlayers: (page: number = 1, pageSize: number = 20) => \n    apiClient.get<Player[]>(`/players?page=${page}&pageSize=${pageSize}`),\n  \n  getPlayer: (id: number) => \n    apiClient.get<Player>(`/players/${id}`),\n  \n  getPlayerByAccountId: (accountId: string) => \n    apiClient.get<Player>(`/players/by-account/${accountId}`),\n  \n  searchPlayers: (searchTerm: string) => \n    apiClient.get<Player[]>(`/players/search?searchTerm=${encodeURIComponent(searchTerm)}`),\n  \n  getPlayerStats: () => \n    apiClient.get<PlayerStats>('/players/stats'),\n  \n  getTopPlayersByLevel: (count: number = 10) => \n    apiClient.get<Player[]>(`/players/top-by-level?count=${count}`),\n  \n  getVipPlayers: (minVipLevel: number = 1) => \n    apiClient.get<Player[]>(`/players/vip?minVipLevel=${minVipLevel}`),\n  \n  updatePlayer: (id: number, data: any) => \n    apiClient.put(`/players/${id}`, data),\n  \n  banPlayer: (id: number, bannedUntil?: string, reason?: string) => \n    apiClient.post(`/players/${id}/ban`, { bannedUntil, reason }),\n  \n  unbanPlayer: (id: number) => \n    apiClient.post(`/players/${id}/unban`),\n};\n\n// 运营数据相关接口\nexport interface GlobalStats {\n  totalRevenue: number;\n  totalVisits: number;\n  totalRegistrations: number;\n  totalLogins: number;\n  todayNewVisits: number;\n  todayNewRegistrations: number;\n  todayNewLogins: number;\n  todayNewPayments: number;\n  todayActiveUsers: number;\n  arpu: number;\n  averageOnlineUsers: number;\n  maxOnlineUsers: number;\n  lastUpdated: string;\n}\n\nexport interface UserInfoStats {\n  totalVisits: number;\n  uniqueVisits: number;\n  totalRegistrations: number;\n  registrationsWithChannel: number;\n  totalLogins: number;\n  sameIpLogins: number;\n  currentOnlineUsers: number;\n  usersWithoutCharacter: number;\n  usersNeverLoggedIn: number;\n  registrationsByChannel: Record<string, number>;\n  visitsByHour: Record<string, number>;\n}\n\nexport interface PaymentInfoStats {\n  totalRevenue: number;\n  totalOrders: number;\n  pendingOrders: number;\n  completedOrders: number;\n  failedOrders: number;\n  averageOrderValue: number;\n  revenueByMethod: Record<string, number>;\n  ordersByStatus: Record<string, number>;\n  topPayers: TopPayer[];\n}\n\nexport interface TopPayer {\n  accountId: string;\n  nickname: string;\n  totalAmount: number;\n  orderCount: number;\n  lastPaymentTime: string;\n}\n\nexport interface ConversionAnalysis {\n  visitToRegistrationRate: number;\n  registrationToPaymentRate: number;\n  registrationToCharacterCreationRate: number;\n  characterCreationToFirstLoginRate: number;\n  analysisDate: string;\n  conversionByChannel: Record<string, number>;\n}\n\nexport interface RetentionAnalysis {\n  nextDayRetentionRate: number;\n  sevenDayRetentionRate: number;\n  monthlyRetentionRate: number;\n  analysisDate: string;\n  retentionByChannel: Record<string, number>;\n  retentionByLevel: Record<string, number>;\n}\n\nexport interface ActiveUserAnalysis {\n  dau: number;\n  wau: number;\n  mau: number;\n  averageSessionTime: number;\n  activeUsersByHour: Record<string, number>;\n  activeUsersByServer: Record<string, number>;\n  analysisDate: string;\n}\n\n// 运营数据API方法\nexport const operationalDataApi = {\n  // 全局统计\n  getGlobalStats: () =>\n    apiClient.get<GlobalStats>('/operational-data/global-stats'),\n\n  getGlobalStatsByDate: (date: string) =>\n    apiClient.get<GlobalStats>(`/operational-data/global-stats/${date}`),\n\n  // 用户信息统计\n  getUserInfoStats: () =>\n    apiClient.get<UserInfoStats>('/operational-data/user-info-stats'),\n\n  getUserInfoStatsByDateRange: (startDate: string, endDate: string) =>\n    apiClient.get<UserInfoStats>(`/operational-data/user-info-stats/${startDate}/${endDate}`),\n\n  // 付费信息统计\n  getPaymentInfoStats: () =>\n    apiClient.get<PaymentInfoStats>('/operational-data/payment-info-stats'),\n\n  getPaymentInfoStatsByDateRange: (startDate: string, endDate: string) =>\n    apiClient.get<PaymentInfoStats>(`/operational-data/payment-info-stats/${startDate}/${endDate}`),\n\n  // 数据分析\n  getConversionAnalysis: (date: string) =>\n    apiClient.get<ConversionAnalysis>(`/operational-data/conversion-analysis/${date}`),\n\n  getRetentionAnalysis: (date: string) =>\n    apiClient.get<RetentionAnalysis>(`/operational-data/retention-analysis/${date}`),\n\n  getActiveUserAnalysis: (date: string) =>\n    apiClient.get<ActiveUserAnalysis>(`/operational-data/active-user-analysis/${date}`),\n\n  // 记录数据\n  recordVisit: (data: { referrer?: string; channel?: string; serverId?: number }) =>\n    apiClient.post('/operational-data/record-visit', data),\n};\n\nexport default apiClient;\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA,WAAW;AACX,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAExD,cAAc;AACd,MAAM,YAA2B,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5C,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,iBAAiB;AACjB,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,iBAAiB;AACjB,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC;IACC,OAAO;AACT,GACA,OAAO;IACL,MAAM,kBAAkB,MAAM,MAAM;IAEpC,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;QAC7D,gBAAgB,MAAM,GAAG;QAEzB,IAAI;YACF,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,cAAc;gBAChB,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,aAAa,aAAa,CAAC,EAAE;gBAClE,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI;gBAE/B,aAAa,OAAO,CAAC,eAAe;gBACpC,gBAAgB,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;gBAEzD,OAAO,UAAU;YACnB;QACF,EAAE,OAAO,cAAc;YACrB,wBAAwB;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;AA0EK,MAAM,UAAU;IACrB,OAAO,CAAC,OACN,UAAU,IAAI,CAAgB,eAAe;IAE/C,QAAQ,CAAC,eACP,UAAU,IAAI,CAAC,gBAAgB;IAEjC,UAAU,CAAC,OACT,UAAU,IAAI,CAAO,kBAAkB;IAEzC,cAAc,CAAC,eACb,UAAU,IAAI,CAAgB,iBAAiB;IAEjD,gBAAgB,CAAC,iBAAyB,cACxC,UAAU,IAAI,CAAC,yBAAyB;YAAE;YAAiB;QAAY;IAEzE,eAAe,CAAC,QACd,UAAU,IAAI,CAAC,wBAAwB;YAAE;QAAM;AACnD;AAEO,MAAM,WAAW;IACtB,UAAU,IACR,UAAU,GAAG,CAAS;IAExB,SAAS,CAAC,KACR,UAAU,GAAG,CAAO,CAAC,OAAO,EAAE,IAAI;IAEpC,mBAAmB,CAAC,WAClB,UAAU,GAAG,CAAO,CAAC,mBAAmB,EAAE,UAAU;IAEtD,YAAY,CAAC,OACX,UAAU,IAAI,CAAO,UAAU;IAEjC,YAAY,CAAC,IAAY,OACvB,UAAU,GAAG,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;IAEtC,YAAY,CAAC,KACX,UAAU,MAAM,CAAC,CAAC,OAAO,EAAE,IAAI;IAEjC,cAAc,CAAC,KACb,UAAU,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;IAExC,gBAAgB,CAAC,KACf,UAAU,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,WAAW,CAAC;IAE1C,gBAAgB,CAAC,OACf,UAAU,GAAG,CAAS,CAAC,eAAe,EAAE,MAAM;AAClD;AAEO,MAAM,aAAa;IACxB,YAAY,CAAC,OAAe,CAAC,EAAE,WAAmB,EAAE,GAClD,UAAU,GAAG,CAAW,CAAC,cAAc,EAAE,KAAK,UAAU,EAAE,UAAU;IAEtE,WAAW,CAAC,KACV,UAAU,GAAG,CAAS,CAAC,SAAS,EAAE,IAAI;IAExC,sBAAsB,CAAC,YACrB,UAAU,GAAG,CAAS,CAAC,oBAAoB,EAAE,WAAW;IAE1D,eAAe,CAAC,aACd,UAAU,GAAG,CAAW,CAAC,2BAA2B,EAAE,mBAAmB,aAAa;IAExF,gBAAgB,IACd,UAAU,GAAG,CAAc;IAE7B,sBAAsB,CAAC,QAAgB,EAAE,GACvC,UAAU,GAAG,CAAW,CAAC,4BAA4B,EAAE,OAAO;IAEhE,eAAe,CAAC,cAAsB,CAAC,GACrC,UAAU,GAAG,CAAW,CAAC,yBAAyB,EAAE,aAAa;IAEnE,cAAc,CAAC,IAAY,OACzB,UAAU,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE;IAElC,WAAW,CAAC,IAAY,aAAsB,SAC5C,UAAU,IAAI,CAAC,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;YAAE;YAAa;QAAO;IAE7D,aAAa,CAAC,KACZ,UAAU,IAAI,CAAC,CAAC,SAAS,EAAE,GAAG,MAAM,CAAC;AACzC;AAkFO,MAAM,qBAAqB;IAChC,OAAO;IACP,gBAAgB,IACd,UAAU,GAAG,CAAc;IAE7B,sBAAsB,CAAC,OACrB,UAAU,GAAG,CAAc,CAAC,+BAA+B,EAAE,MAAM;IAErE,SAAS;IACT,kBAAkB,IAChB,UAAU,GAAG,CAAgB;IAE/B,6BAA6B,CAAC,WAAmB,UAC/C,UAAU,GAAG,CAAgB,CAAC,kCAAkC,EAAE,UAAU,CAAC,EAAE,SAAS;IAE1F,SAAS;IACT,qBAAqB,IACnB,UAAU,GAAG,CAAmB;IAElC,gCAAgC,CAAC,WAAmB,UAClD,UAAU,GAAG,CAAmB,CAAC,qCAAqC,EAAE,UAAU,CAAC,EAAE,SAAS;IAEhG,OAAO;IACP,uBAAuB,CAAC,OACtB,UAAU,GAAG,CAAqB,CAAC,sCAAsC,EAAE,MAAM;IAEnF,sBAAsB,CAAC,OACrB,UAAU,GAAG,CAAoB,CAAC,qCAAqC,EAAE,MAAM;IAEjF,uBAAuB,CAAC,OACtB,UAAU,GAAG,CAAqB,CAAC,uCAAuC,EAAE,MAAM;IAEpF,OAAO;IACP,aAAa,CAAC,OACZ,UAAU,IAAI,CAAC,kCAAkC;AACrD;uCAEe", "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\nimport { authApi, User } from '@/lib/api';\nimport { message } from 'antd';\n\ninterface AuthContextType {\n  user: User | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  login: (username: string, password: string) => Promise<boolean>;\n  logout: () => void;\n  hasRole: (role: string) => boolean;\n  hasAnyRole: (roles: string[]) => boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    // 检查本地存储中的用户信息\n    if (typeof window !== 'undefined') {\n      const storedUser = localStorage.getItem('user');\n      const accessToken = localStorage.getItem('accessToken');\n\n      if (storedUser && accessToken) {\n        try {\n          const parsedUser = JSON.parse(storedUser);\n          setUser(parsedUser);\n        } catch (error) {\n          console.error('Error parsing stored user:', error);\n          localStorage.removeItem('user');\n          localStorage.removeItem('accessToken');\n          localStorage.removeItem('refreshToken');\n        }\n      }\n    }\n\n    setIsLoading(false);\n  }, []);\n\n  const login = async (username: string, password: string): Promise<boolean> => {\n    try {\n      setIsLoading(true);\n      const response = await authApi.login({ username, password });\n      const { token, user: userData } = response.data;\n\n      // 存储认证信息\n      if (typeof window !== 'undefined') {\n        localStorage.setItem('accessToken', token);\n        localStorage.setItem('user', JSON.stringify(userData));\n\n        // 如果响应中有刷新令牌，也存储它\n        if (response.data.refreshToken) {\n          localStorage.setItem('refreshToken', response.data.refreshToken);\n        }\n      }\n\n      setUser(userData);\n      message.success('登录成功');\n      return true;\n    } catch (error: any) {\n      console.error('Login error:', error);\n      const errorMessage = error.response?.data?.message || '登录失败，请检查用户名和密码';\n      message.error(errorMessage);\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    try {\n      if (typeof window !== 'undefined') {\n        const refreshToken = localStorage.getItem('refreshToken');\n        if (refreshToken) {\n          await authApi.logout(refreshToken);\n        }\n      }\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // 清除本地存储\n      if (typeof window !== 'undefined') {\n        localStorage.removeItem('accessToken');\n        localStorage.removeItem('refreshToken');\n        localStorage.removeItem('user');\n      }\n      \n      setUser(null);\n      message.success('已退出登录');\n      \n      // 重定向到登录页\n      window.location.href = '/login';\n    }\n  };\n\n  const hasRole = (role: string): boolean => {\n    return user?.roles?.includes(role) || false;\n  };\n\n  const hasAnyRole = (roles: string[]): boolean => {\n    return roles.some(role => hasRole(role));\n  };\n\n  const isAuthenticated = !!user;\n\n  const value: AuthContextType = {\n    user,\n    isLoading,\n    isAuthenticated,\n    login,\n    logout,\n    hasRole,\n    hasAnyRole,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\n// 角色常量\nexport const ROLES = {\n  SYSTEM_ADMIN: 'SystemAdmin',\n  PRODUCT_MANAGER: 'ProductManager',\n  PRODUCT_SPECIALIST: 'ProductSpecialist',\n  PARTNER_MANAGER: 'PartnerManager',\n  PARTNER_SPECIALIST: 'PartnerSpecialist',\n  CUSTOMER_SERVICE_MANAGER: 'CustomerServiceManager',\n  CUSTOMER_SERVICE_SPECIALIST: 'CustomerServiceSpecialist',\n  VIEWER: 'Viewer',\n} as const;\n\n// 权限检查工具函数\nexport const checkPermission = (userRoles: string[], requiredRoles: string[]): boolean => {\n  return requiredRoles.some(role => userRoles.includes(role));\n};\n\n// 管理员角色检查\nexport const isAdmin = (userRoles: string[]): boolean => {\n  return checkPermission(userRoles, [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER]);\n};\n\n// 客服权限检查\nexport const hasCustomerServicePermission = (userRoles: string[]): boolean => {\n  return checkPermission(userRoles, [\n    ROLES.SYSTEM_ADMIN,\n    ROLES.PRODUCT_MANAGER,\n    ROLES.CUSTOMER_SERVICE_MANAGER,\n    ROLES.CUSTOMER_SERVICE_SPECIALIST,\n  ]);\n};\n\n// 产品管理权限检查\nexport const hasProductPermission = (userRoles: string[]): boolean => {\n  return checkPermission(userRoles, [\n    ROLES.SYSTEM_ADMIN,\n    ROLES.PRODUCT_MANAGER,\n    ROLES.PRODUCT_SPECIALIST,\n  ]);\n};\n\n// 渠道管理权限检查\nexport const hasPartnerPermission = (userRoles: string[]): boolean => {\n  return checkPermission(userRoles, [\n    ROLES.SYSTEM_ADMIN,\n    ROLES.PRODUCT_MANAGER,\n    ROLES.PARTNER_MANAGER,\n    ROLES.PARTNER_SPECIALIST,\n  ]);\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAgBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAMxD,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;QACf,uCAAmC;;QAenC;QAEA,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,UAAkB;QACrC,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBAAE;gBAAU;YAAS;YAC1D,MAAM,EAAE,KAAK,EAAE,MAAM,QAAQ,EAAE,GAAG,SAAS,IAAI;YAE/C,SAAS;YACT,uCAAmC;;YAQnC;YAEA,QAAQ;YACR,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW;YACtD,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YACd,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,uCAAmC;;YAKnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,SAAS;YACT,uCAAmC;;YAInC;YAEA,QAAQ;YACR,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAEhB,UAAU;YACV,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,MAAM,UAAU,CAAC;QACf,OAAO,MAAM,OAAO,SAAS,SAAS;IACxC;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,QAAQ;IACpC;IAEA,MAAM,kBAAkB,CAAC,CAAC;IAE1B,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,MAAM,QAAQ;IACnB,cAAc;IACd,iBAAiB;IACjB,oBAAoB;IACpB,iBAAiB;IACjB,oBAAoB;IACpB,0BAA0B;IAC1B,6BAA6B;IAC7B,QAAQ;AACV;AAGO,MAAM,kBAAkB,CAAC,WAAqB;IACnD,OAAO,cAAc,IAAI,CAAC,CAAA,OAAQ,UAAU,QAAQ,CAAC;AACvD;AAGO,MAAM,UAAU,CAAC;IACtB,OAAO,gBAAgB,WAAW;QAAC,MAAM,YAAY;QAAE,MAAM,eAAe;KAAC;AAC/E;AAGO,MAAM,+BAA+B,CAAC;IAC3C,OAAO,gBAAgB,WAAW;QAChC,MAAM,YAAY;QAClB,MAAM,eAAe;QACrB,MAAM,wBAAwB;QAC9B,MAAM,2BAA2B;KAClC;AACH;AAGO,MAAM,uBAAuB,CAAC;IACnC,OAAO,gBAAgB,WAAW;QAChC,MAAM,YAAY;QAClB,MAAM,eAAe;QACrB,MAAM,kBAAkB;KACzB;AACH;AAGO,MAAM,uBAAuB,CAAC;IACnC,OAAO,gBAAgB,WAAW;QAChC,MAAM,YAAY;QAClB,MAAM,eAAe;QACrB,MAAM,eAAe;QACrB,MAAM,kBAAkB;KACzB;AACH", "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/layout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools';\nimport { AuthProvider } from '@/contexts/AuthContext';\nimport \"./globals.css\";\n\n// 创建 QueryClient 实例\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      staleTime: 5 * 60 * 1000, // 5 minutes\n      retry: 1,\n    },\n  },\n});\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"zh-CN\">\n      <head>\n        <title>游戏管理系统</title>\n        <meta name=\"description\" content=\"游戏运营管理后台系统\" />\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\n      </head>\n      <body>\n        <QueryClientProvider client={queryClient}>\n          <ConfigProvider locale={zhCN}>\n            <AuthProvider>\n              {children}\n            </AuthProvider>\n          </ConfigProvider>\n          <ReactQueryDevtools initialIsOpen={false} />\n        </QueryClientProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AACA;AACA;AAPA;;;;;;;;AAUA,oBAAoB;AACpB,MAAM,cAAc,IAAI,6KAAA,CAAA,cAAW,CAAC;IAClC,gBAAgB;QACd,SAAS;YACP,WAAW,IAAI,KAAK;YACpB,OAAO;QACT;IACF;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;;kCACC,8OAAC;kCAAM;;;;;;kCACP,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;;;;;;;0BAEhC,8OAAC;0BACC,cAAA,8OAAC,sLAAA,CAAA,sBAAmB;oBAAC,QAAQ;;sCAC3B,8OAAC,sNAAA,CAAA,iBAAc;4BAAC,QAAQ,uIAAA,CAAA,UAAI;sCAC1B,cAAA,8OAAC,+HAAA,CAAA,eAAY;0CACV;;;;;;;;;;;sCAGL,8OAAC,oLAAA,CAAA,qBAAkB;4BAAC,eAAe;;;;;;;;;;;;;;;;;;;;;;;AAK7C", "debugId": null}}]}