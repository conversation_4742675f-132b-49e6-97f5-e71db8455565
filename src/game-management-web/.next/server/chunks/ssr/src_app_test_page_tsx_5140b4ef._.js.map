{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/test/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Card, Typography, Space, Button } from 'antd';\nimport { useAuth } from '@/contexts/AuthContext';\n\nconst { Title, Text } = Typography;\n\nconst TestPage: React.FC = () => {\n  const { user, isAuthenticated, login, logout } = useAuth();\n\n  const handleTestLogin = async () => {\n    await login('admin', 'password123');\n  };\n\n  return (\n    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>\n      <Title level={2}>测试页面</Title>\n      \n      <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n        <Card title=\"认证状态\">\n          <Space direction=\"vertical\">\n            <Text>认证状态: {isAuthenticated ? '已登录' : '未登录'}</Text>\n            {user && (\n              <>\n                <Text>用户名: {user.username}</Text>\n                <Text>显示名: {user.displayName}</Text>\n                <Text>角色: {user.roles?.join(', ')}</Text>\n              </>\n            )}\n            <Space>\n              <Button type=\"primary\" onClick={handleTestLogin}>\n                测试登录\n              </Button>\n              <Button onClick={logout}>\n                退出登录\n              </Button>\n            </Space>\n          </Space>\n        </Card>\n\n        <Card title=\"API测试\">\n          <Text>这里可以测试各种API调用</Text>\n        </Card>\n\n        <Card title=\"组件测试\">\n          <Text>这里可以测试各种UI组件</Text>\n        </Card>\n      </Space>\n    </div>\n  );\n};\n\nexport default TestPage;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AACA;AAJA;;;;AAMA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAElC,MAAM,WAAqB;IACzB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEvD,MAAM,kBAAkB;QACtB,MAAM,MAAM,SAAS;IACvB;IAEA,qBACE,8OAAC;QAAI,OAAO;YAAE,SAAS;YAAQ,UAAU;YAAS,QAAQ;QAAS;;0BACjE,8OAAC;gBAAM,OAAO;0BAAG;;;;;;0BAEjB,8OAAC,gMAAA,CAAA,QAAK;gBAAC,WAAU;gBAAW,MAAK;gBAAQ,OAAO;oBAAE,OAAO;gBAAO;;kCAC9D,8OAAC,8KAAA,CAAA,OAAI;wBAAC,OAAM;kCACV,cAAA,8OAAC,gMAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,8OAAC;;wCAAK;wCAAO,kBAAkB,QAAQ;;;;;;;gCACtC,sBACC;;sDACE,8OAAC;;gDAAK;gDAAM,KAAK,QAAQ;;;;;;;sDACzB,8OAAC;;gDAAK;gDAAM,KAAK,WAAW;;;;;;;sDAC5B,8OAAC;;gDAAK;gDAAK,KAAK,KAAK,EAAE,KAAK;;;;;;;;;8CAGhC,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC,kMAAA,CAAA,SAAM;4CAAC,MAAK;4CAAU,SAAS;sDAAiB;;;;;;sDAGjD,8OAAC,kMAAA,CAAA,SAAM;4CAAC,SAAS;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;kCAO/B,8OAAC,8KAAA,CAAA,OAAI;wBAAC,OAAM;kCACV,cAAA,8OAAC;sCAAK;;;;;;;;;;;kCAGR,8OAAC,8KAAA,CAAA,OAAI;wBAAC,OAAM;kCACV,cAAA,8OAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAKhB;uCAEe", "debugId": null}}]}