{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/test/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Card, Typography, Space, Button } from 'antd';\nimport { useAuth } from '@/contexts/AuthContext';\n\nconst { Title, Text } = Typography;\n\nconst TestPage: React.FC = () => {\n  const { user, isAuthenticated, login, logout } = useAuth();\n\n  const handleTestLogin = async () => {\n    await login('111', '111111');\n  };\n\n  return (\n    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>\n      <Title level={2}>测试页面</Title>\n      \n      <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n        <Card title=\"认证状态\">\n          <Space direction=\"vertical\">\n            <Text>认证状态: {isAuthenticated ? '已登录' : '未登录'}</Text>\n            {user && (\n              <>\n                <Text>用户名: {user.username}</Text>\n                <Text>显示名: {user.displayName}</Text>\n                <Text>角色: {user.roles?.join(', ')}</Text>\n              </>\n            )}\n            <Space>\n              <Button type=\"primary\" onClick={handleTestLogin}>\n                测试登录\n              </Button>\n              <Button onClick={logout}>\n                退出登录\n              </Button>\n            </Space>\n          </Space>\n        </Card>\n\n        <Card title=\"API测试\">\n          <Text>这里可以测试各种API调用</Text>\n        </Card>\n\n        <Card title=\"组件测试\">\n          <Text>这里可以测试各种UI组件</Text>\n        </Card>\n      </Space>\n    </div>\n  );\n};\n\nexport default TestPage;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;AAMA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAElC,MAAM,WAAqB;;IACzB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvD,MAAM,kBAAkB;QACtB,MAAM,MAAM,OAAO;IACrB;IAEA,qBACE,6LAAC;QAAI,OAAO;YAAE,SAAS;YAAQ,UAAU;YAAS,QAAQ;QAAS;;0BACjE,6LAAC;gBAAM,OAAO;0BAAG;;;;;;0BAEjB,6LAAC,mMAAA,CAAA,QAAK;gBAAC,WAAU;gBAAW,MAAK;gBAAQ,OAAO;oBAAE,OAAO;gBAAO;;kCAC9D,6LAAC,iLAAA,CAAA,OAAI;wBAAC,OAAM;kCACV,cAAA,6LAAC,mMAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,6LAAC;;wCAAK;wCAAO,kBAAkB,QAAQ;;;;;;;gCACtC,sBACC;;sDACE,6LAAC;;gDAAK;gDAAM,KAAK,QAAQ;;;;;;;sDACzB,6LAAC;;gDAAK;gDAAM,KAAK,WAAW;;;;;;;sDAC5B,6LAAC;;gDAAK;gDAAK,KAAK,KAAK,EAAE,KAAK;;;;;;;;;8CAGhC,6LAAC,mMAAA,CAAA,QAAK;;sDACJ,6LAAC,qMAAA,CAAA,SAAM;4CAAC,MAAK;4CAAU,SAAS;sDAAiB;;;;;;sDAGjD,6LAAC,qMAAA,CAAA,SAAM;4CAAC,SAAS;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;kCAO/B,6LAAC,iLAAA,CAAA,OAAI;wBAAC,OAAM;kCACV,cAAA,6LAAC;sCAAK;;;;;;;;;;;kCAGR,6LAAC,iLAAA,CAAA,OAAI;wBAAC,OAAM;kCACV,cAAA,6LAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAKhB;GA3CM;;QAC6C,kIAAA,CAAA,UAAO;;;KADpD;uCA6CS", "debugId": null}}]}