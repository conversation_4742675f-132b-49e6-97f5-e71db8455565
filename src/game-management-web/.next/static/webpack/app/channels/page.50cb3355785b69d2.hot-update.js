"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/channels/page",{

/***/ "(app-pages-browser)/./src/app/channels/page.tsx":
/*!***********************************!*\
  !*** ./src/app/channels/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Auth/ProtectedRoute */ \"(app-pages-browser)/./src/components/Auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst ChannelsPage = ()=>{\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchText, setSearchText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [channelStatus, setChannelStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    // 模拟渠道数据\n    const channelsData = [\n        {\n            key: \"1\",\n            id: 8001,\n            name: \"App Store\",\n            code: \"APP_STORE\",\n            type: \"应用商店\",\n            status: \"active\",\n            description: \"苹果应用商店官方渠道\",\n            registrations: 12560,\n            activeUsers: 8945,\n            revenue: 256800,\n            conversionRate: 15.8,\n            cpa: 25.5,\n            ltv: 180.2,\n            isActive: true,\n            createdAt: \"2024-01-15 10:00:00\",\n            updatedAt: \"2024-06-25 14:30:00\",\n            contactPerson: \"张经理\",\n            contactPhone: \"138****1234\"\n        },\n        {\n            key: \"2\",\n            id: 8002,\n            name: \"Google Play\",\n            code: \"GOOGLE_PLAY\",\n            type: \"应用商店\",\n            status: \"active\",\n            description: \"Google Play商店官方渠道\",\n            registrations: 18920,\n            activeUsers: 13456,\n            revenue: 389600,\n            conversionRate: 18.2,\n            cpa: 22.8,\n            ltv: 195.6,\n            isActive: true,\n            createdAt: \"2024-01-20 14:30:00\",\n            updatedAt: \"2024-06-25 13:45:00\",\n            contactPerson: \"李经理\",\n            contactPhone: \"139****5678\"\n        },\n        {\n            key: \"3\",\n            id: 8003,\n            name: \"抖音广告\",\n            code: \"DOUYIN_ADS\",\n            type: \"信息流广告\",\n            status: \"active\",\n            description: \"抖音平台信息流广告投放\",\n            registrations: 25680,\n            activeUsers: 15234,\n            revenue: 456700,\n            conversionRate: 12.5,\n            cpa: 35.2,\n            ltv: 165.8,\n            isActive: true,\n            createdAt: \"2024-02-10 09:15:00\",\n            updatedAt: \"2024-06-25 12:20:00\",\n            contactPerson: \"王经理\",\n            contactPhone: \"137****9012\"\n        },\n        {\n            key: \"4\",\n            id: 8004,\n            name: \"微信小游戏\",\n            code: \"WECHAT_GAME\",\n            type: \"小程序\",\n            status: \"paused\",\n            description: \"微信小游戏平台合作渠道\",\n            registrations: 8945,\n            activeUsers: 4567,\n            revenue: 123400,\n            conversionRate: 8.9,\n            cpa: 45.6,\n            ltv: 142.3,\n            isActive: false,\n            createdAt: \"2024-03-05 16:20:00\",\n            updatedAt: \"2024-06-20 10:30:00\",\n            contactPerson: \"赵经理\",\n            contactPhone: \"136****3456\"\n        }\n    ];\n    const getStatusTag = (status)=>{\n        switch(status){\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"success\",\n                    children: \"活跃\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 16\n                }, undefined);\n            case \"paused\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"warning\",\n                    children: \"暂停\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 16\n                }, undefined);\n            case \"inactive\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"default\",\n                    children: \"停用\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"default\",\n                    children: \"未知\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getTypeTag = (type)=>{\n        const typeColors = {\n            \"应用商店\": \"blue\",\n            \"信息流广告\": \"orange\",\n            \"小程序\": \"green\",\n            \"联运平台\": \"purple\",\n            \"自有渠道\": \"cyan\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n            color: typeColors[type] || \"default\",\n            children: type\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n            lineNumber: 134,\n            columnNumber: 12\n        }, undefined);\n    };\n    const columns = [\n        {\n            title: \"ID\",\n            dataIndex: \"id\",\n            key: \"id\",\n            width: 80\n        },\n        {\n            title: \"渠道名称\",\n            key: \"name\",\n            width: 150,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontWeight: 500,\n                                marginBottom: 4\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LinkOutlined, {\n                                    style: {\n                                        marginRight: 8,\n                                        color: \"#1890ff\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, undefined),\n                                record.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"12px\",\n                                color: \"#666\"\n                            },\n                            children: record.code\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"渠道类型\",\n            key: \"type\",\n            width: 120,\n            render: (record)=>getTypeTag(record.type)\n        },\n        {\n            title: \"状态\",\n            key: \"status\",\n            width: 100,\n            render: (record)=>getStatusTag(record.status)\n        },\n        {\n            title: \"注册用户\",\n            dataIndex: \"registrations\",\n            key: \"registrations\",\n            width: 120,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        color: \"#1890ff\",\n                        fontWeight: 500\n                    },\n                    children: value.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"活跃用户\",\n            dataIndex: \"activeUsers\",\n            key: \"activeUsers\",\n            width: 120,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        color: \"#52c41a\",\n                        fontWeight: 500\n                    },\n                    children: value.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"收入 (元)\",\n            dataIndex: \"revenue\",\n            key: \"revenue\",\n            width: 120,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        color: \"#fa8c16\",\n                        fontWeight: 500\n                    },\n                    children: [\n                        \"\\xa5\",\n                        value.toLocaleString()\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"转化率\",\n            key: \"conversionRate\",\n            width: 100,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"12px\",\n                                marginBottom: 4\n                            },\n                            children: [\n                                record.conversionRate,\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_8__.Progress, {\n                            percent: record.conversionRate,\n                            size: \"small\",\n                            showInfo: false,\n                            strokeColor: record.conversionRate > 15 ? \"#52c41a\" : record.conversionRate > 10 ? \"#faad14\" : \"#ff4d4f\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"CPA (元)\",\n            dataIndex: \"cpa\",\n            key: \"cpa\",\n            width: 100,\n            render: (value)=>\"\\xa5\".concat(value)\n        },\n        {\n            title: \"LTV (元)\",\n            dataIndex: \"ltv\",\n            key: \"ltv\",\n            width: 100,\n            render: (value)=>\"\\xa5\".concat(value)\n        },\n        {\n            title: \"联系人\",\n            key: \"contact\",\n            width: 120,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"12px\",\n                                fontWeight: 500\n                            },\n                            children: record.contactPerson\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"11px\",\n                                color: \"#666\"\n                            },\n                            children: record.contactPhone\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"操作\",\n            key: \"action\",\n            width: 200,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Space, {\n                    size: \"small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EyeOutlined, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>handleViewDetail(record),\n                            children: \"详情\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BarChartOutlined, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>handleViewStats(record),\n                            children: \"数据\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditOutlined, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>handleEdit(record),\n                            children: \"编辑\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"link\",\n                            size: \"small\",\n                            danger: true,\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeleteOutlined, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>handleDelete(record),\n                            children: \"删除\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ];\n    const handleViewDetail = (record)=>{\n        Modal.info({\n            title: \"渠道详情 - \".concat(record.name),\n            width: 600,\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: 16\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Space, {\n                            children: [\n                                getTypeTag(record.type),\n                                getStatusTag(record.status)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: 16\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                            gutter: 16,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                    span: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"渠道代码：\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" \",\n                                        record.code\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                    span: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"创建时间：\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" \",\n                                        record.createdAt\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: 16\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"渠道描述：\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, undefined),\n                            \" \",\n                            record.description\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: 16\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                            gutter: 16,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                    span: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"联系人：\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" \",\n                                        record.contactPerson\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                    span: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"联系电话：\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" \",\n                                        record.contactPhone\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: 16\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"关键指标：\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                                gutter: 16,\n                                style: {\n                                    marginTop: 8\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                            title: \"注册用户\",\n                                            value: record.registrations\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                            title: \"活跃用户\",\n                                            value: record.activeUsers\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                            title: \"收入\",\n                                            value: record.revenue,\n                                            prefix: \"\\xa5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                            title: \"转化率\",\n                                            value: record.conversionRate,\n                                            suffix: \"%\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                lineNumber: 295,\n                columnNumber: 9\n            }, undefined)\n        });\n    };\n    const handleViewStats = (record)=>{\n        message.info(\"查看 \".concat(record.name, \" 的详细数据统计\"));\n    };\n    const handleEdit = (record)=>{\n        setEditingChannel(record);\n        form.setFieldsValue(record);\n        setModalVisible(true);\n    };\n    const handleAdd = ()=>{\n        setEditingChannel(null);\n        form.resetFields();\n        setModalVisible(true);\n    };\n    const handleDelete = (record)=>{\n        Modal.confirm({\n            title: \"确认删除\",\n            content: '确定要删除渠道 \"'.concat(record.name, '\" 吗？此操作不可恢复。'),\n            onOk () {\n                message.success(\"已删除渠道: \".concat(record.name));\n            }\n        });\n    };\n    const handleModalOk = ()=>{\n        form.validateFields().then((values)=>{\n            console.log(\"Form values:\", values);\n            message.success(editingChannel ? \"渠道更新成功\" : \"渠道创建成功\");\n            setModalVisible(false);\n        }).catch((info)=>{\n            console.log(\"Validate Failed:\", info);\n        });\n    };\n    const handleSearch = (value)=>{\n        setSearchText(value);\n    };\n    // 计算统计数据\n    const totalRegistrations = channelsData.reduce((sum, item)=>sum + item.registrations, 0);\n    const totalActiveUsers = channelsData.reduce((sum, item)=>sum + item.activeUsers, 0);\n    const totalRevenue = channelsData.reduce((sum, item)=>sum + item.revenue, 0);\n    const avgConversionRate = (channelsData.reduce((sum, item)=>sum + item.conversionRate, 0) / channelsData.length).toFixed(1);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        requiredRoles: [\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_9__.ROLES.SYSTEM_ADMIN,\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_9__.ROLES.PRODUCT_MANAGER\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        justifyContent: \"space-between\",\n                        alignItems: \"center\",\n                        marginBottom: 24\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                            level: 2,\n                            children: \"渠道管理\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"primary\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusOutlined, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 40\n                            }, void 0),\n                            onClick: handleAdd,\n                            children: \"新建渠道\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                    gutter: [\n                        16,\n                        16\n                    ],\n                    style: {\n                        marginBottom: 24\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                    title: \"总注册用户\",\n                                    value: totalRegistrations,\n                                    valueStyle: {\n                                        color: \"#1890ff\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                    title: \"总活跃用户\",\n                                    value: totalActiveUsers,\n                                    valueStyle: {\n                                        color: \"#52c41a\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                    title: \"总收入\",\n                                    value: totalRevenue,\n                                    prefix: \"\\xa5\",\n                                    valueStyle: {\n                                        color: \"#fa8c16\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                    title: \"平均转化率\",\n                                    value: parseFloat(avgConversionRate),\n                                    suffix: \"%\",\n                                    valueStyle: {\n                                        color: \"#722ed1\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    style: {\n                        marginBottom: 16\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                        gutter: [\n                            16,\n                            16\n                        ],\n                        align: \"middle\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                xs: 24,\n                                sm: 8,\n                                lg: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input.Search, {\n                                    placeholder: \"搜索渠道名称\",\n                                    allowClear: true,\n                                    onSearch: handleSearch,\n                                    style: {\n                                        width: \"100%\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                xs: 24,\n                                sm: 6,\n                                lg: 4,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                    placeholder: \"渠道状态\",\n                                    style: {\n                                        width: \"100%\"\n                                    },\n                                    value: channelStatus,\n                                    onChange: setChannelStatus,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"all\",\n                                            children: \"全部状态\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"active\",\n                                            children: \"活跃\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"paused\",\n                                            children: \"暂停\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"inactive\",\n                                            children: \"停用\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                xs: 24,\n                                sm: 6,\n                                lg: 4,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                    placeholder: \"渠道类型\",\n                                    style: {\n                                        width: \"100%\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"all\",\n                                            children: \"全部类型\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"应用商店\",\n                                            children: \"应用商店\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"信息流广告\",\n                                            children: \"信息流广告\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"小程序\",\n                                            children: \"小程序\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"联运平台\",\n                                            children: \"联运平台\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"自有渠道\",\n                                            children: \"自有渠道\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                xs: 24,\n                                sm: 6,\n                                lg: 4,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"primary\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SearchOutlined, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 44\n                                    }, void 0),\n                                    block: true,\n                                    children: \"搜索\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.Table, {\n                        columns: columns,\n                        dataSource: channelsData,\n                        loading: loading,\n                        pagination: {\n                            total: 67,\n                            pageSize: 20,\n                            showSizeChanger: true,\n                            showQuickJumper: true,\n                            showTotal: (total, range)=>\"第 \".concat(range[0], \"-\").concat(range[1], \" 条/共 \").concat(total, \" 条\")\n                        },\n                        scroll: {\n                            x: 1400\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 491,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Modal, {\n                    title: editingChannel ? \"编辑渠道\" : \"新建渠道\",\n                    open: modalVisible,\n                    onOk: handleModalOk,\n                    onCancel: ()=>setModalVisible(false),\n                    width: 600,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form, {\n                        form: form,\n                        layout: \"vertical\",\n                        initialValues: {\n                            status: \"active\",\n                            isActive: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                                gutter: 16,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                            name: \"name\",\n                                            label: \"渠道名称\",\n                                            rules: [\n                                                {\n                                                    required: true,\n                                                    message: \"请输入渠道名称\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"请输入渠道名称\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                            name: \"code\",\n                                            label: \"渠道代码\",\n                                            rules: [\n                                                {\n                                                    required: true,\n                                                    message: \"请输入渠道代码\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"请输入渠道代码\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 524,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                                gutter: 16,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                            name: \"type\",\n                                            label: \"渠道类型\",\n                                            rules: [\n                                                {\n                                                    required: true,\n                                                    message: \"请选择渠道类型\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                placeholder: \"请选择渠道类型\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"应用商店\",\n                                                        children: \"应用商店\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"信息流广告\",\n                                                        children: \"信息流广告\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"小程序\",\n                                                        children: \"小程序\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"联运平台\",\n                                                        children: \"联运平台\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"自有渠道\",\n                                                        children: \"自有渠道\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                            name: \"status\",\n                                            label: \"渠道状态\",\n                                            rules: [\n                                                {\n                                                    required: true,\n                                                    message: \"请选择渠道状态\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                placeholder: \"请选择渠道状态\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"active\",\n                                                        children: \"活跃\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"paused\",\n                                                        children: \"暂停\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"inactive\",\n                                                        children: \"停用\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                name: \"description\",\n                                label: \"渠道描述\",\n                                rules: [\n                                    {\n                                        required: true,\n                                        message: \"请输入渠道描述\"\n                                    }\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TextArea, {\n                                    rows: 3,\n                                    placeholder: \"请输入渠道描述\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                                gutter: 16,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                            name: \"contactPerson\",\n                                            label: \"联系人\",\n                                            rules: [\n                                                {\n                                                    required: true,\n                                                    message: \"请输入联系人\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"请输入联系人\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                            name: \"contactPhone\",\n                                            label: \"联系电话\",\n                                            rules: [\n                                                {\n                                                    required: true,\n                                                    message: \"请输入联系电话\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"请输入联系电话\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                name: \"isActive\",\n                                label: \"是否启用\",\n                                valuePropName: \"checked\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Switch, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n            lineNumber: 395,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n        lineNumber: 394,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChannelsPage, \"yi4aISkPZxwPtw0QwjbzHmL+ek8=\");\n_c = ChannelsPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ChannelsPage);\nvar _c;\n$RefreshReg$(_c, \"ChannelsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/channels/page.tsx\n"));

/***/ })

});