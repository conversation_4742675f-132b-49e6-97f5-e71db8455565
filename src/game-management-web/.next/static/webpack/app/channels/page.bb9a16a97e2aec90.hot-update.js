"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/channels/page",{

/***/ "(app-pages-browser)/./src/app/channels/page.tsx":
/*!***********************************!*\
  !*** ./src/app/channels/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Auth/ProtectedRoute */ \"(app-pages-browser)/./src/components/Auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst ChannelsPage = ()=>{\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchText, setSearchText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [channelStatus, setChannelStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [modalVisible, setModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingChannel, setEditingChannel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [form] = Form.useForm();\n    // 模拟渠道数据\n    const channelsData = [\n        {\n            key: \"1\",\n            id: 8001,\n            name: \"App Store\",\n            code: \"APP_STORE\",\n            type: \"应用商店\",\n            status: \"active\",\n            description: \"苹果应用商店官方渠道\",\n            registrations: 12560,\n            activeUsers: 8945,\n            revenue: 256800,\n            conversionRate: 15.8,\n            cpa: 25.5,\n            ltv: 180.2,\n            isActive: true,\n            createdAt: \"2024-01-15 10:00:00\",\n            updatedAt: \"2024-06-25 14:30:00\",\n            contactPerson: \"张经理\",\n            contactPhone: \"138****1234\"\n        },\n        {\n            key: \"2\",\n            id: 8002,\n            name: \"Google Play\",\n            code: \"GOOGLE_PLAY\",\n            type: \"应用商店\",\n            status: \"active\",\n            description: \"Google Play商店官方渠道\",\n            registrations: 18920,\n            activeUsers: 13456,\n            revenue: 389600,\n            conversionRate: 18.2,\n            cpa: 22.8,\n            ltv: 195.6,\n            isActive: true,\n            createdAt: \"2024-01-20 14:30:00\",\n            updatedAt: \"2024-06-25 13:45:00\",\n            contactPerson: \"李经理\",\n            contactPhone: \"139****5678\"\n        },\n        {\n            key: \"3\",\n            id: 8003,\n            name: \"抖音广告\",\n            code: \"DOUYIN_ADS\",\n            type: \"信息流广告\",\n            status: \"active\",\n            description: \"抖音平台信息流广告投放\",\n            registrations: 25680,\n            activeUsers: 15234,\n            revenue: 456700,\n            conversionRate: 12.5,\n            cpa: 35.2,\n            ltv: 165.8,\n            isActive: true,\n            createdAt: \"2024-02-10 09:15:00\",\n            updatedAt: \"2024-06-25 12:20:00\",\n            contactPerson: \"王经理\",\n            contactPhone: \"137****9012\"\n        },\n        {\n            key: \"4\",\n            id: 8004,\n            name: \"微信小游戏\",\n            code: \"WECHAT_GAME\",\n            type: \"小程序\",\n            status: \"paused\",\n            description: \"微信小游戏平台合作渠道\",\n            registrations: 8945,\n            activeUsers: 4567,\n            revenue: 123400,\n            conversionRate: 8.9,\n            cpa: 45.6,\n            ltv: 142.3,\n            isActive: false,\n            createdAt: \"2024-03-05 16:20:00\",\n            updatedAt: \"2024-06-20 10:30:00\",\n            contactPerson: \"赵经理\",\n            contactPhone: \"136****3456\"\n        }\n    ];\n    const getStatusTag = (status)=>{\n        switch(status){\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"success\",\n                    children: \"活跃\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 16\n                }, undefined);\n            case \"paused\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"warning\",\n                    children: \"暂停\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 16\n                }, undefined);\n            case \"inactive\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"default\",\n                    children: \"停用\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"default\",\n                    children: \"未知\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getTypeTag = (type)=>{\n        const typeColors = {\n            \"应用商店\": \"blue\",\n            \"信息流广告\": \"orange\",\n            \"小程序\": \"green\",\n            \"联运平台\": \"purple\",\n            \"自有渠道\": \"cyan\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n            color: typeColors[type] || \"default\",\n            children: type\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n            lineNumber: 137,\n            columnNumber: 12\n        }, undefined);\n    };\n    const columns = [\n        {\n            title: \"ID\",\n            dataIndex: \"id\",\n            key: \"id\",\n            width: 80\n        },\n        {\n            title: \"渠道名称\",\n            key: \"name\",\n            width: 150,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontWeight: 500,\n                                marginBottom: 4\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LinkOutlined, {\n                                    style: {\n                                        marginRight: 8,\n                                        color: \"#1890ff\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, undefined),\n                                record.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"12px\",\n                                color: \"#666\"\n                            },\n                            children: record.code\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"渠道类型\",\n            key: \"type\",\n            width: 120,\n            render: (record)=>getTypeTag(record.type)\n        },\n        {\n            title: \"状态\",\n            key: \"status\",\n            width: 100,\n            render: (record)=>getStatusTag(record.status)\n        },\n        {\n            title: \"注册用户\",\n            dataIndex: \"registrations\",\n            key: \"registrations\",\n            width: 120,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        color: \"#1890ff\",\n                        fontWeight: 500\n                    },\n                    children: value.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"活跃用户\",\n            dataIndex: \"activeUsers\",\n            key: \"activeUsers\",\n            width: 120,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        color: \"#52c41a\",\n                        fontWeight: 500\n                    },\n                    children: value.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"收入 (元)\",\n            dataIndex: \"revenue\",\n            key: \"revenue\",\n            width: 120,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        color: \"#fa8c16\",\n                        fontWeight: 500\n                    },\n                    children: [\n                        \"\\xa5\",\n                        value.toLocaleString()\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"转化率\",\n            key: \"conversionRate\",\n            width: 100,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"12px\",\n                                marginBottom: 4\n                            },\n                            children: [\n                                record.conversionRate,\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_8__.Progress, {\n                            percent: record.conversionRate,\n                            size: \"small\",\n                            showInfo: false,\n                            strokeColor: record.conversionRate > 15 ? \"#52c41a\" : record.conversionRate > 10 ? \"#faad14\" : \"#ff4d4f\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"CPA (元)\",\n            dataIndex: \"cpa\",\n            key: \"cpa\",\n            width: 100,\n            render: (value)=>\"\\xa5\".concat(value)\n        },\n        {\n            title: \"LTV (元)\",\n            dataIndex: \"ltv\",\n            key: \"ltv\",\n            width: 100,\n            render: (value)=>\"\\xa5\".concat(value)\n        },\n        {\n            title: \"联系人\",\n            key: \"contact\",\n            width: 120,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"12px\",\n                                fontWeight: 500\n                            },\n                            children: record.contactPerson\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"11px\",\n                                color: \"#666\"\n                            },\n                            children: record.contactPhone\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"操作\",\n            key: \"action\",\n            width: 200,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Space, {\n                    size: \"small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EyeOutlined, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>handleViewDetail(record),\n                            children: \"详情\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BarChartOutlined, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>handleViewStats(record),\n                            children: \"数据\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditOutlined, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>handleEdit(record),\n                            children: \"编辑\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"link\",\n                            size: \"small\",\n                            danger: true,\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeleteOutlined, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>handleDelete(record),\n                            children: \"删除\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ];\n    const handleViewDetail = (record)=>{\n        Modal.info({\n            title: \"渠道详情 - \".concat(record.name),\n            width: 600,\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: 16\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Space, {\n                            children: [\n                                getTypeTag(record.type),\n                                getStatusTag(record.status)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: 16\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                            gutter: 16,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                    span: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"渠道代码：\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" \",\n                                        record.code\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                    span: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"创建时间：\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" \",\n                                        record.createdAt\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: 16\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"渠道描述：\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, undefined),\n                            \" \",\n                            record.description\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: 16\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                            gutter: 16,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                    span: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"联系人：\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" \",\n                                        record.contactPerson\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                    span: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"联系电话：\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" \",\n                                        record.contactPhone\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: 16\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"关键指标：\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                                gutter: 16,\n                                style: {\n                                    marginTop: 8\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                            title: \"注册用户\",\n                                            value: record.registrations\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                            title: \"活跃用户\",\n                                            value: record.activeUsers\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                            title: \"收入\",\n                                            value: record.revenue,\n                                            prefix: \"\\xa5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                            title: \"转化率\",\n                                            value: record.conversionRate,\n                                            suffix: \"%\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                lineNumber: 298,\n                columnNumber: 9\n            }, undefined)\n        });\n    };\n    const handleViewStats = (record)=>{\n        message.info(\"查看 \".concat(record.name, \" 的详细数据统计\"));\n    };\n    const handleEdit = (record)=>{\n        setEditingChannel(record);\n        form.setFieldsValue(record);\n        setModalVisible(true);\n    };\n    const handleAdd = ()=>{\n        setEditingChannel(null);\n        form.resetFields();\n        setModalVisible(true);\n    };\n    const handleDelete = (record)=>{\n        Modal.confirm({\n            title: \"确认删除\",\n            content: '确定要删除渠道 \"'.concat(record.name, '\" 吗？此操作不可恢复。'),\n            onOk () {\n                message.success(\"已删除渠道: \".concat(record.name));\n            }\n        });\n    };\n    const handleModalOk = ()=>{\n        form.validateFields().then((values)=>{\n            console.log(\"Form values:\", values);\n            message.success(editingChannel ? \"渠道更新成功\" : \"渠道创建成功\");\n            setModalVisible(false);\n        }).catch((info)=>{\n            console.log(\"Validate Failed:\", info);\n        });\n    };\n    const handleSearch = (value)=>{\n        setSearchText(value);\n    };\n    // 计算统计数据\n    const totalRegistrations = channelsData.reduce((sum, item)=>sum + item.registrations, 0);\n    const totalActiveUsers = channelsData.reduce((sum, item)=>sum + item.activeUsers, 0);\n    const totalRevenue = channelsData.reduce((sum, item)=>sum + item.revenue, 0);\n    const avgConversionRate = (channelsData.reduce((sum, item)=>sum + item.conversionRate, 0) / channelsData.length).toFixed(1);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        requiredRoles: [\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_9__.ROLES.SYSTEM_ADMIN,\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_9__.ROLES.PRODUCT_MANAGER\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        justifyContent: \"space-between\",\n                        alignItems: \"center\",\n                        marginBottom: 24\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                            level: 2,\n                            children: \"渠道管理\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"primary\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusOutlined, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 40\n                            }, void 0),\n                            onClick: handleAdd,\n                            children: \"新建渠道\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 399,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                    gutter: [\n                        16,\n                        16\n                    ],\n                    style: {\n                        marginBottom: 24\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                    title: \"总注册用户\",\n                                    value: totalRegistrations,\n                                    valueStyle: {\n                                        color: \"#1890ff\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                    title: \"总活跃用户\",\n                                    value: totalActiveUsers,\n                                    valueStyle: {\n                                        color: \"#52c41a\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 417,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                    title: \"总收入\",\n                                    value: totalRevenue,\n                                    prefix: \"\\xa5\",\n                                    valueStyle: {\n                                        color: \"#fa8c16\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                    title: \"平均转化率\",\n                                    value: parseFloat(avgConversionRate),\n                                    suffix: \"%\",\n                                    valueStyle: {\n                                        color: \"#722ed1\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 407,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    style: {\n                        marginBottom: 16\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                        gutter: [\n                            16,\n                            16\n                        ],\n                        align: \"middle\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                xs: 24,\n                                sm: 8,\n                                lg: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input.Search, {\n                                    placeholder: \"搜索渠道名称\",\n                                    allowClear: true,\n                                    onSearch: handleSearch,\n                                    style: {\n                                        width: \"100%\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                xs: 24,\n                                sm: 6,\n                                lg: 4,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                    placeholder: \"渠道状态\",\n                                    style: {\n                                        width: \"100%\"\n                                    },\n                                    value: channelStatus,\n                                    onChange: setChannelStatus,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"all\",\n                                            children: \"全部状态\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"active\",\n                                            children: \"活跃\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"paused\",\n                                            children: \"暂停\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"inactive\",\n                                            children: \"停用\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                xs: 24,\n                                sm: 6,\n                                lg: 4,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                    placeholder: \"渠道类型\",\n                                    style: {\n                                        width: \"100%\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"all\",\n                                            children: \"全部类型\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"应用商店\",\n                                            children: \"应用商店\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"信息流广告\",\n                                            children: \"信息流广告\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"小程序\",\n                                            children: \"小程序\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"联运平台\",\n                                            children: \"联运平台\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"自有渠道\",\n                                            children: \"自有渠道\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                xs: 24,\n                                sm: 6,\n                                lg: 4,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"primary\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SearchOutlined, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 44\n                                    }, void 0),\n                                    block: true,\n                                    children: \"搜索\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                        lineNumber: 450,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 449,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.Table, {\n                        columns: columns,\n                        dataSource: channelsData,\n                        loading: loading,\n                        pagination: {\n                            total: 67,\n                            pageSize: 20,\n                            showSizeChanger: true,\n                            showQuickJumper: true,\n                            showTotal: (total, range)=>\"第 \".concat(range[0], \"-\").concat(range[1], \" 条/共 \").concat(total, \" 条\")\n                        },\n                        scroll: {\n                            x: 1400\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 494,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Modal, {\n                    title: editingChannel ? \"编辑渠道\" : \"新建渠道\",\n                    open: modalVisible,\n                    onOk: handleModalOk,\n                    onCancel: ()=>setModalVisible(false),\n                    width: 600,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form, {\n                        form: form,\n                        layout: \"vertical\",\n                        initialValues: {\n                            status: \"active\",\n                            isActive: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                                gutter: 16,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                            name: \"name\",\n                                            label: \"渠道名称\",\n                                            rules: [\n                                                {\n                                                    required: true,\n                                                    message: \"请输入渠道名称\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"请输入渠道名称\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                            name: \"code\",\n                                            label: \"渠道代码\",\n                                            rules: [\n                                                {\n                                                    required: true,\n                                                    message: \"请输入渠道代码\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"请输入渠道代码\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                                gutter: 16,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                            name: \"type\",\n                                            label: \"渠道类型\",\n                                            rules: [\n                                                {\n                                                    required: true,\n                                                    message: \"请选择渠道类型\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                placeholder: \"请选择渠道类型\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"应用商店\",\n                                                        children: \"应用商店\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"信息流广告\",\n                                                        children: \"信息流广告\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"小程序\",\n                                                        children: \"小程序\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"联运平台\",\n                                                        children: \"联运平台\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"自有渠道\",\n                                                        children: \"自有渠道\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                            name: \"status\",\n                                            label: \"渠道状态\",\n                                            rules: [\n                                                {\n                                                    required: true,\n                                                    message: \"请选择渠道状态\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                placeholder: \"请选择渠道状态\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"active\",\n                                                        children: \"活跃\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"paused\",\n                                                        children: \"暂停\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"inactive\",\n                                                        children: \"停用\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                lineNumber: 569,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 547,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                name: \"description\",\n                                label: \"渠道描述\",\n                                rules: [\n                                    {\n                                        required: true,\n                                        message: \"请输入渠道描述\"\n                                    }\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TextArea, {\n                                    rows: 3,\n                                    placeholder: \"请输入渠道描述\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                                gutter: 16,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                            name: \"contactPerson\",\n                                            label: \"联系人\",\n                                            rules: [\n                                                {\n                                                    required: true,\n                                                    message: \"请输入联系人\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"请输入联系人\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                lineNumber: 591,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                            name: \"contactPhone\",\n                                            label: \"联系电话\",\n                                            rules: [\n                                                {\n                                                    required: true,\n                                                    message: \"请输入联系电话\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"请输入联系电话\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 584,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                name: \"isActive\",\n                                label: \"是否启用\",\n                                valuePropName: \"checked\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Switch, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n                    lineNumber: 512,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n            lineNumber: 398,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/channels/page.tsx\",\n        lineNumber: 397,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChannelsPage, \"c5vXsHV02clZOHgvcozX0KrZyeU=\", true);\n_c = ChannelsPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ChannelsPage);\nvar _c;\n$RefreshReg$(_c, \"ChannelsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/channels/page.tsx\n"));

/***/ })

});