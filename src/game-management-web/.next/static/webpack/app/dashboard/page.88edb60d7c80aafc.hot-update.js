"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/Auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/Auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst ProtectedRoute = (param)=>{\n    let { children, requiredRoles = [] } = param;\n    _s();\n    const { isAuthenticated, isLoading, hasAnyRole } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading) {\n            if (!isAuthenticated) {\n                // 未登录，重定向到登录页\n                router.push(\"/login\");\n                return;\n            }\n            if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {\n                // 没有权限，重定向到仪表板或显示无权限页面\n                router.push(\"/dashboard\");\n                return;\n            }\n        }\n    }, [\n        isAuthenticated,\n        isLoading,\n        hasAnyRole,\n        requiredRoles,\n        router\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Auth/ProtectedRoute.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/components/Auth/ProtectedRoute.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!isAuthenticated) {\n        return null; // 将重定向到登录页\n    }\n    if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {\n        return null; // 将重定向到仪表板\n    }\n    // 认证通过，返回子组件\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n_s(ProtectedRoute, \"/tdbXfYBevH6wfScIwcFSmFiirM=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = ProtectedRoute;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProtectedRoute);\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0F1dGgvUHJvdGVjdGVkUm91dGUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRXlDO0FBQ1E7QUFDUTtBQU96RCxNQUFNSyxpQkFBZ0Q7UUFBQyxFQUNyREMsUUFBUSxFQUNSQyxnQkFBZ0IsRUFBRSxFQUNuQjs7SUFDQyxNQUFNLEVBQUVDLGVBQWUsRUFBRUMsU0FBUyxFQUFFQyxVQUFVLEVBQUUsR0FBR1IsOERBQU9BO0lBQzFELE1BQU1TLFNBQVNSLDBEQUFTQTtJQUN4QixNQUFNUyxXQUFXUiw0REFBV0E7SUFFNUJILGdEQUFTQSxDQUFDO1FBQ1IsSUFBSSxDQUFDUSxXQUFXO1lBQ2QsSUFBSSxDQUFDRCxpQkFBaUI7Z0JBQ3BCLGNBQWM7Z0JBQ2RHLE9BQU9FLElBQUksQ0FBQztnQkFDWjtZQUNGO1lBRUEsSUFBSU4sY0FBY08sTUFBTSxHQUFHLEtBQUssQ0FBQ0osV0FBV0gsZ0JBQWdCO2dCQUMxRCx1QkFBdUI7Z0JBQ3ZCSSxPQUFPRSxJQUFJLENBQUM7Z0JBQ1o7WUFDRjtRQUNGO0lBQ0YsR0FBRztRQUFDTDtRQUFpQkM7UUFBV0M7UUFBWUg7UUFBZUk7S0FBTztJQUVsRSxJQUFJRixXQUFXO1FBQ2IscUJBQ0UsOERBQUNNO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOzs7Ozs7Ozs7OztJQUdyQjtJQUVBLElBQUksQ0FBQ1IsaUJBQWlCO1FBQ3BCLE9BQU8sTUFBTSxXQUFXO0lBQzFCO0lBRUEsSUFBSUQsY0FBY08sTUFBTSxHQUFHLEtBQUssQ0FBQ0osV0FBV0gsZ0JBQWdCO1FBQzFELE9BQU8sTUFBTSxXQUFXO0lBQzFCO0lBRUEsYUFBYTtJQUNiLHFCQUFPO2tCQUFHRDs7QUFDWjtHQTFDTUQ7O1FBSStDSCwwREFBT0E7UUFDM0NDLHNEQUFTQTtRQUNQQyx3REFBV0E7OztLQU54QkM7QUE0Q04sK0RBQWVBLGNBQWNBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvQXV0aC9Qcm90ZWN0ZWRSb3V0ZS50c3g/ZDg1MCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIsIHVzZVBhdGhuYW1lIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcblxuaW50ZXJmYWNlIFByb3RlY3RlZFJvdXRlUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xuICByZXF1aXJlZFJvbGVzPzogc3RyaW5nW107XG59XG5cbmNvbnN0IFByb3RlY3RlZFJvdXRlOiBSZWFjdC5GQzxQcm90ZWN0ZWRSb3V0ZVByb3BzPiA9ICh7IFxuICBjaGlsZHJlbiwgXG4gIHJlcXVpcmVkUm9sZXMgPSBbXSBcbn0pID0+IHtcbiAgY29uc3QgeyBpc0F1dGhlbnRpY2F0ZWQsIGlzTG9hZGluZywgaGFzQW55Um9sZSB9ID0gdXNlQXV0aCgpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFpc0xvYWRpbmcpIHtcbiAgICAgIGlmICghaXNBdXRoZW50aWNhdGVkKSB7XG4gICAgICAgIC8vIOacqueZu+W9le+8jOmHjeWumuWQkeWIsOeZu+W9lemhtVxuICAgICAgICByb3V0ZXIucHVzaCgnL2xvZ2luJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgaWYgKHJlcXVpcmVkUm9sZXMubGVuZ3RoID4gMCAmJiAhaGFzQW55Um9sZShyZXF1aXJlZFJvbGVzKSkge1xuICAgICAgICAvLyDmsqHmnInmnYPpmZDvvIzph43lrprlkJHliLDku6rooajmnb/miJbmmL7npLrml6DmnYPpmZDpobXpnaJcbiAgICAgICAgcm91dGVyLnB1c2goJy9kYXNoYm9hcmQnKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgIH1cbiAgfSwgW2lzQXV0aGVudGljYXRlZCwgaXNMb2FkaW5nLCBoYXNBbnlSb2xlLCByZXF1aXJlZFJvbGVzLCByb3V0ZXJdKTtcblxuICBpZiAoaXNMb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBpdGVtcy1jZW50ZXIgbWluLWgtc2NyZWVuXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDBcIj48L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICBpZiAoIWlzQXV0aGVudGljYXRlZCkge1xuICAgIHJldHVybiBudWxsOyAvLyDlsIbph43lrprlkJHliLDnmbvlvZXpobVcbiAgfVxuXG4gIGlmIChyZXF1aXJlZFJvbGVzLmxlbmd0aCA+IDAgJiYgIWhhc0FueVJvbGUocmVxdWlyZWRSb2xlcykpIHtcbiAgICByZXR1cm4gbnVsbDsgLy8g5bCG6YeN5a6a5ZCR5Yiw5Luq6KGo5p2/XG4gIH1cblxuICAvLyDorqTor4HpgJrov4fvvIzov5Tlm57lrZDnu4Tku7ZcbiAgcmV0dXJuIDw+e2NoaWxkcmVufTwvPjtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFByb3RlY3RlZFJvdXRlO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlRWZmZWN0IiwidXNlQXV0aCIsInVzZVJvdXRlciIsInVzZVBhdGhuYW1lIiwiUHJvdGVjdGVkUm91dGUiLCJjaGlsZHJlbiIsInJlcXVpcmVkUm9sZXMiLCJpc0F1dGhlbnRpY2F0ZWQiLCJpc0xvYWRpbmciLCJoYXNBbnlSb2xlIiwicm91dGVyIiwicGF0aG5hbWUiLCJwdXNoIiwibGVuZ3RoIiwiZGl2IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Auth/ProtectedRoute.tsx\n"));

/***/ })

});