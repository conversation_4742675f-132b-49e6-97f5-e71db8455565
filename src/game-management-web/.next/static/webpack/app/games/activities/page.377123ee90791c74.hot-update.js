"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/games/activities/page",{

/***/ "(app-pages-browser)/./src/app/games/activities/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/games/activities/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Auth/ProtectedRoute */ \"(app-pages-browser)/./src/components/Auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(app-pages-browser)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Pause,Play,Plus,Trophy,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Pause,Play,Plus,Trophy,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Pause,Play,Plus,Trophy,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Pause,Play,Plus,Trophy,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Pause,Play,Plus,Trophy,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Pause,Play,Plus,Trophy,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Pause,Play,Plus,Trophy,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Activity status mapping\nconst ActivityStatusMap = {\n    1: {\n        label: \"草稿\",\n        color: \"bg-gray-100 text-gray-800\",\n        icon: _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    2: {\n        label: \"进行中\",\n        color: \"bg-green-100 text-green-800\",\n        icon: _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    3: {\n        label: \"已暂停\",\n        color: \"bg-yellow-100 text-yellow-800\",\n        icon: _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    4: {\n        label: \"已结束\",\n        color: \"bg-blue-100 text-blue-800\",\n        icon: _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    5: {\n        label: \"已取消\",\n        color: \"bg-red-100 text-red-800\",\n        icon: _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    }\n};\n// API functions\nconst activityApi = {\n    async getActivities () {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(\"/api/Activity\", {\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token),\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) throw new Error(\"Failed to fetch activities\");\n        return response.json();\n    },\n    async createActivity (data) {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(\"/api/Activity\", {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token),\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                ...data,\n                startTime: data.startTime.toISOString(),\n                endTime: data.endTime.toISOString()\n            })\n        });\n        if (!response.ok) throw new Error(\"Failed to create activity\");\n        return response.json();\n    },\n    async updateActivity (id, data) {\n        var _data_startTime, _data_endTime;\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(\"/api/Activity/\".concat(id), {\n            method: \"PUT\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token),\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                ...data,\n                startTime: (_data_startTime = data.startTime) === null || _data_startTime === void 0 ? void 0 : _data_startTime.toISOString(),\n                endTime: (_data_endTime = data.endTime) === null || _data_endTime === void 0 ? void 0 : _data_endTime.toISOString()\n            })\n        });\n        if (!response.ok) throw new Error(\"Failed to update activity\");\n        return response.json();\n    },\n    async deleteActivity (id) {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(\"/api/Activity/\".concat(id), {\n            method: \"DELETE\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) throw new Error(\"Failed to delete activity\");\n    },\n    async startActivity (id) {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(\"/api/Activity/\".concat(id, \"/start\"), {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) throw new Error(\"Failed to start activity\");\n    },\n    async pauseActivity (id) {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(\"/api/Activity/\".concat(id, \"/pause\"), {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) throw new Error(\"Failed to pause activity\");\n    },\n    async resumeActivity (id) {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(\"/api/Activity/\".concat(id, \"/resume\"), {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) throw new Error(\"Failed to resume activity\");\n    },\n    async endActivity (id) {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(\"/api/Activity/\".concat(id, \"/end\"), {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) throw new Error(\"Failed to end activity\");\n    }\n};\nconst ActivitiesPage = ()=>{\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchText, setSearchText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activityStatus, setActivityStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [activities, setActivities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isCreateDialogOpen, setIsCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedActivity, setSelectedActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [createForm, setCreateForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        description: \"\",\n        startTime: new Date(),\n        endTime: new Date(),\n        conditions: \"\",\n        rewards: \"\"\n    });\n    const [editForm, setEditForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load activities on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadActivities();\n    }, []);\n    const loadActivities = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const data = await activityApi.getActivities();\n            setActivities(data);\n        } catch (err) {\n            setError(\"加载活动列表失败\");\n            console.error(\"Error loading activities:\", err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetCreateForm = ()=>{\n        setCreateForm({\n            name: \"\",\n            description: \"\",\n            startTime: new Date(),\n            endTime: new Date(),\n            conditions: \"\",\n            rewards: \"\"\n        });\n    };\n    const handleCreateSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            setError(null);\n            await activityApi.createActivity(createForm);\n            setIsCreateDialogOpen(false);\n            resetCreateForm();\n            await loadActivities();\n        } catch (err) {\n            setError(\"创建活动失败\");\n            console.error(\"Error creating activity:\", err);\n        }\n    };\n    const handleEditSubmit = async (e)=>{\n        e.preventDefault();\n        if (!selectedActivity) return;\n        try {\n            setError(null);\n            await activityApi.updateActivity(selectedActivity.id, editForm);\n            setIsEditDialogOpen(false);\n            setSelectedActivity(null);\n            await loadActivities();\n        } catch (err) {\n            setError(\"更新活动失败\");\n            console.error(\"Error updating activity:\", err);\n        }\n    };\n    const handleEdit = (activity)=>{\n        setSelectedActivity(activity);\n        setEditForm({\n            name: activity.name,\n            description: activity.description,\n            startTime: new Date(activity.startTime),\n            endTime: new Date(activity.endTime),\n            conditions: activity.conditions,\n            rewards: activity.rewards\n        });\n        setIsEditDialogOpen(true);\n    };\n    const handleDelete = async (id)=>{\n        if (!confirm(\"确定要删除这个活动吗？\")) return;\n        try {\n            setError(null);\n            await activityApi.deleteActivity(id);\n            await loadActivities();\n        } catch (err) {\n            setError(\"删除活动失败\");\n            console.error(\"Error deleting activity:\", err);\n        }\n    };\n    const handleStatusAction = async (activity, action)=>{\n        try {\n            setError(null);\n            switch(action){\n                case \"start\":\n                    await activityApi.startActivity(activity.id);\n                    break;\n                case \"pause\":\n                    await activityApi.pauseActivity(activity.id);\n                    break;\n                case \"resume\":\n                    await activityApi.resumeActivity(activity.id);\n                    break;\n                case \"end\":\n                    await activityApi.endActivity(activity.id);\n                    break;\n            }\n            await loadActivities();\n        } catch (err) {\n            setError(\"操作失败: \".concat(action));\n            console.error(\"Error \".concat(action, \" activity:\"), err);\n        }\n    };\n    // Filter activities based on search and status\n    const filteredActivities = activities.filter((activity)=>{\n        const matchesSearch = activity.name.toLowerCase().includes(searchText.toLowerCase()) || activity.description.toLowerCase().includes(searchText.toLowerCase());\n        const matchesStatus = activityStatus === \"all\" || activityStatus === \"draft\" && activity.status === 1 || activityStatus === \"active\" && activity.status === 2 || activityStatus === \"paused\" && activity.status === 3 || activityStatus === \"ended\" && activity.status === 4;\n        return matchesSearch && matchesStatus;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        requiredRoles: [\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.ROLES.SYSTEM_ADMIN,\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.ROLES.PRODUCT_MANAGER\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"游戏活动\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"创建活动\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"活动管理\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                    placeholder: \"搜索活动...\",\n                                                    value: searchText,\n                                                    onChange: (e)=>setSearchText(e.target.value),\n                                                    className: \"max-w-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: activityStatus,\n                                                    onValueChange: setActivityStatus,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            className: \"w-40\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"活动状态\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"all\",\n                                                                    children: \"全部状态\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"active\",\n                                                                    children: \"进行中\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"ended\",\n                                                                    children: \"已结束\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"upcoming\",\n                                                                    children: \"即将开始\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8 text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"游戏活动功能正在开发中...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm mt-2\",\n                                                    children: \"完整的活动管理功能即将上线\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                lineNumber: 328,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n            lineNumber: 327,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n        lineNumber: 326,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ActivitiesPage, \"m/iz32yxyOSWBkf7R+VmAfJa/3U=\");\n_c = ActivitiesPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ActivitiesPage);\nvar _c;\n$RefreshReg$(_c, \"ActivitiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/games/activities/page.tsx\n"));

/***/ })

});