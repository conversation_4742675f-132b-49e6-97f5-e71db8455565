"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/games/activities/page",{

/***/ "(app-pages-browser)/./src/app/games/activities/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/games/activities/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Auth/ProtectedRoute */ \"(app-pages-browser)/./src/components/Auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst ActivitiesPage = ()=>{\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchText, setSearchText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activityStatus, setActivityStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [modalVisible, setModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingActivity, setEditingActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [form] = Form.useForm();\n    // 模拟活动数据\n    const activitiesData = [\n        {\n            key: \"1\",\n            id: 1001,\n            name: \"端午节限时活动\",\n            type: \"节日活动\",\n            description: \"端午节期间，完成指定任务可获得丰厚奖励\",\n            startTime: \"2024-06-22 00:00:00\",\n            endTime: \"2024-06-24 23:59:59\",\n            status: \"ended\",\n            isActive: false,\n            participantCount: 8567,\n            rewardItems: [\n                \"粽子\",\n                \"龙舟装饰\",\n                \"经验加成卡\"\n            ],\n            serverId: [\n                1,\n                2,\n                3\n            ],\n            serverNames: [\n                \"服务器1\",\n                \"服务器2\",\n                \"服务器3\"\n            ],\n            createdAt: \"2024-06-20 10:00:00\",\n            createdBy: \"产品经理A\"\n        },\n        {\n            key: \"2\",\n            id: 1002,\n            name: \"夏日狂欢周\",\n            type: \"限时活动\",\n            description: \"夏日特别活动，每日登录送好礼\",\n            startTime: \"2024-06-25 00:00:00\",\n            endTime: \"2024-07-01 23:59:59\",\n            status: \"active\",\n            isActive: true,\n            participantCount: 12450,\n            rewardItems: [\n                \"夏日套装\",\n                \"清凉饮品\",\n                \"金币礼包\"\n            ],\n            serverId: [\n                1,\n                2\n            ],\n            serverNames: [\n                \"服务器1\",\n                \"服务器2\"\n            ],\n            createdAt: \"2024-06-23 15:30:00\",\n            createdBy: \"产品经理B\"\n        },\n        {\n            key: \"3\",\n            id: 1003,\n            name: \"新手引导活动\",\n            type: \"常驻活动\",\n            description: \"帮助新玩家快速上手游戏\",\n            startTime: \"2024-01-01 00:00:00\",\n            endTime: \"2024-12-31 23:59:59\",\n            status: \"active\",\n            isActive: true,\n            participantCount: 45678,\n            rewardItems: [\n                \"新手装备包\",\n                \"经验药水\",\n                \"金币\"\n            ],\n            serverId: [\n                1,\n                2,\n                3,\n                4\n            ],\n            serverNames: [\n                \"服务器1\",\n                \"服务器2\",\n                \"服务器3\",\n                \"服务器4\"\n            ],\n            createdAt: \"2023-12-25 09:00:00\",\n            createdBy: \"系统管理员\"\n        }\n    ];\n    const getStatusTag = (status)=>{\n        switch(status){\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"success\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayCircleOutlined, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 43\n                    }, void 0),\n                    children: \"进行中\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 16\n                }, undefined);\n            case \"scheduled\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"processing\",\n                    children: \"待开始\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 16\n                }, undefined);\n            case \"ended\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"default\",\n                    children: \"已结束\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 16\n                }, undefined);\n            case \"paused\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"warning\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PauseCircleOutlined, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 43\n                    }, void 0),\n                    children: \"已暂停\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"default\",\n                    children: \"未知\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const columns = [\n        {\n            title: \"ID\",\n            dataIndex: \"id\",\n            key: \"id\",\n            width: 80\n        },\n        {\n            title: \"活动名称\",\n            dataIndex: \"name\",\n            key: \"name\",\n            width: 200\n        },\n        {\n            title: \"活动类型\",\n            dataIndex: \"type\",\n            key: \"type\",\n            width: 120,\n            render: (type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"blue\",\n                    children: type\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"状态\",\n            key: \"status\",\n            width: 120,\n            render: (record)=>getStatusTag(record.status)\n        },\n        {\n            title: \"参与人数\",\n            dataIndex: \"participantCount\",\n            key: \"participantCount\",\n            width: 120,\n            render: (count)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        color: \"#1890ff\"\n                    },\n                    children: count.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"开始时间\",\n            dataIndex: \"startTime\",\n            key: \"startTime\",\n            width: 160\n        },\n        {\n            title: \"结束时间\",\n            dataIndex: \"endTime\",\n            key: \"endTime\",\n            width: 160\n        },\n        {\n            title: \"服务器\",\n            key: \"servers\",\n            width: 150,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        record.serverNames.slice(0, 2).map((name, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                                size: \"small\",\n                                children: name\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, undefined)),\n                        record.serverNames.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                            size: \"small\",\n                            children: [\n                                \"+\",\n                                record.serverNames.length - 2\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"操作\",\n            key: \"action\",\n            width: 200,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Space, {\n                    size: \"small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditOutlined, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>handleEdit(record),\n                            children: \"编辑\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: record.status === \"active\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PauseCircleOutlined, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 48\n                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayCircleOutlined, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 74\n                            }, void 0),\n                            onClick: ()=>handleToggleStatus(record),\n                            children: record.status === \"active\" ? \"暂停\" : \"启动\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"link\",\n                            size: \"small\",\n                            danger: true,\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeleteOutlined, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>handleDelete(record),\n                            children: \"删除\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ];\n    const handleEdit = (record)=>{\n        setEditingActivity(record);\n        form.setFieldsValue(record);\n        setModalVisible(true);\n    };\n    const handleAdd = ()=>{\n        setEditingActivity(null);\n        form.resetFields();\n        setModalVisible(true);\n    };\n    const handleToggleStatus = (record)=>{\n        const action = record.status === \"active\" ? \"暂停\" : \"启动\";\n        Modal.confirm({\n            title: \"确认\".concat(action),\n            content: \"确定要\".concat(action, '活动 \"').concat(record.name, '\" 吗？'),\n            onOk () {\n                message.success(\"已\".concat(action, \"活动: \").concat(record.name));\n            }\n        });\n    };\n    const handleDelete = (record)=>{\n        Modal.confirm({\n            title: \"确认删除\",\n            content: '确定要删除活动 \"'.concat(record.name, '\" 吗？此操作不可恢复。'),\n            onOk () {\n                message.success(\"已删除活动: \".concat(record.name));\n            }\n        });\n    };\n    const handleModalOk = ()=>{\n        form.validateFields().then((values)=>{\n            console.log(\"Form values:\", values);\n            message.success(editingActivity ? \"活动更新成功\" : \"活动创建成功\");\n            setModalVisible(false);\n        }).catch((info)=>{\n            console.log(\"Validate Failed:\", info);\n        });\n    };\n    const handleSearch = (value)=>{\n        setSearchText(value);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        requiredRoles: [\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.ROLES.SYSTEM_ADMIN,\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.ROLES.PRODUCT_MANAGER,\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.ROLES.PRODUCT_SPECIALIST\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                    level: 2,\n                    children: \"活动管理\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    style: {\n                        marginBottom: 16\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                        gutter: [\n                            16,\n                            16\n                        ],\n                        align: \"middle\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                xs: 24,\n                                sm: 8,\n                                lg: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input.Search, {\n                                    placeholder: \"搜索活动名称\",\n                                    allowClear: true,\n                                    onSearch: handleSearch,\n                                    style: {\n                                        width: \"100%\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                xs: 24,\n                                sm: 8,\n                                lg: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                    placeholder: \"活动状态\",\n                                    style: {\n                                        width: \"100%\"\n                                    },\n                                    value: activityStatus,\n                                    onChange: setActivityStatus,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"all\",\n                                            children: \"全部状态\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"active\",\n                                            children: \"进行中\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"scheduled\",\n                                            children: \"待开始\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"ended\",\n                                            children: \"已结束\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: \"paused\",\n                                            children: \"已暂停\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                xs: 24,\n                                sm: 8,\n                                lg: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RangePicker, {\n                                    style: {\n                                        width: \"100%\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                xs: 24,\n                                sm: 8,\n                                lg: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Space, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"primary\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SearchOutlined, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 46\n                                            }, void 0),\n                                            children: \"搜索\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"primary\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusOutlined, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 46\n                                            }, void 0),\n                                            onClick: handleAdd,\n                                            children: \"新建活动\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.Table, {\n                        columns: columns,\n                        dataSource: activitiesData,\n                        loading: loading,\n                        pagination: {\n                            total: 156,\n                            pageSize: 20,\n                            showSizeChanger: true,\n                            showQuickJumper: true,\n                            showTotal: (total, range)=>\"第 \".concat(range[0], \"-\").concat(range[1], \" 条/共 \").concat(total, \" 条\")\n                        },\n                        scroll: {\n                            x: 1400\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Modal, {\n                    title: editingActivity ? \"编辑活动\" : \"新建活动\",\n                    open: modalVisible,\n                    onOk: handleModalOk,\n                    onCancel: ()=>setModalVisible(false),\n                    width: 800,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form, {\n                        form: form,\n                        layout: \"vertical\",\n                        initialValues: {\n                            isActive: true,\n                            serverId: [\n                                1\n                            ]\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                                gutter: 16,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                            name: \"name\",\n                                            label: \"活动名称\",\n                                            rules: [\n                                                {\n                                                    required: true,\n                                                    message: \"请输入活动名称\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"请输入活动名称\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                            name: \"type\",\n                                            label: \"活动类型\",\n                                            rules: [\n                                                {\n                                                    required: true,\n                                                    message: \"请选择活动类型\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                placeholder: \"请选择活动类型\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"节日活动\",\n                                                        children: \"节日活动\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"限时活动\",\n                                                        children: \"限时活动\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"常驻活动\",\n                                                        children: \"常驻活动\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                                        value: \"新手活动\",\n                                                        children: \"新手活动\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                name: \"description\",\n                                label: \"活动描述\",\n                                rules: [\n                                    {\n                                        required: true,\n                                        message: \"请输入活动描述\"\n                                    }\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TextArea, {\n                                    rows: 3,\n                                    placeholder: \"请输入活动描述\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                                gutter: 16,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                            name: \"startTime\",\n                                            label: \"开始时间\",\n                                            rules: [\n                                                {\n                                                    required: true,\n                                                    message: \"请选择开始时间\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DatePicker, {\n                                                showTime: true,\n                                                style: {\n                                                    width: \"100%\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                        span: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                            name: \"endTime\",\n                                            label: \"结束时间\",\n                                            rules: [\n                                                {\n                                                    required: true,\n                                                    message: \"请选择结束时间\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DatePicker, {\n                                                showTime: true,\n                                                style: {\n                                                    width: \"100%\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                name: \"serverId\",\n                                label: \"适用服务器\",\n                                rules: [\n                                    {\n                                        required: true,\n                                        message: \"请选择适用服务器\"\n                                    }\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                    mode: \"multiple\",\n                                    placeholder: \"请选择适用服务器\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: 1,\n                                            children: \"服务器1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: 2,\n                                            children: \"服务器2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: 3,\n                                            children: \"服务器3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select.Option, {\n                                            value: 4,\n                                            children: \"服务器4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Form.Item, {\n                                name: \"isActive\",\n                                label: \"是否启用\",\n                                valuePropName: \"checked\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Switch, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n            lineNumber: 251,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n        lineNumber: 250,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ActivitiesPage, \"kOS3AJnt2IpnlcUGBqbK2z8KSts=\", true);\n_c = ActivitiesPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ActivitiesPage);\nvar _c;\n$RefreshReg$(_c, \"ActivitiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/games/activities/page.tsx\n"));

/***/ })

});