"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/games/activities/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js":
/*!**********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-check-big.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ CircleCheckBig; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21.801 10A10 10 0 1 1 17 3.335\",\n            key: \"yps3ct\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 11 3 3L22 4\",\n            key: \"1pflzl\"\n        }\n    ]\n];\nconst CircleCheckBig = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"circle-check-big\", __iconNode);\n //# sourceMappingURL=circle-check-big.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-x.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ CircleX; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m15 9-6 6\",\n            key: \"1uzhvr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 9 6 6\",\n            key: \"z0biqf\"\n        }\n    ]\n];\nconst CircleX = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"circle-x\", __iconNode);\n //# sourceMappingURL=circle-x.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/pause.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Pause; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            x: \"14\",\n            y: \"4\",\n            width: \"4\",\n            height: \"16\",\n            rx: \"1\",\n            key: \"zuxfzm\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            x: \"6\",\n            y: \"4\",\n            width: \"4\",\n            height: \"16\",\n            rx: \"1\",\n            key: \"1okwgv\"\n        }\n    ]\n];\nconst Pause = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"pause\", __iconNode);\n //# sourceMappingURL=pause.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/play.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Play; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"polygon\",\n        {\n            points: \"6 3 20 12 6 21 6 3\",\n            key: \"1oa8hb\"\n        }\n    ]\n];\nconst Play = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"play\", __iconNode);\n //# sourceMappingURL=play.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvcGxheS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHYSxNQUFBQSxhQUF1QjtJQUFDO1FBQUM7UUFBVztZQUFFQyxRQUFRO1lBQXNCQyxLQUFLO1FBQVM7S0FBRTtDQUFBO0FBYTNGLE1BQUFDLE9BQU9DLGdFQUFnQkEsQ0FBQyxRQUFRSiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vc3JjL2ljb25zL3BsYXkudHM/OTE3MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbWydwb2x5Z29uJywgeyBwb2ludHM6ICc2IDMgMjAgMTIgNiAyMSA2IDMnLCBrZXk6ICcxb2E4aGInIH1dXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIFBsYXlcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHOXNlV2R2YmlCd2IybHVkSE05SWpZZ015QXlNQ0F4TWlBMklESXhJRFlnTXlJZ0x6NEtQQzl6ZG1jK0NnPT0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL3BsYXlcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBQbGF5ID0gY3JlYXRlTHVjaWRlSWNvbigncGxheScsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgZGVmYXVsdCBQbGF5O1xuIl0sIm5hbWVzIjpbIl9faWNvbk5vZGUiLCJwb2ludHMiLCJrZXkiLCJQbGF5IiwiY3JlYXRlTHVjaWRlSWNvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/square-pen.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ SquarePen; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\",\n            key: \"1m0v6g\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z\",\n            key: \"ohrbg2\"\n        }\n    ]\n];\nconst SquarePen = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"square-pen\", __iconNode);\n //# sourceMappingURL=square-pen.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/games/activities/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/games/activities/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Auth/ProtectedRoute */ \"(app-pages-browser)/./src/components/Auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(app-pages-browser)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Pause,Play,Plus,Trophy,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Pause,Play,Plus,Trophy,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Pause,Play,Plus,Trophy,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Pause,Play,Plus,Trophy,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Pause,Play,Plus,Trophy,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Pause,Play,Plus,Trophy,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Pause,Play,Plus,Trophy,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Activity status mapping\nconst ActivityStatusMap = {\n    1: {\n        label: \"草稿\",\n        color: \"bg-gray-100 text-gray-800\",\n        icon: _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    2: {\n        label: \"进行中\",\n        color: \"bg-green-100 text-green-800\",\n        icon: _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    3: {\n        label: \"已暂停\",\n        color: \"bg-yellow-100 text-yellow-800\",\n        icon: _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    4: {\n        label: \"已结束\",\n        color: \"bg-blue-100 text-blue-800\",\n        icon: _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    5: {\n        label: \"已取消\",\n        color: \"bg-red-100 text-red-800\",\n        icon: _barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    }\n};\n// API functions\nconst activityApi = {\n    async getActivities () {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(\"/api/Activity\", {\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token),\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) throw new Error(\"Failed to fetch activities\");\n        return response.json();\n    },\n    async createActivity (data) {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(\"/api/Activity\", {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token),\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                ...data,\n                startTime: data.startTime.toISOString(),\n                endTime: data.endTime.toISOString()\n            })\n        });\n        if (!response.ok) throw new Error(\"Failed to create activity\");\n        return response.json();\n    },\n    async updateActivity (id, data) {\n        var _data_startTime, _data_endTime;\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(\"/api/Activity/\".concat(id), {\n            method: \"PUT\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token),\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                ...data,\n                startTime: (_data_startTime = data.startTime) === null || _data_startTime === void 0 ? void 0 : _data_startTime.toISOString(),\n                endTime: (_data_endTime = data.endTime) === null || _data_endTime === void 0 ? void 0 : _data_endTime.toISOString()\n            })\n        });\n        if (!response.ok) throw new Error(\"Failed to update activity\");\n        return response.json();\n    },\n    async deleteActivity (id) {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(\"/api/Activity/\".concat(id), {\n            method: \"DELETE\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) throw new Error(\"Failed to delete activity\");\n    },\n    async startActivity (id) {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(\"/api/Activity/\".concat(id, \"/start\"), {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) throw new Error(\"Failed to start activity\");\n    },\n    async pauseActivity (id) {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(\"/api/Activity/\".concat(id, \"/pause\"), {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) throw new Error(\"Failed to pause activity\");\n    },\n    async resumeActivity (id) {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(\"/api/Activity/\".concat(id, \"/resume\"), {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) throw new Error(\"Failed to resume activity\");\n    },\n    async endActivity (id) {\n        const token = localStorage.getItem(\"token\");\n        const response = await fetch(\"/api/Activity/\".concat(id, \"/end\"), {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) throw new Error(\"Failed to end activity\");\n    }\n};\nconst ActivitiesPage = ()=>{\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchText, setSearchText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activityStatus, setActivityStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [activities, setActivities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isCreateDialogOpen, setIsCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedActivity, setSelectedActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [createForm, setCreateForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        description: \"\",\n        startTime: new Date(),\n        endTime: new Date(),\n        conditions: \"\",\n        rewards: \"\"\n    });\n    const [editForm, setEditForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        requiredRoles: [\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.ROLES.SYSTEM_ADMIN,\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.ROLES.PRODUCT_MANAGER\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"游戏活动\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"创建活动\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"活动管理\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                    placeholder: \"搜索活动...\",\n                                                    value: searchText,\n                                                    onChange: (e)=>setSearchText(e.target.value),\n                                                    className: \"max-w-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: activityStatus,\n                                                    onValueChange: setActivityStatus,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            className: \"w-40\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"活动状态\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"all\",\n                                                                    children: \"全部状态\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"active\",\n                                                                    children: \"进行中\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"ended\",\n                                                                    children: \"已结束\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"upcoming\",\n                                                                    children: \"即将开始\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8 text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_Pause_Play_Plus_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"游戏活动功能正在开发中...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm mt-2\",\n                                                    children: \"完整的活动管理功能即将上线\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/games/activities/page.tsx\",\n        lineNumber: 204,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ActivitiesPage, \"KlFu7FFShSMFAkWsKWgAeCDgxw0=\");\n_c = ActivitiesPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ActivitiesPage);\nvar _c;\n$RefreshReg$(_c, \"ActivitiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/games/activities/page.tsx\n"));

/***/ })

});