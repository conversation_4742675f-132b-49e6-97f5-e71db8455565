"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2663e030831d\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMjY2M2UwMzA4MzFkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   operationalDataApi: () => (/* binding */ operationalDataApi),\n/* harmony export */   playersApi: () => (/* binding */ playersApi),\n/* harmony export */   usersApi: () => (/* binding */ usersApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n// API 基础配置\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5108/api';\n// 创建 axios 实例\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 10000,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// 请求拦截器 - 添加认证令牌\napiClient.interceptors.request.use((config)=>{\n    // 检查是否在浏览器环境中\n    if (true) {\n        const token = localStorage.getItem('accessToken');\n        if (token) {\n            config.headers.Authorization = \"Bearer \".concat(token);\n        }\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器 - 处理认证错误\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, async (error)=>{\n    var _error_response;\n    const originalRequest = error.config;\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401 && !originalRequest._retry && \"object\" !== 'undefined') {\n        originalRequest._retry = true;\n        try {\n            const refreshToken = localStorage.getItem('refreshToken');\n            if (refreshToken) {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(API_BASE_URL, \"/auth/refresh\"), refreshToken);\n                const { token } = response.data;\n                localStorage.setItem('accessToken', token);\n                originalRequest.headers.Authorization = \"Bearer \".concat(token);\n                return apiClient(originalRequest);\n            }\n        } catch (refreshError) {\n            // 刷新令牌失败，清除本地存储并重定向到登录页\n            localStorage.removeItem('accessToken');\n            localStorage.removeItem('refreshToken');\n            localStorage.removeItem('user');\n            window.location.href = '/login';\n        }\n    }\n    return Promise.reject(error);\n});\n// API 方法\nconst authApi = {\n    login: (data)=>apiClient.post('/auth/login', data),\n    logout: (refreshToken)=>apiClient.post('/auth/logout', refreshToken),\n    register: (data)=>apiClient.post('/auth/register', data),\n    refreshToken: (refreshToken)=>apiClient.post('/auth/refresh', refreshToken),\n    changePassword: (currentPassword, newPassword)=>apiClient.post('/auth/change-password', {\n            currentPassword,\n            newPassword\n        }),\n    resetPassword: (email)=>apiClient.post('/auth/reset-password', {\n            email\n        })\n};\nconst usersApi = {\n    getUsers: ()=>apiClient.get('/users'),\n    getUser: (id)=>apiClient.get(\"/users/\".concat(id)),\n    getUserByUsername: (username)=>apiClient.get(\"/users/by-username/\".concat(username)),\n    createUser: (data)=>apiClient.post('/users', data),\n    updateUser: (id, data)=>apiClient.put(\"/users/\".concat(id), data),\n    deleteUser: (id)=>apiClient.delete(\"/users/\".concat(id)),\n    activateUser: (id)=>apiClient.post(\"/users/\".concat(id, \"/activate\")),\n    deactivateUser: (id)=>apiClient.post(\"/users/\".concat(id, \"/deactivate\")),\n    getUsersByRole: (role)=>apiClient.get(\"/users/by-role/\".concat(role))\n};\nconst playersApi = {\n    getPlayers: function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, pageSize = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        return apiClient.get(\"/players?page=\".concat(page, \"&pageSize=\").concat(pageSize));\n    },\n    getPlayer: (id)=>apiClient.get(\"/players/\".concat(id)),\n    getPlayerByAccountId: (accountId)=>apiClient.get(\"/players/by-account/\".concat(accountId)),\n    searchPlayers: (searchTerm)=>apiClient.get(\"/players/search?searchTerm=\".concat(encodeURIComponent(searchTerm))),\n    getPlayerStats: ()=>apiClient.get('/players/stats'),\n    getTopPlayersByLevel: function() {\n        let count = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return apiClient.get(\"/players/top-by-level?count=\".concat(count));\n    },\n    getVipPlayers: function() {\n        let minVipLevel = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        return apiClient.get(\"/players/vip?minVipLevel=\".concat(minVipLevel));\n    },\n    updatePlayer: (id, data)=>apiClient.put(\"/players/\".concat(id), data),\n    banPlayer: (id, bannedUntil, reason)=>apiClient.post(\"/players/\".concat(id, \"/ban\"), {\n            bannedUntil,\n            reason\n        }),\n    unbanPlayer: (id)=>apiClient.post(\"/players/\".concat(id, \"/unban\"))\n};\n// 运营数据API方法\nconst operationalDataApi = {\n    // 全局统计\n    getGlobalStats: ()=>apiClient.get('/operational-data/global-stats'),\n    getGlobalStatsByDate: (date)=>apiClient.get(\"/operational-data/global-stats/\".concat(date)),\n    // 用户信息统计\n    getUserInfoStats: ()=>apiClient.get('/operational-data/user-info-stats'),\n    getUserInfoStatsByDateRange: (startDate, endDate)=>apiClient.get(\"/operational-data/user-info-stats/\".concat(startDate, \"/\").concat(endDate)),\n    // 付费信息统计\n    getPaymentInfoStats: ()=>apiClient.get('/operational-data/payment-info-stats'),\n    getPaymentInfoStatsByDateRange: (startDate, endDate)=>apiClient.get(\"/operational-data/payment-info-stats/\".concat(startDate, \"/\").concat(endDate)),\n    // 数据分析\n    getConversionAnalysis: (date)=>apiClient.get(\"/operational-data/conversion-analysis/\".concat(date)),\n    getRetentionAnalysis: (date)=>apiClient.get(\"/operational-data/retention-analysis/\".concat(date)),\n    getActiveUserAnalysis: (date)=>apiClient.get(\"/operational-data/active-user-analysis/\".concat(date)),\n    // 记录数据\n    recordVisit: (data)=>apiClient.post('/operational-data/record-visit', data)\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvYXBpLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBZ0Y7QUFFaEYsV0FBVztBQUNYLE1BQU1DLGVBQWVDLE9BQU9BLENBQUNDLEdBQUcsQ0FBQ0MsbUJBQW1CLElBQUk7QUFFeEQsY0FBYztBQUNkLE1BQU1DLFlBQTJCTCw2Q0FBS0EsQ0FBQ00sTUFBTSxDQUFDO0lBQzVDQyxTQUFTTjtJQUNUTyxTQUFTO0lBQ1RDLFNBQVM7UUFDUCxnQkFBZ0I7SUFDbEI7QUFDRjtBQUVBLGlCQUFpQjtBQUNqQkosVUFBVUssWUFBWSxDQUFDQyxPQUFPLENBQUNDLEdBQUcsQ0FDaEMsQ0FBQ0M7SUFDQyxjQUFjO0lBQ2QsSUFBSSxJQUE2QixFQUFFO1FBQ2pDLE1BQU1DLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztRQUNuQyxJQUFJRixPQUFPO1lBQ1RELE9BQU9KLE9BQU8sQ0FBQ1EsYUFBYSxHQUFHLFVBQWdCLE9BQU5IO1FBQzNDO0lBQ0Y7SUFDQSxPQUFPRDtBQUNULEdBQ0EsQ0FBQ0s7SUFDQyxPQUFPQyxRQUFRQyxNQUFNLENBQUNGO0FBQ3hCO0FBR0YsaUJBQWlCO0FBQ2pCYixVQUFVSyxZQUFZLENBQUNXLFFBQVEsQ0FBQ1QsR0FBRyxDQUNqQyxDQUFDUztJQUNDLE9BQU9BO0FBQ1QsR0FDQSxPQUFPSDtRQUdEQTtJQUZKLE1BQU1JLGtCQUFrQkosTUFBTUwsTUFBTTtJQUVwQyxJQUFJSyxFQUFBQSxrQkFBQUEsTUFBTUcsUUFBUSxjQUFkSCxzQ0FBQUEsZ0JBQWdCSyxNQUFNLE1BQUssT0FBTyxDQUFDRCxnQkFBZ0JFLE1BQU0sSUFBSSxhQUFrQixhQUFhO1FBQzlGRixnQkFBZ0JFLE1BQU0sR0FBRztRQUV6QixJQUFJO1lBQ0YsTUFBTUMsZUFBZVYsYUFBYUMsT0FBTyxDQUFDO1lBQzFDLElBQUlTLGNBQWM7Z0JBQ2hCLE1BQU1KLFdBQVcsTUFBTXJCLDZDQUFLQSxDQUFDMEIsSUFBSSxDQUFDLEdBQWdCLE9BQWJ6QixjQUFhLGtCQUFnQndCO2dCQUNsRSxNQUFNLEVBQUVYLEtBQUssRUFBRSxHQUFHTyxTQUFTTSxJQUFJO2dCQUUvQlosYUFBYWEsT0FBTyxDQUFDLGVBQWVkO2dCQUNwQ1EsZ0JBQWdCYixPQUFPLENBQUNRLGFBQWEsR0FBRyxVQUFnQixPQUFOSDtnQkFFbEQsT0FBT1QsVUFBVWlCO1lBQ25CO1FBQ0YsRUFBRSxPQUFPTyxjQUFjO1lBQ3JCLHdCQUF3QjtZQUN4QmQsYUFBYWUsVUFBVSxDQUFDO1lBQ3hCZixhQUFhZSxVQUFVLENBQUM7WUFDeEJmLGFBQWFlLFVBQVUsQ0FBQztZQUN4QkMsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7UUFDekI7SUFDRjtJQUVBLE9BQU9kLFFBQVFDLE1BQU0sQ0FBQ0Y7QUFDeEI7QUF5RUYsU0FBUztBQUNGLE1BQU1nQixVQUFVO0lBQ3JCQyxPQUFPLENBQUNSLE9BQ050QixVQUFVcUIsSUFBSSxDQUFnQixlQUFlQztJQUUvQ1MsUUFBUSxDQUFDWCxlQUNQcEIsVUFBVXFCLElBQUksQ0FBQyxnQkFBZ0JEO0lBRWpDWSxVQUFVLENBQUNWLE9BQ1R0QixVQUFVcUIsSUFBSSxDQUFPLGtCQUFrQkM7SUFFekNGLGNBQWMsQ0FBQ0EsZUFDYnBCLFVBQVVxQixJQUFJLENBQWdCLGlCQUFpQkQ7SUFFakRhLGdCQUFnQixDQUFDQyxpQkFBeUJDLGNBQ3hDbkMsVUFBVXFCLElBQUksQ0FBQyx5QkFBeUI7WUFBRWE7WUFBaUJDO1FBQVk7SUFFekVDLGVBQWUsQ0FBQ0MsUUFDZHJDLFVBQVVxQixJQUFJLENBQUMsd0JBQXdCO1lBQUVnQjtRQUFNO0FBQ25ELEVBQUU7QUFFSyxNQUFNQyxXQUFXO0lBQ3RCQyxVQUFVLElBQ1J2QyxVQUFVd0MsR0FBRyxDQUFTO0lBRXhCQyxTQUFTLENBQUNDLEtBQ1IxQyxVQUFVd0MsR0FBRyxDQUFPLFVBQWEsT0FBSEU7SUFFaENDLG1CQUFtQixDQUFDQyxXQUNsQjVDLFVBQVV3QyxHQUFHLENBQU8sc0JBQStCLE9BQVRJO0lBRTVDQyxZQUFZLENBQUN2QixPQUNYdEIsVUFBVXFCLElBQUksQ0FBTyxVQUFVQztJQUVqQ3dCLFlBQVksQ0FBQ0osSUFBWXBCLE9BQ3ZCdEIsVUFBVStDLEdBQUcsQ0FBTyxVQUFhLE9BQUhMLEtBQU1wQjtJQUV0QzBCLFlBQVksQ0FBQ04sS0FDWDFDLFVBQVVpRCxNQUFNLENBQUMsVUFBYSxPQUFIUDtJQUU3QlEsY0FBYyxDQUFDUixLQUNiMUMsVUFBVXFCLElBQUksQ0FBQyxVQUFhLE9BQUhxQixJQUFHO0lBRTlCUyxnQkFBZ0IsQ0FBQ1QsS0FDZjFDLFVBQVVxQixJQUFJLENBQUMsVUFBYSxPQUFIcUIsSUFBRztJQUU5QlUsZ0JBQWdCLENBQUNDLE9BQ2ZyRCxVQUFVd0MsR0FBRyxDQUFTLGtCQUF1QixPQUFMYTtBQUM1QyxFQUFFO0FBRUssTUFBTUMsYUFBYTtJQUN4QkMsWUFBWTtZQUFDQyx3RUFBZSxHQUFHQyw0RUFBbUI7ZUFDaER6RCxVQUFVd0MsR0FBRyxDQUFXLGlCQUFrQ2lCLE9BQWpCRCxNQUFLLGNBQXFCLE9BQVRDOztJQUU1REMsV0FBVyxDQUFDaEIsS0FDVjFDLFVBQVV3QyxHQUFHLENBQVMsWUFBZSxPQUFIRTtJQUVwQ2lCLHNCQUFzQixDQUFDQyxZQUNyQjVELFVBQVV3QyxHQUFHLENBQVMsdUJBQWlDLE9BQVZvQjtJQUUvQ0MsZUFBZSxDQUFDQyxhQUNkOUQsVUFBVXdDLEdBQUcsQ0FBVyw4QkFBNkQsT0FBL0J1QixtQkFBbUJEO0lBRTNFRSxnQkFBZ0IsSUFDZGhFLFVBQVV3QyxHQUFHLENBQWM7SUFFN0J5QixzQkFBc0I7WUFBQ0MseUVBQWdCO2VBQ3JDbEUsVUFBVXdDLEdBQUcsQ0FBVywrQkFBcUMsT0FBTjBCOztJQUV6REMsZUFBZTtZQUFDQywrRUFBc0I7ZUFDcENwRSxVQUFVd0MsR0FBRyxDQUFXLDRCQUF3QyxPQUFaNEI7O0lBRXREQyxjQUFjLENBQUMzQixJQUFZcEIsT0FDekJ0QixVQUFVK0MsR0FBRyxDQUFDLFlBQWUsT0FBSEwsS0FBTXBCO0lBRWxDZ0QsV0FBVyxDQUFDNUIsSUFBWTZCLGFBQXNCQyxTQUM1Q3hFLFVBQVVxQixJQUFJLENBQUMsWUFBZSxPQUFIcUIsSUFBRyxTQUFPO1lBQUU2QjtZQUFhQztRQUFPO0lBRTdEQyxhQUFhLENBQUMvQixLQUNaMUMsVUFBVXFCLElBQUksQ0FBQyxZQUFlLE9BQUhxQixJQUFHO0FBQ2xDLEVBQUU7QUFpRkYsWUFBWTtBQUNMLE1BQU1nQyxxQkFBcUI7SUFDaEMsT0FBTztJQUNQQyxnQkFBZ0IsSUFDZDNFLFVBQVV3QyxHQUFHLENBQWM7SUFFN0JvQyxzQkFBc0IsQ0FBQ0MsT0FDckI3RSxVQUFVd0MsR0FBRyxDQUFjLGtDQUF1QyxPQUFMcUM7SUFFL0QsU0FBUztJQUNUQyxrQkFBa0IsSUFDaEI5RSxVQUFVd0MsR0FBRyxDQUFnQjtJQUUvQnVDLDZCQUE2QixDQUFDQyxXQUFtQkMsVUFDL0NqRixVQUFVd0MsR0FBRyxDQUFnQixxQ0FBa0R5QyxPQUFiRCxXQUFVLEtBQVcsT0FBUkM7SUFFakYsU0FBUztJQUNUQyxxQkFBcUIsSUFDbkJsRixVQUFVd0MsR0FBRyxDQUFtQjtJQUVsQzJDLGdDQUFnQyxDQUFDSCxXQUFtQkMsVUFDbERqRixVQUFVd0MsR0FBRyxDQUFtQix3Q0FBcUR5QyxPQUFiRCxXQUFVLEtBQVcsT0FBUkM7SUFFdkYsT0FBTztJQUNQRyx1QkFBdUIsQ0FBQ1AsT0FDdEI3RSxVQUFVd0MsR0FBRyxDQUFxQix5Q0FBOEMsT0FBTHFDO0lBRTdFUSxzQkFBc0IsQ0FBQ1IsT0FDckI3RSxVQUFVd0MsR0FBRyxDQUFvQix3Q0FBNkMsT0FBTHFDO0lBRTNFUyx1QkFBdUIsQ0FBQ1QsT0FDdEI3RSxVQUFVd0MsR0FBRyxDQUFxQiwwQ0FBK0MsT0FBTHFDO0lBRTlFLE9BQU87SUFDUFUsYUFBYSxDQUFDakUsT0FDWnRCLFVBQVVxQixJQUFJLENBQUMsa0NBQWtDQztBQUNyRCxFQUFFO0FBRUYsaUVBQWV0QixTQUFTQSxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvY2FmZS9Eb2N1bWVudHMvZ2FtZW1hbmFnZXdlYi9zcmMvZ2FtZS1tYW5hZ2VtZW50LXdlYi9zcmMvbGliL2FwaS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXhpb3MsIHsgQXhpb3NJbnN0YW5jZSwgQXhpb3NSZXF1ZXN0Q29uZmlnLCBBeGlvc1Jlc3BvbnNlIH0gZnJvbSAnYXhpb3MnO1xuXG4vLyBBUEkg5Z+656GA6YWN572uXG5jb25zdCBBUElfQkFTRV9VUkwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjUxMDgvYXBpJztcblxuLy8g5Yib5bu6IGF4aW9zIOWunuS+i1xuY29uc3QgYXBpQ2xpZW50OiBBeGlvc0luc3RhbmNlID0gYXhpb3MuY3JlYXRlKHtcbiAgYmFzZVVSTDogQVBJX0JBU0VfVVJMLFxuICB0aW1lb3V0OiAxMDAwMCxcbiAgaGVhZGVyczoge1xuICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gIH0sXG59KTtcblxuLy8g6K+35rGC5oum5oiq5ZmoIC0g5re75Yqg6K6k6K+B5Luk54mMXG5hcGlDbGllbnQuaW50ZXJjZXB0b3JzLnJlcXVlc3QudXNlKFxuICAoY29uZmlnKSA9PiB7XG4gICAgLy8g5qOA5p+l5piv5ZCm5Zyo5rWP6KeI5Zmo546v5aKD5LitXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhY2Nlc3NUb2tlbicpO1xuICAgICAgaWYgKHRva2VuKSB7XG4gICAgICAgIGNvbmZpZy5oZWFkZXJzLkF1dGhvcml6YXRpb24gPSBgQmVhcmVyICR7dG9rZW59YDtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGNvbmZpZztcbiAgfSxcbiAgKGVycm9yKSA9PiB7XG4gICAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKTtcbiAgfVxuKTtcblxuLy8g5ZON5bqU5oum5oiq5ZmoIC0g5aSE55CG6K6k6K+B6ZSZ6K+vXG5hcGlDbGllbnQuaW50ZXJjZXB0b3JzLnJlc3BvbnNlLnVzZShcbiAgKHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlKSA9PiB7XG4gICAgcmV0dXJuIHJlc3BvbnNlO1xuICB9LFxuICBhc3luYyAoZXJyb3IpID0+IHtcbiAgICBjb25zdCBvcmlnaW5hbFJlcXVlc3QgPSBlcnJvci5jb25maWc7XG5cbiAgICBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDAxICYmICFvcmlnaW5hbFJlcXVlc3QuX3JldHJ5ICYmIHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBvcmlnaW5hbFJlcXVlc3QuX3JldHJ5ID0gdHJ1ZTtcblxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgcmVmcmVzaFRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3JlZnJlc2hUb2tlbicpO1xuICAgICAgICBpZiAocmVmcmVzaFRva2VuKSB7XG4gICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5wb3N0KGAke0FQSV9CQVNFX1VSTH0vYXV0aC9yZWZyZXNoYCwgcmVmcmVzaFRva2VuKTtcbiAgICAgICAgICBjb25zdCB7IHRva2VuIH0gPSByZXNwb25zZS5kYXRhO1xuXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2FjY2Vzc1Rva2VuJywgdG9rZW4pO1xuICAgICAgICAgIG9yaWdpbmFsUmVxdWVzdC5oZWFkZXJzLkF1dGhvcml6YXRpb24gPSBgQmVhcmVyICR7dG9rZW59YDtcblxuICAgICAgICAgIHJldHVybiBhcGlDbGllbnQob3JpZ2luYWxSZXF1ZXN0KTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAocmVmcmVzaEVycm9yKSB7XG4gICAgICAgIC8vIOWIt+aWsOS7pOeJjOWksei0pe+8jOa4hemZpOacrOWcsOWtmOWCqOW5tumHjeWumuWQkeWIsOeZu+W9lemhtVxuICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnYWNjZXNzVG9rZW4nKTtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3JlZnJlc2hUb2tlbicpO1xuICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndXNlcicpO1xuICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvbG9naW4nO1xuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiBQcm9taXNlLnJlamVjdChlcnJvcik7XG4gIH1cbik7XG5cbi8vIEFQSSDmjqXlj6PnsbvlnovlrprkuYlcbmV4cG9ydCBpbnRlcmZhY2UgTG9naW5SZXF1ZXN0IHtcbiAgdXNlcm5hbWU6IHN0cmluZztcbiAgcGFzc3dvcmQ6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBMb2dpblJlc3BvbnNlIHtcbiAgdG9rZW46IHN0cmluZztcbiAgZXhwaXJlc0F0OiBzdHJpbmc7XG4gIHVzZXI6IFVzZXI7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgVXNlciB7XG4gIGlkOiBudW1iZXI7XG4gIHVzZXJuYW1lOiBzdHJpbmc7XG4gIGVtYWlsOiBzdHJpbmc7XG4gIGRpc3BsYXlOYW1lPzogc3RyaW5nO1xuICBjcmVhdGVkQXQ6IHN0cmluZztcbiAgbGFzdExvZ2luQXQ/OiBzdHJpbmc7XG4gIGlzQWN0aXZlOiBib29sZWFuO1xuICByb2xlczogc3RyaW5nW107XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQ3JlYXRlVXNlclJlcXVlc3Qge1xuICB1c2VybmFtZTogc3RyaW5nO1xuICBlbWFpbDogc3RyaW5nO1xuICBwYXNzd29yZDogc3RyaW5nO1xuICBkaXNwbGF5TmFtZT86IHN0cmluZztcbiAgcm9sZXM6IHN0cmluZ1tdO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFVwZGF0ZVVzZXJSZXF1ZXN0IHtcbiAgdXNlcm5hbWU/OiBzdHJpbmc7XG4gIGVtYWlsPzogc3RyaW5nO1xuICBkaXNwbGF5TmFtZT86IHN0cmluZztcbiAgaXNBY3RpdmU/OiBib29sZWFuO1xuICByb2xlcz86IHN0cmluZ1tdO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFBsYXllciB7XG4gIGlkOiBudW1iZXI7XG4gIGFjY291bnRJZDogc3RyaW5nO1xuICBuaWNrbmFtZTogc3RyaW5nO1xuICBsZXZlbDogbnVtYmVyO1xuICBjbGFzczogc3RyaW5nO1xuICBleHBlcmllbmNlOiBudW1iZXI7XG4gIGdvbGQ6IG51bWJlcjtcbiAgZGlhbW9uZHM6IG51bWJlcjtcbiAgdmlwTGV2ZWw6IG51bWJlcjtcbiAgbGFzdExvZ2luQXQ/OiBzdHJpbmc7XG4gIHRvdGFsUGxheVRpbWU6IHN0cmluZztcbiAgaXBBZGRyZXNzPzogc3RyaW5nO1xuICBzZXJ2ZXJJZDogbnVtYmVyO1xuICBzZXJ2ZXJOYW1lOiBzdHJpbmc7XG4gIGlzQmFubmVkOiBib29sZWFuO1xuICBiYW5uZWRVbnRpbD86IHN0cmluZztcbiAgYmFuUmVhc29uPzogc3RyaW5nO1xuICBjcmVhdGVkQXQ6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBQbGF5ZXJTdGF0cyB7XG4gIHRvdGFsUGxheWVyczogbnVtYmVyO1xuICBhY3RpdmVQbGF5ZXJzOiBudW1iZXI7XG4gIG5ld1BsYXllcnNUb2RheTogbnVtYmVyO1xuICB2aXBQbGF5ZXJzOiBudW1iZXI7XG4gIGF2ZXJhZ2VMZXZlbDogbnVtYmVyO1xuICBsZXZlbERpc3RyaWJ1dGlvbjogUmVjb3JkPHN0cmluZywgbnVtYmVyPjtcbiAgY2xhc3NEaXN0cmlidXRpb246IFJlY29yZDxzdHJpbmcsIG51bWJlcj47XG59XG5cbi8vIEFQSSDmlrnms5VcbmV4cG9ydCBjb25zdCBhdXRoQXBpID0ge1xuICBsb2dpbjogKGRhdGE6IExvZ2luUmVxdWVzdCkgPT4gXG4gICAgYXBpQ2xpZW50LnBvc3Q8TG9naW5SZXNwb25zZT4oJy9hdXRoL2xvZ2luJywgZGF0YSksXG4gIFxuICBsb2dvdXQ6IChyZWZyZXNoVG9rZW46IHN0cmluZykgPT4gXG4gICAgYXBpQ2xpZW50LnBvc3QoJy9hdXRoL2xvZ291dCcsIHJlZnJlc2hUb2tlbiksXG4gIFxuICByZWdpc3RlcjogKGRhdGE6IENyZWF0ZVVzZXJSZXF1ZXN0KSA9PiBcbiAgICBhcGlDbGllbnQucG9zdDxVc2VyPignL2F1dGgvcmVnaXN0ZXInLCBkYXRhKSxcbiAgXG4gIHJlZnJlc2hUb2tlbjogKHJlZnJlc2hUb2tlbjogc3RyaW5nKSA9PiBcbiAgICBhcGlDbGllbnQucG9zdDxMb2dpblJlc3BvbnNlPignL2F1dGgvcmVmcmVzaCcsIHJlZnJlc2hUb2tlbiksXG4gIFxuICBjaGFuZ2VQYXNzd29yZDogKGN1cnJlbnRQYXNzd29yZDogc3RyaW5nLCBuZXdQYXNzd29yZDogc3RyaW5nKSA9PiBcbiAgICBhcGlDbGllbnQucG9zdCgnL2F1dGgvY2hhbmdlLXBhc3N3b3JkJywgeyBjdXJyZW50UGFzc3dvcmQsIG5ld1Bhc3N3b3JkIH0pLFxuICBcbiAgcmVzZXRQYXNzd29yZDogKGVtYWlsOiBzdHJpbmcpID0+IFxuICAgIGFwaUNsaWVudC5wb3N0KCcvYXV0aC9yZXNldC1wYXNzd29yZCcsIHsgZW1haWwgfSksXG59O1xuXG5leHBvcnQgY29uc3QgdXNlcnNBcGkgPSB7XG4gIGdldFVzZXJzOiAoKSA9PiBcbiAgICBhcGlDbGllbnQuZ2V0PFVzZXJbXT4oJy91c2VycycpLFxuICBcbiAgZ2V0VXNlcjogKGlkOiBudW1iZXIpID0+IFxuICAgIGFwaUNsaWVudC5nZXQ8VXNlcj4oYC91c2Vycy8ke2lkfWApLFxuICBcbiAgZ2V0VXNlckJ5VXNlcm5hbWU6ICh1c2VybmFtZTogc3RyaW5nKSA9PiBcbiAgICBhcGlDbGllbnQuZ2V0PFVzZXI+KGAvdXNlcnMvYnktdXNlcm5hbWUvJHt1c2VybmFtZX1gKSxcbiAgXG4gIGNyZWF0ZVVzZXI6IChkYXRhOiBDcmVhdGVVc2VyUmVxdWVzdCkgPT4gXG4gICAgYXBpQ2xpZW50LnBvc3Q8VXNlcj4oJy91c2VycycsIGRhdGEpLFxuICBcbiAgdXBkYXRlVXNlcjogKGlkOiBudW1iZXIsIGRhdGE6IFVwZGF0ZVVzZXJSZXF1ZXN0KSA9PiBcbiAgICBhcGlDbGllbnQucHV0PFVzZXI+KGAvdXNlcnMvJHtpZH1gLCBkYXRhKSxcbiAgXG4gIGRlbGV0ZVVzZXI6IChpZDogbnVtYmVyKSA9PiBcbiAgICBhcGlDbGllbnQuZGVsZXRlKGAvdXNlcnMvJHtpZH1gKSxcbiAgXG4gIGFjdGl2YXRlVXNlcjogKGlkOiBudW1iZXIpID0+IFxuICAgIGFwaUNsaWVudC5wb3N0KGAvdXNlcnMvJHtpZH0vYWN0aXZhdGVgKSxcbiAgXG4gIGRlYWN0aXZhdGVVc2VyOiAoaWQ6IG51bWJlcikgPT4gXG4gICAgYXBpQ2xpZW50LnBvc3QoYC91c2Vycy8ke2lkfS9kZWFjdGl2YXRlYCksXG4gIFxuICBnZXRVc2Vyc0J5Um9sZTogKHJvbGU6IHN0cmluZykgPT4gXG4gICAgYXBpQ2xpZW50LmdldDxVc2VyW10+KGAvdXNlcnMvYnktcm9sZS8ke3JvbGV9YCksXG59O1xuXG5leHBvcnQgY29uc3QgcGxheWVyc0FwaSA9IHtcbiAgZ2V0UGxheWVyczogKHBhZ2U6IG51bWJlciA9IDEsIHBhZ2VTaXplOiBudW1iZXIgPSAyMCkgPT4gXG4gICAgYXBpQ2xpZW50LmdldDxQbGF5ZXJbXT4oYC9wbGF5ZXJzP3BhZ2U9JHtwYWdlfSZwYWdlU2l6ZT0ke3BhZ2VTaXplfWApLFxuICBcbiAgZ2V0UGxheWVyOiAoaWQ6IG51bWJlcikgPT4gXG4gICAgYXBpQ2xpZW50LmdldDxQbGF5ZXI+KGAvcGxheWVycy8ke2lkfWApLFxuICBcbiAgZ2V0UGxheWVyQnlBY2NvdW50SWQ6IChhY2NvdW50SWQ6IHN0cmluZykgPT4gXG4gICAgYXBpQ2xpZW50LmdldDxQbGF5ZXI+KGAvcGxheWVycy9ieS1hY2NvdW50LyR7YWNjb3VudElkfWApLFxuICBcbiAgc2VhcmNoUGxheWVyczogKHNlYXJjaFRlcm06IHN0cmluZykgPT4gXG4gICAgYXBpQ2xpZW50LmdldDxQbGF5ZXJbXT4oYC9wbGF5ZXJzL3NlYXJjaD9zZWFyY2hUZXJtPSR7ZW5jb2RlVVJJQ29tcG9uZW50KHNlYXJjaFRlcm0pfWApLFxuICBcbiAgZ2V0UGxheWVyU3RhdHM6ICgpID0+IFxuICAgIGFwaUNsaWVudC5nZXQ8UGxheWVyU3RhdHM+KCcvcGxheWVycy9zdGF0cycpLFxuICBcbiAgZ2V0VG9wUGxheWVyc0J5TGV2ZWw6IChjb3VudDogbnVtYmVyID0gMTApID0+IFxuICAgIGFwaUNsaWVudC5nZXQ8UGxheWVyW10+KGAvcGxheWVycy90b3AtYnktbGV2ZWw/Y291bnQ9JHtjb3VudH1gKSxcbiAgXG4gIGdldFZpcFBsYXllcnM6IChtaW5WaXBMZXZlbDogbnVtYmVyID0gMSkgPT4gXG4gICAgYXBpQ2xpZW50LmdldDxQbGF5ZXJbXT4oYC9wbGF5ZXJzL3ZpcD9taW5WaXBMZXZlbD0ke21pblZpcExldmVsfWApLFxuICBcbiAgdXBkYXRlUGxheWVyOiAoaWQ6IG51bWJlciwgZGF0YTogYW55KSA9PiBcbiAgICBhcGlDbGllbnQucHV0KGAvcGxheWVycy8ke2lkfWAsIGRhdGEpLFxuICBcbiAgYmFuUGxheWVyOiAoaWQ6IG51bWJlciwgYmFubmVkVW50aWw/OiBzdHJpbmcsIHJlYXNvbj86IHN0cmluZykgPT4gXG4gICAgYXBpQ2xpZW50LnBvc3QoYC9wbGF5ZXJzLyR7aWR9L2JhbmAsIHsgYmFubmVkVW50aWwsIHJlYXNvbiB9KSxcbiAgXG4gIHVuYmFuUGxheWVyOiAoaWQ6IG51bWJlcikgPT4gXG4gICAgYXBpQ2xpZW50LnBvc3QoYC9wbGF5ZXJzLyR7aWR9L3VuYmFuYCksXG59O1xuXG4vLyDov5DokKXmlbDmja7nm7jlhbPmjqXlj6NcbmV4cG9ydCBpbnRlcmZhY2UgR2xvYmFsU3RhdHMge1xuICB0b3RhbFJldmVudWU6IG51bWJlcjtcbiAgdG90YWxWaXNpdHM6IG51bWJlcjtcbiAgdG90YWxSZWdpc3RyYXRpb25zOiBudW1iZXI7XG4gIHRvdGFsTG9naW5zOiBudW1iZXI7XG4gIHRvZGF5TmV3VmlzaXRzOiBudW1iZXI7XG4gIHRvZGF5TmV3UmVnaXN0cmF0aW9uczogbnVtYmVyO1xuICB0b2RheU5ld0xvZ2luczogbnVtYmVyO1xuICB0b2RheU5ld1BheW1lbnRzOiBudW1iZXI7XG4gIHRvZGF5QWN0aXZlVXNlcnM6IG51bWJlcjtcbiAgYXJwdTogbnVtYmVyO1xuICBhdmVyYWdlT25saW5lVXNlcnM6IG51bWJlcjtcbiAgbWF4T25saW5lVXNlcnM6IG51bWJlcjtcbiAgbGFzdFVwZGF0ZWQ6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBVc2VySW5mb1N0YXRzIHtcbiAgdG90YWxWaXNpdHM6IG51bWJlcjtcbiAgdW5pcXVlVmlzaXRzOiBudW1iZXI7XG4gIHRvdGFsUmVnaXN0cmF0aW9uczogbnVtYmVyO1xuICByZWdpc3RyYXRpb25zV2l0aENoYW5uZWw6IG51bWJlcjtcbiAgdG90YWxMb2dpbnM6IG51bWJlcjtcbiAgc2FtZUlwTG9naW5zOiBudW1iZXI7XG4gIGN1cnJlbnRPbmxpbmVVc2VyczogbnVtYmVyO1xuICB1c2Vyc1dpdGhvdXRDaGFyYWN0ZXI6IG51bWJlcjtcbiAgdXNlcnNOZXZlckxvZ2dlZEluOiBudW1iZXI7XG4gIHJlZ2lzdHJhdGlvbnNCeUNoYW5uZWw6IFJlY29yZDxzdHJpbmcsIG51bWJlcj47XG4gIHZpc2l0c0J5SG91cjogUmVjb3JkPHN0cmluZywgbnVtYmVyPjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBQYXltZW50SW5mb1N0YXRzIHtcbiAgdG90YWxSZXZlbnVlOiBudW1iZXI7XG4gIHRvdGFsT3JkZXJzOiBudW1iZXI7XG4gIHBlbmRpbmdPcmRlcnM6IG51bWJlcjtcbiAgY29tcGxldGVkT3JkZXJzOiBudW1iZXI7XG4gIGZhaWxlZE9yZGVyczogbnVtYmVyO1xuICBhdmVyYWdlT3JkZXJWYWx1ZTogbnVtYmVyO1xuICByZXZlbnVlQnlNZXRob2Q6IFJlY29yZDxzdHJpbmcsIG51bWJlcj47XG4gIG9yZGVyc0J5U3RhdHVzOiBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+O1xuICB0b3BQYXllcnM6IFRvcFBheWVyW107XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgVG9wUGF5ZXIge1xuICBhY2NvdW50SWQ6IHN0cmluZztcbiAgbmlja25hbWU6IHN0cmluZztcbiAgdG90YWxBbW91bnQ6IG51bWJlcjtcbiAgb3JkZXJDb3VudDogbnVtYmVyO1xuICBsYXN0UGF5bWVudFRpbWU6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDb252ZXJzaW9uQW5hbHlzaXMge1xuICB2aXNpdFRvUmVnaXN0cmF0aW9uUmF0ZTogbnVtYmVyO1xuICByZWdpc3RyYXRpb25Ub1BheW1lbnRSYXRlOiBudW1iZXI7XG4gIHJlZ2lzdHJhdGlvblRvQ2hhcmFjdGVyQ3JlYXRpb25SYXRlOiBudW1iZXI7XG4gIGNoYXJhY3RlckNyZWF0aW9uVG9GaXJzdExvZ2luUmF0ZTogbnVtYmVyO1xuICBhbmFseXNpc0RhdGU6IHN0cmluZztcbiAgY29udmVyc2lvbkJ5Q2hhbm5lbDogUmVjb3JkPHN0cmluZywgbnVtYmVyPjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBSZXRlbnRpb25BbmFseXNpcyB7XG4gIG5leHREYXlSZXRlbnRpb25SYXRlOiBudW1iZXI7XG4gIHNldmVuRGF5UmV0ZW50aW9uUmF0ZTogbnVtYmVyO1xuICBtb250aGx5UmV0ZW50aW9uUmF0ZTogbnVtYmVyO1xuICBhbmFseXNpc0RhdGU6IHN0cmluZztcbiAgcmV0ZW50aW9uQnlDaGFubmVsOiBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+O1xuICByZXRlbnRpb25CeUxldmVsOiBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+O1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEFjdGl2ZVVzZXJBbmFseXNpcyB7XG4gIGRhdTogbnVtYmVyO1xuICB3YXU6IG51bWJlcjtcbiAgbWF1OiBudW1iZXI7XG4gIGF2ZXJhZ2VTZXNzaW9uVGltZTogbnVtYmVyO1xuICBhY3RpdmVVc2Vyc0J5SG91cjogUmVjb3JkPHN0cmluZywgbnVtYmVyPjtcbiAgYWN0aXZlVXNlcnNCeVNlcnZlcjogUmVjb3JkPHN0cmluZywgbnVtYmVyPjtcbiAgYW5hbHlzaXNEYXRlOiBzdHJpbmc7XG59XG5cbi8vIOi/kOiQpeaVsOaNrkFQSeaWueazlVxuZXhwb3J0IGNvbnN0IG9wZXJhdGlvbmFsRGF0YUFwaSA9IHtcbiAgLy8g5YWo5bGA57uf6K6hXG4gIGdldEdsb2JhbFN0YXRzOiAoKSA9PlxuICAgIGFwaUNsaWVudC5nZXQ8R2xvYmFsU3RhdHM+KCcvb3BlcmF0aW9uYWwtZGF0YS9nbG9iYWwtc3RhdHMnKSxcblxuICBnZXRHbG9iYWxTdGF0c0J5RGF0ZTogKGRhdGU6IHN0cmluZykgPT5cbiAgICBhcGlDbGllbnQuZ2V0PEdsb2JhbFN0YXRzPihgL29wZXJhdGlvbmFsLWRhdGEvZ2xvYmFsLXN0YXRzLyR7ZGF0ZX1gKSxcblxuICAvLyDnlKjmiLfkv6Hmga/nu5/orqFcbiAgZ2V0VXNlckluZm9TdGF0czogKCkgPT5cbiAgICBhcGlDbGllbnQuZ2V0PFVzZXJJbmZvU3RhdHM+KCcvb3BlcmF0aW9uYWwtZGF0YS91c2VyLWluZm8tc3RhdHMnKSxcblxuICBnZXRVc2VySW5mb1N0YXRzQnlEYXRlUmFuZ2U6IChzdGFydERhdGU6IHN0cmluZywgZW5kRGF0ZTogc3RyaW5nKSA9PlxuICAgIGFwaUNsaWVudC5nZXQ8VXNlckluZm9TdGF0cz4oYC9vcGVyYXRpb25hbC1kYXRhL3VzZXItaW5mby1zdGF0cy8ke3N0YXJ0RGF0ZX0vJHtlbmREYXRlfWApLFxuXG4gIC8vIOS7mOi0ueS/oeaBr+e7n+iuoVxuICBnZXRQYXltZW50SW5mb1N0YXRzOiAoKSA9PlxuICAgIGFwaUNsaWVudC5nZXQ8UGF5bWVudEluZm9TdGF0cz4oJy9vcGVyYXRpb25hbC1kYXRhL3BheW1lbnQtaW5mby1zdGF0cycpLFxuXG4gIGdldFBheW1lbnRJbmZvU3RhdHNCeURhdGVSYW5nZTogKHN0YXJ0RGF0ZTogc3RyaW5nLCBlbmREYXRlOiBzdHJpbmcpID0+XG4gICAgYXBpQ2xpZW50LmdldDxQYXltZW50SW5mb1N0YXRzPihgL29wZXJhdGlvbmFsLWRhdGEvcGF5bWVudC1pbmZvLXN0YXRzLyR7c3RhcnREYXRlfS8ke2VuZERhdGV9YCksXG5cbiAgLy8g5pWw5o2u5YiG5p6QXG4gIGdldENvbnZlcnNpb25BbmFseXNpczogKGRhdGU6IHN0cmluZykgPT5cbiAgICBhcGlDbGllbnQuZ2V0PENvbnZlcnNpb25BbmFseXNpcz4oYC9vcGVyYXRpb25hbC1kYXRhL2NvbnZlcnNpb24tYW5hbHlzaXMvJHtkYXRlfWApLFxuXG4gIGdldFJldGVudGlvbkFuYWx5c2lzOiAoZGF0ZTogc3RyaW5nKSA9PlxuICAgIGFwaUNsaWVudC5nZXQ8UmV0ZW50aW9uQW5hbHlzaXM+KGAvb3BlcmF0aW9uYWwtZGF0YS9yZXRlbnRpb24tYW5hbHlzaXMvJHtkYXRlfWApLFxuXG4gIGdldEFjdGl2ZVVzZXJBbmFseXNpczogKGRhdGU6IHN0cmluZykgPT5cbiAgICBhcGlDbGllbnQuZ2V0PEFjdGl2ZVVzZXJBbmFseXNpcz4oYC9vcGVyYXRpb25hbC1kYXRhL2FjdGl2ZS11c2VyLWFuYWx5c2lzLyR7ZGF0ZX1gKSxcblxuICAvLyDorrDlvZXmlbDmja5cbiAgcmVjb3JkVmlzaXQ6IChkYXRhOiB7IHJlZmVycmVyPzogc3RyaW5nOyBjaGFubmVsPzogc3RyaW5nOyBzZXJ2ZXJJZD86IG51bWJlciB9KSA9PlxuICAgIGFwaUNsaWVudC5wb3N0KCcvb3BlcmF0aW9uYWwtZGF0YS9yZWNvcmQtdmlzaXQnLCBkYXRhKSxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGFwaUNsaWVudDtcbiJdLCJuYW1lcyI6WyJheGlvcyIsIkFQSV9CQVNFX1VSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfVVJMIiwiYXBpQ2xpZW50IiwiY3JlYXRlIiwiYmFzZVVSTCIsInRpbWVvdXQiLCJoZWFkZXJzIiwiaW50ZXJjZXB0b3JzIiwicmVxdWVzdCIsInVzZSIsImNvbmZpZyIsInRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsIkF1dGhvcml6YXRpb24iLCJlcnJvciIsIlByb21pc2UiLCJyZWplY3QiLCJyZXNwb25zZSIsIm9yaWdpbmFsUmVxdWVzdCIsInN0YXR1cyIsIl9yZXRyeSIsInJlZnJlc2hUb2tlbiIsInBvc3QiLCJkYXRhIiwic2V0SXRlbSIsInJlZnJlc2hFcnJvciIsInJlbW92ZUl0ZW0iLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJhdXRoQXBpIiwibG9naW4iLCJsb2dvdXQiLCJyZWdpc3RlciIsImNoYW5nZVBhc3N3b3JkIiwiY3VycmVudFBhc3N3b3JkIiwibmV3UGFzc3dvcmQiLCJyZXNldFBhc3N3b3JkIiwiZW1haWwiLCJ1c2Vyc0FwaSIsImdldFVzZXJzIiwiZ2V0IiwiZ2V0VXNlciIsImlkIiwiZ2V0VXNlckJ5VXNlcm5hbWUiLCJ1c2VybmFtZSIsImNyZWF0ZVVzZXIiLCJ1cGRhdGVVc2VyIiwicHV0IiwiZGVsZXRlVXNlciIsImRlbGV0ZSIsImFjdGl2YXRlVXNlciIsImRlYWN0aXZhdGVVc2VyIiwiZ2V0VXNlcnNCeVJvbGUiLCJyb2xlIiwicGxheWVyc0FwaSIsImdldFBsYXllcnMiLCJwYWdlIiwicGFnZVNpemUiLCJnZXRQbGF5ZXIiLCJnZXRQbGF5ZXJCeUFjY291bnRJZCIsImFjY291bnRJZCIsInNlYXJjaFBsYXllcnMiLCJzZWFyY2hUZXJtIiwiZW5jb2RlVVJJQ29tcG9uZW50IiwiZ2V0UGxheWVyU3RhdHMiLCJnZXRUb3BQbGF5ZXJzQnlMZXZlbCIsImNvdW50IiwiZ2V0VmlwUGxheWVycyIsIm1pblZpcExldmVsIiwidXBkYXRlUGxheWVyIiwiYmFuUGxheWVyIiwiYmFubmVkVW50aWwiLCJyZWFzb24iLCJ1bmJhblBsYXllciIsIm9wZXJhdGlvbmFsRGF0YUFwaSIsImdldEdsb2JhbFN0YXRzIiwiZ2V0R2xvYmFsU3RhdHNCeURhdGUiLCJkYXRlIiwiZ2V0VXNlckluZm9TdGF0cyIsImdldFVzZXJJbmZvU3RhdHNCeURhdGVSYW5nZSIsInN0YXJ0RGF0ZSIsImVuZERhdGUiLCJnZXRQYXltZW50SW5mb1N0YXRzIiwiZ2V0UGF5bWVudEluZm9TdGF0c0J5RGF0ZVJhbmdlIiwiZ2V0Q29udmVyc2lvbkFuYWx5c2lzIiwiZ2V0UmV0ZW50aW9uQW5hbHlzaXMiLCJnZXRBY3RpdmVVc2VyQW5hbHlzaXMiLCJyZWNvcmRWaXNpdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});