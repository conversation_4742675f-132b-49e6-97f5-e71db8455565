"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   ROLES: () => (/* binding */ ROLES),\n/* harmony export */   checkPermission: () => (/* binding */ checkPermission),\n/* harmony export */   hasCustomerServicePermission: () => (/* binding */ hasCustomerServicePermission),\n/* harmony export */   hasPartnerPermission: () => (/* binding */ hasPartnerPermission),\n/* harmony export */   hasProductPermission: () => (/* binding */ hasProductPermission),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,ROLES,checkPermission,isAdmin,hasCustomerServicePermission,hasProductPermission,hasPartnerPermission auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // 检查本地存储中的用户信息\n            if (true) {\n                const storedUser = localStorage.getItem('user');\n                const accessToken = localStorage.getItem('accessToken');\n                if (storedUser && accessToken) {\n                    try {\n                        const parsedUser = JSON.parse(storedUser);\n                        setUser(parsedUser);\n                    } catch (error) {\n                        console.error('Error parsing stored user:', error);\n                        localStorage.removeItem('user');\n                        localStorage.removeItem('accessToken');\n                        localStorage.removeItem('refreshToken');\n                    }\n                }\n            }\n            setIsLoading(false);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (username, password)=>{\n        try {\n            setIsLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.login({\n                username,\n                password\n            });\n            const { token, user: userData } = response.data;\n            // 存储认证信息\n            localStorage.setItem('accessToken', token);\n            localStorage.setItem('user', JSON.stringify(userData));\n            // 如果响应中有刷新令牌，也存储它\n            if (response.data.refreshToken) {\n                localStorage.setItem('refreshToken', response.data.refreshToken);\n            }\n            setUser(userData);\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('登录成功');\n            return true;\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Login error:', error);\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || '登录失败，请检查用户名和密码';\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(errorMessage);\n            return false;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            if (true) {\n                const refreshToken = localStorage.getItem('refreshToken');\n                if (refreshToken) {\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.logout(refreshToken);\n                }\n            }\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            // 清除本地存储\n            if (true) {\n                localStorage.removeItem('accessToken');\n                localStorage.removeItem('refreshToken');\n                localStorage.removeItem('user');\n            }\n            setUser(null);\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('已退出登录');\n            // 重定向到登录页\n            window.location.href = '/login';\n        }\n    };\n    const hasRole = (role)=>{\n        var _user_roles;\n        return (user === null || user === void 0 ? void 0 : (_user_roles = user.roles) === null || _user_roles === void 0 ? void 0 : _user_roles.includes(role)) || false;\n    };\n    const hasAnyRole = (roles)=>{\n        return roles.some((role)=>hasRole(role));\n    };\n    const isAuthenticated = !!user;\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        login,\n        logout,\n        hasRole,\n        hasAnyRole\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/contexts/AuthContext.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AuthProvider, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\n_c = AuthProvider;\nconst useAuth = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// 角色常量\nconst ROLES = {\n    SYSTEM_ADMIN: 'SystemAdmin',\n    PRODUCT_MANAGER: 'ProductManager',\n    PRODUCT_SPECIALIST: 'ProductSpecialist',\n    PARTNER_MANAGER: 'PartnerManager',\n    PARTNER_SPECIALIST: 'PartnerSpecialist',\n    CUSTOMER_SERVICE_MANAGER: 'CustomerServiceManager',\n    CUSTOMER_SERVICE_SPECIALIST: 'CustomerServiceSpecialist',\n    VIEWER: 'Viewer'\n};\n// 权限检查工具函数\nconst checkPermission = (userRoles, requiredRoles)=>{\n    return requiredRoles.some((role)=>userRoles.includes(role));\n};\n// 管理员角色检查\nconst isAdmin = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER\n    ]);\n};\n// 客服权限检查\nconst hasCustomerServicePermission = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER,\n        ROLES.CUSTOMER_SERVICE_MANAGER,\n        ROLES.CUSTOMER_SERVICE_SPECIALIST\n    ]);\n};\n// 产品管理权限检查\nconst hasProductPermission = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER,\n        ROLES.PRODUCT_SPECIALIST\n    ]);\n};\n// 渠道管理权限检查\nconst hasPartnerPermission = (userRoles)=>{\n    return checkPermission(userRoles, [\n        ROLES.SYSTEM_ADMIN,\n        ROLES.PRODUCT_MANAGER,\n        ROLES.PARTNER_MANAGER,\n        ROLES.PARTNER_SPECIALIST\n    ]);\n};\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ })

});