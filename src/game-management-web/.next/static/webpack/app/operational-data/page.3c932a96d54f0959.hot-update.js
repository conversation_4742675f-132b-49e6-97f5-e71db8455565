"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/operational-data/page",{

/***/ "(app-pages-browser)/./src/app/operational-data/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/operational-data/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OperationalDataPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/date-picker/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tabs/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Card,Col,DatePicker,Progress,Row,Select,Statistic,Table,Tabs!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/progress/index.js\");\n/* harmony import */ var _barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DollarOutlined,EyeOutlined,LoginOutlined,ShoppingCartOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DollarOutlined,EyeOutlined,LoginOutlined,ShoppingCartOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=DollarOutlined,EyeOutlined,LoginOutlined,ShoppingCartOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DollarOutlined,EyeOutlined,LoginOutlined,ShoppingCartOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LoginOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DollarOutlined,EyeOutlined,LoginOutlined,ShoppingCartOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/TeamOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=DollarOutlined,EyeOutlined,LoginOutlined,ShoppingCartOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DollarOutlined,EyeOutlined,LoginOutlined,ShoppingCartOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/TrophyOutlined.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Auth/ProtectedRoute */ \"(app-pages-browser)/./src/components/Auth/ProtectedRoute.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst { RangePicker } = _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst { Option } = _barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nfunction OperationalDataPage() {\n    _s();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(dayjs__WEBPACK_IMPORTED_MODULE_4___default()().format('YYYY-MM-DD'));\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        dayjs__WEBPACK_IMPORTED_MODULE_4___default()().subtract(7, 'day').format('YYYY-MM-DD'),\n        dayjs__WEBPACK_IMPORTED_MODULE_4___default()().format('YYYY-MM-DD')\n    ]);\n    // 全局统计数据\n    const { data: globalStats, isLoading: globalStatsLoading, error: globalStatsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'globalStats'\n        ],\n        queryFn: {\n            \"OperationalDataPage.useQuery\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_2__.operationalDataApi.getGlobalStats()\n        }[\"OperationalDataPage.useQuery\"]\n    });\n    // 用户信息统计\n    const { data: userInfoStats, isLoading: userInfoLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'userInfoStats',\n            dateRange\n        ],\n        queryFn: {\n            \"OperationalDataPage.useQuery\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_2__.operationalDataApi.getUserInfoStatsByDateRange(dateRange[0], dateRange[1])\n        }[\"OperationalDataPage.useQuery\"]\n    });\n    // 付费信息统计\n    const { data: paymentInfoStats, isLoading: paymentInfoLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'paymentInfoStats',\n            dateRange\n        ],\n        queryFn: {\n            \"OperationalDataPage.useQuery\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_2__.operationalDataApi.getPaymentInfoStatsByDateRange(dateRange[0], dateRange[1])\n        }[\"OperationalDataPage.useQuery\"]\n    });\n    // 转化率分析\n    const { data: conversionAnalysis, isLoading: conversionLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'conversionAnalysis',\n            selectedDate\n        ],\n        queryFn: {\n            \"OperationalDataPage.useQuery\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_2__.operationalDataApi.getConversionAnalysis(selectedDate)\n        }[\"OperationalDataPage.useQuery\"]\n    });\n    // 留存率分析\n    const { data: retentionAnalysis, isLoading: retentionLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'retentionAnalysis',\n            selectedDate\n        ],\n        queryFn: {\n            \"OperationalDataPage.useQuery\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_2__.operationalDataApi.getRetentionAnalysis(selectedDate)\n        }[\"OperationalDataPage.useQuery\"]\n    });\n    // 活跃用户分析\n    const { data: activeUserAnalysis, isLoading: activeUserLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'activeUserAnalysis',\n            selectedDate\n        ],\n        queryFn: {\n            \"OperationalDataPage.useQuery\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_2__.operationalDataApi.getActiveUserAnalysis(selectedDate)\n        }[\"OperationalDataPage.useQuery\"]\n    });\n    const formatNumber = (num)=>{\n        if (num >= 1000000) {\n            return (num / 1000000).toFixed(1) + 'M';\n        }\n        if (num >= 1000) {\n            return (num / 1000).toFixed(1) + 'K';\n        }\n        return num.toString();\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('zh-CN', {\n            style: 'currency',\n            currency: 'CNY'\n        }).format(amount);\n    };\n    const formatPercentage = (rate)=>{\n        return (rate * 100).toFixed(2) + '%';\n    };\n    if (globalStatsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            requiredRoles: [\n                'SystemAdmin',\n                'ProductManager',\n                'ProductSpecialist'\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: \"加载失败\",\n                description: \"无法加载运营数据，请稍后重试\",\n                type: \"error\",\n                showIcon: true\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        requiredRoles: [\n            'SystemAdmin',\n            'ProductManager',\n            'ProductSpecialist'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                padding: '24px'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        marginBottom: '24px',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            children: \"运营数据\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '16px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    value: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(selectedDate),\n                                    onChange: (date)=>setSelectedDate((date === null || date === void 0 ? void 0 : date.format('YYYY-MM-DD')) || dayjs__WEBPACK_IMPORTED_MODULE_4___default()().format('YYYY-MM-DD')),\n                                    placeholder: \"选择分析日期\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RangePicker, {\n                                    value: [\n                                        dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange[0]),\n                                        dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange[1])\n                                    ],\n                                    onChange: (dates)=>{\n                                        if (dates && dates[0] && dates[1]) {\n                                            setDateRange([\n                                                dates[0].format('YYYY-MM-DD'),\n                                                dates[1].format('YYYY-MM-DD')\n                                            ]);\n                                        }\n                                    },\n                                    placeholder: [\n                                        '开始日期',\n                                        '结束日期'\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    defaultActiveKey: \"overview\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPane, {\n                            tab: \"数据概览\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    gutter: [\n                                        16,\n                                        16\n                                    ],\n                                    style: {\n                                        marginBottom: '24px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            md: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"总营收\",\n                                                    value: (globalStats === null || globalStats === void 0 ? void 0 : globalStats.totalRevenue) || 0,\n                                                    formatter: (value)=>formatCurrency(Number(value)),\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    loading: globalStatsLoading\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            md: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"总访问量\",\n                                                    value: (globalStats === null || globalStats === void 0 ? void 0 : globalStats.totalVisits) || 0,\n                                                    formatter: (value)=>formatNumber(Number(value)),\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    loading: globalStatsLoading\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            md: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"总注册数\",\n                                                    value: (globalStats === null || globalStats === void 0 ? void 0 : globalStats.totalRegistrations) || 0,\n                                                    formatter: (value)=>formatNumber(Number(value)),\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    loading: globalStatsLoading\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            md: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"总登录数\",\n                                                    value: (globalStats === null || globalStats === void 0 ? void 0 : globalStats.totalLogins) || 0,\n                                                    formatter: (value)=>formatNumber(Number(value)),\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    loading: globalStatsLoading\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    title: \"今日数据\",\n                                    style: {\n                                        marginBottom: '24px'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        gutter: [\n                                            16,\n                                            16\n                                        ],\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                xs: 24,\n                                                sm: 12,\n                                                md: 6,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"新增访问\",\n                                                    value: (globalStats === null || globalStats === void 0 ? void 0 : globalStats.todayNewVisits) || 0,\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    loading: globalStatsLoading\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                xs: 24,\n                                                sm: 12,\n                                                md: 6,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"新增注册\",\n                                                    value: (globalStats === null || globalStats === void 0 ? void 0 : globalStats.todayNewRegistrations) || 0,\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    loading: globalStatsLoading\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                xs: 24,\n                                                sm: 12,\n                                                md: 6,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"新增登录\",\n                                                    value: (globalStats === null || globalStats === void 0 ? void 0 : globalStats.todayNewLogins) || 0,\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    loading: globalStatsLoading\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                xs: 24,\n                                                sm: 12,\n                                                md: 6,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"活跃用户\",\n                                                    value: (globalStats === null || globalStats === void 0 ? void 0 : globalStats.todayActiveUsers) || 0,\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    loading: globalStatsLoading\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    gutter: [\n                                        16,\n                                        16\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                title: \"收入指标\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"ARPU (平均每用户收入)\",\n                                                    value: (globalStats === null || globalStats === void 0 ? void 0 : globalStats.arpu) || 0,\n                                                    formatter: (value)=>formatCurrency(Number(value)),\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    loading: globalStatsLoading\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                title: \"在线指标\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    gutter: 16,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            span: 12,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                title: \"平均在线\",\n                                                                value: (globalStats === null || globalStats === void 0 ? void 0 : globalStats.averageOnlineUsers) || 0,\n                                                                formatter: (value)=>formatNumber(Number(value)),\n                                                                loading: globalStatsLoading\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            span: 12,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                title: \"最高在线\",\n                                                                value: (globalStats === null || globalStats === void 0 ? void 0 : globalStats.maxOnlineUsers) || 0,\n                                                                formatter: (value)=>formatNumber(Number(value)),\n                                                                loading: globalStatsLoading\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, \"overview\", true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPane, {\n                            tab: \"用户分析\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                gutter: [\n                                    16,\n                                    16\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        xs: 24,\n                                        lg: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            title: \"用户统计\",\n                                            loading: userInfoLoading,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                gutter: [\n                                                    16,\n                                                    16\n                                                ],\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        span: 12,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            title: \"总访问量\",\n                                                            value: (userInfoStats === null || userInfoStats === void 0 ? void 0 : userInfoStats.totalVisits) || 0,\n                                                            formatter: (value)=>formatNumber(Number(value))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        span: 12,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            title: \"独立访问\",\n                                                            value: (userInfoStats === null || userInfoStats === void 0 ? void 0 : userInfoStats.uniqueVisits) || 0,\n                                                            formatter: (value)=>formatNumber(Number(value))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        span: 12,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            title: \"总注册数\",\n                                                            value: (userInfoStats === null || userInfoStats === void 0 ? void 0 : userInfoStats.totalRegistrations) || 0,\n                                                            formatter: (value)=>formatNumber(Number(value))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        span: 12,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            title: \"当前在线\",\n                                                            value: (userInfoStats === null || userInfoStats === void 0 ? void 0 : userInfoStats.currentOnlineUsers) || 0,\n                                                            formatter: (value)=>formatNumber(Number(value))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        xs: 24,\n                                        lg: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            title: \"用户状态\",\n                                            loading: userInfoLoading,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                gutter: [\n                                                    16,\n                                                    16\n                                                ],\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        span: 12,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            title: \"未创角用户\",\n                                                            value: (userInfoStats === null || userInfoStats === void 0 ? void 0 : userInfoStats.usersWithoutCharacter) || 0,\n                                                            formatter: (value)=>formatNumber(Number(value))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        span: 12,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            title: \"未登录用户\",\n                                                            value: (userInfoStats === null || userInfoStats === void 0 ? void 0 : userInfoStats.usersNeverLoggedIn) || 0,\n                                                            formatter: (value)=>formatNumber(Number(value))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        span: 12,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            title: \"同IP登录\",\n                                                            value: (userInfoStats === null || userInfoStats === void 0 ? void 0 : userInfoStats.sameIpLogins) || 0,\n                                                            formatter: (value)=>formatNumber(Number(value))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        span: 12,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            title: \"渠道注册\",\n                                                            value: (userInfoStats === null || userInfoStats === void 0 ? void 0 : userInfoStats.registrationsWithChannel) || 0,\n                                                            formatter: (value)=>formatNumber(Number(value))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this)\n                        }, \"user-analysis\", false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPane, {\n                            tab: \"付费分析\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    gutter: [\n                                        16,\n                                        16\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            lg: 16,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                title: \"付费统计\",\n                                                loading: paymentInfoLoading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    gutter: [\n                                                        16,\n                                                        16\n                                                    ],\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            xs: 24,\n                                                            sm: 12,\n                                                            md: 6,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                title: \"总营收\",\n                                                                value: (paymentInfoStats === null || paymentInfoStats === void 0 ? void 0 : paymentInfoStats.totalRevenue) || 0,\n                                                                formatter: (value)=>formatCurrency(Number(value)),\n                                                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 33\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            xs: 24,\n                                                            sm: 12,\n                                                            md: 6,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                title: \"总订单\",\n                                                                value: (paymentInfoStats === null || paymentInfoStats === void 0 ? void 0 : paymentInfoStats.totalOrders) || 0,\n                                                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 33\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            xs: 24,\n                                                            sm: 12,\n                                                            md: 6,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                title: \"成功订单\",\n                                                                value: (paymentInfoStats === null || paymentInfoStats === void 0 ? void 0 : paymentInfoStats.completedOrders) || 0,\n                                                                valueStyle: {\n                                                                    color: '#3f8600'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            xs: 24,\n                                                            sm: 12,\n                                                            md: 6,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                title: \"失败订单\",\n                                                                value: (paymentInfoStats === null || paymentInfoStats === void 0 ? void 0 : paymentInfoStats.failedOrders) || 0,\n                                                                valueStyle: {\n                                                                    color: '#cf1322'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                title: \"平均订单价值\",\n                                                loading: paymentInfoLoading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    value: (paymentInfoStats === null || paymentInfoStats === void 0 ? void 0 : paymentInfoStats.averageOrderValue) || 0,\n                                                    formatter: (value)=>formatCurrency(Number(value)),\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 29\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    title: \"付费排行榜\",\n                                    style: {\n                                        marginTop: '16px'\n                                    },\n                                    loading: paymentInfoLoading,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        dataSource: (paymentInfoStats === null || paymentInfoStats === void 0 ? void 0 : paymentInfoStats.topPayers) || [],\n                                        pagination: false,\n                                        size: \"small\",\n                                        columns: [\n                                            {\n                                                title: '排名',\n                                                key: 'rank',\n                                                render: (_, __, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: [\n                                                            index + 1 <= 3 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                style: {\n                                                                    color: [\n                                                                        '#FFD700',\n                                                                        '#C0C0C0',\n                                                                        '#CD7F32'\n                                                                    ][index]\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 43\n                                                            }, void 0) : null,\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                width: 80\n                                            },\n                                            {\n                                                title: '账号ID',\n                                                dataIndex: 'accountId',\n                                                key: 'accountId'\n                                            },\n                                            {\n                                                title: '昵称',\n                                                dataIndex: 'nickname',\n                                                key: 'nickname'\n                                            },\n                                            {\n                                                title: '总金额',\n                                                dataIndex: 'totalAmount',\n                                                key: 'totalAmount',\n                                                render: (amount)=>formatCurrency(amount)\n                                            },\n                                            {\n                                                title: '订单数',\n                                                dataIndex: 'orderCount',\n                                                key: 'orderCount'\n                                            },\n                                            {\n                                                title: '最后付费时间',\n                                                dataIndex: 'lastPaymentTime',\n                                                key: 'lastPaymentTime',\n                                                render: (time)=>dayjs__WEBPACK_IMPORTED_MODULE_4___default()(time).format('YYYY-MM-DD HH:mm')\n                                            }\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, \"payment-analysis\", true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPane, {\n                            tab: \"转化分析\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    gutter: [\n                                        16,\n                                        16\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                title: \"转化率分析\",\n                                                loading: conversionLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    justifyContent: 'space-between',\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"访问-注册转化率\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatPercentage((conversionAnalysis === null || conversionAnalysis === void 0 ? void 0 : conversionAnalysis.visitToRegistrationRate) || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                percent: ((conversionAnalysis === null || conversionAnalysis === void 0 ? void 0 : conversionAnalysis.visitToRegistrationRate) || 0) * 100,\n                                                                showInfo: false\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    justifyContent: 'space-between',\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"注册-付费转化率\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 433,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatPercentage((conversionAnalysis === null || conversionAnalysis === void 0 ? void 0 : conversionAnalysis.registrationToPaymentRate) || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                percent: ((conversionAnalysis === null || conversionAnalysis === void 0 ? void 0 : conversionAnalysis.registrationToPaymentRate) || 0) * 100,\n                                                                showInfo: false\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    justifyContent: 'space-between',\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"注册-创角转化率\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 440,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatPercentage((conversionAnalysis === null || conversionAnalysis === void 0 ? void 0 : conversionAnalysis.registrationToCharacterCreationRate) || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                percent: ((conversionAnalysis === null || conversionAnalysis === void 0 ? void 0 : conversionAnalysis.registrationToCharacterCreationRate) || 0) * 100,\n                                                                showInfo: false\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                title: \"留存率分析\",\n                                                loading: retentionLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    justifyContent: 'space-between',\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"次日留存率\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 451,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatPercentage((retentionAnalysis === null || retentionAnalysis === void 0 ? void 0 : retentionAnalysis.nextDayRetentionRate) || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 452,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                percent: ((retentionAnalysis === null || retentionAnalysis === void 0 ? void 0 : retentionAnalysis.nextDayRetentionRate) || 0) * 100,\n                                                                showInfo: false,\n                                                                strokeColor: \"#52c41a\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    justifyContent: 'space-between',\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"七日留存率\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatPercentage((retentionAnalysis === null || retentionAnalysis === void 0 ? void 0 : retentionAnalysis.sevenDayRetentionRate) || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                percent: ((retentionAnalysis === null || retentionAnalysis === void 0 ? void 0 : retentionAnalysis.sevenDayRetentionRate) || 0) * 100,\n                                                                showInfo: false,\n                                                                strokeColor: \"#1890ff\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    justifyContent: 'space-between',\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"月留存率\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatPercentage((retentionAnalysis === null || retentionAnalysis === void 0 ? void 0 : retentionAnalysis.monthlyRetentionRate) || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                        lineNumber: 466,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                percent: ((retentionAnalysis === null || retentionAnalysis === void 0 ? void 0 : retentionAnalysis.monthlyRetentionRate) || 0) * 100,\n                                                                showInfo: false,\n                                                                strokeColor: \"#722ed1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    title: \"活跃用户分析\",\n                                    style: {\n                                        marginTop: '16px'\n                                    },\n                                    loading: activeUserLoading,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        gutter: [\n                                            16,\n                                            16\n                                        ],\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                xs: 24,\n                                                sm: 8,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"日活跃用户 (DAU)\",\n                                                    value: (activeUserAnalysis === null || activeUserAnalysis === void 0 ? void 0 : activeUserAnalysis.dau) || 0,\n                                                    formatter: (value)=>formatNumber(Number(value)),\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 29\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                xs: 24,\n                                                sm: 8,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"周活跃用户 (WAU)\",\n                                                    value: (activeUserAnalysis === null || activeUserAnalysis === void 0 ? void 0 : activeUserAnalysis.wau) || 0,\n                                                    formatter: (value)=>formatNumber(Number(value)),\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 29\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                xs: 24,\n                                                sm: 8,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Card_Col_DatePicker_Progress_Row_Select_Statistic_Table_Tabs_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"月活跃用户 (MAU)\",\n                                                    value: (activeUserAnalysis === null || activeUserAnalysis === void 0 ? void 0 : activeUserAnalysis.mau) || 0,\n                                                    formatter: (value)=>formatNumber(Number(value)),\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_EyeOutlined_LoginOutlined_ShoppingCartOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 29\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, \"conversion-analysis\", true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/operational-data/page.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\n_s(OperationalDataPage, \"WCaX/pISMLAkCkkBp7Czn1q+ZvY=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery\n    ];\n});\n_c = OperationalDataPage;\nvar _c;\n$RefreshReg$(_c, \"OperationalDataPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/operational-data/page.tsx\n"));

/***/ })

});