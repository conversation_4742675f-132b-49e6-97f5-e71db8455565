"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reports/page",{

/***/ "(app-pages-browser)/./src/app/reports/page.tsx":
/*!**********************************!*\
  !*** ./src/app/reports/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Auth/ProtectedRoute */ \"(app-pages-browser)/./src/components/Auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst ReportsPage = ()=>{\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [reportType, setReportType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"revenue\");\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"week\");\n    // 模拟报表数据\n    const revenueData = [\n        {\n            date: \"2024-06-19\",\n            revenue: 125600,\n            orders: 1256,\n            avgOrder: 100\n        },\n        {\n            date: \"2024-06-20\",\n            revenue: 134200,\n            orders: 1342,\n            avgOrder: 100\n        },\n        {\n            date: \"2024-06-21\",\n            revenue: 145800,\n            orders: 1458,\n            avgOrder: 100\n        },\n        {\n            date: \"2024-06-22\",\n            revenue: 156900,\n            orders: 1569,\n            avgOrder: 100\n        },\n        {\n            date: \"2024-06-23\",\n            revenue: 167300,\n            orders: 1673,\n            avgOrder: 100\n        },\n        {\n            date: \"2024-06-24\",\n            revenue: 178500,\n            orders: 1785,\n            avgOrder: 100\n        },\n        {\n            date: \"2024-06-25\",\n            revenue: 189200,\n            orders: 1892,\n            avgOrder: 100\n        }\n    ];\n    const playerData = [\n        {\n            date: \"2024-06-19\",\n            newUsers: 456,\n            activeUsers: 12500,\n            retention: 68.5\n        },\n        {\n            date: \"2024-06-20\",\n            newUsers: 523,\n            activeUsers: 13200,\n            retention: 69.2\n        },\n        {\n            date: \"2024-06-21\",\n            newUsers: 612,\n            activeUsers: 13800,\n            retention: 70.1\n        },\n        {\n            date: \"2024-06-22\",\n            newUsers: 589,\n            activeUsers: 14200,\n            retention: 71.3\n        },\n        {\n            date: \"2024-06-23\",\n            newUsers: 634,\n            activeUsers: 14600,\n            retention: 72.0\n        },\n        {\n            date: \"2024-06-24\",\n            newUsers: 678,\n            activeUsers: 15100,\n            retention: 72.8\n        },\n        {\n            date: \"2024-06-25\",\n            newUsers: 712,\n            activeUsers: 15600,\n            retention: 73.5\n        }\n    ];\n    const serverData = [\n        {\n            server: \"服务器1\",\n            players: 1256,\n            cpu: 45.2,\n            memory: 68.5,\n            uptime: 99.8\n        },\n        {\n            server: \"服务器2\",\n            players: 1890,\n            cpu: 85.7,\n            memory: 92.3,\n            uptime: 98.5\n        },\n        {\n            server: \"服务器3\",\n            players: 0,\n            cpu: 0,\n            memory: 0,\n            uptime: 0\n        },\n        {\n            server: \"服务器4\",\n            players: 0,\n            cpu: 0,\n            memory: 0,\n            uptime: 95.2\n        }\n    ];\n    const getReportColumns = ()=>{\n        switch(reportType){\n            case \"revenue\":\n                return [\n                    {\n                        title: \"日期\",\n                        dataIndex: \"date\",\n                        key: \"date\"\n                    },\n                    {\n                        title: \"收入 (元)\",\n                        dataIndex: \"revenue\",\n                        key: \"revenue\",\n                        render: (value)=>\"\\xa5\".concat(value.toLocaleString())\n                    },\n                    {\n                        title: \"订单数\",\n                        dataIndex: \"orders\",\n                        key: \"orders\",\n                        render: (value)=>value.toLocaleString()\n                    },\n                    {\n                        title: \"客单价 (元)\",\n                        dataIndex: \"avgOrder\",\n                        key: \"avgOrder\",\n                        render: (value)=>\"\\xa5\".concat(value)\n                    }\n                ];\n            case \"players\":\n                return [\n                    {\n                        title: \"日期\",\n                        dataIndex: \"date\",\n                        key: \"date\"\n                    },\n                    {\n                        title: \"新增用户\",\n                        dataIndex: \"newUsers\",\n                        key: \"newUsers\",\n                        render: (value)=>value.toLocaleString()\n                    },\n                    {\n                        title: \"活跃用户\",\n                        dataIndex: \"activeUsers\",\n                        key: \"activeUsers\",\n                        render: (value)=>value.toLocaleString()\n                    },\n                    {\n                        title: \"留存率 (%)\",\n                        dataIndex: \"retention\",\n                        key: \"retention\",\n                        render: (value)=>\"\".concat(value, \"%\")\n                    }\n                ];\n            case \"servers\":\n                return [\n                    {\n                        title: \"服务器\",\n                        dataIndex: \"server\",\n                        key: \"server\"\n                    },\n                    {\n                        title: \"在线玩家\",\n                        dataIndex: \"players\",\n                        key: \"players\",\n                        render: (value)=>value.toLocaleString()\n                    },\n                    {\n                        title: \"CPU使用率\",\n                        dataIndex: \"cpu\",\n                        key: \"cpu\",\n                        render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            value,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                        percent: value,\n                                        size: \"small\",\n                                        showInfo: false\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, undefined)\n                    },\n                    {\n                        title: \"内存使用率\",\n                        dataIndex: \"memory\",\n                        key: \"memory\",\n                        render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            value,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                        percent: value,\n                                        size: \"small\",\n                                        showInfo: false\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 15\n                            }, undefined)\n                    },\n                    {\n                        title: \"可用性 (%)\",\n                        dataIndex: \"uptime\",\n                        key: \"uptime\",\n                        render: (value)=>\"\".concat(value, \"%\")\n                    }\n                ];\n            default:\n                return [];\n        }\n    };\n    const getReportData = ()=>{\n        switch(reportType){\n            case \"revenue\":\n                return revenueData;\n            case \"players\":\n                return playerData;\n            case \"servers\":\n                return serverData;\n            default:\n                return [];\n        }\n    };\n    const getStatistics = ()=>{\n        switch(reportType){\n            case \"revenue\":\n                const totalRevenue = revenueData.reduce((sum, item)=>sum + item.revenue, 0);\n                const totalOrders = revenueData.reduce((sum, item)=>sum + item.orders, 0);\n                const avgRevenue = totalRevenue / revenueData.length;\n                return [\n                    {\n                        title: \"总收入\",\n                        value: totalRevenue,\n                        prefix: \"\\xa5\",\n                        suffix: \"\",\n                        color: \"#3f8600\"\n                    },\n                    {\n                        title: \"总订单数\",\n                        value: totalOrders,\n                        prefix: \"\",\n                        suffix: \"\",\n                        color: \"#1890ff\"\n                    },\n                    {\n                        title: \"日均收入\",\n                        value: Math.round(avgRevenue),\n                        prefix: \"\\xa5\",\n                        suffix: \"\",\n                        color: \"#722ed1\"\n                    },\n                    {\n                        title: \"平均客单价\",\n                        value: Math.round(totalRevenue / totalOrders),\n                        prefix: \"\\xa5\",\n                        suffix: \"\",\n                        color: \"#fa8c16\"\n                    }\n                ];\n            case \"players\":\n                const totalNewUsers = playerData.reduce((sum, item)=>sum + item.newUsers, 0);\n                const avgActiveUsers = Math.round(playerData.reduce((sum, item)=>sum + item.activeUsers, 0) / playerData.length);\n                const avgRetention = (playerData.reduce((sum, item)=>sum + item.retention, 0) / playerData.length).toFixed(1);\n                return [\n                    {\n                        title: \"总新增用户\",\n                        value: totalNewUsers,\n                        prefix: \"\",\n                        suffix: \"\",\n                        color: \"#3f8600\"\n                    },\n                    {\n                        title: \"平均活跃用户\",\n                        value: avgActiveUsers,\n                        prefix: \"\",\n                        suffix: \"\",\n                        color: \"#1890ff\"\n                    },\n                    {\n                        title: \"平均留存率\",\n                        value: parseFloat(avgRetention),\n                        prefix: \"\",\n                        suffix: \"%\",\n                        color: \"#722ed1\"\n                    },\n                    {\n                        title: \"日均新增\",\n                        value: Math.round(totalNewUsers / playerData.length),\n                        prefix: \"\",\n                        suffix: \"\",\n                        color: \"#fa8c16\"\n                    }\n                ];\n            case \"servers\":\n                const onlineServers = serverData.filter((s)=>s.uptime > 0).length;\n                const totalPlayers = serverData.reduce((sum, item)=>sum + item.players, 0);\n                const avgCpu = (serverData.reduce((sum, item)=>sum + item.cpu, 0) / serverData.length).toFixed(1);\n                const avgUptime = (serverData.reduce((sum, item)=>sum + item.uptime, 0) / serverData.length).toFixed(1);\n                return [\n                    {\n                        title: \"在线服务器\",\n                        value: onlineServers,\n                        prefix: \"\",\n                        suffix: \"/\".concat(serverData.length),\n                        color: \"#3f8600\"\n                    },\n                    {\n                        title: \"总在线玩家\",\n                        value: totalPlayers,\n                        prefix: \"\",\n                        suffix: \"\",\n                        color: \"#1890ff\"\n                    },\n                    {\n                        title: \"平均CPU使用率\",\n                        value: parseFloat(avgCpu),\n                        prefix: \"\",\n                        suffix: \"%\",\n                        color: \"#722ed1\"\n                    },\n                    {\n                        title: \"平均可用性\",\n                        value: parseFloat(avgUptime),\n                        prefix: \"\",\n                        suffix: \"%\",\n                        color: \"#fa8c16\"\n                    }\n                ];\n            default:\n                return [];\n        }\n    };\n    const handleExport = ()=>{\n        // 模拟导出功能\n        setLoading(true);\n        setTimeout(()=>{\n            setLoading(false);\n            // 这里可以实现真实的导出逻辑\n            const link = document.createElement(\"a\");\n            link.href = \"data:text/csv;charset=utf-8,\";\n            link.download = \"\".concat(reportType, \"_report_\").concat(new Date().toISOString().split(\"T\")[0], \".csv\");\n            // link.click();\n            console.log(\"导出报表:\", reportType);\n        }, 1000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        requiredRoles: [\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.ROLES.SYSTEM_ADMIN,\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.ROLES.PRODUCT_MANAGER\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        justifyContent: \"space-between\",\n                        alignItems: \"center\",\n                        marginBottom: 24\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                            level: 2,\n                            children: \"数据报表\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Space, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"primary\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileExcelOutlined, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    loading: loading,\n                                    onClick: handleExport,\n                                    children: \"导出Excel\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DownloadOutlined, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 27\n                                    }, void 0),\n                                    children: \"导出PDF\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    style: {\n                        marginBottom: 16\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                        gutter: [\n                            16,\n                            16\n                        ],\n                        align: \"middle\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                xs: 24,\n                                sm: 8,\n                                lg: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                    placeholder: \"报表类型\",\n                                    style: {\n                                        width: \"100%\"\n                                    },\n                                    value: reportType,\n                                    onChange: setReportType,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select.Option, {\n                                            value: \"revenue\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BarChartOutlined, {\n                                                    style: {\n                                                        marginRight: 8\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"收入报表\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select.Option, {\n                                            value: \"players\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LineChartOutlined, {\n                                                    style: {\n                                                        marginRight: 8\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"玩家报表\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select.Option, {\n                                            value: \"servers\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PieChartOutlined, {\n                                                    style: {\n                                                        marginRight: 8\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"服务器报表\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                xs: 24,\n                                sm: 8,\n                                lg: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                    placeholder: \"时间范围\",\n                                    style: {\n                                        width: \"100%\"\n                                    },\n                                    value: dateRange,\n                                    onChange: setDateRange,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select.Option, {\n                                            value: \"today\",\n                                            children: \"今天\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select.Option, {\n                                            value: \"week\",\n                                            children: \"本周\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select.Option, {\n                                            value: \"month\",\n                                            children: \"本月\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select.Option, {\n                                            value: \"quarter\",\n                                            children: \"本季度\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select.Option, {\n                                            value: \"year\",\n                                            children: \"本年\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                                xs: 24,\n                                sm: 8,\n                                lg: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RangePicker, {\n                                    style: {\n                                        width: \"100%\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                    gutter: [\n                        16,\n                        16\n                    ],\n                    style: {\n                        marginBottom: 24\n                    },\n                    children: getStatistics().map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                            xs: 24,\n                            sm: 12,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                    title: stat.title,\n                                    value: stat.value,\n                                    prefix: stat.prefix,\n                                    suffix: stat.suffix,\n                                    valueStyle: {\n                                        color: stat.color\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 15\n                            }, undefined)\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Space, {\n                        children: [\n                            reportType === \"revenue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BarChartOutlined, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 44\n                            }, void 0),\n                            reportType === \"players\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LineChartOutlined, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 44\n                            }, void 0),\n                            reportType === \"servers\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PieChartOutlined, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 44\n                            }, void 0),\n                            reportType === \"revenue\" && \"收入明细\",\n                            reportType === \"players\" && \"玩家数据\",\n                            reportType === \"servers\" && \"服务器状态\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 13\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                        columns: getReportColumns(),\n                        dataSource: getReportData(),\n                        loading: loading,\n                        pagination: {\n                            pageSize: 10,\n                            showSizeChanger: true,\n                            showQuickJumper: true,\n                            showTotal: (total, range)=>\"第 \".concat(range[0], \"-\").concat(range[1], \" 条/共 \").concat(total, \" 条\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n            lineNumber: 209,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/reports/page.tsx\",\n        lineNumber: 208,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ReportsPage, \"qVS5BwuPiQoNd5GPKjKx0IZhYqU=\");\n_c = ReportsPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ReportsPage);\nvar _c;\n$RefreshReg$(_c, \"ReportsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/reports/page.tsx\n"));

/***/ })

});