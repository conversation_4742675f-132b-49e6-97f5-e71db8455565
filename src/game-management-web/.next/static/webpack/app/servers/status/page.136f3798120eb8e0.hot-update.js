"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/servers/status/page",{

/***/ "(app-pages-browser)/./src/app/servers/status/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/servers/status/page.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Auth/ProtectedRoute */ \"(app-pages-browser)/./src/components/Auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ServerStatusPage = ()=>{\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 模拟服务器状态数据\n    const serversData = [\n        {\n            key: \"1\",\n            id: 1,\n            name: \"服务器1\",\n            region: \"华东\",\n            ip: \"*************\",\n            port: 8080,\n            status: \"online\",\n            playerCount: 1256,\n            maxPlayers: 2000,\n            cpuUsage: 45.2,\n            memoryUsage: 68.5,\n            diskUsage: 32.1,\n            networkIn: 125.6,\n            networkOut: 89.3,\n            uptime: \"15天3小时25分钟\",\n            version: \"2.5.1\",\n            lastUpdate: \"2024-06-25 14:30:25\",\n            maintenance: false\n        },\n        {\n            key: \"2\",\n            id: 2,\n            name: \"服务器2\",\n            region: \"华南\",\n            ip: \"*************\",\n            port: 8080,\n            status: \"warning\",\n            playerCount: 1890,\n            maxPlayers: 2000,\n            cpuUsage: 85.7,\n            memoryUsage: 92.3,\n            diskUsage: 78.9,\n            networkIn: 256.8,\n            networkOut: 198.7,\n            uptime: \"8天12小时15分钟\",\n            version: \"2.5.1\",\n            lastUpdate: \"2024-06-25 14:28:12\",\n            maintenance: false\n        },\n        {\n            key: \"3\",\n            id: 3,\n            name: \"服务器3\",\n            region: \"华北\",\n            ip: \"*************\",\n            port: 8080,\n            status: \"maintenance\",\n            playerCount: 0,\n            maxPlayers: 2000,\n            cpuUsage: 0,\n            memoryUsage: 0,\n            diskUsage: 45.6,\n            networkIn: 0,\n            networkOut: 0,\n            uptime: \"维护中\",\n            version: \"2.5.0\",\n            lastUpdate: \"2024-06-25 02:00:00\",\n            maintenance: true\n        },\n        {\n            key: \"4\",\n            id: 4,\n            name: \"服务器4\",\n            region: \"华西\",\n            ip: \"*************\",\n            port: 8080,\n            status: \"offline\",\n            playerCount: 0,\n            maxPlayers: 2000,\n            cpuUsage: 0,\n            memoryUsage: 0,\n            diskUsage: 0,\n            networkIn: 0,\n            networkOut: 0,\n            uptime: \"离线\",\n            version: \"2.4.8\",\n            lastUpdate: \"2024-06-24 18:45:30\",\n            maintenance: false\n        }\n    ];\n    const getStatusTag = (status)=>{\n        switch(status){\n            case \"online\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"success\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckCircleOutlined, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 43\n                    }, void 0),\n                    children: \"在线\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 16\n                }, undefined);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"warning\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ExclamationCircleOutlined, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 43\n                    }, void 0),\n                    children: \"警告\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 16\n                }, undefined);\n            case \"maintenance\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"processing\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingOutlined, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 46\n                    }, void 0),\n                    children: \"维护中\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 16\n                }, undefined);\n            case \"offline\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"error\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CloseCircleOutlined, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 41\n                    }, void 0),\n                    children: \"离线\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                    color: \"default\",\n                    children: \"未知\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getUsageColor = (usage)=>{\n        if (usage >= 90) return \"#ff4d4f\";\n        if (usage >= 70) return \"#faad14\";\n        return \"#52c41a\";\n    };\n    const columns = [\n        {\n            title: \"服务器\",\n            key: \"server\",\n            width: 200,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontWeight: 500,\n                                fontSize: \"14px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CloudServerOutlined, {\n                                    style: {\n                                        marginRight: 8\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, undefined),\n                                record.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"12px\",\n                                color: \"#666\",\n                                marginTop: 4\n                            },\n                            children: [\n                                record.region,\n                                \" | \",\n                                record.ip,\n                                \":\",\n                                record.port\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"状态\",\n            key: \"status\",\n            width: 120,\n            render: (record)=>getStatusTag(record.status)\n        },\n        {\n            title: \"在线玩家\",\n            key: \"players\",\n            width: 150,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"14px\",\n                                fontWeight: 500\n                            },\n                            children: [\n                                record.playerCount.toLocaleString(),\n                                \" / \",\n                                record.maxPlayers.toLocaleString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                            percent: Math.round(record.playerCount / record.maxPlayers * 100),\n                            size: \"small\",\n                            showInfo: false,\n                            strokeColor: record.playerCount / record.maxPlayers > 0.9 ? \"#ff4d4f\" : \"#1890ff\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"CPU使用率\",\n            key: \"cpu\",\n            width: 120,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"12px\",\n                                marginBottom: 4\n                            },\n                            children: [\n                                record.cpuUsage,\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                            percent: record.cpuUsage,\n                            size: \"small\",\n                            showInfo: false,\n                            strokeColor: getUsageColor(record.cpuUsage)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"内存使用率\",\n            key: \"memory\",\n            width: 120,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"12px\",\n                                marginBottom: 4\n                            },\n                            children: [\n                                record.memoryUsage,\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                            percent: record.memoryUsage,\n                            size: \"small\",\n                            showInfo: false,\n                            strokeColor: getUsageColor(record.memoryUsage)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"磁盘使用率\",\n            key: \"disk\",\n            width: 120,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"12px\",\n                                marginBottom: 4\n                            },\n                            children: [\n                                record.diskUsage,\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                            percent: record.diskUsage,\n                            size: \"small\",\n                            showInfo: false,\n                            strokeColor: getUsageColor(record.diskUsage)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"网络流量\",\n            key: \"network\",\n            width: 120,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        fontSize: \"12px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"↓ \",\n                                record.networkIn,\n                                \" MB/s\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"↑ \",\n                                record.networkOut,\n                                \" MB/s\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"运行时间\",\n            dataIndex: \"uptime\",\n            key: \"uptime\",\n            width: 150\n        },\n        {\n            title: \"版本\",\n            dataIndex: \"version\",\n            key: \"version\",\n            width: 100\n        },\n        {\n            title: \"操作\",\n            key: \"action\",\n            width: 200,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Space, {\n                    size: \"small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tooltip, {\n                            title: \"查看详情\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                type: \"link\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EyeOutlined, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: ()=>handleViewDetail(record)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tooltip, {\n                            title: \"重启服务器\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                type: \"link\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReloadOutlined, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: ()=>handleRestart(record),\n                                disabled: record.status === \"offline\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tooltip, {\n                            title: \"服务器设置\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                type: \"link\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingOutlined, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: ()=>handleSettings(record)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ];\n    const handleViewDetail = (record)=>{\n        Modal.info({\n            title: \"\".concat(record.name, \" 详细信息\"),\n            width: 600,\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                    gutter: [\n                        16,\n                        16\n                    ],\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                            span: 12,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                size: \"small\",\n                                title: \"基本信息\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"服务器名称：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"地区：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.region\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"IP地址：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.ip,\n                                            \":\",\n                                            record.port\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"版本：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.version\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"状态：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            getStatusTag(record.status)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                            span: 12,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                size: \"small\",\n                                title: \"性能指标\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"CPU使用率：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.cpuUsage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"内存使用率：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.memoryUsage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"磁盘使用率：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.diskUsage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"运行时间：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.uptime\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                            span: 12,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                size: \"small\",\n                                title: \"玩家信息\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"在线玩家：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.playerCount\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"最大容量：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.maxPlayers\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"负载率：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            Math.round(record.playerCount / record.maxPlayers * 100),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                            span: 12,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                size: \"small\",\n                                title: \"网络流量\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"入站流量：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.networkIn,\n                                            \" MB/s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"出站流量：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.networkOut,\n                                            \" MB/s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"最后更新：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 20\n                                            }, undefined),\n                                            record.lastUpdate\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                lineNumber: 284,\n                columnNumber: 9\n            }, undefined)\n        });\n    };\n    const handleRestart = (record)=>{\n        Modal.confirm({\n            title: \"确认重启\",\n            content: '确定要重启 \"'.concat(record.name, '\" 吗？这将断开所有在线玩家的连接。'),\n            onOk () {\n                message.success(\"正在重启 \".concat(record.name, \"...\"));\n            }\n        });\n    };\n    const handleSettings = (record)=>{\n        message.info(\"打开 \".concat(record.name, \" 的设置页面\"));\n    };\n    const handleRefresh = async ()=>{\n        setRefreshing(true);\n        // 模拟刷新延迟\n        setTimeout(()=>{\n            setRefreshing(false);\n            message.success(\"服务器状态已刷新\");\n        }, 1000);\n    };\n    // 计算统计数据\n    const onlineServers = serversData.filter((s)=>s.status === \"online\").length;\n    const warningServers = serversData.filter((s)=>s.status === \"warning\").length;\n    const offlineServers = serversData.filter((s)=>s.status === \"offline\").length;\n    const maintenanceServers = serversData.filter((s)=>s.status === \"maintenance\").length;\n    const totalPlayers = serversData.reduce((sum, s)=>sum + s.playerCount, 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        requiredRoles: [\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.ROLES.SYSTEM_ADMIN,\n            _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.ROLES.PRODUCT_MANAGER\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        justifyContent: \"space-between\",\n                        alignItems: \"center\",\n                        marginBottom: 24\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                            level: 2,\n                            children: \"服务器状态\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"primary\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReloadOutlined, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 19\n                            }, void 0),\n                            loading: refreshing,\n                            onClick: handleRefresh,\n                            children: \"刷新状态\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 356,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Row, {\n                    gutter: [\n                        16,\n                        16\n                    ],\n                    style: {\n                        marginBottom: 24\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                    title: \"在线服务器\",\n                                    value: onlineServers,\n                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckCircleOutlined, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    valueStyle: {\n                                        color: \"#3f8600\"\n                                    },\n                                    suffix: \"/ \".concat(serversData.length)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                    title: \"警告服务器\",\n                                    value: warningServers,\n                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ExclamationCircleOutlined, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    valueStyle: {\n                                        color: \"#faad14\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                    title: \"离线服务器\",\n                                    value: offlineServers,\n                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CloseCircleOutlined, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    valueStyle: {\n                                        color: \"#cf1322\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Col, {\n                            xs: 24,\n                            sm: 6,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Statistic, {\n                                    title: \"总在线玩家\",\n                                    value: totalPlayers,\n                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CloudServerOutlined, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    valueStyle: {\n                                        color: \"#1890ff\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 369,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                        columns: columns,\n                        dataSource: serversData,\n                        loading: loading,\n                        pagination: false,\n                        scroll: {\n                            x: 1200\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n                    lineNumber: 414,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n            lineNumber: 355,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/gamemanageweb/src/game-management-web/src/app/servers/status/page.tsx\",\n        lineNumber: 354,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ServerStatusPage, \"lhNByLX2gFRQ79WuN3SeJXYTk7Q=\");\n_c = ServerStatusPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ServerStatusPage);\nvar _c;\n$RefreshReg$(_c, \"ServerStatusPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/servers/status/page.tsx\n"));

/***/ })

});