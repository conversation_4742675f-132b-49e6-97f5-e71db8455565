{"c": ["app/layout", "app/operational-data/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/LoginOutlined.js", "(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/ShoppingCartOutlined.js", "(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LoginOutlined.js", "(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js", "(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js", "(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js", "(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js", "(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js", "(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/suspense.js", "(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js", "(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fcafe%2FDocuments%2Fgamemanageweb%2Fsrc%2Fgame-management-web%2Fsrc%2Fapp%2Foperational-data%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/operational-data/page.tsx"]}