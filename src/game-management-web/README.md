# 游戏管理系统 - Web前端

这是游戏管理系统的React Next.js前端应用程序，使用TypeScript和Ant Design构建。

## 技术栈

- **框架**: Next.js 15 with App Router
- **语言**: TypeScript
- **UI库**: Ant Design 5
- **状态管理**: React Context + TanStack Query
- **HTTP客户端**: Axios
- **构建工具**: Turbopack (Next.js内置)

## 项目结构

```
src/
├── app/                    # Next.js App Router页面
│   ├── dashboard/         # 仪表板页面
│   ├── login/            # 登录页面
│   ├── test/             # 测试页面
│   ├── layout.tsx        # 根布局
│   └── page.tsx          # 首页
├── components/            # 可复用组件
│   ├── Auth/             # 认证相关组件
│   └── Layout/           # 布局组件
├── contexts/             # React Context
│   └── AuthContext.tsx  # 认证上下文
├── lib/                  # 工具库
│   └── api.ts           # API客户端
└── styles/              # 样式文件
```

## 已实现功能

### 认证系统
- ✅ JWT认证
- ✅ 登录/登出
- ✅ 角色权限管理
- ✅ 路由保护
- ✅ 自动token刷新

### 用户界面
- ✅ 响应式布局
- ✅ 侧边栏导航
- ✅ 用户下拉菜单
- ✅ 中文本地化

### API集成
- ✅ 认证API
- ✅ 用户管理API
- ✅ 玩家管理API
- ✅ 请求/响应拦截器
- ✅ 错误处理

### 页面
- ✅ 登录页面
- ✅ 仪表板页面
- ✅ 测试页面

## 开发环境设置

### 前置要求
- Node.js 18+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

应用程序将在 http://localhost:3000 启动

### 构建生产版本
```bash
npm run build
npm start
```

## 环境变量

创建 `.env.local` 文件：

```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:5000/api
```

## API端点

当前配置的API端点：

- **认证**: `/auth/login`, `/auth/logout`, `/auth/register`
- **用户**: `/users` (CRUD操作)
- **玩家**: `/players` (查询、统计、管理)

## 角色权限

系统支持以下角色：

- `SystemAdmin`: 系统管理员
- `ProductManager`: 产品经理
- `ProductSpecialist`: 产品专员
- `PartnerManager`: 渠道经理
- `PartnerSpecialist`: 渠道专员
- `CustomerServiceManager`: 客服经理
- `CustomerServiceSpecialist`: 客服专员
- `Viewer`: 只读用户

## 测试

访问 `/test` 页面可以测试基本功能：
- 认证状态
- API调用
- 组件渲染

## 下一步开发计划

1. **完善页面功能**
   - 玩家管理页面
   - 支付管理页面
   - 服务器监控页面
   - 数据报表页面

2. **增强用户体验**
   - 加载状态
   - 错误边界
   - 国际化支持

3. **性能优化**
   - 代码分割
   - 图片优化
   - 缓存策略

4. **测试覆盖**
   - 单元测试
   - 集成测试
   - E2E测试

## 故障排除

### 常见问题

1. **编译错误**: 检查TypeScript类型定义
2. **API连接失败**: 确认后端服务运行状态
3. **认证问题**: 检查token存储和过期时间

### 开发工具

- React Developer Tools
- TanStack Query Devtools (开发环境自动启用)
- Next.js内置性能分析

## 贡献指南

1. 遵循TypeScript严格模式
2. 使用Ant Design组件
3. 保持代码格式一致
4. 添加适当的错误处理
5. 更新相关文档
