{"version": 3, "sources": ["../../../src/lib/eslint/writeDefaultConfig.ts"], "names": ["writeDefaultConfig", "baseDir", "exists", "emptyEslintrc", "emptyPkgJsonConfig", "selectedConfig", "eslintrcFile", "pkgJsonPath", "packageJsonConfig", "ext", "path", "extname", "newFileContent", "CommentJson", "stringify", "fs", "writeFile", "os", "EOL", "Log", "info", "bold", "basename", "eslintConfig", "join", "console", "log", "green"], "mappings": ";;;;+BASsBA;;;eAAAA;;;oBATS;4BACH;2DACb;6DACE;qEACY;6DAGR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEd,eAAeA,mBACpBC,OAAe,EACf,EAAEC,MAAM,EAAEC,aAAa,EAAEC,kBAAkB,EAAmB,EAC9DC,cAAmB,EACnBC,YAA2B,EAC3BC,WAA0B,EAC1BC,iBAA+C;IAE/C,IAAI,CAACN,UAAUC,iBAAiBG,cAAc;QAC5C,MAAMG,MAAMC,aAAI,CAACC,OAAO,CAACL;QAEzB,IAAIM;QACJ,IAAIH,QAAQ,WAAWA,QAAQ,QAAQ;YACrCG,iBAAiB;QACnB,OAAO;YACLA,iBAAiBC,aAAYC,SAAS,CAACT,gBAAgB,MAAM;YAE7D,IAAII,QAAQ,OAAO;gBACjBG,iBAAiB,sBAAsBA;YACzC;QACF;QAEA,MAAMG,YAAE,CAACC,SAAS,CAACV,cAAcM,iBAAiBK,WAAE,CAACC,GAAG;QAExDC,KAAIC,IAAI,CACN,CAAC,gDAAgD,EAAEC,IAAAA,gBAAI,EACrDX,aAAI,CAACY,QAAQ,CAAChB,eACd,yBAAyB,CAAC;IAEhC,OAAO,IAAI,CAACJ,UAAUE,sBAAsBI,mBAAmB;QAC7DA,kBAAkBe,YAAY,GAAGlB;QAEjC,IAAIE,aACF,MAAMQ,YAAE,CAACC,SAAS,CAChBT,aACAM,aAAYC,SAAS,CAACN,mBAAmB,MAAM,KAAKS,WAAE,CAACC,GAAG;QAG9DC,KAAIC,IAAI,CACN,CAAC,qBAAqB,EAAEC,IAAAA,gBAAI,EAC1B,gBACA,8CAA8C,CAAC;IAErD,OAAO,IAAI,CAACnB,QAAQ;QAClB,MAAMa,YAAE,CAACC,SAAS,CAChBN,aAAI,CAACc,IAAI,CAACvB,SAAS,mBACnBY,aAAYC,SAAS,CAACT,gBAAgB,MAAM,KAAKY,WAAE,CAACC,GAAG;QAGzDO,QAAQC,GAAG,CACTC,IAAAA,iBAAK,EACH,CAAC,eAAe,EAAEN,IAAAA,gBAAI,EACpB,kBACA,uDAAuD,CAAC;IAGhE;AACF"}