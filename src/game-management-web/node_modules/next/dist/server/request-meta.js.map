{"version": 3, "sources": ["../../src/server/request-meta.ts"], "names": ["NEXT_REQUEST_META", "addRequestMeta", "getNextInternalQuery", "getRequestMeta", "removeRequestMeta", "setRequestMeta", "Symbol", "for", "req", "key", "meta", "request", "value", "query", "keysToInclude", "nextInternalQuery"], "mappings": "AAAA,+BAA+B;;;;;;;;;;;;;;;;;;;IAUlBA,iBAAiB;eAAjBA;;IAuKGC,cAAc;eAAdA;;IAoEAC,oBAAoB;eAApBA;;IAhGAC,cAAc;eAAdA;;IA6CAC,iBAAiB;eAAjBA;;IA9BAC,cAAc;eAAdA;;;AA1JT,MAAML,oBAAoBM,OAAOC,GAAG,CAAC;AA2IrC,SAASJ,eACdK,GAAwB,EACxBC,GAAO;IAEP,MAAMC,OAAOF,GAAG,CAACR,kBAAkB,IAAI,CAAC;IACxC,OAAO,OAAOS,QAAQ,WAAWC,IAAI,CAACD,IAAI,GAAGC;AAC/C;AASO,SAASL,eAAeG,GAAwB,EAAEE,IAAiB;IACxEF,GAAG,CAACR,kBAAkB,GAAGU;IACzB,OAAOA;AACT;AAUO,SAAST,eACdU,OAA4B,EAC5BF,GAAM,EACNG,KAAqB;IAErB,MAAMF,OAAOP,eAAeQ;IAC5BD,IAAI,CAACD,IAAI,GAAGG;IACZ,OAAOP,eAAeM,SAASD;AACjC;AASO,SAASN,kBACdO,OAA4B,EAC5BF,GAAM;IAEN,MAAMC,OAAOP,eAAeQ;IAC5B,OAAOD,IAAI,CAACD,IAAI;IAChB,OAAOJ,eAAeM,SAASD;AACjC;AA4CO,SAASR,qBACdW,KAAyB;IAEzB,MAAMC,gBAA6C;QACjD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAMC,oBAAuC,CAAC;IAE9C,KAAK,MAAMN,OAAOK,cAAe;QAC/B,IAAIL,OAAOI,OAAO;YAChB,2CAA2C;YAC3CE,iBAAiB,CAACN,IAAI,GAAGI,KAAK,CAACJ,IAAI;QACrC;IACF;IAEA,OAAOM;AACT"}