'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  Link,
  BarChart
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';

const ChannelsPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [channelStatus, setChannelStatus] = useState<string>('all');
  const [modalVisible, setModalVisible] = useState(false);
  const [editingChannel, setEditingChannel] = useState<any>(null);
  const [form] = Form.useForm();

  // 模拟渠道数据
  const channelsData = [
    {
      key: '1',
      id: 8001,
      name: 'App Store',
      code: 'APP_STORE',
      type: '应用商店',
      status: 'active',
      description: '苹果应用商店官方渠道',
      registrations: 12560,
      activeUsers: 8945,
      revenue: 256800,
      conversionRate: 15.8,
      cpa: 25.5,
      ltv: 180.2,
      isActive: true,
      createdAt: '2024-01-15 10:00:00',
      updatedAt: '2024-06-25 14:30:00',
      contactPerson: '张经理',
      contactPhone: '138****1234'
    },
    {
      key: '2',
      id: 8002,
      name: 'Google Play',
      code: 'GOOGLE_PLAY',
      type: '应用商店',
      status: 'active',
      description: 'Google Play商店官方渠道',
      registrations: 18920,
      activeUsers: 13456,
      revenue: 389600,
      conversionRate: 18.2,
      cpa: 22.8,
      ltv: 195.6,
      isActive: true,
      createdAt: '2024-01-20 14:30:00',
      updatedAt: '2024-06-25 13:45:00',
      contactPerson: '李经理',
      contactPhone: '139****5678'
    },
    {
      key: '3',
      id: 8003,
      name: '抖音广告',
      code: 'DOUYIN_ADS',
      type: '信息流广告',
      status: 'active',
      description: '抖音平台信息流广告投放',
      registrations: 25680,
      activeUsers: 15234,
      revenue: 456700,
      conversionRate: 12.5,
      cpa: 35.2,
      ltv: 165.8,
      isActive: true,
      createdAt: '2024-02-10 09:15:00',
      updatedAt: '2024-06-25 12:20:00',
      contactPerson: '王经理',
      contactPhone: '137****9012'
    },
    {
      key: '4',
      id: 8004,
      name: '微信小游戏',
      code: 'WECHAT_GAME',
      type: '小程序',
      status: 'paused',
      description: '微信小游戏平台合作渠道',
      registrations: 8945,
      activeUsers: 4567,
      revenue: 123400,
      conversionRate: 8.9,
      cpa: 45.6,
      ltv: 142.3,
      isActive: false,
      createdAt: '2024-03-05 16:20:00',
      updatedAt: '2024-06-20 10:30:00',
      contactPerson: '赵经理',
      contactPhone: '136****3456'
    }
  ];

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'active':
        return <Tag color="success">活跃</Tag>;
      case 'paused':
        return <Tag color="warning">暂停</Tag>;
      case 'inactive':
        return <Tag color="default">停用</Tag>;
      default:
        return <Tag color="default">未知</Tag>;
    }
  };

  const getTypeTag = (type: string) => {
    const typeColors = {
      '应用商店': 'blue',
      '信息流广告': 'orange',
      '小程序': 'green',
      '联运平台': 'purple',
      '自有渠道': 'cyan'
    };
    return <Tag color={typeColors[type as keyof typeof typeColors] || 'default'}>{type}</Tag>;
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '渠道名称',
      key: 'name',
      width: 150,
      render: (record: any) => (
        <div>
          <div style={{ fontWeight: 500, marginBottom: 4 }}>
            <LinkOutlined style={{ marginRight: 8, color: '#1890ff' }} />
            {record.name}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.code}
          </div>
        </div>
      ),
    },
    {
      title: '渠道类型',
      key: 'type',
      width: 120,
      render: (record: any) => getTypeTag(record.type),
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (record: any) => getStatusTag(record.status),
    },
    {
      title: '注册用户',
      dataIndex: 'registrations',
      key: 'registrations',
      width: 120,
      render: (value: number) => (
        <span style={{ color: '#1890ff', fontWeight: 500 }}>
          {value.toLocaleString()}
        </span>
      ),
    },
    {
      title: '活跃用户',
      dataIndex: 'activeUsers',
      key: 'activeUsers',
      width: 120,
      render: (value: number) => (
        <span style={{ color: '#52c41a', fontWeight: 500 }}>
          {value.toLocaleString()}
        </span>
      ),
    },
    {
      title: '收入 (元)',
      dataIndex: 'revenue',
      key: 'revenue',
      width: 120,
      render: (value: number) => (
        <span style={{ color: '#fa8c16', fontWeight: 500 }}>
          ¥{value.toLocaleString()}
        </span>
      ),
    },
    {
      title: '转化率',
      key: 'conversionRate',
      width: 100,
      render: (record: any) => (
        <div>
          <div style={{ fontSize: '12px', marginBottom: 4 }}>{record.conversionRate}%</div>
          <Progress 
            percent={record.conversionRate} 
            size="small" 
            showInfo={false}
            strokeColor={record.conversionRate > 15 ? '#52c41a' : record.conversionRate > 10 ? '#faad14' : '#ff4d4f'}
          />
        </div>
      ),
    },
    {
      title: 'CPA (元)',
      dataIndex: 'cpa',
      key: 'cpa',
      width: 100,
      render: (value: number) => `¥${value}`,
    },
    {
      title: 'LTV (元)',
      dataIndex: 'ltv',
      key: 'ltv',
      width: 100,
      render: (value: number) => `¥${value}`,
    },
    {
      title: '联系人',
      key: 'contact',
      width: 120,
      render: (record: any) => (
        <div>
          <div style={{ fontSize: '12px', fontWeight: 500 }}>{record.contactPerson}</div>
          <div style={{ fontSize: '11px', color: '#666' }}>{record.contactPhone}</div>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (record: any) => (
        <Space size="small">
          <Button 
            type="link" 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          <Button 
            type="link" 
            size="small" 
            icon={<BarChartOutlined />}
            onClick={() => handleViewStats(record)}
          >
            数据
          </Button>
          <Button 
            type="link" 
            size="small" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button 
            type="link" 
            size="small" 
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleViewDetail = (record: any) => {
    Modal.info({
      title: `渠道详情 - ${record.name}`,
      width: 600,
      content: (
        <div>
          <div style={{ marginBottom: 16 }}>
            <Space>
              {getTypeTag(record.type)}
              {getStatusTag(record.status)}
            </Space>
          </div>
          <div style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <strong>渠道代码：</strong> {record.code}
              </Col>
              <Col span={12}>
                <strong>创建时间：</strong> {record.createdAt}
              </Col>
            </Row>
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>渠道描述：</strong> {record.description}
          </div>
          <div style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <strong>联系人：</strong> {record.contactPerson}
              </Col>
              <Col span={12}>
                <strong>联系电话：</strong> {record.contactPhone}
              </Col>
            </Row>
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>关键指标：</strong>
            <Row gutter={16} style={{ marginTop: 8 }}>
              <Col span={6}>
                <Statistic title="注册用户" value={record.registrations} />
              </Col>
              <Col span={6}>
                <Statistic title="活跃用户" value={record.activeUsers} />
              </Col>
              <Col span={6}>
                <Statistic title="收入" value={record.revenue} prefix="¥" />
              </Col>
              <Col span={6}>
                <Statistic title="转化率" value={record.conversionRate} suffix="%" />
              </Col>
            </Row>
          </div>
        </div>
      ),
    });
  };

  const handleViewStats = (record: any) => {
    message.info(`查看 ${record.name} 的详细数据统计`);
  };

  const handleEdit = (record: any) => {
    setEditingChannel(record);
    form.setFieldsValue(record);
    setModalVisible(true);
  };

  const handleAdd = () => {
    setEditingChannel(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除渠道 "${record.name}" 吗？此操作不可恢复。`,
      onOk() {
        message.success(`已删除渠道: ${record.name}`);
      },
    });
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      console.log('Form values:', values);
      message.success(editingChannel ? '渠道更新成功' : '渠道创建成功');
      setModalVisible(false);
    }).catch(info => {
      console.log('Validate Failed:', info);
    });
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  // 计算统计数据
  const totalRegistrations = channelsData.reduce((sum, item) => sum + item.registrations, 0);
  const totalActiveUsers = channelsData.reduce((sum, item) => sum + item.activeUsers, 0);
  const totalRevenue = channelsData.reduce((sum, item) => sum + item.revenue, 0);
  const avgConversionRate = (channelsData.reduce((sum, item) => sum + item.conversionRate, 0) / channelsData.length).toFixed(1);

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER]}>
      <div>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
          <Title level={2}>渠道管理</Title>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            新建渠道
          </Button>
        </div>
        
        {/* 统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={6} lg={6}>
            <Card>
              <Statistic
                title="总注册用户"
                value={totalRegistrations}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6} lg={6}>
            <Card>
              <Statistic
                title="总活跃用户"
                value={totalActiveUsers}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6} lg={6}>
            <Card>
              <Statistic
                title="总收入"
                value={totalRevenue}
                prefix="¥"
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6} lg={6}>
            <Card>
              <Statistic
                title="平均转化率"
                value={parseFloat(avgConversionRate)}
                suffix="%"
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 搜索和筛选 */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={8} lg={6}>
              <Input.Search
                placeholder="搜索渠道名称"
                allowClear
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={24} sm={6} lg={4}>
              <Select
                placeholder="渠道状态"
                style={{ width: '100%' }}
                value={channelStatus}
                onChange={setChannelStatus}
              >
                <Select.Option value="all">全部状态</Select.Option>
                <Select.Option value="active">活跃</Select.Option>
                <Select.Option value="paused">暂停</Select.Option>
                <Select.Option value="inactive">停用</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={6} lg={4}>
              <Select
                placeholder="渠道类型"
                style={{ width: '100%' }}
              >
                <Select.Option value="all">全部类型</Select.Option>
                <Select.Option value="应用商店">应用商店</Select.Option>
                <Select.Option value="信息流广告">信息流广告</Select.Option>
                <Select.Option value="小程序">小程序</Select.Option>
                <Select.Option value="联运平台">联运平台</Select.Option>
                <Select.Option value="自有渠道">自有渠道</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={6} lg={4}>
              <Button type="primary" icon={<SearchOutlined />} block>
                搜索
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 渠道列表 */}
        <Card>
          <Table
            columns={columns}
            dataSource={channelsData}
            loading={loading}
            pagination={{
              total: 67,
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            scroll={{ x: 1400 }}
          />
        </Card>

        {/* 新建/编辑渠道弹窗 */}
        <Modal
          title={editingChannel ? '编辑渠道' : '新建渠道'}
          open={modalVisible}
          onOk={handleModalOk}
          onCancel={() => setModalVisible(false)}
          width={600}
        >
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              status: 'active',
              isActive: true
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label="渠道名称"
                  rules={[{ required: true, message: '请输入渠道名称' }]}
                >
                  <Input placeholder="请输入渠道名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="code"
                  label="渠道代码"
                  rules={[{ required: true, message: '请输入渠道代码' }]}
                >
                  <Input placeholder="请输入渠道代码" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="type"
                  label="渠道类型"
                  rules={[{ required: true, message: '请选择渠道类型' }]}
                >
                  <Select placeholder="请选择渠道类型">
                    <Select.Option value="应用商店">应用商店</Select.Option>
                    <Select.Option value="信息流广告">信息流广告</Select.Option>
                    <Select.Option value="小程序">小程序</Select.Option>
                    <Select.Option value="联运平台">联运平台</Select.Option>
                    <Select.Option value="自有渠道">自有渠道</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="status"
                  label="渠道状态"
                  rules={[{ required: true, message: '请选择渠道状态' }]}
                >
                  <Select placeholder="请选择渠道状态">
                    <Select.Option value="active">活跃</Select.Option>
                    <Select.Option value="paused">暂停</Select.Option>
                    <Select.Option value="inactive">停用</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="description"
              label="渠道描述"
              rules={[{ required: true, message: '请输入渠道描述' }]}
            >
              <TextArea rows={3} placeholder="请输入渠道描述" />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="contactPerson"
                  label="联系人"
                  rules={[{ required: true, message: '请输入联系人' }]}
                >
                  <Input placeholder="请输入联系人" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="contactPhone"
                  label="联系电话"
                  rules={[{ required: true, message: '请输入联系电话' }]}
                >
                  <Input placeholder="请输入联系电话" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="isActive"
              label="是否启用"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </ProtectedRoute>
  );
};

export default ChannelsPage;
