'use client';

import React from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Send,
  User,
  Clock,
  MessageCircle,
  FileText,
  CheckCircle,
  XCircle,
  RotateCcw,
  Star
} from 'lucide-react';

// Import types and maps from main component
interface CustomerServiceTicketDto {
  id: number;
  ticketId: string;
  playerId: number;
  playerAccountId: string;
  playerNickname: string;
  title: string;
  description: string;
  status: number;
  priority: number;
  category: number;
  assignedToUserId?: number;
  assignedToUserName?: string;
  firstResponseAt?: string;
  resolvedAt?: string;
  closedAt?: string;
  resolution?: string;
  satisfactionRating?: number;
  satisfactionFeedback?: string;
  createdAt: string;
  updatedAt: string;
  messageCount: number;
  hasUnreadMessages: boolean;
  messages: TicketMessageDto[];
}

interface TicketMessageDto {
  id: number;
  ticketId: number;
  userId?: number;
  senderName: string;
  message: string;
  isFromPlayer: boolean;
  isInternal: boolean;
  attachments?: string;
  createdAt: string;
}

interface CreateCustomerServiceTicketDto {
  playerId: number;
  playerAccountId: string;
  title: string;
  description: string;
  priority: number;
  category: number;
  assignedToUserId?: number;
}

interface UserDto {
  id: number;
  username: string;
  displayName?: string;
  role: string;
}

const TicketStatusMap = {
  0: { label: '待处理', color: 'bg-yellow-100 text-yellow-800', icon: Clock },
  1: { label: '处理中', color: 'bg-blue-100 text-blue-800', icon: MessageCircle },
  2: { label: '已解决', color: 'bg-green-100 text-green-800', icon: CheckCircle },
  3: { label: '已关闭', color: 'bg-gray-100 text-gray-800', icon: XCircle }
};

const TicketPriorityMap = {
  0: { label: '低', color: 'bg-gray-100 text-gray-800' },
  1: { label: '中', color: 'bg-blue-100 text-blue-800' },
  2: { label: '高', color: 'bg-orange-100 text-orange-800' },
  3: { label: '紧急', color: 'bg-red-100 text-red-800' }
};

const TicketCategoryMap = {
  0: { label: '账号问题', icon: User },
  1: { label: '游戏问题', icon: MessageCircle },
  2: { label: '充值问题', icon: Star },
  3: { label: '技术问题', icon: FileText },
  4: { label: '投诉建议', icon: MessageCircle },
  5: { label: '其他', icon: FileText }
};

interface DialogsProps {
  // Create ticket dialog
  showCreateDialog: boolean;
  setShowCreateDialog: (show: boolean) => void;
  newTicket: CreateCustomerServiceTicketDto;
  setNewTicket: (ticket: CreateCustomerServiceTicketDto) => void;
  users: UserDto[];
  handleCreateTicket: () => void;
  loading: boolean;

  // Ticket detail dialog
  showTicketDialog: boolean;
  setShowTicketDialog: (show: boolean) => void;
  selectedTicket: CustomerServiceTicketDto | null;
  newMessage: string;
  setNewMessage: (message: string) => void;
  isInternalNote: boolean;
  setIsInternalNote: (isInternal: boolean) => void;
  handleAddMessage: () => void;
  handleChangeStatus: (ticketId: number, status: number) => void;

  // Assign dialog
  showAssignDialog: boolean;
  setShowAssignDialog: (show: boolean) => void;
  selectedAssigneeId: number | null;
  setSelectedAssigneeId: (id: number | null) => void;
  handleAssignTicket: () => void;

  // Resolve dialog
  showResolveDialog: boolean;
  setShowResolveDialog: (show: boolean) => void;
  resolution: string;
  setResolution: (resolution: string) => void;
  handleResolveTicket: () => void;
}

const CustomerServiceDialogs: React.FC<DialogsProps> = ({
  showCreateDialog,
  setShowCreateDialog,
  newTicket,
  setNewTicket,
  users,
  handleCreateTicket,
  loading,
  showTicketDialog,
  setShowTicketDialog,
  selectedTicket,
  newMessage,
  setNewMessage,
  isInternalNote,
  setIsInternalNote,
  handleAddMessage,
  handleChangeStatus,
  showAssignDialog,
  setShowAssignDialog,
  selectedAssigneeId,
  setSelectedAssigneeId,
  handleAssignTicket,
  showResolveDialog,
  setShowResolveDialog,
  resolution,
  setResolution,
  handleResolveTicket
}) => {
  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const getStatusInfo = (status: number) => {
    return TicketStatusMap[status as keyof typeof TicketStatusMap] || TicketStatusMap[0];
  };

  const getPriorityInfo = (priority: number) => {
    return TicketPriorityMap[priority as keyof typeof TicketPriorityMap] || TicketPriorityMap[1];
  };

  const getCategoryInfo = (category: number) => {
    return TicketCategoryMap[category as keyof typeof TicketCategoryMap] || TicketCategoryMap[5];
  };

  return (
    <>
      {/* Create Ticket Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>创建新工单</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">玩家ID</label>
                <Input
                  type="number"
                  placeholder="输入玩家ID"
                  value={newTicket.playerId || ''}
                  onChange={(e) => setNewTicket({
                    ...newTicket,
                    playerId: parseInt(e.target.value) || 0
                  })}
                />
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">玩家账号</label>
                <Input
                  placeholder="输入玩家账号"
                  value={newTicket.playerAccountId}
                  onChange={(e) => setNewTicket({
                    ...newTicket,
                    playerAccountId: e.target.value
                  })}
                />
              </div>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">工单标题</label>
              <Input
                placeholder="输入工单标题"
                value={newTicket.title}
                onChange={(e) => setNewTicket({
                  ...newTicket,
                  title: e.target.value
                })}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">问题描述</label>
              <Textarea
                placeholder="详细描述问题..."
                rows={4}
                value={newTicket.description}
                onChange={(e) => setNewTicket({
                  ...newTicket,
                  description: e.target.value
                })}
              />
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">优先级</label>
                <Select 
                  value={newTicket.priority.toString()} 
                  onValueChange={(value) => setNewTicket({
                    ...newTicket,
                    priority: parseInt(value)
                  })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(TicketPriorityMap).map(([priority, info]) => (
                      <SelectItem key={priority} value={priority}>
                        {info.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">分类</label>
                <Select 
                  value={newTicket.category.toString()} 
                  onValueChange={(value) => setNewTicket({
                    ...newTicket,
                    category: parseInt(value)
                  })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(TicketCategoryMap).map(([category, info]) => (
                      <SelectItem key={category} value={category}>
                        {info.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">分配给</label>
                <Select 
                  value={newTicket.assignedToUserId?.toString() || ''} 
                  onValueChange={(value) => setNewTicket({
                    ...newTicket,
                    assignedToUserId: value ? parseInt(value) : undefined
                  })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择处理人" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">暂不分配</SelectItem>
                    {users.map(user => (
                      <SelectItem key={user.id} value={user.id.toString()}>
                        {user.displayName || user.username}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                取消
              </Button>
              <Button onClick={handleCreateTicket} disabled={loading}>
                创建工单
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Ticket Detail Dialog */}
      <Dialog open={showTicketDialog} onOpenChange={setShowTicketDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>工单详情</DialogTitle>
          </DialogHeader>
          {selectedTicket && (
            <Tabs defaultValue="details" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="details">基本信息</TabsTrigger>
                <TabsTrigger value="messages">消息记录</TabsTrigger>
                <TabsTrigger value="actions">操作</TabsTrigger>
              </TabsList>

              <TabsContent value="details" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">工单信息</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500">工单号</label>
                        <p className="font-mono">{selectedTicket.ticketId}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">标题</label>
                        <p className="font-medium">{selectedTicket.title}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">描述</label>
                        <p className="text-sm whitespace-pre-wrap">{selectedTicket.description}</p>
                      </div>
                      <div className="flex space-x-4">
                        <div>
                          <label className="text-sm font-medium text-gray-500">状态</label>
                          <div className="mt-1">
                            {(() => {
                              const statusInfo = getStatusInfo(selectedTicket.status);
                              const StatusIcon = statusInfo.icon;
                              return (
                                <Badge className={statusInfo.color}>
                                  <StatusIcon className="h-3 w-3 mr-1" />
                                  {statusInfo.label}
                                </Badge>
                              );
                            })()}
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">优先级</label>
                          <div className="mt-1">
                            <Badge className={getPriorityInfo(selectedTicket.priority).color}>
                              {getPriorityInfo(selectedTicket.priority).label}
                            </Badge>
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">分类</label>
                          <div className="mt-1">
                            <Badge variant="outline">
                              {getCategoryInfo(selectedTicket.category).label}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">玩家信息</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500">玩家ID</label>
                        <p>{selectedTicket.playerId}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">账号</label>
                        <p>{selectedTicket.playerAccountId}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">昵称</label>
                        <p>{selectedTicket.playerNickname || '未设置'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">处理人</label>
                        <p>{selectedTicket.assignedToUserName || '未分配'}</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">时间信息</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">创建时间</label>
                        <p className="text-sm">{formatDateTime(selectedTicket.createdAt)}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">更新时间</label>
                        <p className="text-sm">{formatDateTime(selectedTicket.updatedAt)}</p>
                      </div>
                      {selectedTicket.firstResponseAt && (
                        <div>
                          <label className="text-sm font-medium text-gray-500">首次响应时间</label>
                          <p className="text-sm">{formatDateTime(selectedTicket.firstResponseAt)}</p>
                        </div>
                      )}
                      {selectedTicket.resolvedAt && (
                        <div>
                          <label className="text-sm font-medium text-gray-500">解决时间</label>
                          <p className="text-sm">{formatDateTime(selectedTicket.resolvedAt)}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {selectedTicket.resolution && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">解决方案</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm whitespace-pre-wrap">{selectedTicket.resolution}</p>
                    </CardContent>
                  </Card>
                )}

                {selectedTicket.satisfactionRating && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">满意度评价</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-sm font-medium">评分:</span>
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star
                              key={star}
                              className={`h-4 w-4 ${
                                star <= selectedTicket.satisfactionRating!
                                  ? 'text-yellow-400 fill-current'
                                  : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                        <span className="text-sm text-gray-500">
                          ({selectedTicket.satisfactionRating}/5)
                        </span>
                      </div>
                      {selectedTicket.satisfactionFeedback && (
                        <div>
                          <span className="text-sm font-medium">反馈:</span>
                          <p className="text-sm mt-1">{selectedTicket.satisfactionFeedback}</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="messages" className="space-y-4">
                <div className="max-h-96 overflow-y-auto space-y-3">
                  {selectedTicket.messages.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <MessageCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>暂无消息记录</p>
                    </div>
                  ) : (
                    selectedTicket.messages.map((message) => (
                      <Card key={message.id} className={message.isInternal ? 'border-yellow-200 bg-yellow-50' : ''}>
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start mb-2">
                            <div className="flex items-center space-x-2">
                              <User className="h-4 w-4 text-gray-500" />
                              <span className="font-medium">{message.senderName}</span>
                              {message.isFromPlayer && (
                                <Badge variant="outline" className="text-xs">玩家</Badge>
                              )}
                              {message.isInternal && (
                                <Badge variant="secondary" className="text-xs">内部备注</Badge>
                              )}
                            </div>
                            <div className="flex items-center text-xs text-gray-500">
                              <Clock className="h-3 w-3 mr-1" />
                              {formatDateTime(message.createdAt)}
                            </div>
                          </div>
                          <p className="text-sm whitespace-pre-wrap">{message.message}</p>
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>

                {/* Add Message Form */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">添加消息</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-4">
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={isInternalNote}
                          onChange={(e) => setIsInternalNote(e.target.checked)}
                          className="rounded"
                        />
                        <span className="text-sm">内部备注（玩家不可见）</span>
                      </label>
                    </div>
                    <Textarea
                      placeholder="输入消息内容..."
                      rows={3}
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                    />
                    <Button onClick={handleAddMessage} disabled={!newMessage || loading}>
                      <Send className="h-4 w-4 mr-2" />
                      发送消息
                    </Button>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="actions" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">状态操作</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      {selectedTicket.status === 0 && (
                        <Button
                          className="w-full"
                          onClick={() => handleChangeStatus(selectedTicket.id, 1)}
                          disabled={loading}
                        >
                          <MessageCircle className="h-4 w-4 mr-2" />
                          开始处理
                        </Button>
                      )}
                      {selectedTicket.status === 1 && (
                        <Button
                          className="w-full"
                          onClick={() => setShowResolveDialog(true)}
                          disabled={loading}
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          标记为已解决
                        </Button>
                      )}
                      {selectedTicket.status === 2 && (
                        <>
                          <Button
                            className="w-full"
                            onClick={() => handleChangeStatus(selectedTicket.id, 3)}
                            disabled={loading}
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            关闭工单
                          </Button>
                          <Button
                            variant="outline"
                            className="w-full"
                            onClick={() => handleChangeStatus(selectedTicket.id, 1)}
                            disabled={loading}
                          >
                            <RotateCcw className="h-4 w-4 mr-2" />
                            重新打开
                          </Button>
                        </>
                      )}
                      {selectedTicket.status === 3 && (
                        <Button
                          variant="outline"
                          className="w-full"
                          onClick={() => handleChangeStatus(selectedTicket.id, 1)}
                          disabled={loading}
                        >
                          <RotateCcw className="h-4 w-4 mr-2" />
                          重新打开
                        </Button>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">分配操作</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Button
                        className="w-full"
                        variant="outline"
                        onClick={() => setShowAssignDialog(true)}
                        disabled={loading}
                      >
                        <User className="h-4 w-4 mr-2" />
                        重新分配
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          )}
        </DialogContent>
      </Dialog>

      {/* Assign Ticket Dialog */}
      <Dialog open={showAssignDialog} onOpenChange={setShowAssignDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>分配工单</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">选择处理人</label>
              <Select
                value={selectedAssigneeId?.toString() || ''}
                onValueChange={(value) => setSelectedAssigneeId(value ? parseInt(value) : null)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择处理人" />
                </SelectTrigger>
                <SelectContent>
                  {users.map(user => (
                    <SelectItem key={user.id} value={user.id.toString()}>
                      {user.displayName || user.username}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowAssignDialog(false)}>
                取消
              </Button>
              <Button onClick={handleAssignTicket} disabled={!selectedAssigneeId || loading}>
                确认分配
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Resolve Ticket Dialog */}
      <Dialog open={showResolveDialog} onOpenChange={setShowResolveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>解决工单</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">解决方案</label>
              <Textarea
                placeholder="请详细描述解决方案..."
                rows={4}
                value={resolution}
                onChange={(e) => setResolution(e.target.value)}
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowResolveDialog(false)}>
                取消
              </Button>
              <Button onClick={handleResolveTicket} disabled={!resolution || loading}>
                确认解决
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default CustomerServiceDialogs;
