'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Search,
  Plus,
  Eye,
  Edit,
  MessageCircle,
  User,
  Clock,
  Calendar,
  Star
} from 'lucide-react';
} from '@ant-design/icons';
import { ROLES } from '@/contexts/AuthContext';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

const CustomerServicePage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [ticketStatus, setTicketStatus] = useState<string>('all');
  const [ticketPriority, setTicketPriority] = useState<string>('all');
  const [modalVisible, setModalVisible] = useState(false);
  const [replyModalVisible, setReplyModalVisible] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState<any>(null);
  const [form] = Form.useForm();
  const [replyForm] = Form.useForm();

  // 模拟工单数据
  const ticketsData = [
    {
      key: '1',
      id: 6001,
      title: '无法登录游戏',
      category: '技术问题',
      priority: 'high',
      status: 'open',
      playerId: 12345,
      playerName: '玩家ABC',
      playerLevel: 45,
      description: '从昨天开始就无法登录游戏，一直显示连接超时错误',
      createdAt: '2024-06-25 10:30:00',
      updatedAt: '2024-06-25 14:25:00',
      assignedTo: '客服专员A',
      responseTime: '2小时15分钟',
      satisfaction: null,
      replies: [
        {
          id: 1,
          author: '客服专员A',
          content: '您好，我们已经收到您的问题反馈。请问您使用的是什么设备和网络环境？',
          timestamp: '2024-06-25 12:45:00',
          isStaff: true
        },
        {
          id: 2,
          author: '玩家ABC',
          content: '我用的是iPhone 13，家里的WiFi网络',
          timestamp: '2024-06-25 13:20:00',
          isStaff: false
        }
      ]
    },
    {
      key: '2',
      id: 6002,
      title: '充值未到账',
      category: '充值问题',
      priority: 'urgent',
      status: 'in_progress',
      playerId: 67890,
      playerName: '玩家XYZ',
      playerLevel: 78,
      description: '昨天充值了98元，但是钻石没有到账，订单号：PAY202406250001',
      createdAt: '2024-06-24 16:20:00',
      updatedAt: '2024-06-25 09:15:00',
      assignedTo: '客服主管B',
      responseTime: '30分钟',
      satisfaction: null,
      replies: [
        {
          id: 1,
          author: '客服主管B',
          content: '您好，我们正在核实您的充值记录，请稍等片刻。',
          timestamp: '2024-06-24 16:50:00',
          isStaff: true
        }
      ]
    },
    {
      key: '3',
      id: 6003,
      title: '账号被误封',
      category: '账号问题',
      priority: 'high',
      status: 'resolved',
      playerId: 11111,
      playerName: '玩家DEF',
      playerLevel: 92,
      description: '我的账号突然被封了，说是使用外挂，但我从来没有使用过任何外挂程序',
      createdAt: '2024-06-23 14:00:00',
      updatedAt: '2024-06-24 10:30:00',
      assignedTo: '客服专员C',
      responseTime: '1小时45分钟',
      satisfaction: 5,
      replies: [
        {
          id: 1,
          author: '客服专员C',
          content: '您好，我们会仔细核查您的账号情况，请提供您的游戏ID和注册邮箱。',
          timestamp: '2024-06-23 15:45:00',
          isStaff: true
        },
        {
          id: 2,
          author: '玩家DEF',
          content: '游戏ID：DEF123，邮箱：<EMAIL>',
          timestamp: '2024-06-23 16:00:00',
          isStaff: false
        },
        {
          id: 3,
          author: '客服专员C',
          content: '经过核查，确实是系统误判，我们已经为您解封账号并补偿相应道具。',
          timestamp: '2024-06-24 10:30:00',
          isStaff: true
        }
      ]
    }
  ];

  const getPriorityTag = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <Tag color="red">紧急</Tag>;
      case 'high':
        return <Tag color="orange">高</Tag>;
      case 'medium':
        return <Tag color="blue">中</Tag>;
      case 'low':
        return <Tag color="green">低</Tag>;
      default:
        return <Tag color="default">普通</Tag>;
    }
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'open':
        return <Tag color="red">待处理</Tag>;
      case 'in_progress':
        return <Tag color="processing">处理中</Tag>;
      case 'resolved':
        return <Tag color="success">已解决</Tag>;
      case 'closed':
        return <Tag color="default">已关闭</Tag>;
      default:
        return <Tag color="default">未知</Tag>;
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '工单标题',
      key: 'title',
      width: 200,
      render: (record: any) => (
        <div>
          <div style={{ fontWeight: 500, marginBottom: 4 }}>
            {record.title}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.description.substring(0, 50)}...
          </div>
        </div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category: string) => (
        <Tag color="blue">{category}</Tag>
      ),
    },
    {
      title: '优先级',
      key: 'priority',
      width: 80,
      render: (record: any) => getPriorityTag(record.priority),
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (record: any) => getStatusTag(record.status),
    },
    {
      title: '玩家信息',
      key: 'player',
      width: 150,
      render: (record: any) => (
        <div>
          <div style={{ fontWeight: 500 }}>
            <Avatar size="small" icon={<UserOutlined />} style={{ marginRight: 8 }} />
            {record.playerName}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            ID: {record.playerId} | Lv.{record.playerLevel}
          </div>
        </div>
      ),
    },
    {
      title: '负责人',
      dataIndex: 'assignedTo',
      key: 'assignedTo',
      width: 120,
    },
    {
      title: '响应时间',
      dataIndex: 'responseTime',
      key: 'responseTime',
      width: 120,
      render: (time: string) => (
        <span style={{ color: '#1890ff' }}>
          <ClockCircleOutlined style={{ marginRight: 4 }} />
          {time}
        </span>
      ),
    },
    {
      title: '满意度',
      key: 'satisfaction',
      width: 120,
      render: (record: any) => (
        record.satisfaction ? (
          <Rate disabled defaultValue={record.satisfaction} style={{ fontSize: '14px' }} />
        ) : (
          <span style={{ color: '#999' }}>未评价</span>
        )
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (record: any) => (
        <Space size="small">
          <Button 
            type="link" 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            查看
          </Button>
          <Button 
            type="link" 
            size="small" 
            icon={<MessageOutlined />}
            onClick={() => handleReply(record)}
          >
            回复
          </Button>
          <Button 
            type="link" 
            size="small" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  const handleViewDetail = (record: any) => {
    Modal.info({
      title: `工单详情 - ${record.title}`,
      width: 800,
      content: (
        <div>
          <div style={{ marginBottom: 16 }}>
            <Space>
              <Tag color="blue">{record.category}</Tag>
              {getPriorityTag(record.priority)}
              {getStatusTag(record.status)}
            </Space>
          </div>
          <div style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <strong>玩家：</strong> {record.playerName} (ID: {record.playerId})
              </Col>
              <Col span={12}>
                <strong>等级：</strong> Lv.{record.playerLevel}
              </Col>
            </Row>
          </div>
          <div style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <strong>负责人：</strong> {record.assignedTo}
              </Col>
              <Col span={12}>
                <strong>响应时间：</strong> {record.responseTime}
              </Col>
            </Row>
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>问题描述：</strong>
            <div style={{ 
              marginTop: 8, 
              padding: 12, 
              backgroundColor: '#f5f5f5', 
              borderRadius: 4 
            }}>
              {record.description}
            </div>
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>对话记录：</strong>
            <div style={{ marginTop: 8, maxHeight: 300, overflow: 'auto' }}>
              {record.replies.map((reply: any) => (
                <div key={reply.id} style={{ 
                  marginBottom: 12, 
                  padding: 12, 
                  backgroundColor: reply.isStaff ? '#e6f7ff' : '#f6ffed',
                  borderRadius: 4,
                  borderLeft: `4px solid ${reply.isStaff ? '#1890ff' : '#52c41a'}`
                }}>
                  <div style={{ 
                    fontSize: '12px', 
                    color: '#666', 
                    marginBottom: 4,
                    display: 'flex',
                    justifyContent: 'space-between'
                  }}>
                    <span><strong>{reply.author}</strong></span>
                    <span>{reply.timestamp}</span>
                  </div>
                  <div>{reply.content}</div>
                </div>
              ))}
            </div>
          </div>
          {record.satisfaction && (
            <div>
              <strong>满意度评价：</strong>
              <Rate disabled defaultValue={record.satisfaction} style={{ marginLeft: 8 }} />
            </div>
          )}
        </div>
      ),
    });
  };

  const handleReply = (record: any) => {
    setSelectedTicket(record);
    replyForm.resetFields();
    setReplyModalVisible(true);
  };

  const handleEdit = (record: any) => {
    form.setFieldsValue(record);
    setModalVisible(true);
  };

  const handleAdd = () => {
    form.resetFields();
    setModalVisible(true);
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      console.log('Form values:', values);
      message.success('工单保存成功');
      setModalVisible(false);
    }).catch(info => {
      console.log('Validate Failed:', info);
    });
  };

  const handleReplyOk = () => {
    replyForm.validateFields().then(values => {
      console.log('Reply values:', values);
      message.success('回复发送成功');
      setReplyModalVisible(false);
    }).catch(info => {
      console.log('Validate Failed:', info);
    });
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.CUSTOMER_SERVICE_MANAGER, ROLES.CUSTOMER_SERVICE_SPECIALIST]}>
      <MainLayout>
        <div className="p-6">
          <div className="mb-6 flex justify-between items-center">
            <h1 className="text-3xl font-bold text-gray-900">客服工单</h1>
            <Button onClick={handleAdd}>
              <Plus className="h-4 w-4 mr-2" />
              新建工单
            </Button>
          </div>
        
        {/* 搜索和筛选 */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={8} lg={6}>
              <Input.Search
                placeholder="搜索工单标题"
                allowClear
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={24} sm={6} lg={4}>
              <Select
                placeholder="工单状态"
                style={{ width: '100%' }}
                value={ticketStatus}
                onChange={setTicketStatus}
              >
                <Select.Option value="all">全部状态</Select.Option>
                <Select.Option value="open">待处理</Select.Option>
                <Select.Option value="in_progress">处理中</Select.Option>
                <Select.Option value="resolved">已解决</Select.Option>
                <Select.Option value="closed">已关闭</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={6} lg={4}>
              <Select
                placeholder="优先级"
                style={{ width: '100%' }}
                value={ticketPriority}
                onChange={setTicketPriority}
              >
                <Select.Option value="all">全部优先级</Select.Option>
                <Select.Option value="urgent">紧急</Select.Option>
                <Select.Option value="high">高</Select.Option>
                <Select.Option value="medium">中</Select.Option>
                <Select.Option value="low">低</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={8} lg={6}>
              <RangePicker style={{ width: '100%' }} />
            </Col>
            <Col xs={24} sm={6} lg={4}>
              <Button type="primary" icon={<SearchOutlined />} block>
                搜索
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 工单列表 */}
        <Card>
          <Table
            columns={columns}
            dataSource={ticketsData}
            loading={loading}
            pagination={{
              total: 456,
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            scroll={{ x: 1600 }}
          />
        </Card>

        {/* 新建/编辑工单弹窗 */}
        <Modal
          title="工单信息"
          open={modalVisible}
          onOk={handleModalOk}
          onCancel={() => setModalVisible(false)}
          width={600}
        >
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              priority: 'medium',
              status: 'open'
            }}
          >
            <Form.Item
              name="title"
              label="工单标题"
              rules={[{ required: true, message: '请输入工单标题' }]}
            >
              <Input placeholder="请输入工单标题" />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="category"
                  label="问题分类"
                  rules={[{ required: true, message: '请选择问题分类' }]}
                >
                  <Select placeholder="请选择问题分类">
                    <Select.Option value="技术问题">技术问题</Select.Option>
                    <Select.Option value="充值问题">充值问题</Select.Option>
                    <Select.Option value="账号问题">账号问题</Select.Option>
                    <Select.Option value="游戏问题">游戏问题</Select.Option>
                    <Select.Option value="其他问题">其他问题</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="priority"
                  label="优先级"
                  rules={[{ required: true, message: '请选择优先级' }]}
                >
                  <Select placeholder="请选择优先级">
                    <Select.Option value="urgent">紧急</Select.Option>
                    <Select.Option value="high">高</Select.Option>
                    <Select.Option value="medium">中</Select.Option>
                    <Select.Option value="low">低</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="description"
              label="问题描述"
              rules={[{ required: true, message: '请输入问题描述' }]}
            >
              <TextArea rows={4} placeholder="请详细描述问题" />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="assignedTo"
                  label="负责人"
                >
                  <Select placeholder="请选择负责人">
                    <Select.Option value="客服专员A">客服专员A</Select.Option>
                    <Select.Option value="客服专员B">客服专员B</Select.Option>
                    <Select.Option value="客服主管A">客服主管A</Select.Option>
                    <Select.Option value="客服主管B">客服主管B</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="status"
                  label="状态"
                >
                  <Select placeholder="请选择状态">
                    <Select.Option value="open">待处理</Select.Option>
                    <Select.Option value="in_progress">处理中</Select.Option>
                    <Select.Option value="resolved">已解决</Select.Option>
                    <Select.Option value="closed">已关闭</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Modal>

        {/* 回复工单弹窗 */}
        <Modal
          title={`回复工单 - ${selectedTicket?.title}`}
          open={replyModalVisible}
          onOk={handleReplyOk}
          onCancel={() => setReplyModalVisible(false)}
          width={600}
        >
          <Form
            form={replyForm}
            layout="vertical"
          >
            <Form.Item
              name="content"
              label="回复内容"
              rules={[{ required: true, message: '请输入回复内容' }]}
            >
              <TextArea rows={6} placeholder="请输入回复内容" />
            </Form.Item>
            <Form.Item
              name="status"
              label="更新状态"
            >
              <Select placeholder="选择工单状态">
                <Select.Option value="in_progress">处理中</Select.Option>
                <Select.Option value="resolved">已解决</Select.Option>
                <Select.Option value="closed">已关闭</Select.Option>
              </Select>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </ProtectedRoute>
  );
};

export default CustomerServicePage;
