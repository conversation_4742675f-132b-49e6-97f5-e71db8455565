'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Search,
  Plus,
  Eye,
  Edit,
  MessageCircle,
  User,
  Clock,
  Calendar,
  Star
} from 'lucide-react';

import { ROLES } from '@/contexts/AuthContext';

const CustomerServicePage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [ticketStatus, setTicketStatus] = useState<string>('all');
  const [ticketPriority, setTicketPriority] = useState<string>('all');

  const handleAdd = () => {
    console.log('添加工单');
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.CUSTOMER_SERVICE_MANAGER, ROLES.CUSTOMER_SERVICE_SPECIALIST]}>
      <MainLayout>
        <div className="p-6">
          <div className="mb-6 flex justify-between items-center">
            <h1 className="text-3xl font-bold text-gray-900">客服工单</h1>
            <Button onClick={handleAdd}>
              <Plus className="h-4 w-4 mr-2" />
              新建工单
            </Button>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>工单管理</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-4">
                  <Input
                    placeholder="搜索工单..."
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    className="max-w-sm"
                  />
                  <Select value={ticketStatus} onValueChange={setTicketStatus}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="open">待处理</SelectItem>
                      <SelectItem value="in_progress">处理中</SelectItem>
                      <SelectItem value="resolved">已解决</SelectItem>
                      <SelectItem value="closed">已关闭</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="text-center py-8 text-gray-500">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>客服工单功能正在开发中...</p>
                  <p className="text-sm mt-2">完整的工单管理功能即将上线</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};
      title: '无法登录游戏',
      category: '技术问题',
      priority: 'high',
      status: 'open',
      playerId: 12345,
      playerName: '玩家ABC',
      playerLevel: 45,
      description: '从昨天开始就无法登录游戏，一直显示连接超时错误',
      createdAt: '2024-06-25 10:30:00',
      updatedAt: '2024-06-25 14:25:00',
      assignedTo: '客服专员A',
      responseTime: '2小时15分钟',
      satisfaction: null,
      replies: [
        {
          id: 1,
          author: '客服专员A',
          content: '您好，我们已经收到您的问题反馈。请问您使用的是什么设备和网络环境？',
          timestamp: '2024-06-25 12:45:00',
          isStaff: true
        },
        {
          id: 2,
          author: '玩家ABC',
          content: '我用的是iPhone 13，家里的WiFi网络',
          timestamp: '2024-06-25 13:20:00',
          isStaff: false
        }
      ]
    },
    {
      key: '2',
      id: 6002,
      title: '充值未到账',
      category: '充值问题',
      priority: 'urgent',
      status: 'in_progress',
      playerId: 67890,
      playerName: '玩家XYZ',
      playerLevel: 78,
      description: '昨天充值了98元，但是钻石没有到账，订单号：PAY202406250001',
      createdAt: '2024-06-24 16:20:00',
      updatedAt: '2024-06-25 09:15:00',
      assignedTo: '客服主管B',
      responseTime: '30分钟',
      satisfaction: null,
      replies: [
        {
          id: 1,
          author: '客服主管B',
          content: '您好，我们正在核实您的充值记录，请稍等片刻。',
          timestamp: '2024-06-24 16:50:00',
          isStaff: true
        }
      ]
    },
    {
      key: '3',
      id: 6003,
      title: '账号被误封',
      category: '账号问题',
      priority: 'high',
      status: 'resolved',
      playerId: 11111,
      playerName: '玩家DEF',
      playerLevel: 92,
      description: '我的账号突然被封了，说是使用外挂，但我从来没有使用过任何外挂程序',
      createdAt: '2024-06-23 14:00:00',
      updatedAt: '2024-06-24 10:30:00',
      assignedTo: '客服专员C',
      responseTime: '1小时45分钟',
      satisfaction: 5,
      replies: [
        {
          id: 1,
          author: '客服专员C',
          content: '您好，我们会仔细核查您的账号情况，请提供您的游戏ID和注册邮箱。',
          timestamp: '2024-06-23 15:45:00',
          isStaff: true
        },
        {
          id: 2,
          author: '玩家DEF',
          content: '游戏ID：DEF123，邮箱：<EMAIL>',
          timestamp: '2024-06-23 16:00:00',
          isStaff: false
        },
        {
          id: 3,
          author: '客服专员C',
          content: '经过核查，确实是系统误判，我们已经为您解封账号并补偿相应道具。',
          timestamp: '2024-06-24 10:30:00',
          isStaff: true
        }
      ]
    }
  ];

  const getPriorityTag = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <Tag color="red">紧急</Tag>;
      case 'high':
        return <Tag color="orange">高</Tag>;
      case 'medium':
        return <Tag color="blue">中</Tag>;
      case 'low':
        return <Tag color="green">低</Tag>;
      default:
        return <Tag color="default">普通</Tag>;
    }
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'open':
        return <Tag color="red">待处理</Tag>;
      case 'in_progress':
        return <Tag color="processing">处理中</Tag>;
      case 'resolved':
        return <Tag color="success">已解决</Tag>;
      case 'closed':
        return <Tag color="default">已关闭</Tag>;
      default:
        return <Tag color="default">未知</Tag>;
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '工单标题',
      key: 'title',
      width: 200,
      render: (record: any) => (
        <div>
          <div style={{ fontWeight: 500, marginBottom: 4 }}>
            {record.title}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.description.substring(0, 50)}...
          </div>
        </div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category: string) => (
        <Tag color="blue">{category}</Tag>
      ),
    },
    {
      title: '优先级',
      key: 'priority',
      width: 80,
      render: (record: any) => getPriorityTag(record.priority),
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (record: any) => getStatusTag(record.status),
    },
    {
      title: '玩家信息',
      key: 'player',
      width: 150,
      render: (record: any) => (
        <div>
          <div style={{ fontWeight: 500 }}>
            <Avatar size="small" icon={<UserOutlined />} style={{ marginRight: 8 }} />
            {record.playerName}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            ID: {record.playerId} | Lv.{record.playerLevel}
          </div>
        </div>
      ),
    },
    {
      title: '负责人',
      dataIndex: 'assignedTo',
      key: 'assignedTo',
      width: 120,
    },
    {
      title: '响应时间',
      dataIndex: 'responseTime',
      key: 'responseTime',
      width: 120,
      render: (time: string) => (
        <span style={{ color: '#1890ff' }}>
          <ClockCircleOutlined style={{ marginRight: 4 }} />
          {time}
        </span>
      ),
    },
    {
      title: '满意度',
      key: 'satisfaction',
      width: 120,
      render: (record: any) => (
        record.satisfaction ? (
          <Rate disabled defaultValue={record.satisfaction} style={{ fontSize: '14px' }} />
        ) : (
          <span style={{ color: '#999' }}>未评价</span>
        )
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (record: any) => (
        <Space size="small">
          <Button 
            type="link" 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            查看
          </Button>
          <Button 
            type="link" 
            size="small" 
            icon={<MessageOutlined />}
            onClick={() => handleReply(record)}
          >
            回复
          </Button>
          <Button 
            type="link" 
            size="small" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  const handleViewDetail = (record: any) => {
    // TODO: 实现Modal功能
    console.log('查看详情:', record);
    /*
    Modal.info({
      title: `工单详情 - ${record.title}`,
      width: 800,
      content: (
        <div>
          <div style={{ marginBottom: 16 }}>
            <Space>
              <Tag color="blue">{record.category}</Tag>
              {getPriorityTag(record.priority)}
              {getStatusTag(record.status)}
            </Space>
          </div>
          <div style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <strong>玩家：</strong> {record.playerName} (ID: {record.playerId})
              </Col>
              <Col span={12}>
                <strong>等级：</strong> Lv.{record.playerLevel}
              </Col>
            </Row>
          </div>
          <div style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <strong>负责人：</strong> {record.assignedTo}
              </Col>
              <Col span={12}>
                <strong>响应时间：</strong> {record.responseTime}
              </Col>
            </Row>
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>问题描述：</strong>
            <div style={{ 
              marginTop: 8, 
              padding: 12, 
              backgroundColor: '#f5f5f5', 
              borderRadius: 4 
            }}>
              {record.description}
            </div>
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>对话记录：</strong>
            <div style={{ marginTop: 8, maxHeight: 300, overflow: 'auto' }}>
              {record.replies.map((reply: any) => (
                <div key={reply.id} style={{ 
                  marginBottom: 12, 
                  padding: 12, 
                  backgroundColor: reply.isStaff ? '#e6f7ff' : '#f6ffed',
                  borderRadius: 4,
                  borderLeft: `4px solid ${reply.isStaff ? '#1890ff' : '#52c41a'}`
                }}>
                  <div style={{ 
                    fontSize: '12px', 
                    color: '#666', 
                    marginBottom: 4,
                    display: 'flex',
                    justifyContent: 'space-between'
                  }}>
                    <span><strong>{reply.author}</strong></span>
                    <span>{reply.timestamp}</span>
                  </div>
                  <div>{reply.content}</div>
                </div>
              ))}
            </div>
          </div>
          {record.satisfaction && (
            <div>
              <strong>满意度评价：</strong>
              <Rate disabled defaultValue={record.satisfaction} style={{ marginLeft: 8 }} />
            </div>
          )}
        </div>
      ),
    });
    */
  };

  const handleReply = (record: any) => {
    console.log('回复工单:', record);
    // TODO: 实现回复功能
  };

  const handleEdit = (record: any) => {
    console.log('编辑工单:', record);
    // TODO: 实现编辑功能
  };

  const handleAdd = () => {
    console.log('添加工单');
    // TODO: 实现添加功能
  };

  const handleModalOk = () => {
    console.log('保存工单');
    // TODO: 实现保存功能
  };

  const handleReplyOk = () => {
    console.log('发送回复');
    // TODO: 实现回复功能
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.CUSTOMER_SERVICE_MANAGER, ROLES.CUSTOMER_SERVICE_SPECIALIST]}>
      <MainLayout>
        <div className="p-6">
          <div className="mb-6 flex justify-between items-center">
            <h1 className="text-3xl font-bold text-gray-900">客服工单</h1>
            <Button onClick={handleAdd}>
              <Plus className="h-4 w-4 mr-2" />
              新建工单
            </Button>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>工单管理</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-4">
                  <Input
                    placeholder="搜索工单..."
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    className="max-w-sm"
                  />
                  <Select value={ticketStatus} onValueChange={setTicketStatus}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="open">待处理</SelectItem>
                      <SelectItem value="in_progress">处理中</SelectItem>
                      <SelectItem value="resolved">已解决</SelectItem>
                      <SelectItem value="closed">已关闭</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="text-center py-8 text-gray-500">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>客服工单功能正在开发中...</p>
                  <p className="text-sm mt-2">完整的工单管理功能即将上线</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

export default CustomerServicePage;
