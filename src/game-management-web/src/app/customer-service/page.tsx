'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Plus,
  MessageSquare
} from 'lucide-react';

import { ROLES } from '@/contexts/AuthContext';

const CustomerServicePage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [ticketStatus, setTicketStatus] = useState<string>('all');
  const [ticketPriority, setTicketPriority] = useState<string>('all');

  const handleAdd = () => {
    console.log('添加工单');
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.CUSTOMER_SERVICE_MANAGER, ROLES.CUSTOMER_SERVICE_SPECIALIST]}>
      <MainLayout>
        <div className="p-6">
          <div className="mb-6 flex justify-between items-center">
            <h1 className="text-3xl font-bold text-gray-900">客服工单</h1>
            <Button onClick={handleAdd}>
              <Plus className="h-4 w-4 mr-2" />
              新建工单
            </Button>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>工单管理</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-4">
                  <Input
                    placeholder="搜索工单..."
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    className="max-w-sm"
                  />
                  <Select value={ticketStatus} onValueChange={setTicketStatus}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="open">待处理</SelectItem>
                      <SelectItem value="in_progress">处理中</SelectItem>
                      <SelectItem value="resolved">已解决</SelectItem>
                      <SelectItem value="closed">已关闭</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="text-center py-8 text-gray-500">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>客服工单功能正在开发中...</p>
                  <p className="text-sm mt-2">完整的工单管理功能即将上线</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

export default CustomerServicePage;
