'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import MainLayout from '@/components/Layout/MainLayout';
import {
  User,
  DollarSign,
  Server,
  Trophy,
  TrendingUp,
  TrendingDown,
  Users,
  Activity
} from 'lucide-react';

const DashboardContent: React.FC = () => {
  const router = useRouter();
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    // 检查用户是否已登录
    if (typeof window !== 'undefined') {
      const storedUser = localStorage.getItem('user');
      const accessToken = localStorage.getItem('accessToken');

      if (!storedUser || !accessToken) {
        // 未登录，重定向到登录页
        router.push('/login');
        return;
      }

      try {
        const parsedUser = JSON.parse(storedUser);
        setUser(parsedUser);
      } catch (error) {
        console.error('解析用户信息失败:', error);
        router.push('/login');
      }
    }
  }, [router]);



  if (!user) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
      }}>
        <div>加载中...</div>
      </div>
    );
  }

  // 模拟数据
  const stats = {
    totalPlayers: 125430,
    activePlayers: 8567,
    todayRevenue: 45678.90,
    monthRevenue: 1234567.89,
    onlineServers: 12,
    totalServers: 15,
  };

  const recentPlayers = [
    {
      key: '1',
      nickname: '龙战士',
      level: 85,
      class: '战士',
      server: '服务器1',
      lastLogin: '2024-06-25 14:30',
      status: 'online',
    },
    {
      key: '2',
      nickname: '法师小明',
      level: 72,
      class: '法师',
      server: '服务器2',
      lastLogin: '2024-06-25 14:25',
      status: 'online',
    },
    {
      key: '3',
      nickname: '弓箭手',
      level: 68,
      class: '弓箭手',
      server: '服务器1',
      lastLogin: '2024-06-25 14:20',
      status: 'offline',
    },
  ];

  const serverStatus = [
    {
      key: '1',
      name: '服务器1',
      status: 'online',
      players: 1250,
      maxPlayers: 2000,
      cpu: 65,
      memory: 78,
    },
    {
      key: '2',
      name: '服务器2',
      status: 'online',
      players: 980,
      maxPlayers: 2000,
      cpu: 45,
      memory: 62,
    },
    {
      key: '3',
      name: '服务器3',
      status: 'maintenance',
      players: 0,
      maxPlayers: 2000,
      cpu: 0,
      memory: 0,
    },
  ];

  // 渲染玩家状态Badge
  const renderPlayerStatus = (status: string) => (
    <Badge variant={status === 'online' ? 'success' : 'secondary'}>
      {status === 'online' ? '在线' : '离线'}
    </Badge>
  );

  // 渲染服务器状态Badge
  const renderServerStatus = (status: string) => {
    const variant = status === 'online' ? 'success' : status === 'maintenance' ? 'warning' : 'destructive';
    const text = status === 'online' ? '在线' : status === 'maintenance' ? '维护中' : '离线';
    return <Badge variant={variant}>{text}</Badge>;
  };

  // 渲染进度条
  const renderProgress = (value: number) => {
    const colorClass = value > 80 ? 'bg-red-500' : value > 60 ? 'bg-yellow-500' : 'bg-green-500';
    return (
      <div className="w-full">
        <div className="flex justify-between text-xs mb-1">
          <span>{value}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full ${colorClass}`}
            style={{ width: `${value}%` }}
          />
        </div>
      </div>
    );
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">仪表板</h1>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总玩家数</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.totalPlayers.toLocaleString()}</div>
            <div className="flex items-center text-xs text-green-600">
              <TrendingUp className="h-3 w-3 mr-1" />
              <span>12%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃玩家</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.activePlayers.toLocaleString()}</div>
            <div className="flex items-center text-xs text-blue-600">
              <TrendingUp className="h-3 w-3 mr-1" />
              <span>8%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日收入</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">¥{stats.todayRevenue.toLocaleString()}</div>
            <div className="flex items-center text-xs text-red-600">
              <TrendingDown className="h-3 w-3 mr-1" />
              <span>3%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">在线服务器</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{stats.onlineServers}/{stats.totalServers}</div>
          </CardContent>
        </Card>
      </div>

      {/* 图表和表格 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>最近登录玩家</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>昵称</TableHead>
                  <TableHead>等级</TableHead>
                  <TableHead>职业</TableHead>
                  <TableHead>服务器</TableHead>
                  <TableHead>最后登录</TableHead>
                  <TableHead>状态</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentPlayers.map((player) => (
                  <TableRow key={player.key}>
                    <TableCell>{player.nickname}</TableCell>
                    <TableCell>{player.level}</TableCell>
                    <TableCell>{player.class}</TableCell>
                    <TableCell>{player.server}</TableCell>
                    <TableCell>{player.lastLogin}</TableCell>
                    <TableCell>{renderPlayerStatus(player.status)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>服务器状态</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>服务器名称</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>在线玩家</TableHead>
                  <TableHead>CPU使用率</TableHead>
                  <TableHead>内存使用率</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {serverStatus.map((server) => (
                  <TableRow key={server.key}>
                    <TableCell>{server.name}</TableCell>
                    <TableCell>{renderServerStatus(server.status)}</TableCell>
                    <TableCell>{server.players}/{server.maxPlayers}</TableCell>
                    <TableCell>{renderProgress(server.cpu)}</TableCell>
                    <TableCell>{renderProgress(server.memory)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      {/* 快速操作 */}
      <Card>
        <CardHeader>
          <CardTitle>快速操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex flex-col items-center p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
              <Users className="h-6 w-6 text-blue-600 mb-2" />
              <span className="text-sm font-medium">玩家管理</span>
            </div>
            <div className="flex flex-col items-center p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
              <DollarSign className="h-6 w-6 text-green-600 mb-2" />
              <span className="text-sm font-medium">支付管理</span>
            </div>
            <div className="flex flex-col items-center p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
              <Server className="h-6 w-6 text-purple-600 mb-2" />
              <span className="text-sm font-medium">服务器管理</span>
            </div>
            <div className="flex flex-col items-center p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
              <Trophy className="h-6 w-6 text-orange-600 mb-2" />
              <span className="text-sm font-medium">活动管理</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

const DashboardPage: React.FC = () => {
  return (
    <MainLayout>
      <DashboardContent />
    </MainLayout>
  );
};

export default DashboardPage;
