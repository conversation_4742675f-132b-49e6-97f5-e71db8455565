'use client';

import React from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import { Row, Col, Card, Statistic, Typography, Space, Progress, Table, Tag } from 'antd';
import {
  UserOutlined,
  DollarOutlined,
  CloudServerOutlined,
  TrophyOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

const DashboardContent: React.FC = () => {
  // 模拟数据
  const stats = {
    totalPlayers: 125430,
    activePlayers: 8567,
    todayRevenue: 45678.90,
    monthRevenue: 1234567.89,
    onlineServers: 12,
    totalServers: 15,
  };

  const recentPlayers = [
    {
      key: '1',
      nickname: '龙战士',
      level: 85,
      class: '战士',
      server: '服务器1',
      lastLogin: '2024-06-25 14:30',
      status: 'online',
    },
    {
      key: '2',
      nickname: '法师小明',
      level: 72,
      class: '法师',
      server: '服务器2',
      lastLogin: '2024-06-25 14:25',
      status: 'online',
    },
    {
      key: '3',
      nickname: '弓箭手',
      level: 68,
      class: '弓箭手',
      server: '服务器1',
      lastLogin: '2024-06-25 14:20',
      status: 'offline',
    },
  ];

  const serverStatus = [
    {
      key: '1',
      name: '服务器1',
      status: 'online',
      players: 1250,
      maxPlayers: 2000,
      cpu: 65,
      memory: 78,
    },
    {
      key: '2',
      name: '服务器2',
      status: 'online',
      players: 980,
      maxPlayers: 2000,
      cpu: 45,
      memory: 62,
    },
    {
      key: '3',
      name: '服务器3',
      status: 'maintenance',
      players: 0,
      maxPlayers: 2000,
      cpu: 0,
      memory: 0,
    },
  ];

  const playerColumns = [
    {
      title: '昵称',
      dataIndex: 'nickname',
      key: 'nickname',
    },
    {
      title: '等级',
      dataIndex: 'level',
      key: 'level',
    },
    {
      title: '职业',
      dataIndex: 'class',
      key: 'class',
    },
    {
      title: '服务器',
      dataIndex: 'server',
      key: 'server',
    },
    {
      title: '最后登录',
      dataIndex: 'lastLogin',
      key: 'lastLogin',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'online' ? 'green' : 'default'}>
          {status === 'online' ? '在线' : '离线'}
        </Tag>
      ),
    },
  ];

  const serverColumns = [
    {
      title: '服务器名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const color = status === 'online' ? 'green' : status === 'maintenance' ? 'orange' : 'red';
        const text = status === 'online' ? '在线' : status === 'maintenance' ? '维护中' : '离线';
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '在线玩家',
      key: 'playerCount',
      render: (record: any) => `${record.players}/${record.maxPlayers}`,
    },
    {
      title: 'CPU使用率',
      dataIndex: 'cpu',
      key: 'cpu',
      render: (cpu: number) => (
        <Progress 
          percent={cpu} 
          size="small" 
          status={cpu > 80 ? 'exception' : cpu > 60 ? 'active' : 'success'}
        />
      ),
    },
    {
      title: '内存使用率',
      dataIndex: 'memory',
      key: 'memory',
      render: (memory: number) => (
        <Progress 
          percent={memory} 
          size="small" 
          status={memory > 80 ? 'exception' : memory > 60 ? 'active' : 'success'}
        />
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>仪表板</Title>
      
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总玩家数"
              value={stats.totalPlayers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
              suffix={
                <Space>
                  <ArrowUpOutlined />
                  <Text type="success">12%</Text>
                </Space>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃玩家"
              value={stats.activePlayers}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#1890ff' }}
              suffix={
                <Space>
                  <ArrowUpOutlined />
                  <Text style={{ color: '#1890ff' }}>8%</Text>
                </Space>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日收入"
              value={stats.todayRevenue}
              precision={2}
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#cf1322' }}
              suffix={
                <Space>
                  <ArrowDownOutlined />
                  <Text type="danger">3%</Text>
                </Space>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="在线服务器"
              value={`${stats.onlineServers}/${stats.totalServers}`}
              prefix={<CloudServerOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表和表格 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="最近登录玩家" size="small">
            <Table
              columns={playerColumns}
              dataSource={recentPlayers}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="服务器状态" size="small">
            <Table
              columns={serverColumns}
              dataSource={serverStatus}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="快速操作" size="small">
            <Space wrap>
              <Card.Grid style={{ width: '25%', textAlign: 'center' }}>
                <UserOutlined style={{ fontSize: 24, color: '#1890ff' }} />
                <div style={{ marginTop: 8 }}>玩家管理</div>
              </Card.Grid>
              <Card.Grid style={{ width: '25%', textAlign: 'center' }}>
                <DollarOutlined style={{ fontSize: 24, color: '#52c41a' }} />
                <div style={{ marginTop: 8 }}>支付管理</div>
              </Card.Grid>
              <Card.Grid style={{ width: '25%', textAlign: 'center' }}>
                <CloudServerOutlined style={{ fontSize: 24, color: '#722ed1' }} />
                <div style={{ marginTop: 8 }}>服务器管理</div>
              </Card.Grid>
              <Card.Grid style={{ width: '25%', textAlign: 'center' }}>
                <TrophyOutlined style={{ fontSize: 24, color: '#fa8c16' }} />
                <div style={{ marginTop: 8 }}>活动管理</div>
              </Card.Grid>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

const DashboardPage: React.FC = () => {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  );
};

export default DashboardPage;
