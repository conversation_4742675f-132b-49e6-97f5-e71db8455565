'use client';

import React, { useState, useEffect } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Play,
  Pause,
  Trophy,
  Calendar,
  Users,
  Award,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

// Activity types
interface ActivityDto {
  id: number;
  name: string;
  description: string;
  status: number;
  startTime: string;
  endTime: string;
  conditions?: string;
  rewards?: string;
  participantCount: number;
  completionCount: number;
  createdAt: string;
}

interface CreateActivityDto {
  name: string;
  description: string;
  startTime: Date;
  endTime: Date;
  conditions?: string;
  rewards?: string;
}

interface UpdateActivityDto {
  name?: string;
  description?: string;
  startTime?: Date;
  endTime?: Date;
  conditions?: string;
  rewards?: string;
}

// Activity status mapping
const ActivityStatusMap = {
  1: { label: '草稿', color: 'bg-gray-100 text-gray-800', icon: Edit },
  2: { label: '进行中', color: 'bg-green-100 text-green-800', icon: Play },
  3: { label: '已暂停', color: 'bg-yellow-100 text-yellow-800', icon: Pause },
  4: { label: '已结束', color: 'bg-blue-100 text-blue-800', icon: CheckCircle },
  5: { label: '已取消', color: 'bg-red-100 text-red-800', icon: XCircle }
};

// API functions
const activityApi = {
  async getActivities(): Promise<ActivityDto[]> {
    const token = localStorage.getItem('token');
    const response = await fetch('http://localhost:5109/api/Activity', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) throw new Error('Failed to fetch activities');
    return response.json();
  },

  async createActivity(data: CreateActivityDto): Promise<ActivityDto> {
    const token = localStorage.getItem('token');
    const response = await fetch('http://localhost:5109/api/Activity', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ...data,
        startTime: data.startTime.toISOString(),
        endTime: data.endTime.toISOString()
      })
    });
    if (!response.ok) throw new Error('Failed to create activity');
    return response.json();
  },

  async updateActivity(id: number, data: UpdateActivityDto): Promise<ActivityDto> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/Activity/${id}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ...data,
        startTime: data.startTime?.toISOString(),
        endTime: data.endTime?.toISOString()
      })
    });
    if (!response.ok) throw new Error('Failed to update activity');
    return response.json();
  },

  async deleteActivity(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/Activity/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (!response.ok) throw new Error('Failed to delete activity');
  },

  async startActivity(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/Activity/${id}/start`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (!response.ok) throw new Error('Failed to start activity');
  },

  async pauseActivity(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/Activity/${id}/pause`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (!response.ok) throw new Error('Failed to pause activity');
  },

  async resumeActivity(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/Activity/${id}/resume`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (!response.ok) throw new Error('Failed to resume activity');
  },

  async endActivity(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/Activity/${id}/end`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (!response.ok) throw new Error('Failed to end activity');
  }
};

const ActivitiesPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [activityStatus, setActivityStatus] = useState<string>('all');
  const [activities, setActivities] = useState<ActivityDto[]>([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<ActivityDto | null>(null);
  const [createForm, setCreateForm] = useState<CreateActivityDto>({
    name: '',
    description: '',
    startTime: new Date(),
    endTime: new Date(),
    conditions: '',
    rewards: ''
  });
  const [editForm, setEditForm] = useState<UpdateActivityDto>({});
  const [error, setError] = useState<string | null>(null);

  // Load activities on component mount
  useEffect(() => {
    loadActivities();
  }, []);

  const loadActivities = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await activityApi.getActivities();
      setActivities(data);
    } catch (err) {
      setError('加载活动列表失败');
      console.error('Error loading activities:', err);
    } finally {
      setLoading(false);
    }
  };

  const resetCreateForm = () => {
    setCreateForm({
      name: '',
      description: '',
      startTime: new Date(),
      endTime: new Date(),
      conditions: '',
      rewards: ''
    });
  };

  const handleCreateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setError(null);
      await activityApi.createActivity(createForm);
      setIsCreateDialogOpen(false);
      resetCreateForm();
      await loadActivities();
    } catch (err) {
      setError('创建活动失败');
      console.error('Error creating activity:', err);
    }
  };

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedActivity) return;

    try {
      setError(null);
      await activityApi.updateActivity(selectedActivity.id, editForm);
      setIsEditDialogOpen(false);
      setSelectedActivity(null);
      await loadActivities();
    } catch (err) {
      setError('更新活动失败');
      console.error('Error updating activity:', err);
    }
  };

  const handleEdit = (activity: ActivityDto) => {
    setSelectedActivity(activity);
    setEditForm({
      name: activity.name,
      description: activity.description,
      startTime: new Date(activity.startTime),
      endTime: new Date(activity.endTime),
      conditions: activity.conditions,
      rewards: activity.rewards
    });
    setIsEditDialogOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (!confirm('确定要删除这个活动吗？')) return;

    try {
      setError(null);
      await activityApi.deleteActivity(id);
      await loadActivities();
    } catch (err) {
      setError('删除活动失败');
      console.error('Error deleting activity:', err);
    }
  };

  const handleStatusAction = async (activity: ActivityDto, action: string) => {
    try {
      setError(null);
      switch (action) {
        case 'start':
          await activityApi.startActivity(activity.id);
          break;
        case 'pause':
          await activityApi.pauseActivity(activity.id);
          break;
        case 'resume':
          await activityApi.resumeActivity(activity.id);
          break;
        case 'end':
          await activityApi.endActivity(activity.id);
          break;
      }
      await loadActivities();
    } catch (err) {
      setError(`操作失败: ${action}`);
      console.error(`Error ${action} activity:`, err);
    }
  };

  // Filter activities based on search and status
  const filteredActivities = activities.filter(activity => {
    const matchesSearch = activity.name.toLowerCase().includes(searchText.toLowerCase()) ||
                         activity.description.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = activityStatus === 'all' ||
                         (activityStatus === 'draft' && activity.status === 1) ||
                         (activityStatus === 'active' && activity.status === 2) ||
                         (activityStatus === 'paused' && activity.status === 3) ||
                         (activityStatus === 'ended' && activity.status === 4);
    return matchesSearch && matchesStatus;
  });

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER]}>
      <MainLayout>
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">游戏活动管理</h1>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  新建活动
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>创建新活动</DialogTitle>
                </DialogHeader>
                <form onSubmit={handleCreateSubmit} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">活动名称</Label>
                      <Input
                        id="name"
                        value={createForm.name}
                        onChange={(e) => setCreateForm({ ...createForm, name: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="description">活动描述</Label>
                      <Input
                        id="description"
                        value={createForm.description}
                        onChange={(e) => setCreateForm({ ...createForm, description: e.target.value })}
                        required
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="startTime">开始时间</Label>
                      <Input
                        id="startTime"
                        type="datetime-local"
                        value={format(createForm.startTime, "yyyy-MM-dd'T'HH:mm")}
                        onChange={(e) => setCreateForm({ ...createForm, startTime: new Date(e.target.value) })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="endTime">结束时间</Label>
                      <Input
                        id="endTime"
                        type="datetime-local"
                        value={format(createForm.endTime, "yyyy-MM-dd'T'HH:mm")}
                        onChange={(e) => setCreateForm({ ...createForm, endTime: new Date(e.target.value) })}
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="conditions">参与条件</Label>
                    <Textarea
                      id="conditions"
                      value={createForm.conditions}
                      onChange={(e) => setCreateForm({ ...createForm, conditions: e.target.value })}
                      placeholder="描述活动的参与条件..."
                    />
                  </div>
                  <div>
                    <Label htmlFor="rewards">活动奖励</Label>
                    <Textarea
                      id="rewards"
                      value={createForm.rewards}
                      onChange={(e) => setCreateForm({ ...createForm, rewards: e.target.value })}
                      placeholder="描述活动的奖励内容..."
                    />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      取消
                    </Button>
                    <Button type="submit" disabled={loading}>
                      {loading ? '创建中...' : '创建活动'}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>

          {error && (
            <Alert className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Card>
            <CardHeader>
              <CardTitle>活动列表</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4 mb-6">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="搜索活动名称..."
                      value={searchText}
                      onChange={(e) => setSearchText(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="w-48">
                  <Select value={activityStatus} onValueChange={setActivityStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="draft">草稿</SelectItem>
                      <SelectItem value="active">进行中</SelectItem>
                      <SelectItem value="paused">已暂停</SelectItem>
                      <SelectItem value="ended">已结束</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-500">加载中...</p>
                </div>
              ) : filteredActivities.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Trophy className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>暂无活动数据</p>
                  <p className="text-sm mt-2">点击"新建活动"创建第一个活动</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>活动名称</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>开始时间</TableHead>
                      <TableHead>结束时间</TableHead>
                      <TableHead>参与人数</TableHead>
                      <TableHead>完成人数</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredActivities.map((activity) => {
                      const statusInfo = ActivityStatusMap[activity.status as keyof typeof ActivityStatusMap];
                      const StatusIcon = statusInfo?.icon || Trophy;

                      return (
                        <TableRow key={activity.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{activity.name}</div>
                              <div className="text-sm text-gray-500">{activity.description}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={statusInfo?.color || 'bg-gray-100 text-gray-800'}>
                              <StatusIcon className="h-3 w-3 mr-1" />
                              {statusInfo?.label || '未知'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center text-sm">
                              <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                              {format(new Date(activity.startTime), 'yyyy-MM-dd HH:mm', { locale: zhCN })}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center text-sm">
                              <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                              {format(new Date(activity.endTime), 'yyyy-MM-dd HH:mm', { locale: zhCN })}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Users className="h-4 w-4 mr-1 text-gray-400" />
                              {activity.participantCount}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Award className="h-4 w-4 mr-1 text-gray-400" />
                              {activity.completionCount}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEdit(activity)}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>

                              {activity.status === 1 && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleStatusAction(activity, 'start')}
                                  className="text-green-600 hover:text-green-700"
                                >
                                  <Play className="h-3 w-3" />
                                </Button>
                              )}

                              {activity.status === 2 && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleStatusAction(activity, 'pause')}
                                  className="text-yellow-600 hover:text-yellow-700"
                                >
                                  <Pause className="h-3 w-3" />
                                </Button>
                              )}

                              {activity.status === 3 && (
                                <>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleStatusAction(activity, 'resume')}
                                    className="text-green-600 hover:text-green-700"
                                  >
                                    <Play className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleStatusAction(activity, 'end')}
                                    className="text-blue-600 hover:text-blue-700"
                                  >
                                    <CheckCircle className="h-3 w-3" />
                                  </Button>
                                </>
                              )}

                              {(activity.status === 2 || activity.status === 3) && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleStatusAction(activity, 'end')}
                                  className="text-blue-600 hover:text-blue-700"
                                >
                                  <CheckCircle className="h-3 w-3" />
                                </Button>
                              )}

                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDelete(activity.id)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>

          {/* Edit Activity Dialog */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>编辑活动</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleEditSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-name">活动名称</Label>
                    <Input
                      id="edit-name"
                      value={editForm.name || ''}
                      onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-description">活动描述</Label>
                    <Input
                      id="edit-description"
                      value={editForm.description || ''}
                      onChange={(e) => setEditForm({ ...editForm, description: e.target.value })}
                      required
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-startTime">开始时间</Label>
                    <Input
                      id="edit-startTime"
                      type="datetime-local"
                      value={editForm.startTime ? format(editForm.startTime, "yyyy-MM-dd'T'HH:mm") : ''}
                      onChange={(e) => setEditForm({ ...editForm, startTime: new Date(e.target.value) })}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-endTime">结束时间</Label>
                    <Input
                      id="edit-endTime"
                      type="datetime-local"
                      value={editForm.endTime ? format(editForm.endTime, "yyyy-MM-dd'T'HH:mm") : ''}
                      onChange={(e) => setEditForm({ ...editForm, endTime: new Date(e.target.value) })}
                      required
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="edit-conditions">参与条件</Label>
                  <Textarea
                    id="edit-conditions"
                    value={editForm.conditions || ''}
                    onChange={(e) => setEditForm({ ...editForm, conditions: e.target.value })}
                    placeholder="描述活动的参与条件..."
                  />
                </div>
                <div>
                  <Label htmlFor="edit-rewards">活动奖励</Label>
                  <Textarea
                    id="edit-rewards"
                    value={editForm.rewards || ''}
                    onChange={(e) => setEditForm({ ...editForm, rewards: e.target.value })}
                    placeholder="描述活动的奖励内容..."
                  />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                    取消
                  </Button>
                  <Button type="submit" disabled={loading}>
                    {loading ? '更新中...' : '更新活动'}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

export default ActivitiesPage;
