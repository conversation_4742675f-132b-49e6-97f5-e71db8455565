'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Play,
  Pause,
  Trophy,
  Calendar
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';
const { TextArea } = Input;

const ActivitiesPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [activityStatus, setActivityStatus] = useState<string>('all');
  const [modalVisible, setModalVisible] = useState(false);
  const [editingActivity, setEditingActivity] = useState<any>(null);
  const [form] = Form.useForm();

  // 模拟活动数据
  const activitiesData = [
    {
      key: '1',
      id: 1001,
      name: '端午节限时活动',
      type: '节日活动',
      description: '端午节期间，完成指定任务可获得丰厚奖励',
      startTime: '2024-06-22 00:00:00',
      endTime: '2024-06-24 23:59:59',
      status: 'ended',
      isActive: false,
      participantCount: 8567,
      rewardItems: ['粽子', '龙舟装饰', '经验加成卡'],
      serverId: [1, 2, 3],
      serverNames: ['服务器1', '服务器2', '服务器3'],
      createdAt: '2024-06-20 10:00:00',
      createdBy: '产品经理A'
    },
    {
      key: '2',
      id: 1002,
      name: '夏日狂欢周',
      type: '限时活动',
      description: '夏日特别活动，每日登录送好礼',
      startTime: '2024-06-25 00:00:00',
      endTime: '2024-07-01 23:59:59',
      status: 'active',
      isActive: true,
      participantCount: 12450,
      rewardItems: ['夏日套装', '清凉饮品', '金币礼包'],
      serverId: [1, 2],
      serverNames: ['服务器1', '服务器2'],
      createdAt: '2024-06-23 15:30:00',
      createdBy: '产品经理B'
    },
    {
      key: '3',
      id: 1003,
      name: '新手引导活动',
      type: '常驻活动',
      description: '帮助新玩家快速上手游戏',
      startTime: '2024-01-01 00:00:00',
      endTime: '2024-12-31 23:59:59',
      status: 'active',
      isActive: true,
      participantCount: 45678,
      rewardItems: ['新手装备包', '经验药水', '金币'],
      serverId: [1, 2, 3, 4],
      serverNames: ['服务器1', '服务器2', '服务器3', '服务器4'],
      createdAt: '2023-12-25 09:00:00',
      createdBy: '系统管理员'
    }
  ];

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'active':
        return <Tag color="success" icon={<PlayCircleOutlined />}>进行中</Tag>;
      case 'scheduled':
        return <Tag color="processing">待开始</Tag>;
      case 'ended':
        return <Tag color="default">已结束</Tag>;
      case 'paused':
        return <Tag color="warning" icon={<PauseCircleOutlined />}>已暂停</Tag>;
      default:
        return <Tag color="default">未知</Tag>;
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '活动名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: '活动类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: string) => (
        <Tag color="blue">{type}</Tag>
      ),
    },
    {
      title: '状态',
      key: 'status',
      width: 120,
      render: (record: any) => getStatusTag(record.status),
    },
    {
      title: '参与人数',
      dataIndex: 'participantCount',
      key: 'participantCount',
      width: 120,
      render: (count: number) => (
        <span style={{ color: '#1890ff' }}>{count.toLocaleString()}</span>
      ),
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 160,
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      key: 'endTime',
      width: 160,
    },
    {
      title: '服务器',
      key: 'servers',
      width: 150,
      render: (record: any) => (
        <div>
          {record.serverNames.slice(0, 2).map((name: string, index: number) => (
            <Tag key={index} size="small">{name}</Tag>
          ))}
          {record.serverNames.length > 2 && (
            <Tag size="small">+{record.serverNames.length - 2}</Tag>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (record: any) => (
        <Space size="small">
          <Button 
            type="link" 
            size="small" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button 
            type="link" 
            size="small" 
            icon={record.status === 'active' ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            onClick={() => handleToggleStatus(record)}
          >
            {record.status === 'active' ? '暂停' : '启动'}
          </Button>
          <Button 
            type="link" 
            size="small" 
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleEdit = (record: any) => {
    setEditingActivity(record);
    form.setFieldsValue(record);
    setModalVisible(true);
  };

  const handleAdd = () => {
    setEditingActivity(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleToggleStatus = (record: any) => {
    const action = record.status === 'active' ? '暂停' : '启动';
    Modal.confirm({
      title: `确认${action}`,
      content: `确定要${action}活动 "${record.name}" 吗？`,
      onOk() {
        message.success(`已${action}活动: ${record.name}`);
      },
    });
  };

  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除活动 "${record.name}" 吗？此操作不可恢复。`,
      onOk() {
        message.success(`已删除活动: ${record.name}`);
      },
    });
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      console.log('Form values:', values);
      message.success(editingActivity ? '活动更新成功' : '活动创建成功');
      setModalVisible(false);
    }).catch(info => {
      console.log('Validate Failed:', info);
    });
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST]}>
      <div>
        <Title level={2}>活动管理</Title>
        
        {/* 搜索和筛选 */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={8} lg={6}>
              <Input.Search
                placeholder="搜索活动名称"
                allowClear
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={24} sm={8} lg={6}>
              <Select
                placeholder="活动状态"
                style={{ width: '100%' }}
                value={activityStatus}
                onChange={setActivityStatus}
              >
                <Select.Option value="all">全部状态</Select.Option>
                <Select.Option value="active">进行中</Select.Option>
                <Select.Option value="scheduled">待开始</Select.Option>
                <Select.Option value="ended">已结束</Select.Option>
                <Select.Option value="paused">已暂停</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={8} lg={6}>
              <RangePicker style={{ width: '100%' }} />
            </Col>
            <Col xs={24} sm={8} lg={6}>
              <Space>
                <Button type="primary" icon={<SearchOutlined />}>
                  搜索
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                  新建活动
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 活动列表 */}
        <Card>
          <Table
            columns={columns}
            dataSource={activitiesData}
            loading={loading}
            pagination={{
              total: 156,
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            scroll={{ x: 1400 }}
          />
        </Card>

        {/* 新建/编辑活动弹窗 */}
        <Modal
          title={editingActivity ? '编辑活动' : '新建活动'}
          open={modalVisible}
          onOk={handleModalOk}
          onCancel={() => setModalVisible(false)}
          width={800}
        >
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              isActive: true,
              serverId: [1]
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label="活动名称"
                  rules={[{ required: true, message: '请输入活动名称' }]}
                >
                  <Input placeholder="请输入活动名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="type"
                  label="活动类型"
                  rules={[{ required: true, message: '请选择活动类型' }]}
                >
                  <Select placeholder="请选择活动类型">
                    <Select.Option value="节日活动">节日活动</Select.Option>
                    <Select.Option value="限时活动">限时活动</Select.Option>
                    <Select.Option value="常驻活动">常驻活动</Select.Option>
                    <Select.Option value="新手活动">新手活动</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="description"
              label="活动描述"
              rules={[{ required: true, message: '请输入活动描述' }]}
            >
              <TextArea rows={3} placeholder="请输入活动描述" />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="startTime"
                  label="开始时间"
                  rules={[{ required: true, message: '请选择开始时间' }]}
                >
                  <DatePicker showTime style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="endTime"
                  label="结束时间"
                  rules={[{ required: true, message: '请选择结束时间' }]}
                >
                  <DatePicker showTime style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="serverId"
              label="适用服务器"
              rules={[{ required: true, message: '请选择适用服务器' }]}
            >
              <Select mode="multiple" placeholder="请选择适用服务器">
                <Select.Option value={1}>服务器1</Select.Option>
                <Select.Option value={2}>服务器2</Select.Option>
                <Select.Option value={3}>服务器3</Select.Option>
                <Select.Option value={4}>服务器4</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item
              name="isActive"
              label="是否启用"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </ProtectedRoute>
  );
};

export default ActivitiesPage;
