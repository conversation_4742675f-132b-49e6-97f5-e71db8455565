'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  Bell,
  Calendar
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';

const AnnouncementsPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [announcementType, setAnnouncementType] = useState<string>('all');
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAnnouncement, setEditingAnnouncement] = useState<any>(null);
  const [form] = Form.useForm();

  // 模拟公告数据
  const announcementsData = [
    {
      key: '1',
      id: 2001,
      title: '服务器维护公告',
      type: '维护公告',
      content: '为了给玩家提供更好的游戏体验，服务器将于6月26日凌晨2:00-6:00进行维护升级...',
      priority: 'high',
      isActive: true,
      startTime: '2024-06-25 18:00:00',
      endTime: '2024-06-27 18:00:00',
      serverId: [1, 2, 3],
      serverNames: ['服务器1', '服务器2', '服务器3'],
      viewCount: 15678,
      createdAt: '2024-06-25 17:30:00',
      createdBy: '系统管理员',
      status: 'published'
    },
    {
      key: '2',
      id: 2002,
      title: '新版本更新内容预告',
      type: '版本公告',
      content: '即将到来的2.5版本将带来全新的职业、副本和装备系统...',
      priority: 'medium',
      isActive: true,
      startTime: '2024-06-25 12:00:00',
      endTime: '2024-07-05 12:00:00',
      serverId: [1, 2, 3, 4],
      serverNames: ['服务器1', '服务器2', '服务器3', '服务器4'],
      viewCount: 23456,
      createdAt: '2024-06-25 11:45:00',
      createdBy: '产品经理A',
      status: 'published'
    },
    {
      key: '3',
      id: 2003,
      title: '端午节活动结束感谢',
      type: '活动公告',
      content: '感谢各位玩家对端午节活动的热情参与，活动已圆满结束...',
      priority: 'low',
      isActive: false,
      startTime: '2024-06-24 20:00:00',
      endTime: '2024-06-25 20:00:00',
      serverId: [1, 2],
      serverNames: ['服务器1', '服务器2'],
      viewCount: 8901,
      createdAt: '2024-06-24 19:30:00',
      createdBy: '运营专员B',
      status: 'expired'
    }
  ];

  const getPriorityTag = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Tag color="red">高</Tag>;
      case 'medium':
        return <Tag color="orange">中</Tag>;
      case 'low':
        return <Tag color="green">低</Tag>;
      default:
        return <Tag color="default">普通</Tag>;
    }
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'published':
        return <Tag color="success">已发布</Tag>;
      case 'draft':
        return <Tag color="default">草稿</Tag>;
      case 'expired':
        return <Tag color="warning">已过期</Tag>;
      default:
        return <Tag color="default">未知</Tag>;
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '公告标题',
      dataIndex: 'title',
      key: 'title',
      width: 250,
      render: (title: string, record: any) => (
        <div>
          <div style={{ fontWeight: 500 }}>{title}</div>
          <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
            {record.content.substring(0, 50)}...
          </div>
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: string) => (
        <Tag color="blue">{type}</Tag>
      ),
    },
    {
      title: '优先级',
      key: 'priority',
      width: 80,
      render: (record: any) => getPriorityTag(record.priority),
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (record: any) => getStatusTag(record.status),
    },
    {
      title: '浏览量',
      dataIndex: 'viewCount',
      key: 'viewCount',
      width: 100,
      render: (count: number) => (
        <span style={{ color: '#1890ff' }}>{count.toLocaleString()}</span>
      ),
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 160,
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      key: 'endTime',
      width: 160,
    },
    {
      title: '服务器',
      key: 'servers',
      width: 150,
      render: (record: any) => (
        <div>
          {record.serverNames.slice(0, 2).map((name: string, index: number) => (
            <Tag key={index} size="small">{name}</Tag>
          ))}
          {record.serverNames.length > 2 && (
            <Tag size="small">+{record.serverNames.length - 2}</Tag>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (record: any) => (
        <Space size="small">
          <Button 
            type="link" 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button 
            type="link" 
            size="small" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button 
            type="link" 
            size="small" 
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleView = (record: any) => {
    Modal.info({
      title: record.title,
      width: 800,
      content: (
        <div>
          <div style={{ marginBottom: 16 }}>
            <Space>
              <Tag color="blue">{record.type}</Tag>
              {getPriorityTag(record.priority)}
              {getStatusTag(record.status)}
            </Space>
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>适用服务器：</strong>
            {record.serverNames.map((name: string, index: number) => (
              <Tag key={index}>{name}</Tag>
            ))}
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>显示时间：</strong> {record.startTime} 至 {record.endTime}
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>浏览量：</strong> {record.viewCount.toLocaleString()}
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>创建者：</strong> {record.createdBy}
          </div>
          <div>
            <strong>公告内容：</strong>
            <div style={{ 
              marginTop: 8, 
              padding: 16, 
              backgroundColor: '#f5f5f5', 
              borderRadius: 4,
              whiteSpace: 'pre-wrap'
            }}>
              {record.content}
            </div>
          </div>
        </div>
      ),
    });
  };

  const handleEdit = (record: any) => {
    setEditingAnnouncement(record);
    form.setFieldsValue(record);
    setModalVisible(true);
  };

  const handleAdd = () => {
    setEditingAnnouncement(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除公告 "${record.title}" 吗？此操作不可恢复。`,
      onOk() {
        message.success(`已删除公告: ${record.title}`);
      },
    });
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      console.log('Form values:', values);
      message.success(editingAnnouncement ? '公告更新成功' : '公告创建成功');
      setModalVisible(false);
    }).catch(info => {
      console.log('Validate Failed:', info);
    });
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST]}>
      <div>
        <Title level={2}>公告管理</Title>
        
        {/* 搜索和筛选 */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={8} lg={6}>
              <Input.Search
                placeholder="搜索公告标题"
                allowClear
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={24} sm={8} lg={6}>
              <Select
                placeholder="公告类型"
                style={{ width: '100%' }}
                value={announcementType}
                onChange={setAnnouncementType}
              >
                <Select.Option value="all">全部类型</Select.Option>
                <Select.Option value="维护公告">维护公告</Select.Option>
                <Select.Option value="版本公告">版本公告</Select.Option>
                <Select.Option value="活动公告">活动公告</Select.Option>
                <Select.Option value="系统公告">系统公告</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={8} lg={6}>
              <RangePicker style={{ width: '100%' }} />
            </Col>
            <Col xs={24} sm={8} lg={6}>
              <Space>
                <Button type="primary" icon={<SearchOutlined />}>
                  搜索
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                  新建公告
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 公告列表 */}
        <Card>
          <Table
            columns={columns}
            dataSource={announcementsData}
            loading={loading}
            pagination={{
              total: 89,
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            scroll={{ x: 1400 }}
          />
        </Card>

        {/* 新建/编辑公告弹窗 */}
        <Modal
          title={editingAnnouncement ? '编辑公告' : '新建公告'}
          open={modalVisible}
          onOk={handleModalOk}
          onCancel={() => setModalVisible(false)}
          width={800}
        >
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              priority: 'medium',
              isActive: true,
              serverId: [1]
            }}
          >
            <Row gutter={16}>
              <Col span={16}>
                <Form.Item
                  name="title"
                  label="公告标题"
                  rules={[{ required: true, message: '请输入公告标题' }]}
                >
                  <Input placeholder="请输入公告标题" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="type"
                  label="公告类型"
                  rules={[{ required: true, message: '请选择公告类型' }]}
                >
                  <Select placeholder="请选择公告类型">
                    <Select.Option value="维护公告">维护公告</Select.Option>
                    <Select.Option value="版本公告">版本公告</Select.Option>
                    <Select.Option value="活动公告">活动公告</Select.Option>
                    <Select.Option value="系统公告">系统公告</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="content"
              label="公告内容"
              rules={[{ required: true, message: '请输入公告内容' }]}
            >
              <TextArea rows={6} placeholder="请输入公告内容" />
            </Form.Item>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="priority"
                  label="优先级"
                  rules={[{ required: true, message: '请选择优先级' }]}
                >
                  <Select placeholder="请选择优先级">
                    <Select.Option value="high">高</Select.Option>
                    <Select.Option value="medium">中</Select.Option>
                    <Select.Option value="low">低</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="startTime"
                  label="开始时间"
                  rules={[{ required: true, message: '请选择开始时间' }]}
                >
                  <DatePicker showTime style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="endTime"
                  label="结束时间"
                  rules={[{ required: true, message: '请选择结束时间' }]}
                >
                  <DatePicker showTime style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="serverId"
              label="适用服务器"
              rules={[{ required: true, message: '请选择适用服务器' }]}
            >
              <Select mode="multiple" placeholder="请选择适用服务器">
                <Select.Option value={1}>服务器1</Select.Option>
                <Select.Option value={2}>服务器2</Select.Option>
                <Select.Option value={3}>服务器3</Select.Option>
                <Select.Option value={4}>服务器4</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item
              name="isActive"
              label="是否启用"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </ProtectedRoute>
  );
};

export default AnnouncementsPage;
