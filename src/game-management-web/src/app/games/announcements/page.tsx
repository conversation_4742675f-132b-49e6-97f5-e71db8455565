'use client';

import React, { useState, useEffect } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  Bell,
  Calendar,
  Play,
  Pause,
  CheckCircle,
  AlertCircle,
  Clock,
  Users,
  Star
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

// TypeScript interfaces
interface AnnouncementDto {
  id: number;
  title: string;
  content: string;
  type: number;
  startTime: string;
  endTime?: string;
  isActive: boolean;
  targetServers?: string;
  targetPlayers?: string;
  priority: number;
  viewCount: number;
  imageUrl?: string;
  linkUrl?: string;
  createdAt: string;
}

interface CreateAnnouncementDto {
  title: string;
  content: string;
  type: number;
  startTime: Date;
  endTime?: Date;
  priority: number;
  targetServers?: string;
  targetPlayers?: string;
  imageUrl?: string;
  linkUrl?: string;
}

interface UpdateAnnouncementDto {
  title?: string;
  content?: string;
  type?: number;
  startTime?: Date;
  endTime?: Date;
  priority?: number;
  targetServers?: string;
  targetPlayers?: string;
  imageUrl?: string;
  linkUrl?: string;
}

// Announcement type mapping
const AnnouncementTypeMap = {
  1: { label: '系统公告', color: 'bg-blue-100 text-blue-800', icon: Bell },
  2: { label: '维护公告', color: 'bg-orange-100 text-orange-800', icon: AlertCircle },
  3: { label: '活动公告', color: 'bg-green-100 text-green-800', icon: Star },
  4: { label: '更新公告', color: 'bg-purple-100 text-purple-800', icon: CheckCircle }
};

// API functions
const announcementApi = {
  async getAnnouncements(): Promise<AnnouncementDto[]> {
    const token = localStorage.getItem('token');
    const response = await fetch('http://localhost:5109/api/Announcement', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) throw new Error('Failed to fetch announcements');
    return response.json();
  },

  async createAnnouncement(data: CreateAnnouncementDto): Promise<AnnouncementDto> {
    const token = localStorage.getItem('token');
    const response = await fetch('http://localhost:5109/api/Announcement', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ...data,
        startTime: data.startTime.toISOString(),
        endTime: data.endTime?.toISOString()
      })
    });
    if (!response.ok) throw new Error('Failed to create announcement');
    return response.json();
  },

  async updateAnnouncement(id: number, data: UpdateAnnouncementDto): Promise<AnnouncementDto> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/Announcement/${id}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ...data,
        startTime: data.startTime?.toISOString(),
        endTime: data.endTime?.toISOString()
      })
    });
    if (!response.ok) throw new Error('Failed to update announcement');
    return response.json();
  },

  async deleteAnnouncement(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/Announcement/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (!response.ok) throw new Error('Failed to delete announcement');
  },

  async publishAnnouncement(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/Announcement/${id}/publish`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (!response.ok) throw new Error('Failed to publish announcement');
  },

  async activateAnnouncement(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/Announcement/${id}/activate`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (!response.ok) throw new Error('Failed to activate announcement');
  },

  async deactivateAnnouncement(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/Announcement/${id}/deactivate`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (!response.ok) throw new Error('Failed to deactivate announcement');
  }
};

const AnnouncementsPage: React.FC = () => {
  // State management
  const [announcements, setAnnouncements] = useState<AnnouncementDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchText, setSearchText] = useState('');
  const [announcementType, setAnnouncementType] = useState<string>('all');

  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Form states
  const [createForm, setCreateForm] = useState<CreateAnnouncementDto>({
    title: '',
    content: '',
    type: 1,
    startTime: new Date(),
    endTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours later
    priority: 1,
    targetServers: '',
    imageUrl: '',
    linkUrl: ''
  });

  const [editForm, setEditForm] = useState<UpdateAnnouncementDto>({});
  const [editingId, setEditingId] = useState<number | null>(null);

  // Data loading
  const loadAnnouncements = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await announcementApi.getAnnouncements();
      setAnnouncements(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载公告失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAnnouncements();
  }, []);

  // Event handlers
  const handleCreateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setLoading(true);
      await announcementApi.createAnnouncement(createForm);
      setIsCreateDialogOpen(false);
      setCreateForm({
        title: '',
        content: '',
        type: 1,
        startTime: new Date(),
        endTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
        priority: 1,
        targetServers: '',
        imageUrl: '',
        linkUrl: ''
      });
      await loadAnnouncements();
    } catch (err) {
      setError(err instanceof Error ? err.message : '创建公告失败');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (announcement: AnnouncementDto) => {
    setEditingId(announcement.id);
    setEditForm({
      title: announcement.title,
      content: announcement.content,
      type: announcement.type,
      startTime: new Date(announcement.startTime),
      endTime: announcement.endTime ? new Date(announcement.endTime) : undefined,
      priority: announcement.priority,
      targetServers: announcement.targetServers,
      imageUrl: announcement.imageUrl,
      linkUrl: announcement.linkUrl
    });
    setIsEditDialogOpen(true);
  };

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingId) return;

    try {
      setLoading(true);
      await announcementApi.updateAnnouncement(editingId, editForm);
      setIsEditDialogOpen(false);
      setEditingId(null);
      setEditForm({});
      await loadAnnouncements();
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新公告失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('确定要删除这个公告吗？')) return;

    try {
      setLoading(true);
      await announcementApi.deleteAnnouncement(id);
      await loadAnnouncements();
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除公告失败');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusAction = async (announcement: AnnouncementDto, action: 'activate' | 'deactivate' | 'publish') => {
    try {
      setLoading(true);
      switch (action) {
        case 'activate':
          await announcementApi.activateAnnouncement(announcement.id);
          break;
        case 'deactivate':
          await announcementApi.deactivateAnnouncement(announcement.id);
          break;
        case 'publish':
          await announcementApi.publishAnnouncement(announcement.id);
          break;
      }
      await loadAnnouncements();
    } catch (err) {
      setError(err instanceof Error ? err.message : `${action}操作失败`);
    } finally {
      setLoading(false);
    }
  };

  // Data filtering
  const filteredAnnouncements = announcements.filter(announcement => {
    const matchesSearch = announcement.title.toLowerCase().includes(searchText.toLowerCase()) ||
                         announcement.content.toLowerCase().includes(searchText.toLowerCase());
    const matchesType = announcementType === 'all' || announcement.type.toString() === announcementType;
    return matchesSearch && matchesType;
  });

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER]}>
      <MainLayout>
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">游戏公告管理</h1>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  发布公告
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>发布新公告</DialogTitle>
                </DialogHeader>
                <form onSubmit={handleCreateSubmit} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="title">公告标题</Label>
                      <Input
                        id="title"
                        value={createForm.title}
                        onChange={(e) => setCreateForm({ ...createForm, title: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="type">公告类型</Label>
                      <Select value={createForm.type.toString()} onValueChange={(value) => setCreateForm({ ...createForm, type: parseInt(value) })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">系统公告</SelectItem>
                          <SelectItem value="2">维护公告</SelectItem>
                          <SelectItem value="3">活动公告</SelectItem>
                          <SelectItem value="4">更新公告</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="content">公告内容</Label>
                    <Textarea
                      id="content"
                      value={createForm.content}
                      onChange={(e) => setCreateForm({ ...createForm, content: e.target.value })}
                      placeholder="输入公告的详细内容..."
                      className="min-h-[120px]"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="startTime">开始时间</Label>
                      <Input
                        id="startTime"
                        type="datetime-local"
                        value={format(createForm.startTime, "yyyy-MM-dd'T'HH:mm")}
                        onChange={(e) => setCreateForm({ ...createForm, startTime: new Date(e.target.value) })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="endTime">结束时间</Label>
                      <Input
                        id="endTime"
                        type="datetime-local"
                        value={createForm.endTime ? format(createForm.endTime, "yyyy-MM-dd'T'HH:mm") : ''}
                        onChange={(e) => setCreateForm({ ...createForm, endTime: e.target.value ? new Date(e.target.value) : undefined })}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="priority">优先级 (1-10)</Label>
                      <Input
                        id="priority"
                        type="number"
                        min="1"
                        max="10"
                        value={createForm.priority}
                        onChange={(e) => setCreateForm({ ...createForm, priority: parseInt(e.target.value) || 1 })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="targetServers">目标服务器 (JSON格式)</Label>
                      <Input
                        id="targetServers"
                        value={createForm.targetServers}
                        onChange={(e) => setCreateForm({ ...createForm, targetServers: e.target.value })}
                        placeholder="例: [1,2,3]"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="imageUrl">图片链接</Label>
                      <Input
                        id="imageUrl"
                        value={createForm.imageUrl}
                        onChange={(e) => setCreateForm({ ...createForm, imageUrl: e.target.value })}
                        placeholder="可选的图片URL"
                      />
                    </div>
                    <div>
                      <Label htmlFor="linkUrl">跳转链接</Label>
                      <Input
                        id="linkUrl"
                        value={createForm.linkUrl}
                        onChange={(e) => setCreateForm({ ...createForm, linkUrl: e.target.value })}
                        placeholder="可选的跳转URL"
                      />
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      取消
                    </Button>
                    <Button type="submit" disabled={loading}>
                      {loading ? '发布中...' : '发布公告'}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>

          {error && (
            <Alert className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Card>
            <CardHeader>
              <CardTitle>公告列表</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4 mb-6">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="搜索公告标题或内容..."
                      value={searchText}
                      onChange={(e) => setSearchText(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="w-48">
                  <Select value={announcementType} onValueChange={setAnnouncementType}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部类型</SelectItem>
                      <SelectItem value="1">系统公告</SelectItem>
                      <SelectItem value="2">维护公告</SelectItem>
                      <SelectItem value="3">活动公告</SelectItem>
                      <SelectItem value="4">更新公告</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-500">加载中...</p>
                </div>
              ) : filteredAnnouncements.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Bell className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>暂无公告数据</p>
                  <p className="text-sm mt-2">点击"发布公告"创建第一个公告</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>公告标题</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>开始时间</TableHead>
                      <TableHead>结束时间</TableHead>
                      <TableHead>优先级</TableHead>
                      <TableHead>浏览量</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAnnouncements.map((announcement) => {
                      const typeInfo = AnnouncementTypeMap[announcement.type as keyof typeof AnnouncementTypeMap];
                      const TypeIcon = typeInfo?.icon || Bell;
                      const now = new Date();
                      const startTime = new Date(announcement.startTime);
                      const endTime = announcement.endTime ? new Date(announcement.endTime) : null;

                      let statusColor = 'bg-gray-100 text-gray-800';
                      let statusText = '草稿';

                      if (announcement.isActive) {
                        if (startTime > now) {
                          statusColor = 'bg-yellow-100 text-yellow-800';
                          statusText = '待发布';
                        } else if (!endTime || endTime > now) {
                          statusColor = 'bg-green-100 text-green-800';
                          statusText = '已发布';
                        } else {
                          statusColor = 'bg-red-100 text-red-800';
                          statusText = '已过期';
                        }
                      }

                      return (
                        <TableRow key={announcement.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{announcement.title}</div>
                              <div className="text-sm text-gray-500 truncate max-w-xs">
                                {announcement.content}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={typeInfo?.color || 'bg-gray-100 text-gray-800'}>
                              <TypeIcon className="h-3 w-3 mr-1" />
                              {typeInfo?.label || '未知'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={statusColor}>
                              {statusText}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center text-sm">
                              <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                              {format(startTime, 'yyyy-MM-dd HH:mm', { locale: zhCN })}
                            </div>
                          </TableCell>
                          <TableCell>
                            {endTime ? (
                              <div className="flex items-center text-sm">
                                <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                                {format(endTime, 'yyyy-MM-dd HH:mm', { locale: zhCN })}
                              </div>
                            ) : (
                              <span className="text-gray-400">永久</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Star className="h-4 w-4 mr-1 text-yellow-400" />
                              {announcement.priority}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Eye className="h-4 w-4 mr-1 text-gray-400" />
                              {announcement.viewCount}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEdit(announcement)}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>

                              {announcement.isActive ? (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleStatusAction(announcement, 'deactivate')}
                                  className="text-orange-600 hover:text-orange-700"
                                >
                                  <Pause className="h-3 w-3" />
                                </Button>
                              ) : (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleStatusAction(announcement, 'activate')}
                                  className="text-green-600 hover:text-green-700"
                                >
                                  <Play className="h-3 w-3" />
                                </Button>
                              )}

                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleStatusAction(announcement, 'publish')}
                                className="text-blue-600 hover:text-blue-700"
                              >
                                <CheckCircle className="h-3 w-3" />
                              </Button>

                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDelete(announcement.id)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>

          {/* Edit Announcement Dialog */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>编辑公告</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleEditSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-title">公告标题</Label>
                    <Input
                      id="edit-title"
                      value={editForm.title || ''}
                      onChange={(e) => setEditForm({ ...editForm, title: e.target.value })}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-type">公告类型</Label>
                    <Select value={editForm.type?.toString() || '1'} onValueChange={(value) => setEditForm({ ...editForm, type: parseInt(value) })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">系统公告</SelectItem>
                        <SelectItem value="2">维护公告</SelectItem>
                        <SelectItem value="3">活动公告</SelectItem>
                        <SelectItem value="4">更新公告</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div>
                  <Label htmlFor="edit-content">公告内容</Label>
                  <Textarea
                    id="edit-content"
                    value={editForm.content || ''}
                    onChange={(e) => setEditForm({ ...editForm, content: e.target.value })}
                    placeholder="输入公告的详细内容..."
                    className="min-h-[120px]"
                    required
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-startTime">开始时间</Label>
                    <Input
                      id="edit-startTime"
                      type="datetime-local"
                      value={editForm.startTime ? format(editForm.startTime, "yyyy-MM-dd'T'HH:mm") : ''}
                      onChange={(e) => setEditForm({ ...editForm, startTime: new Date(e.target.value) })}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-endTime">结束时间</Label>
                    <Input
                      id="edit-endTime"
                      type="datetime-local"
                      value={editForm.endTime ? format(editForm.endTime, "yyyy-MM-dd'T'HH:mm") : ''}
                      onChange={(e) => setEditForm({ ...editForm, endTime: e.target.value ? new Date(e.target.value) : undefined })}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-priority">优先级 (1-10)</Label>
                    <Input
                      id="edit-priority"
                      type="number"
                      min="1"
                      max="10"
                      value={editForm.priority || 1}
                      onChange={(e) => setEditForm({ ...editForm, priority: parseInt(e.target.value) || 1 })}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-targetServers">目标服务器 (JSON格式)</Label>
                    <Input
                      id="edit-targetServers"
                      value={editForm.targetServers || ''}
                      onChange={(e) => setEditForm({ ...editForm, targetServers: e.target.value })}
                      placeholder="例: [1,2,3]"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-imageUrl">图片链接</Label>
                    <Input
                      id="edit-imageUrl"
                      value={editForm.imageUrl || ''}
                      onChange={(e) => setEditForm({ ...editForm, imageUrl: e.target.value })}
                      placeholder="可选的图片URL"
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-linkUrl">跳转链接</Label>
                    <Input
                      id="edit-linkUrl"
                      value={editForm.linkUrl || ''}
                      onChange={(e) => setEditForm({ ...editForm, linkUrl: e.target.value })}
                      placeholder="可选的跳转URL"
                    />
                  </div>
                </div>
                <div className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                    取消
                  </Button>
                  <Button type="submit" disabled={loading}>
                    {loading ? '更新中...' : '更新公告'}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

export default AnnouncementsPage;
