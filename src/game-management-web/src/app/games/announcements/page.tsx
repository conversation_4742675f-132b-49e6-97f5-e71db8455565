'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  Bell,
  Calendar
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';

const AnnouncementsPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [announcementType, setAnnouncementType] = useState<string>('all');

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER]}>
      <MainLayout>
        <div className="p-6">
          <div className="mb-6 flex justify-between items-center">
            <h1 className="text-3xl font-bold text-gray-900">游戏公告</h1>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              发布公告
            </Button>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>公告管理</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-4">
                  <Input
                    placeholder="搜索公告..."
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    className="max-w-sm"
                  />
                  <Select value={announcementType} onValueChange={setAnnouncementType}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="公告类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部类型</SelectItem>
                      <SelectItem value="maintenance">维护公告</SelectItem>
                      <SelectItem value="event">活动公告</SelectItem>
                      <SelectItem value="update">更新公告</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="text-center py-8 text-gray-500">
                  <Bell className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>游戏公告功能正在开发中...</p>
                  <p className="text-sm mt-2">完整的公告管理功能即将上线</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

export default AnnouncementsPage;
