'use client';

import React, { useState, useEffect } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  Gift,
  Sword,
  Shield,
  Zap,
  Package,
  Star,
  DollarSign,
  BarChart3,
  Settings,
  Play,
  Pause,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';

// TypeScript interfaces
interface GameItemDto {
  id: number;
  itemId: string;
  name: string;
  description?: string;
  type: number;
  rarity: number;
  level: number;
  price: number;
  properties?: string;
  isActive: boolean;
  iconUrl?: string;
  maxStack: number;
  isTradeable: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CreateGameItemDto {
  itemId: string;
  name: string;
  description?: string;
  type: number;
  rarity: number;
  level: number;
  price: number;
  properties?: string;
  isActive: boolean;
  iconUrl?: string;
  maxStack: number;
  isTradeable: boolean;
}

interface UpdateGameItemDto {
  name?: string;
  description?: string;
  type?: number;
  rarity?: number;
  level?: number;
  price?: number;
  properties?: string;
  isActive?: boolean;
  iconUrl?: string;
  maxStack?: number;
  isTradeable?: boolean;
}

// Item type and rarity mappings
const ItemTypeMap = {
  1: { label: '武器', color: 'bg-red-100 text-red-800', icon: Sword },
  2: { label: '防具', color: 'bg-blue-100 text-blue-800', icon: Shield },
  3: { label: '消耗品', color: 'bg-green-100 text-green-800', icon: Zap },
  4: { label: '材料', color: 'bg-yellow-100 text-yellow-800', icon: Package },
  5: { label: '其他', color: 'bg-gray-100 text-gray-800', icon: Gift }
};

const ItemRarityMap = {
  1: { label: '普通', color: 'bg-gray-100 text-gray-800', stars: 1 },
  2: { label: '优秀', color: 'bg-green-100 text-green-800', stars: 2 },
  3: { label: '稀有', color: 'bg-blue-100 text-blue-800', stars: 3 },
  4: { label: '史诗', color: 'bg-purple-100 text-purple-800', stars: 4 },
  5: { label: '传说', color: 'bg-orange-100 text-orange-800', stars: 5 }
};

// API functions
const itemApi = {
  async getItems(): Promise<GameItemDto[]> {
    const token = localStorage.getItem('token');
    const response = await fetch('http://localhost:5109/api/Item', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) throw new Error('Failed to fetch items');
    return response.json();
  },

  async createItem(data: CreateGameItemDto): Promise<GameItemDto> {
    const token = localStorage.getItem('token');
    const response = await fetch('http://localhost:5109/api/Item', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    if (!response.ok) throw new Error('Failed to create item');
    return response.json();
  },

  async updateItem(id: number, data: UpdateGameItemDto): Promise<GameItemDto> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/Item/${id}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    if (!response.ok) throw new Error('Failed to update item');
    return response.json();
  },

  async deleteItem(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/Item/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (!response.ok) throw new Error('Failed to delete item');
  },

  async activateItem(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/Item/${id}/activate`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (!response.ok) throw new Error('Failed to activate item');
  },

  async deactivateItem(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/Item/${id}/deactivate`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (!response.ok) throw new Error('Failed to deactivate item');
  }
};

const ItemsPage: React.FC = () => {
  // State management
  const [items, setItems] = useState<GameItemDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchText, setSearchText] = useState('');
  const [itemCategory, setItemCategory] = useState<string>('all');

  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Form states
  const [createForm, setCreateForm] = useState<CreateGameItemDto>({
    itemId: '',
    name: '',
    description: '',
    type: 1,
    rarity: 1,
    level: 1,
    price: 0,
    properties: '',
    isActive: true,
    iconUrl: '',
    maxStack: 1,
    isTradeable: true
  });

  const [editForm, setEditForm] = useState<UpdateGameItemDto>({});
  const [editingId, setEditingId] = useState<number | null>(null);

  // Data loading
  const loadItems = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await itemApi.getItems();
      setItems(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载道具失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadItems();
  }, []);

  // Event handlers
  const handleCreateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setLoading(true);
      await itemApi.createItem(createForm);
      setIsCreateDialogOpen(false);
      setCreateForm({
        itemId: '',
        name: '',
        description: '',
        type: 1,
        rarity: 1,
        level: 1,
        price: 0,
        properties: '',
        isActive: true,
        iconUrl: '',
        maxStack: 1,
        isTradeable: true
      });
      await loadItems();
    } catch (err) {
      setError(err instanceof Error ? err.message : '创建道具失败');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (item: GameItemDto) => {
    setEditingId(item.id);
    setEditForm({
      name: item.name,
      description: item.description,
      type: item.type,
      rarity: item.rarity,
      level: item.level,
      price: item.price,
      properties: item.properties,
      isActive: item.isActive,
      iconUrl: item.iconUrl,
      maxStack: item.maxStack,
      isTradeable: item.isTradeable
    });
    setIsEditDialogOpen(true);
  };

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingId) return;

    try {
      setLoading(true);
      await itemApi.updateItem(editingId, editForm);
      setIsEditDialogOpen(false);
      setEditingId(null);
      setEditForm({});
      await loadItems();
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新道具失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('确定要删除这个道具吗？')) return;

    try {
      setLoading(true);
      await itemApi.deleteItem(id);
      await loadItems();
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除道具失败');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusAction = async (item: GameItemDto, action: 'activate' | 'deactivate') => {
    try {
      setLoading(true);
      if (action === 'activate') {
        await itemApi.activateItem(item.id);
      } else {
        await itemApi.deactivateItem(item.id);
      }
      await loadItems();
    } catch (err) {
      setError(err instanceof Error ? err.message : `${action}操作失败`);
    } finally {
      setLoading(false);
    }
  };

  // Data filtering
  const filteredItems = items.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchText.toLowerCase()) ||
                         item.itemId.toLowerCase().includes(searchText.toLowerCase()) ||
                         (item.description && item.description.toLowerCase().includes(searchText.toLowerCase()));
    const matchesType = itemCategory === 'all' || item.type.toString() === itemCategory;
    return matchesSearch && matchesType;
  });

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER]}>
      <MainLayout>
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">游戏道具管理</h1>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  添加道具
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>添加新道具</DialogTitle>
                </DialogHeader>
                <form onSubmit={handleCreateSubmit} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="itemId">道具ID</Label>
                      <Input
                        id="itemId"
                        value={createForm.itemId}
                        onChange={(e) => setCreateForm({ ...createForm, itemId: e.target.value })}
                        placeholder="例: SWORD_001"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="name">道具名称</Label>
                      <Input
                        id="name"
                        value={createForm.name}
                        onChange={(e) => setCreateForm({ ...createForm, name: e.target.value })}
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="description">道具描述</Label>
                    <Textarea
                      id="description"
                      value={createForm.description}
                      onChange={(e) => setCreateForm({ ...createForm, description: e.target.value })}
                      placeholder="输入道具的详细描述..."
                      className="min-h-[80px]"
                    />
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="type">道具类型</Label>
                      <Select value={createForm.type.toString()} onValueChange={(value) => setCreateForm({ ...createForm, type: parseInt(value) })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">武器</SelectItem>
                          <SelectItem value="2">防具</SelectItem>
                          <SelectItem value="3">消耗品</SelectItem>
                          <SelectItem value="4">材料</SelectItem>
                          <SelectItem value="5">其他</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="rarity">稀有度</Label>
                      <Select value={createForm.rarity.toString()} onValueChange={(value) => setCreateForm({ ...createForm, rarity: parseInt(value) })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">普通</SelectItem>
                          <SelectItem value="2">优秀</SelectItem>
                          <SelectItem value="3">稀有</SelectItem>
                          <SelectItem value="4">史诗</SelectItem>
                          <SelectItem value="5">传说</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="level">等级要求</Label>
                      <Input
                        id="level"
                        type="number"
                        min="1"
                        max="100"
                        value={createForm.level}
                        onChange={(e) => setCreateForm({ ...createForm, level: parseInt(e.target.value) || 1 })}
                        required
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="price">价格</Label>
                      <Input
                        id="price"
                        type="number"
                        min="0"
                        step="0.01"
                        value={createForm.price}
                        onChange={(e) => setCreateForm({ ...createForm, price: parseFloat(e.target.value) || 0 })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="maxStack">最大堆叠</Label>
                      <Input
                        id="maxStack"
                        type="number"
                        min="1"
                        max="999"
                        value={createForm.maxStack}
                        onChange={(e) => setCreateForm({ ...createForm, maxStack: parseInt(e.target.value) || 1 })}
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="properties">道具属性 (JSON格式)</Label>
                    <Textarea
                      id="properties"
                      value={createForm.properties}
                      onChange={(e) => setCreateForm({ ...createForm, properties: e.target.value })}
                      placeholder='例: {"attack": 100, "defense": 50}'
                      className="min-h-[60px]"
                    />
                  </div>
                  <div>
                    <Label htmlFor="iconUrl">图标链接</Label>
                    <Input
                      id="iconUrl"
                      value={createForm.iconUrl}
                      onChange={(e) => setCreateForm({ ...createForm, iconUrl: e.target.value })}
                      placeholder="可选的图标URL"
                    />
                  </div>
                  <div className="flex items-center space-x-4">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={createForm.isActive}
                        onChange={(e) => setCreateForm({ ...createForm, isActive: e.target.checked })}
                        className="rounded"
                      />
                      <span>启用状态</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={createForm.isTradeable}
                        onChange={(e) => setCreateForm({ ...createForm, isTradeable: e.target.checked })}
                        className="rounded"
                      />
                      <span>可交易</span>
                    </label>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      取消
                    </Button>
                    <Button type="submit" disabled={loading}>
                      {loading ? '创建中...' : '创建道具'}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>

          {error && (
            <Alert className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Card>
            <CardHeader>
              <CardTitle>道具列表</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4 mb-6">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="搜索道具名称、ID或描述..."
                      value={searchText}
                      onChange={(e) => setSearchText(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="w-48">
                  <Select value={itemCategory} onValueChange={setItemCategory}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部类型</SelectItem>
                      <SelectItem value="1">武器</SelectItem>
                      <SelectItem value="2">防具</SelectItem>
                      <SelectItem value="3">消耗品</SelectItem>
                      <SelectItem value="4">材料</SelectItem>
                      <SelectItem value="5">其他</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-500">加载中...</p>
                </div>
              ) : filteredItems.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Gift className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>暂无道具数据</p>
                  <p className="text-sm mt-2">点击"添加道具"创建第一个道具</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>道具信息</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>稀有度</TableHead>
                      <TableHead>等级</TableHead>
                      <TableHead>价格</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>堆叠</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredItems.map((item) => {
                      const typeInfo = ItemTypeMap[item.type as keyof typeof ItemTypeMap];
                      const rarityInfo = ItemRarityMap[item.rarity as keyof typeof ItemRarityMap];
                      const TypeIcon = typeInfo?.icon || Gift;

                      return (
                        <TableRow key={item.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              {item.iconUrl ? (
                                <img src={item.iconUrl} alt={item.name} className="w-8 h-8 rounded" />
                              ) : (
                                <div className="w-8 h-8 bg-gray-200 rounded flex items-center justify-center">
                                  <TypeIcon className="h-4 w-4 text-gray-500" />
                                </div>
                              )}
                              <div>
                                <div className="font-medium">{item.name}</div>
                                <div className="text-sm text-gray-500">{item.itemId}</div>
                                {item.description && (
                                  <div className="text-xs text-gray-400 truncate max-w-xs">
                                    {item.description}
                                  </div>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={typeInfo?.color || 'bg-gray-100 text-gray-800'}>
                              <TypeIcon className="h-3 w-3 mr-1" />
                              {typeInfo?.label || '未知'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={rarityInfo?.color || 'bg-gray-100 text-gray-800'}>
                              <div className="flex items-center">
                                {Array.from({ length: rarityInfo?.stars || 1 }).map((_, i) => (
                                  <Star key={i} className="h-3 w-3 mr-0.5 fill-current" />
                                ))}
                                <span className="ml-1">{rarityInfo?.label || '未知'}</span>
                              </div>
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <BarChart3 className="h-4 w-4 mr-1 text-gray-400" />
                              {item.level}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <DollarSign className="h-4 w-4 mr-1 text-green-500" />
                              {item.price.toFixed(2)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={item.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                              {item.isActive ? '启用' : '禁用'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Package className="h-4 w-4 mr-1 text-gray-400" />
                              {item.maxStack}
                              {item.isTradeable && (
                                <span className="ml-2 text-xs text-green-600">可交易</span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEdit(item)}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>

                              {item.isActive ? (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleStatusAction(item, 'deactivate')}
                                  className="text-orange-600 hover:text-orange-700"
                                >
                                  <Pause className="h-3 w-3" />
                                </Button>
                              ) : (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleStatusAction(item, 'activate')}
                                  className="text-green-600 hover:text-green-700"
                                >
                                  <Play className="h-3 w-3" />
                                </Button>
                              )}

                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDelete(item.id)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>

          {/* Edit Item Dialog */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>编辑道具</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleEditSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-name">道具名称</Label>
                    <Input
                      id="edit-name"
                      value={editForm.name || ''}
                      onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-type">道具类型</Label>
                    <Select value={editForm.type?.toString() || '1'} onValueChange={(value) => setEditForm({ ...editForm, type: parseInt(value) })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">武器</SelectItem>
                        <SelectItem value="2">防具</SelectItem>
                        <SelectItem value="3">消耗品</SelectItem>
                        <SelectItem value="4">材料</SelectItem>
                        <SelectItem value="5">其他</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div>
                  <Label htmlFor="edit-description">道具描述</Label>
                  <Textarea
                    id="edit-description"
                    value={editForm.description || ''}
                    onChange={(e) => setEditForm({ ...editForm, description: e.target.value })}
                    placeholder="输入道具的详细描述..."
                    className="min-h-[80px]"
                  />
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="edit-rarity">稀有度</Label>
                    <Select value={editForm.rarity?.toString() || '1'} onValueChange={(value) => setEditForm({ ...editForm, rarity: parseInt(value) })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">普通</SelectItem>
                        <SelectItem value="2">优秀</SelectItem>
                        <SelectItem value="3">稀有</SelectItem>
                        <SelectItem value="4">史诗</SelectItem>
                        <SelectItem value="5">传说</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="edit-level">等级要求</Label>
                    <Input
                      id="edit-level"
                      type="number"
                      min="1"
                      max="100"
                      value={editForm.level || 1}
                      onChange={(e) => setEditForm({ ...editForm, level: parseInt(e.target.value) || 1 })}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-price">价格</Label>
                    <Input
                      id="edit-price"
                      type="number"
                      min="0"
                      step="0.01"
                      value={editForm.price || 0}
                      onChange={(e) => setEditForm({ ...editForm, price: parseFloat(e.target.value) || 0 })}
                      required
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-maxStack">最大堆叠</Label>
                    <Input
                      id="edit-maxStack"
                      type="number"
                      min="1"
                      max="999"
                      value={editForm.maxStack || 1}
                      onChange={(e) => setEditForm({ ...editForm, maxStack: parseInt(e.target.value) || 1 })}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-iconUrl">图标链接</Label>
                    <Input
                      id="edit-iconUrl"
                      value={editForm.iconUrl || ''}
                      onChange={(e) => setEditForm({ ...editForm, iconUrl: e.target.value })}
                      placeholder="可选的图标URL"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="edit-properties">道具属性 (JSON格式)</Label>
                  <Textarea
                    id="edit-properties"
                    value={editForm.properties || ''}
                    onChange={(e) => setEditForm({ ...editForm, properties: e.target.value })}
                    placeholder='例: {"attack": 100, "defense": 50}'
                    className="min-h-[60px]"
                  />
                </div>
                <div className="flex items-center space-x-4">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={editForm.isActive ?? true}
                      onChange={(e) => setEditForm({ ...editForm, isActive: e.target.checked })}
                      className="rounded"
                    />
                    <span>启用状态</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={editForm.isTradeable ?? true}
                      onChange={(e) => setEditForm({ ...editForm, isTradeable: e.target.checked })}
                      className="rounded"
                    />
                    <span>可交易</span>
                  </label>
                </div>
                <div className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                    取消
                  </Button>
                  <Button type="submit" disabled={loading}>
                    {loading ? '更新中...' : '更新道具'}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

export default ItemsPage;
