'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  Gift
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';

const ItemsPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [itemCategory, setItemCategory] = useState<string>('all');
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [form] = Form.useForm();

  // 模拟道具数据
  const itemsData = [
    {
      key: '1',
      id: 3001,
      name: '传说之剑',
      category: '武器',
      rarity: 'legendary',
      level: 80,
      description: '传说中的神器，拥有无与伦比的攻击力',
      icon: '/icons/legendary_sword.png',
      price: 5000,
      currency: 'gold',
      isActive: true,
      stackable: false,
      maxStack: 1,
      sellPrice: 2500,
      attributes: {
        attack: 1200,
        critical: 15,
        durability: 100
      },
      createdAt: '2024-06-20 10:00:00',
      createdBy: '系统管理员'
    },
    {
      key: '2',
      id: 3002,
      name: '生命药水',
      category: '消耗品',
      rarity: 'common',
      level: 1,
      description: '恢复500点生命值',
      icon: '/icons/health_potion.png',
      price: 50,
      currency: 'gold',
      isActive: true,
      stackable: true,
      maxStack: 99,
      sellPrice: 25,
      attributes: {
        healAmount: 500
      },
      createdAt: '2024-06-15 14:30:00',
      createdBy: '产品经理A'
    },
    {
      key: '3',
      id: 3003,
      name: '钻石礼包',
      category: '礼包',
      rarity: 'epic',
      level: 1,
      description: '包含1000钻石和随机装备',
      icon: '/icons/diamond_pack.png',
      price: 98,
      currency: 'rmb',
      isActive: true,
      stackable: true,
      maxStack: 10,
      sellPrice: 0,
      attributes: {
        diamonds: 1000,
        randomEquipment: 1
      },
      createdAt: '2024-06-18 09:15:00',
      createdBy: '运营专员B'
    }
  ];

  const getRarityTag = (rarity: string) => {
    const rarityConfig = {
      common: { color: 'default', text: '普通' },
      uncommon: { color: 'green', text: '优秀' },
      rare: { color: 'blue', text: '稀有' },
      epic: { color: 'purple', text: '史诗' },
      legendary: { color: 'gold', text: '传说' }
    };
    const config = rarityConfig[rarity as keyof typeof rarityConfig] || rarityConfig.common;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getCurrencyText = (currency: string) => {
    switch (currency) {
      case 'gold': return '金币';
      case 'diamond': return '钻石';
      case 'rmb': return '人民币';
      default: return currency;
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '道具图标',
      key: 'icon',
      width: 80,
      render: (record: any) => (
        <div style={{ width: 40, height: 40, backgroundColor: '#f0f0f0', borderRadius: 4, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <GiftOutlined style={{ fontSize: 20, color: '#666' }} />
        </div>
      ),
    },
    {
      title: '道具名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (name: string, record: any) => (
        <div>
          <div style={{ fontWeight: 500 }}>{name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            Lv.{record.level}
          </div>
        </div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category: string) => (
        <Tag color="blue">{category}</Tag>
      ),
    },
    {
      title: '品质',
      key: 'rarity',
      width: 100,
      render: (record: any) => getRarityTag(record.rarity),
    },
    {
      title: '价格',
      key: 'price',
      width: 120,
      render: (record: any) => (
        <span style={{ color: record.currency === 'rmb' ? '#f50' : '#1890ff' }}>
          {record.price} {getCurrencyText(record.currency)}
        </span>
      ),
    },
    {
      title: '可堆叠',
      key: 'stackable',
      width: 100,
      render: (record: any) => (
        <div>
          {record.stackable ? (
            <Tag color="green">是 ({record.maxStack})</Tag>
          ) : (
            <Tag color="default">否</Tag>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      key: 'isActive',
      width: 80,
      render: (record: any) => (
        record.isActive ? 
          <Tag color="success">启用</Tag> : 
          <Tag color="default">禁用</Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (record: any) => (
        <Space size="small">
          <Button 
            type="link" 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button 
            type="link" 
            size="small" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button 
            type="link" 
            size="small" 
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleView = (record: any) => {
    Modal.info({
      title: record.name,
      width: 600,
      content: (
        <div>
          <div style={{ marginBottom: 16 }}>
            <Space>
              <Tag color="blue">{record.category}</Tag>
              {getRarityTag(record.rarity)}
              <Tag color={record.isActive ? 'success' : 'default'}>
                {record.isActive ? '启用' : '禁用'}
              </Tag>
            </Space>
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>等级要求：</strong> Lv.{record.level}
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>价格：</strong> {record.price} {getCurrencyText(record.currency)}
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>出售价格：</strong> {record.sellPrice} 金币
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>堆叠：</strong> {record.stackable ? `可堆叠 (最大${record.maxStack})` : '不可堆叠'}
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>描述：</strong> {record.description}
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>属性：</strong>
            <div style={{ marginTop: 8 }}>
              {Object.entries(record.attributes).map(([key, value]) => (
                <Tag key={key} style={{ margin: '2px' }}>
                  {key}: {String(value)}
                </Tag>
              ))}
            </div>
          </div>
          <div>
            <strong>创建者：</strong> {record.createdBy}
          </div>
        </div>
      ),
    });
  };

  const handleEdit = (record: any) => {
    setEditingItem(record);
    form.setFieldsValue({
      ...record,
      attributes: Object.entries(record.attributes).map(([key, value]) => ({ key, value }))
    });
    setModalVisible(true);
  };

  const handleAdd = () => {
    setEditingItem(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除道具 "${record.name}" 吗？此操作不可恢复。`,
      onOk() {
        message.success(`已删除道具: ${record.name}`);
      },
    });
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      console.log('Form values:', values);
      message.success(editingItem ? '道具更新成功' : '道具创建成功');
      setModalVisible(false);
    }).catch(info => {
      console.log('Validate Failed:', info);
    });
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST]}>
      <div>
        <Title level={2}>道具管理</Title>
        
        {/* 搜索和筛选 */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={8} lg={6}>
              <Input.Search
                placeholder="搜索道具名称"
                allowClear
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={24} sm={8} lg={6}>
              <Select
                placeholder="道具分类"
                style={{ width: '100%' }}
                value={itemCategory}
                onChange={setItemCategory}
              >
                <Select.Option value="all">全部分类</Select.Option>
                <Select.Option value="武器">武器</Select.Option>
                <Select.Option value="防具">防具</Select.Option>
                <Select.Option value="消耗品">消耗品</Select.Option>
                <Select.Option value="材料">材料</Select.Option>
                <Select.Option value="礼包">礼包</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={8} lg={6}>
              <Select
                placeholder="道具品质"
                style={{ width: '100%' }}
              >
                <Select.Option value="all">全部品质</Select.Option>
                <Select.Option value="common">普通</Select.Option>
                <Select.Option value="uncommon">优秀</Select.Option>
                <Select.Option value="rare">稀有</Select.Option>
                <Select.Option value="epic">史诗</Select.Option>
                <Select.Option value="legendary">传说</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={8} lg={6}>
              <Space>
                <Button type="primary" icon={<SearchOutlined />}>
                  搜索
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                  新建道具
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 道具列表 */}
        <Card>
          <Table
            columns={columns}
            dataSource={itemsData}
            loading={loading}
            pagination={{
              total: 234,
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            scroll={{ x: 1300 }}
          />
        </Card>

        {/* 新建/编辑道具弹窗 */}
        <Modal
          title={editingItem ? '编辑道具' : '新建道具'}
          open={modalVisible}
          onOk={handleModalOk}
          onCancel={() => setModalVisible(false)}
          width={800}
        >
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              rarity: 'common',
              currency: 'gold',
              isActive: true,
              stackable: false,
              maxStack: 1,
              level: 1
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label="道具名称"
                  rules={[{ required: true, message: '请输入道具名称' }]}
                >
                  <Input placeholder="请输入道具名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="category"
                  label="道具分类"
                  rules={[{ required: true, message: '请选择道具分类' }]}
                >
                  <Select placeholder="请选择道具分类">
                    <Select.Option value="武器">武器</Select.Option>
                    <Select.Option value="防具">防具</Select.Option>
                    <Select.Option value="消耗品">消耗品</Select.Option>
                    <Select.Option value="材料">材料</Select.Option>
                    <Select.Option value="礼包">礼包</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="description"
              label="道具描述"
              rules={[{ required: true, message: '请输入道具描述' }]}
            >
              <TextArea rows={3} placeholder="请输入道具描述" />
            </Form.Item>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="rarity"
                  label="道具品质"
                  rules={[{ required: true, message: '请选择道具品质' }]}
                >
                  <Select placeholder="请选择道具品质">
                    <Select.Option value="common">普通</Select.Option>
                    <Select.Option value="uncommon">优秀</Select.Option>
                    <Select.Option value="rare">稀有</Select.Option>
                    <Select.Option value="epic">史诗</Select.Option>
                    <Select.Option value="legendary">传说</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="level"
                  label="等级要求"
                  rules={[{ required: true, message: '请输入等级要求' }]}
                >
                  <InputNumber min={1} max={100} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="currency"
                  label="货币类型"
                  rules={[{ required: true, message: '请选择货币类型' }]}
                >
                  <Select placeholder="请选择货币类型">
                    <Select.Option value="gold">金币</Select.Option>
                    <Select.Option value="diamond">钻石</Select.Option>
                    <Select.Option value="rmb">人民币</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="price"
                  label="购买价格"
                  rules={[{ required: true, message: '请输入购买价格' }]}
                >
                  <InputNumber min={0} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="sellPrice"
                  label="出售价格"
                  rules={[{ required: true, message: '请输入出售价格' }]}
                >
                  <InputNumber min={0} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="stackable"
                  label="可堆叠"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="maxStack"
                  label="最大堆叠数"
                >
                  <InputNumber min={1} max={999} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="isActive"
                  label="是否启用"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Modal>
      </div>
    </ProtectedRoute>
  );
};

export default ItemsPage;
