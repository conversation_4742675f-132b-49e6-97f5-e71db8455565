'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  User,
  DollarSign,
  Eye,
  LogIn,
  Users,
  ShoppingCart,
  Trophy,
  Percent
} from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { operationalDataApi, GlobalStats, UserInfoStats, PaymentInfoStats, ConversionAnalysis, RetentionAnalysis, ActiveUserAnalysis } from '@/lib/api';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import dayjs from 'dayjs';

export default function OperationalDataPage() {
  const [selectedDate, setSelectedDate] = useState<string>(dayjs().format('YYYY-MM-DD'));
  const [dateRange, setDateRange] = useState<[string, string]>([
    dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD')
  ]);

  // 全局统计数据
  const { data: globalStats, isLoading: globalStatsLoading, error: globalStatsError } = useQuery({
    queryKey: ['globalStats'],
    queryFn: () => operationalDataApi.getGlobalStats(),
  });

  // 用户信息统计
  const { data: userInfoStats, isLoading: userInfoLoading } = useQuery({
    queryKey: ['userInfoStats', dateRange],
    queryFn: () => operationalDataApi.getUserInfoStatsByDateRange(dateRange[0], dateRange[1]),
  });

  // 付费信息统计
  const { data: paymentInfoStats, isLoading: paymentInfoLoading } = useQuery({
    queryKey: ['paymentInfoStats', dateRange],
    queryFn: () => operationalDataApi.getPaymentInfoStatsByDateRange(dateRange[0], dateRange[1]),
  });

  // 转化率分析
  const { data: conversionAnalysis, isLoading: conversionLoading } = useQuery({
    queryKey: ['conversionAnalysis', selectedDate],
    queryFn: () => operationalDataApi.getConversionAnalysis(selectedDate),
  });

  // 留存率分析
  const { data: retentionAnalysis, isLoading: retentionLoading } = useQuery({
    queryKey: ['retentionAnalysis', selectedDate],
    queryFn: () => operationalDataApi.getRetentionAnalysis(selectedDate),
  });

  // 活跃用户分析
  const { data: activeUserAnalysis, isLoading: activeUserLoading } = useQuery({
    queryKey: ['activeUserAnalysis', selectedDate],
    queryFn: () => operationalDataApi.getActiveUserAnalysis(selectedDate),
  });

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    }).format(amount);
  };

  const formatPercentage = (rate: number) => {
    return (rate * 100).toFixed(2) + '%';
  };

  if (globalStatsError) {
    return (
      <ProtectedRoute requiredRoles={['SystemAdmin', 'ProductManager', 'ProductSpecialist']}>
        <Alert
          message="加载失败"
          description="无法加载运营数据，请稍后重试"
          type="error"
          showIcon
        />
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredRoles={['SystemAdmin', 'ProductManager', 'ProductSpecialist']}>
      <MainLayout>
        <div className="p-6">
          <div className="mb-6 flex justify-between items-center">
            <h1 className="text-3xl font-bold text-gray-900">运营数据</h1>
            <div className="flex gap-4">
              <Button variant="outline">选择日期</Button>
              <Button variant="outline">日期范围</Button>
            </div>
          </div>

          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总营收</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  ¥{globalStats?.totalRevenue?.toLocaleString() || '0'}
                </div>
                <p className="text-xs text-muted-foreground">
                  +20.1% 较上月
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总访问量</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {globalStats?.totalVisits?.toLocaleString() || '0'}
                </div>
                <p className="text-xs text-muted-foreground">
                  +15.3% 较上月
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总注册数</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {globalStats?.totalRegistrations?.toLocaleString() || '0'}
                </div>
                <p className="text-xs text-muted-foreground">
                  +8.7% 较上月
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">活跃用户</CardTitle>
                <User className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {globalStats?.activeUsers?.toLocaleString() || '0'}
                </div>
                <p className="text-xs text-muted-foreground">
                  +12.5% 较上月
                </p>
              </CardContent>
            </Card>
          </div>

          {/* 详细数据区域 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>用户统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">新增用户</span>
                    <span className="font-medium">1,234</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">活跃用户</span>
                    <span className="font-medium">8,765</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">留存率</span>
                    <span className="font-medium">68.5%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>收入统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">今日收入</span>
                    <span className="font-medium">¥12,345</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">本月收入</span>
                    <span className="font-medium">¥456,789</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">ARPU</span>
                    <span className="font-medium">¥52.3</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
}
