'use client';

import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, DatePicker, Select, Spin, Alert, Tabs, Table, Progress } from 'antd';
import { 
  UserOutlined, 
  DollarOutlined, 
  EyeOutlined, 
  LoginOutlined,
  TeamOutlined,
  ShoppingCartOutlined,
  TrophyOutlined,
  PercentageOutlined
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { operationalDataApi, GlobalStats, UserInfoStats, PaymentInfoStats, ConversionAnalysis, RetentionAnalysis, ActiveUserAnalysis } from '@/lib/api';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;

export default function OperationalDataPage() {
  const [selectedDate, setSelectedDate] = useState<string>(dayjs().format('YYYY-MM-DD'));
  const [dateRange, setDateRange] = useState<[string, string]>([
    dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD')
  ]);

  // 全局统计数据
  const { data: globalStats, isLoading: globalStatsLoading, error: globalStatsError } = useQuery({
    queryKey: ['globalStats'],
    queryFn: () => operationalDataApi.getGlobalStats(),
  });

  // 用户信息统计
  const { data: userInfoStats, isLoading: userInfoLoading } = useQuery({
    queryKey: ['userInfoStats', dateRange],
    queryFn: () => operationalDataApi.getUserInfoStatsByDateRange(dateRange[0], dateRange[1]),
  });

  // 付费信息统计
  const { data: paymentInfoStats, isLoading: paymentInfoLoading } = useQuery({
    queryKey: ['paymentInfoStats', dateRange],
    queryFn: () => operationalDataApi.getPaymentInfoStatsByDateRange(dateRange[0], dateRange[1]),
  });

  // 转化率分析
  const { data: conversionAnalysis, isLoading: conversionLoading } = useQuery({
    queryKey: ['conversionAnalysis', selectedDate],
    queryFn: () => operationalDataApi.getConversionAnalysis(selectedDate),
  });

  // 留存率分析
  const { data: retentionAnalysis, isLoading: retentionLoading } = useQuery({
    queryKey: ['retentionAnalysis', selectedDate],
    queryFn: () => operationalDataApi.getRetentionAnalysis(selectedDate),
  });

  // 活跃用户分析
  const { data: activeUserAnalysis, isLoading: activeUserLoading } = useQuery({
    queryKey: ['activeUserAnalysis', selectedDate],
    queryFn: () => operationalDataApi.getActiveUserAnalysis(selectedDate),
  });

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    }).format(amount);
  };

  const formatPercentage = (rate: number) => {
    return (rate * 100).toFixed(2) + '%';
  };

  if (globalStatsError) {
    return (
      <ProtectedRoute requiredRoles={['SystemAdmin', 'ProductManager', 'ProductSpecialist']}>
        <Alert
          message="加载失败"
          description="无法加载运营数据，请稍后重试"
          type="error"
          showIcon
        />
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredRoles={['SystemAdmin', 'ProductManager', 'ProductSpecialist']}>
      <div style={{ padding: '24px' }}>
        <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h1>运营数据</h1>
          <div style={{ display: 'flex', gap: '16px' }}>
            <DatePicker
              value={dayjs(selectedDate)}
              onChange={(date) => setSelectedDate(date?.format('YYYY-MM-DD') || dayjs().format('YYYY-MM-DD'))}
              placeholder="选择分析日期"
            />
            <RangePicker
              value={[dayjs(dateRange[0]), dayjs(dateRange[1])]}
              onChange={(dates) => {
                if (dates && dates[0] && dates[1]) {
                  setDateRange([dates[0].format('YYYY-MM-DD'), dates[1].format('YYYY-MM-DD')]);
                }
              }}
              placeholder={['开始日期', '结束日期']}
            />
          </div>
        </div>

        <Tabs
          defaultActiveKey="overview"
          items={[
            {
              key: 'overview',
              label: '数据概览',
              children: (
                <div>
            {/* 全局统计卡片 */}
            <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="总营收"
                    value={globalStats?.totalRevenue || 0}
                    formatter={(value) => formatCurrency(Number(value))}
                    prefix={<DollarOutlined />}
                    loading={globalStatsLoading}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="总访问量"
                    value={globalStats?.totalVisits || 0}
                    formatter={(value) => formatNumber(Number(value))}
                    prefix={<EyeOutlined />}
                    loading={globalStatsLoading}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="总注册数"
                    value={globalStats?.totalRegistrations || 0}
                    formatter={(value) => formatNumber(Number(value))}
                    prefix={<UserOutlined />}
                    loading={globalStatsLoading}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="总登录数"
                    value={globalStats?.totalLogins || 0}
                    formatter={(value) => formatNumber(Number(value))}
                    prefix={<LoginOutlined />}
                    loading={globalStatsLoading}
                  />
                </Card>
              </Col>
            </Row>

            {/* 今日数据 */}
            <Card title="今日数据" style={{ marginBottom: '24px' }}>
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12} md={6}>
                  <Statistic
                    title="新增访问"
                    value={globalStats?.todayNewVisits || 0}
                    prefix={<EyeOutlined />}
                    loading={globalStatsLoading}
                  />
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Statistic
                    title="新增注册"
                    value={globalStats?.todayNewRegistrations || 0}
                    prefix={<UserOutlined />}
                    loading={globalStatsLoading}
                  />
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Statistic
                    title="新增登录"
                    value={globalStats?.todayNewLogins || 0}
                    prefix={<LoginOutlined />}
                    loading={globalStatsLoading}
                  />
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Statistic
                    title="活跃用户"
                    value={globalStats?.todayActiveUsers || 0}
                    prefix={<TeamOutlined />}
                    loading={globalStatsLoading}
                  />
                </Col>
              </Row>
            </Card>

            {/* ARPU和在线数据 */}
            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <Card title="收入指标">
                  <Statistic
                    title="ARPU (平均每用户收入)"
                    value={globalStats?.arpu || 0}
                    formatter={(value) => formatCurrency(Number(value))}
                    prefix={<DollarOutlined />}
                    loading={globalStatsLoading}
                  />
                </Card>
              </Col>
              <Col xs={24} md={12}>
                <Card title="在线指标">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Statistic
                        title="平均在线"
                        value={globalStats?.averageOnlineUsers || 0}
                        formatter={(value) => formatNumber(Number(value))}
                        loading={globalStatsLoading}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="最高在线"
                        value={globalStats?.maxOnlineUsers || 0}
                        formatter={(value) => formatNumber(Number(value))}
                        loading={globalStatsLoading}
                      />
                    </Col>
                  </Row>
                </Card>
              </Col>
            </Row>
                </div>
              )
            },
            {
              key: 'user-analysis',
              label: '用户分析',
              children: (
                <div>
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={12}>
                <Card title="用户统计" loading={userInfoLoading}>
                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <Statistic
                        title="总访问量"
                        value={userInfoStats?.totalVisits || 0}
                        formatter={(value) => formatNumber(Number(value))}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="独立访问"
                        value={userInfoStats?.uniqueVisits || 0}
                        formatter={(value) => formatNumber(Number(value))}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="总注册数"
                        value={userInfoStats?.totalRegistrations || 0}
                        formatter={(value) => formatNumber(Number(value))}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="当前在线"
                        value={userInfoStats?.currentOnlineUsers || 0}
                        formatter={(value) => formatNumber(Number(value))}
                      />
                    </Col>
                  </Row>
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card title="用户状态" loading={userInfoLoading}>
                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <Statistic
                        title="未创角用户"
                        value={userInfoStats?.usersWithoutCharacter || 0}
                        formatter={(value) => formatNumber(Number(value))}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="未登录用户"
                        value={userInfoStats?.usersNeverLoggedIn || 0}
                        formatter={(value) => formatNumber(Number(value))}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="同IP登录"
                        value={userInfoStats?.sameIpLogins || 0}
                        formatter={(value) => formatNumber(Number(value))}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="渠道注册"
                        value={userInfoStats?.registrationsWithChannel || 0}
                        formatter={(value) => formatNumber(Number(value))}
                      />
                    </Col>
                  </Row>
                </Card>
              </Col>
            </Row>
                </div>
              )
            },
            {
              key: 'payment-analysis',
              label: '付费分析',
              children: (
                <div>
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={16}>
                <Card title="付费统计" loading={paymentInfoLoading}>
                  <Row gutter={[16, 16]}>
                    <Col xs={24} sm={12} md={6}>
                      <Statistic
                        title="总营收"
                        value={paymentInfoStats?.totalRevenue || 0}
                        formatter={(value) => formatCurrency(Number(value))}
                        prefix={<DollarOutlined />}
                      />
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                      <Statistic
                        title="总订单"
                        value={paymentInfoStats?.totalOrders || 0}
                        prefix={<ShoppingCartOutlined />}
                      />
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                      <Statistic
                        title="成功订单"
                        value={paymentInfoStats?.completedOrders || 0}
                        valueStyle={{ color: '#3f8600' }}
                      />
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                      <Statistic
                        title="失败订单"
                        value={paymentInfoStats?.failedOrders || 0}
                        valueStyle={{ color: '#cf1322' }}
                      />
                    </Col>
                  </Row>
                </Card>
              </Col>
              <Col xs={24} lg={8}>
                <Card title="平均订单价值" loading={paymentInfoLoading}>
                  <Statistic
                    value={paymentInfoStats?.averageOrderValue || 0}
                    formatter={(value) => formatCurrency(Number(value))}
                    prefix={<DollarOutlined />}
                  />
                </Card>
              </Col>
            </Row>

            {/* 付费排行榜 */}
            <Card title="付费排行榜" style={{ marginTop: '16px' }} loading={paymentInfoLoading}>
              <Table
                dataSource={paymentInfoStats?.topPayers || []}
                pagination={false}
                size="small"
                columns={[
                  {
                    title: '排名',
                    key: 'rank',
                    render: (_, __, index) => (
                      <span style={{ fontWeight: 'bold' }}>
                        {index + 1 <= 3 ? <TrophyOutlined style={{ color: ['#FFD700', '#C0C0C0', '#CD7F32'][index] }} /> : null}
                        {index + 1}
                      </span>
                    ),
                    width: 80,
                  },
                  {
                    title: '账号ID',
                    dataIndex: 'accountId',
                    key: 'accountId',
                  },
                  {
                    title: '昵称',
                    dataIndex: 'nickname',
                    key: 'nickname',
                  },
                  {
                    title: '总金额',
                    dataIndex: 'totalAmount',
                    key: 'totalAmount',
                    render: (amount: number) => formatCurrency(amount),
                  },
                  {
                    title: '订单数',
                    dataIndex: 'orderCount',
                    key: 'orderCount',
                  },
                  {
                    title: '最后付费时间',
                    dataIndex: 'lastPaymentTime',
                    key: 'lastPaymentTime',
                    render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm'),
                  },
                ]}
              />
            </Card>
                </div>
              )
            },
            {
              key: 'conversion-analysis',
              label: '转化分析',
              children: (
                <div>
            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <Card title="转化率分析" loading={conversionLoading}>
                  <div style={{ marginBottom: '16px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                      <span>访问-注册转化率</span>
                      <span>{formatPercentage(conversionAnalysis?.visitToRegistrationRate || 0)}</span>
                    </div>
                    <Progress percent={(conversionAnalysis?.visitToRegistrationRate || 0) * 100} showInfo={false} />
                  </div>
                  <div style={{ marginBottom: '16px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                      <span>注册-付费转化率</span>
                      <span>{formatPercentage(conversionAnalysis?.registrationToPaymentRate || 0)}</span>
                    </div>
                    <Progress percent={(conversionAnalysis?.registrationToPaymentRate || 0) * 100} showInfo={false} />
                  </div>
                  <div style={{ marginBottom: '16px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                      <span>注册-创角转化率</span>
                      <span>{formatPercentage(conversionAnalysis?.registrationToCharacterCreationRate || 0)}</span>
                    </div>
                    <Progress percent={(conversionAnalysis?.registrationToCharacterCreationRate || 0) * 100} showInfo={false} />
                  </div>
                </Card>
              </Col>
              <Col xs={24} md={12}>
                <Card title="留存率分析" loading={retentionLoading}>
                  <div style={{ marginBottom: '16px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                      <span>次日留存率</span>
                      <span>{formatPercentage(retentionAnalysis?.nextDayRetentionRate || 0)}</span>
                    </div>
                    <Progress percent={(retentionAnalysis?.nextDayRetentionRate || 0) * 100} showInfo={false} strokeColor="#52c41a" />
                  </div>
                  <div style={{ marginBottom: '16px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                      <span>七日留存率</span>
                      <span>{formatPercentage(retentionAnalysis?.sevenDayRetentionRate || 0)}</span>
                    </div>
                    <Progress percent={(retentionAnalysis?.sevenDayRetentionRate || 0) * 100} showInfo={false} strokeColor="#1890ff" />
                  </div>
                  <div style={{ marginBottom: '16px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                      <span>月留存率</span>
                      <span>{formatPercentage(retentionAnalysis?.monthlyRetentionRate || 0)}</span>
                    </div>
                    <Progress percent={(retentionAnalysis?.monthlyRetentionRate || 0) * 100} showInfo={false} strokeColor="#722ed1" />
                  </div>
                </Card>
              </Col>
            </Row>

            <Card title="活跃用户分析" style={{ marginTop: '16px' }} loading={activeUserLoading}>
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={8}>
                  <Statistic
                    title="日活跃用户 (DAU)"
                    value={activeUserAnalysis?.dau || 0}
                    formatter={(value) => formatNumber(Number(value))}
                    prefix={<TeamOutlined />}
                  />
                </Col>
                <Col xs={24} sm={8}>
                  <Statistic
                    title="周活跃用户 (WAU)"
                    value={activeUserAnalysis?.wau || 0}
                    formatter={(value) => formatNumber(Number(value))}
                    prefix={<TeamOutlined />}
                  />
                </Col>
                <Col xs={24} sm={8}>
                  <Statistic
                    title="月活跃用户 (MAU)"
                    value={activeUserAnalysis?.mau || 0}
                    formatter={(value) => formatNumber(Number(value))}
                    prefix={<TeamOutlined />}
                  />
                </Col>
              </Row>
            </Card>
                </div>
              )
            }
          ]}
        />
      </div>
    </ProtectedRoute>
  );
}
