'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Search,
  DollarSign,
  Eye,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Calendar,
  CreditCard
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';

const PaymentsPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [paymentStatus, setPaymentStatus] = useState<string>('all');

  // 模拟支付数据
  const paymentsData = [
    {
      key: '1',
      orderId: 'PAY202406250001',
      playerId: 1001,
      playerName: '龙战士',
      amount: 98.00,
      currency: 'CNY',
      paymentMethod: '微信支付',
      productName: '钻石礼包(大)',
      productId: 'DIAMOND_LARGE',
      quantity: 1,
      status: 'completed',
      transactionId: 'WX20240625143025001',
      createdAt: '2024-06-25 14:30:25',
      completedAt: '2024-06-25 14:30:45',
      serverId: 1,
      serverName: '服务器1'
    },
    {
      key: '2',
      orderId: 'PAY202406250002',
      playerId: 1002,
      playerName: '法师小明',
      amount: 30.00,
      currency: 'CNY',
      paymentMethod: '支付宝',
      productName: '月卡',
      productId: 'MONTHLY_CARD',
      quantity: 1,
      status: 'pending',
      transactionId: 'ALI20240625134512002',
      createdAt: '2024-06-25 13:45:12',
      completedAt: null,
      serverId: 2,
      serverName: '服务器2'
    },
    {
      key: '3',
      orderId: 'PAY202406250003',
      playerId: 1003,
      playerName: '弓箭手',
      amount: 198.00,
      currency: 'CNY',
      paymentMethod: 'Apple Pay',
      productName: '钻石礼包(超级)',
      productId: 'DIAMOND_SUPER',
      quantity: 1,
      status: 'failed',
      transactionId: 'APP20240625120130003',
      createdAt: '2024-06-25 12:01:30',
      completedAt: null,
      failReason: '支付超时',
      serverId: 1,
      serverName: '服务器1'
    }
  ];

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'completed':
        return <Tag color="success" icon={<CheckCircleOutlined />}>已完成</Tag>;
      case 'pending':
        return <Tag color="processing" icon={<ExclamationCircleOutlined />}>处理中</Tag>;
      case 'failed':
        return <Tag color="error" icon={<CloseCircleOutlined />}>失败</Tag>;
      case 'refunded':
        return <Tag color="warning">已退款</Tag>;
      default:
        return <Tag color="default">未知</Tag>;
    }
  };

  const handleSearch = () => {
    console.log('搜索:', searchText);
  };

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST]}>
      <MainLayout>
        <div className="p-6">
          <div className="mb-6 flex justify-between items-center">
            <h1 className="text-3xl font-bold text-gray-900">支付管理</h1>
          </div>

          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">今日收入</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  ¥12,580.50
                </div>
                <p className="text-xs text-muted-foreground">
                  今日支付收入
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">本月收入</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  ¥458,920.80
                </div>
                <p className="text-xs text-muted-foreground">
                  本月累计收入
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">今日订单</CardTitle>
                <CreditCard className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">
                  1,256
                </div>
                <p className="text-xs text-muted-foreground">
                  今日订单数量
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">成功率</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  96.8%
                </div>
                <p className="text-xs text-muted-foreground">
                  支付成功率
                </p>
              </CardContent>
            </Card>
          </div>

          {/* 搜索和筛选 */}
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索订单号或玩家"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    className="pl-10"
                  />
                </div>

                <Select value={paymentStatus} onValueChange={setPaymentStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="支付状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="completed">已完成</SelectItem>
                    <SelectItem value="pending">处理中</SelectItem>
                    <SelectItem value="failed">失败</SelectItem>
                    <SelectItem value="refunded">已退款</SelectItem>
                  </SelectContent>
                </Select>

                <Button variant="outline">
                  <Calendar className="h-4 w-4 mr-2" />
                  日期范围
                </Button>

                <Button>
                  <Search className="h-4 w-4 mr-2" />
                  搜索
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 支付列表 */}
          <Card>
            <CardHeader>
              <CardTitle>支付订单列表</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>订单号</TableHead>
                      <TableHead>玩家</TableHead>
                      <TableHead>商品</TableHead>
                      <TableHead>金额</TableHead>
                      <TableHead>支付方式</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>时间</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paymentsData.map((payment: any) => (
                      <TableRow key={payment.key}>
                        <TableCell className="font-medium">{payment.orderId}</TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{payment.playerName}</div>
                            <div className="text-sm text-muted-foreground">ID: {payment.playerId}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{payment.productName}</div>
                            <div className="text-sm text-muted-foreground">{payment.productId}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium text-green-600">
                            ¥{payment.amount.toFixed(2)}
                          </div>
                        </TableCell>
                        <TableCell>{payment.paymentMethod}</TableCell>
                        <TableCell>
                          <Badge
                            variant={payment.status === 'completed' ? 'default' :
                                   payment.status === 'pending' ? 'secondary' :
                                   payment.status === 'failed' ? 'destructive' : 'outline'}
                          >
                            {payment.status === 'completed' ? '已完成' :
                             payment.status === 'pending' ? '处理中' :
                             payment.status === 'failed' ? '失败' : '已退款'}
                          </Badge>
                        </TableCell>
                        <TableCell>{payment.createTime || '2024-06-27 10:30:00'}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            {payment.status === 'completed' && (
                              <Button variant="outline" size="sm">
                                退款
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

export default PaymentsPage;
