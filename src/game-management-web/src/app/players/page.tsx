'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Search,
  User,
  Plus,
  Edit,
  Trash2,
  Ban,
  CheckCircle,
  Shield,
  ShieldOff,
  Calendar,
  Server,
  Crown
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';
import { useQuery } from '@tanstack/react-query';
import { players<PERSON><PERSON>, <PERSON>, PlayerStats } from '@/lib/api';

const PlayersPage: React.FC = () => {
  const [searchText, setSearchText] = useState('');
  const [selectedServer, setSelectedServer] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // 获取玩家统计数据
  const { data: playerStats, isLoading: statsLoading } = useQuery({
    queryKey: ['playerStats'],
    queryFn: () => playersApi.getPlayerStats(),
  });

  // 获取玩家列表
  const { data: playersData, isLoading: playersLoading } = useQuery({
    queryKey: ['players', currentPage, pageSize],
    queryFn: () => playersApi.getPlayers(currentPage, pageSize),
  });

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN').format(amount);
  };

  const getVipBadgeColor = (vipLevel: number) => {
    if (vipLevel >= 8) return 'bg-yellow-500';
    if (vipLevel >= 5) return 'bg-purple-500';
    if (vipLevel >= 3) return 'bg-blue-500';
    return 'bg-gray-500';
  };

  const getStatusBadge = (player: Player) => {
    if (player.isBanned) {
      return <Badge variant="destructive">已封禁</Badge>;
    }
    // 简单的在线状态判断 - 如果最后登录时间在1小时内则认为在线
    const lastLogin = new Date(player.lastLoginAt || '');
    const now = new Date();
    const isOnline = (now.getTime() - lastLogin.getTime()) < 3600000; // 1小时

    return isOnline ?
      <Badge variant="default" className="bg-green-500">在线</Badge> :
      <Badge variant="secondary">离线</Badge>;
  };

  const handleEdit = (player: Player) => {
    // TODO: 实现编辑功能
    console.log('编辑玩家:', player.nickname);
  };

  const handleBan = (player: Player) => {
    // TODO: 实现封禁功能
    console.log('封禁玩家:', player.nickname);
  };

  const handleUnban = (player: Player) => {
    // TODO: 实现解封功能
    console.log('解封玩家:', player.nickname);
  };

  const handleSearch = () => {
    // TODO: 实现搜索功能
    console.log('搜索:', searchText);
  };



  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST, ROLES.CUSTOMER_SERVICE_MANAGER, ROLES.CUSTOMER_SERVICE_SPECIALIST]}>
      <MainLayout>
        <div className="p-6">
          <div className="mb-6 flex justify-between items-center">
            <h1 className="text-3xl font-bold text-gray-900">玩家管理</h1>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              添加玩家
            </Button>
          </div>

          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总玩家数</CardTitle>
                <User className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {formatNumber(playerStats?.totalPlayers || 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  注册玩家总数
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">活跃玩家</CardTitle>
                <User className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {formatNumber(playerStats?.activePlayers || 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  近期活跃玩家
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">VIP玩家</CardTitle>
                <Crown className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">
                  {formatNumber(playerStats?.vipPlayers || 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  VIP等级玩家
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">今日新增</CardTitle>
                <User className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {formatNumber(playerStats?.newPlayersToday || 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  今日新注册
                </p>
              </CardContent>
            </Card>
          </div>

          {/* 搜索和筛选 */}
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索玩家昵称或账号ID"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    className="pl-10"
                  />
                </div>

                <Select value={selectedServer} onValueChange={setSelectedServer}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择服务器" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部服务器</SelectItem>
                    <SelectItem value="1">服务器1</SelectItem>
                    <SelectItem value="2">服务器2</SelectItem>
                    <SelectItem value="3">服务器3</SelectItem>
                  </SelectContent>
                </Select>

                <Button variant="outline">
                  <Calendar className="h-4 w-4 mr-2" />
                  日期范围
                </Button>

                <Button onClick={handleSearch}>
                  <Search className="h-4 w-4 mr-2" />
                  搜索
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 玩家列表 */}
          <Card>
            <CardHeader>
              <CardTitle>玩家列表</CardTitle>
            </CardHeader>
            <CardContent>
              {playersLoading ? (
                <div className="flex justify-center py-8">
                  <div className="text-muted-foreground">加载中...</div>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>账号ID</TableHead>
                        <TableHead>昵称</TableHead>
                        <TableHead>等级</TableHead>
                        <TableHead>职业</TableHead>
                        <TableHead>VIP等级</TableHead>
                        <TableHead>金币</TableHead>
                        <TableHead>钻石</TableHead>
                        <TableHead>服务器</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>最后登录</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {playersData?.data?.map((player: Player) => (
                        <TableRow key={player.id}>
                          <TableCell className="font-medium">{player.id}</TableCell>
                          <TableCell>{player.accountId}</TableCell>
                          <TableCell className="font-medium">{player.nickname}</TableCell>
                          <TableCell>
                            <Badge variant="outline">Lv.{player.level}</Badge>
                          </TableCell>
                          <TableCell>{player.class}</TableCell>
                          <TableCell>
                            <Badge className={`${getVipBadgeColor(player.vipLevel)} text-white`}>
                              VIP{player.vipLevel}
                            </Badge>
                          </TableCell>
                          <TableCell>{formatCurrency(player.gold)}</TableCell>
                          <TableCell>{formatCurrency(player.diamonds)}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Server className="h-4 w-4 mr-1 text-muted-foreground" />
                              {player.serverName}
                            </div>
                          </TableCell>
                          <TableCell>{getStatusBadge(player)}</TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {player.lastLoginAt ? new Date(player.lastLoginAt).toLocaleString('zh-CN') : '从未登录'}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEdit(player)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              {player.isBanned ? (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleUnban(player)}
                                >
                                  <CheckCircle className="h-4 w-4" />
                                </Button>
                              ) : (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleBan(player)}
                                >
                                  <Ban className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}

              {/* 分页 */}
              <div className="flex items-center justify-between space-x-2 py-4">
                <div className="text-sm text-muted-foreground">
                  显示第 {(currentPage - 1) * pageSize + 1} - {Math.min(currentPage * pageSize, playerStats?.totalPlayers || 0)} 条，
                  共 {playerStats?.totalPlayers || 0} 条记录
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage <= 1}
                  >
                    上一页
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage * pageSize >= (playerStats?.totalPlayers || 0)}
                  >
                    下一页
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

export default PlayersPage;
