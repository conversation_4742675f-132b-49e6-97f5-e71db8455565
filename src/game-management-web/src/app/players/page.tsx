'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  User,
  Plus,
  Edit,
  Trash2,
  Ban,
  CheckCircle
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';

const PlayersPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedServer, setSelectedServer] = useState<string>('all');

  // 模拟玩家数据
  const playersData = [
    {
      key: '1',
      id: 1001,
      accountId: 'ACC001',
      nickname: '龙战士',
      level: 85,
      class: '战士',
      experience: 2450000,
      gold: 125000,
      diamonds: 5600,
      vipLevel: 8,
      serverId: 1,
      serverName: '服务器1',
      lastLoginAt: '2024-06-25 14:30:25',
      totalPlayTime: '156小时30分钟',
      ipAddress: '*************',
      isBanned: false,
      createdAt: '2024-01-15 10:20:30',
      status: 'online'
    },
    {
      key: '2',
      id: 1002,
      accountId: 'ACC002',
      nickname: '法师小明',
      level: 72,
      class: '法师',
      experience: 1850000,
      gold: 89000,
      diamonds: 3200,
      vipLevel: 5,
      serverId: 2,
      serverName: '服务器2',
      lastLoginAt: '2024-06-25 13:45:12',
      totalPlayTime: '98小时15分钟',
      ipAddress: '*************',
      isBanned: false,
      createdAt: '2024-02-20 16:45:20',
      status: 'online'
    },
    {
      key: '3',
      id: 1003,
      accountId: 'ACC003',
      nickname: '弓箭手',
      level: 68,
      class: '弓箭手',
      experience: 1650000,
      gold: 76000,
      diamonds: 2800,
      vipLevel: 3,
      serverId: 1,
      serverName: '服务器1',
      lastLoginAt: '2024-06-24 20:15:45',
      totalPlayTime: '87小时45分钟',
      ipAddress: '*************',
      isBanned: true,
      bannedUntil: '2024-07-01 00:00:00',
      banReason: '使用外挂',
      createdAt: '2024-03-10 09:30:15',
      status: 'offline'
    }
  ];

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '账号ID',
      dataIndex: 'accountId',
      key: 'accountId',
      width: 100,
    },
    {
      title: '昵称',
      dataIndex: 'nickname',
      key: 'nickname',
      width: 120,
    },
    {
      title: '等级',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      sorter: (a: any, b: any) => a.level - b.level,
    },
    {
      title: '职业',
      dataIndex: 'class',
      key: 'class',
      width: 100,
    },
    {
      title: 'VIP等级',
      dataIndex: 'vipLevel',
      key: 'vipLevel',
      width: 100,
      render: (vipLevel: number) => (
        <Tag color={vipLevel >= 5 ? 'gold' : 'blue'}>VIP{vipLevel}</Tag>
      ),
    },
    {
      title: '服务器',
      dataIndex: 'serverName',
      key: 'serverName',
      width: 120,
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (record: any) => {
        if (record.isBanned) {
          return <Tag color="red">已封禁</Tag>;
        }
        return record.status === 'online' ? 
          <Tag color="green">在线</Tag> : 
          <Tag color="default">离线</Tag>;
      },
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginAt',
      key: 'lastLoginAt',
      width: 160,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (record: any) => (
        <Space size="small">
          <Button 
            type="link" 
            size="small" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          {record.isBanned ? (
            <Button 
              type="link" 
              size="small" 
              icon={<CheckCircleOutlined />}
              onClick={() => handleUnban(record)}
            >
              解封
            </Button>
          ) : (
            <Button 
              type="link" 
              size="small" 
              danger
              icon={<StopOutlined />}
              onClick={() => handleBan(record)}
            >
              封禁
            </Button>
          )}
        </Space>
      ),
    },
  ];

  const handleEdit = (record: any) => {
    message.info(`编辑玩家: ${record.nickname}`);
  };

  const handleBan = (record: any) => {
    Modal.confirm({
      title: '确认封禁',
      content: `确定要封禁玩家 "${record.nickname}" 吗？`,
      onOk() {
        message.success(`已封禁玩家: ${record.nickname}`);
      },
    });
  };

  const handleUnban = (record: any) => {
    Modal.confirm({
      title: '确认解封',
      content: `确定要解封玩家 "${record.nickname}" 吗？`,
      onOk() {
        message.success(`已解封玩家: ${record.nickname}`);
      },
    });
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
    message.info(`搜索: ${value}`);
  };

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST, ROLES.CUSTOMER_SERVICE_MANAGER, ROLES.CUSTOMER_SERVICE_SPECIALIST]}>
      <div>
        <Title level={2}>玩家管理</Title>
        
        {/* 统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={8} lg={6}>
            <Card>
              <Statistic
                title="总玩家数"
                value={125430}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8} lg={6}>
            <Card>
              <Statistic
                title="在线玩家"
                value={8567}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8} lg={6}>
            <Card>
              <Statistic
                title="VIP玩家"
                value={2341}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8} lg={6}>
            <Card>
              <Statistic
                title="封禁玩家"
                value={156}
                prefix={<StopOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 搜索和筛选 */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={8} lg={6}>
              <Input.Search
                placeholder="搜索玩家昵称或账号ID"
                allowClear
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={24} sm={8} lg={6}>
              <Select
                placeholder="选择服务器"
                style={{ width: '100%' }}
                value={selectedServer}
                onChange={setSelectedServer}
              >
                <Select.Option value="all">全部服务器</Select.Option>
                <Select.Option value="1">服务器1</Select.Option>
                <Select.Option value="2">服务器2</Select.Option>
                <Select.Option value="3">服务器3</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={8} lg={6}>
              <RangePicker style={{ width: '100%' }} />
            </Col>
            <Col xs={24} sm={8} lg={6}>
              <Button type="primary" icon={<SearchOutlined />}>
                搜索
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 玩家列表 */}
        <Card>
          <Table
            columns={columns}
            dataSource={playersData}
            loading={loading}
            pagination={{
              total: 125430,
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            scroll={{ x: 1200 }}
          />
        </Card>
      </div>
    </ProtectedRoute>
  );
};

export default PlayersPage;
