'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import {
  Download,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  FileSpreadsheet
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';

const ReportsPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [reportType, setReportType] = useState<string>('revenue');
  const [dateRange, setDateRange] = useState<string>('week');

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER]}>
      <MainLayout>
        <div className="p-6">
          <div className="mb-6 flex justify-between items-center">
            <h1 className="text-3xl font-bold text-gray-900">数据报表</h1>
            <Button>
              <Download className="h-4 w-4 mr-2" />
              导出报表
            </Button>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>报表管理</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-4">
                  <Select value={reportType} onValueChange={setReportType}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="报表类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="revenue">收入报表</SelectItem>
                      <SelectItem value="player">玩家报表</SelectItem>
                      <SelectItem value="server">服务器报表</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={dateRange} onValueChange={setDateRange}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="时间范围" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="week">本周</SelectItem>
                      <SelectItem value="month">本月</SelectItem>
                      <SelectItem value="quarter">本季度</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="text-center py-8 text-gray-500">
                  <BarChart className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>数据报表功能正在开发中...</p>
                  <p className="text-sm mt-2">完整的报表分析功能即将上线</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

export default ReportsPage;
