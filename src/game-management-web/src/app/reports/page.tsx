'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import {
  Download,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  FileSpreadsheet
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';

const ReportsPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [reportType, setReportType] = useState<string>('revenue');
  const [dateRange, setDateRange] = useState<string>('week');

  // 模拟报表数据
  const revenueData = [
    { date: '2024-06-19', revenue: 125600, orders: 1256, avgOrder: 100 },
    { date: '2024-06-20', revenue: 134200, orders: 1342, avgOrder: 100 },
    { date: '2024-06-21', revenue: 145800, orders: 1458, avgOrder: 100 },
    { date: '2024-06-22', revenue: 156900, orders: 1569, avgOrder: 100 },
    { date: '2024-06-23', revenue: 167300, orders: 1673, avgOrder: 100 },
    { date: '2024-06-24', revenue: 178500, orders: 1785, avgOrder: 100 },
    { date: '2024-06-25', revenue: 189200, orders: 1892, avgOrder: 100 }
  ];

  const playerData = [
    { date: '2024-06-19', newUsers: 456, activeUsers: 12500, retention: 68.5 },
    { date: '2024-06-20', newUsers: 523, activeUsers: 13200, retention: 69.2 },
    { date: '2024-06-21', newUsers: 612, activeUsers: 13800, retention: 70.1 },
    { date: '2024-06-22', newUsers: 589, activeUsers: 14200, retention: 71.3 },
    { date: '2024-06-23', newUsers: 634, activeUsers: 14600, retention: 72.0 },
    { date: '2024-06-24', newUsers: 678, activeUsers: 15100, retention: 72.8 },
    { date: '2024-06-25', newUsers: 712, activeUsers: 15600, retention: 73.5 }
  ];

  const serverData = [
    { server: '服务器1', players: 1256, cpu: 45.2, memory: 68.5, uptime: 99.8 },
    { server: '服务器2', players: 1890, cpu: 85.7, memory: 92.3, uptime: 98.5 },
    { server: '服务器3', players: 0, cpu: 0, memory: 0, uptime: 0 },
    { server: '服务器4', players: 0, cpu: 0, memory: 0, uptime: 95.2 }
  ];

  const getReportColumns = () => {
    switch (reportType) {
      case 'revenue':
        return [
          { title: '日期', dataIndex: 'date', key: 'date' },
          { 
            title: '收入 (元)', 
            dataIndex: 'revenue', 
            key: 'revenue',
            render: (value: number) => `¥${value.toLocaleString()}`
          },
          { 
            title: '订单数', 
            dataIndex: 'orders', 
            key: 'orders',
            render: (value: number) => value.toLocaleString()
          },
          { 
            title: '客单价 (元)', 
            dataIndex: 'avgOrder', 
            key: 'avgOrder',
            render: (value: number) => `¥${value}`
          }
        ];
      case 'players':
        return [
          { title: '日期', dataIndex: 'date', key: 'date' },
          { 
            title: '新增用户', 
            dataIndex: 'newUsers', 
            key: 'newUsers',
            render: (value: number) => value.toLocaleString()
          },
          { 
            title: '活跃用户', 
            dataIndex: 'activeUsers', 
            key: 'activeUsers',
            render: (value: number) => value.toLocaleString()
          },
          { 
            title: '留存率 (%)', 
            dataIndex: 'retention', 
            key: 'retention',
            render: (value: number) => `${value}%`
          }
        ];
      case 'servers':
        return [
          { title: '服务器', dataIndex: 'server', key: 'server' },
          { 
            title: '在线玩家', 
            dataIndex: 'players', 
            key: 'players',
            render: (value: number) => value.toLocaleString()
          },
          { 
            title: 'CPU使用率', 
            dataIndex: 'cpu', 
            key: 'cpu',
            render: (value: number) => (
              <div>
                <div>{value}%</div>
                <Progress percent={value} size="small" showInfo={false} />
              </div>
            )
          },
          { 
            title: '内存使用率', 
            dataIndex: 'memory', 
            key: 'memory',
            render: (value: number) => (
              <div>
                <div>{value}%</div>
                <Progress percent={value} size="small" showInfo={false} />
              </div>
            )
          },
          { 
            title: '可用性 (%)', 
            dataIndex: 'uptime', 
            key: 'uptime',
            render: (value: number) => `${value}%`
          }
        ];
      default:
        return [];
    }
  };

  const getReportData = () => {
    switch (reportType) {
      case 'revenue':
        return revenueData;
      case 'players':
        return playerData;
      case 'servers':
        return serverData;
      default:
        return [];
    }
  };

  const getStatistics = () => {
    switch (reportType) {
      case 'revenue':
        const totalRevenue = revenueData.reduce((sum, item) => sum + item.revenue, 0);
        const totalOrders = revenueData.reduce((sum, item) => sum + item.orders, 0);
        const avgRevenue = totalRevenue / revenueData.length;
        return [
          { title: '总收入', value: totalRevenue, prefix: '¥', suffix: '', color: '#3f8600' },
          { title: '总订单数', value: totalOrders, prefix: '', suffix: '', color: '#1890ff' },
          { title: '日均收入', value: Math.round(avgRevenue), prefix: '¥', suffix: '', color: '#722ed1' },
          { title: '平均客单价', value: Math.round(totalRevenue / totalOrders), prefix: '¥', suffix: '', color: '#fa8c16' }
        ];
      case 'players':
        const totalNewUsers = playerData.reduce((sum, item) => sum + item.newUsers, 0);
        const avgActiveUsers = Math.round(playerData.reduce((sum, item) => sum + item.activeUsers, 0) / playerData.length);
        const avgRetention = (playerData.reduce((sum, item) => sum + item.retention, 0) / playerData.length).toFixed(1);
        return [
          { title: '总新增用户', value: totalNewUsers, prefix: '', suffix: '', color: '#3f8600' },
          { title: '平均活跃用户', value: avgActiveUsers, prefix: '', suffix: '', color: '#1890ff' },
          { title: '平均留存率', value: parseFloat(avgRetention), prefix: '', suffix: '%', color: '#722ed1' },
          { title: '日均新增', value: Math.round(totalNewUsers / playerData.length), prefix: '', suffix: '', color: '#fa8c16' }
        ];
      case 'servers':
        const onlineServers = serverData.filter(s => s.uptime > 0).length;
        const totalPlayers = serverData.reduce((sum, item) => sum + item.players, 0);
        const avgCpu = (serverData.reduce((sum, item) => sum + item.cpu, 0) / serverData.length).toFixed(1);
        const avgUptime = (serverData.reduce((sum, item) => sum + item.uptime, 0) / serverData.length).toFixed(1);
        return [
          { title: '在线服务器', value: onlineServers, prefix: '', suffix: `/${serverData.length}`, color: '#3f8600' },
          { title: '总在线玩家', value: totalPlayers, prefix: '', suffix: '', color: '#1890ff' },
          { title: '平均CPU使用率', value: parseFloat(avgCpu), prefix: '', suffix: '%', color: '#722ed1' },
          { title: '平均可用性', value: parseFloat(avgUptime), prefix: '', suffix: '%', color: '#fa8c16' }
        ];
      default:
        return [];
    }
  };

  const handleExport = () => {
    // 模拟导出功能
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      // 这里可以实现真实的导出逻辑
      const link = document.createElement('a');
      link.href = 'data:text/csv;charset=utf-8,';
      link.download = `${reportType}_report_${new Date().toISOString().split('T')[0]}.csv`;
      // link.click();
      console.log('导出报表:', reportType);
    }, 1000);
  };

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER]}>
      <div>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
          <Title level={2}>数据报表</Title>
          <Space>
            <Button 
              type="primary" 
              icon={<FileExcelOutlined />}
              loading={loading}
              onClick={handleExport}
            >
              导出Excel
            </Button>
            <Button icon={<DownloadOutlined />}>
              导出PDF
            </Button>
          </Space>
        </div>
        
        {/* 筛选条件 */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={8} lg={6}>
              <Select
                placeholder="报表类型"
                style={{ width: '100%' }}
                value={reportType}
                onChange={setReportType}
              >
                <Select.Option value="revenue">
                  <BarChartOutlined style={{ marginRight: 8 }} />
                  收入报表
                </Select.Option>
                <Select.Option value="players">
                  <LineChartOutlined style={{ marginRight: 8 }} />
                  玩家报表
                </Select.Option>
                <Select.Option value="servers">
                  <PieChartOutlined style={{ marginRight: 8 }} />
                  服务器报表
                </Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={8} lg={6}>
              <Select
                placeholder="时间范围"
                style={{ width: '100%' }}
                value={dateRange}
                onChange={setDateRange}
              >
                <Select.Option value="today">今天</Select.Option>
                <Select.Option value="week">本周</Select.Option>
                <Select.Option value="month">本月</Select.Option>
                <Select.Option value="quarter">本季度</Select.Option>
                <Select.Option value="year">本年</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={8} lg={6}>
              <RangePicker style={{ width: '100%' }} />
            </Col>
          </Row>
        </Card>

        {/* 统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          {getStatistics().map((stat, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <Card>
                <Statistic
                  title={stat.title}
                  value={stat.value}
                  prefix={stat.prefix}
                  suffix={stat.suffix}
                  valueStyle={{ color: stat.color }}
                />
              </Card>
            </Col>
          ))}
        </Row>

        {/* 报表数据 */}
        <Card 
          title={
            <Space>
              {reportType === 'revenue' && <BarChartOutlined />}
              {reportType === 'players' && <LineChartOutlined />}
              {reportType === 'servers' && <PieChartOutlined />}
              {reportType === 'revenue' && '收入明细'}
              {reportType === 'players' && '玩家数据'}
              {reportType === 'servers' && '服务器状态'}
            </Space>
          }
        >
          <Table
            columns={getReportColumns()}
            dataSource={getReportData()}
            loading={loading}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
          />
        </Card>
      </div>
    </ProtectedRoute>
  );
};

export default ReportsPage;
