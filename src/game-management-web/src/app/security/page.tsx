'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import { 
  Card, 
  Table, 
  Button, 
  Input, 
  Space, 
  Tag, 
  Typography, 
  Row, 
  Col,
  Select,
  DatePicker,
  Modal,
  message,
  Statistic,
  Alert
} from 'antd';
import {
  SearchOutlined,
  SecurityScanOutlined,
  WarningOutlined,
  StopOutlined,
  EyeOutlined,
  UserDeleteOutlined,
  SafetyOutlined
} from '@ant-design/icons';
import { ROLES } from '@/contexts/AuthContext';

const { Title } = Typography;
const { RangePicker } = DatePicker;

const SecurityPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [riskLevel, setRiskLevel] = useState<string>('all');
  const [eventType, setEventType] = useState<string>('all');

  // 模拟安全事件数据
  const securityEventsData = [
    {
      key: '1',
      id: 7001,
      type: 'suspicious_login',
      title: '异常登录检测',
      description: '玩家从异常地理位置登录',
      riskLevel: 'high',
      playerId: 12345,
      playerName: '玩家ABC',
      ip: '************',
      location: '美国纽约',
      deviceInfo: 'iPhone 13 Pro',
      timestamp: '2024-06-25 14:30:25',
      status: 'investigating',
      actionTaken: null,
      handledBy: '安全专员A'
    },
    {
      key: '2',
      id: 7002,
      type: 'cheat_detection',
      title: '外挂检测',
      description: '检测到玩家使用速度外挂',
      riskLevel: 'critical',
      playerId: 67890,
      playerName: '玩家XYZ',
      ip: '*************',
      location: '中国上海',
      deviceInfo: 'Windows 10',
      timestamp: '2024-06-25 13:45:12',
      status: 'resolved',
      actionTaken: '账号封禁7天',
      handledBy: '安全专员B'
    },
    {
      key: '3',
      id: 7003,
      type: 'payment_fraud',
      title: '支付欺诈',
      description: '检测到可疑的充值行为',
      riskLevel: 'high',
      playerId: 11111,
      playerName: '玩家DEF',
      ip: '*************',
      location: '中国北京',
      deviceInfo: 'Android 12',
      timestamp: '2024-06-25 12:20:30',
      status: 'pending',
      actionTaken: null,
      handledBy: null
    },
    {
      key: '4',
      id: 7004,
      type: 'account_sharing',
      title: '账号共享',
      description: '检测到账号在多个设备同时登录',
      riskLevel: 'medium',
      playerId: 22222,
      playerName: '玩家GHI',
      ip: '************',
      location: '中国广州',
      deviceInfo: 'Multiple devices',
      timestamp: '2024-06-25 11:15:45',
      status: 'resolved',
      actionTaken: '发送安全提醒',
      handledBy: '安全专员A'
    }
  ];

  const getRiskLevelTag = (level: string) => {
    switch (level) {
      case 'critical':
        return <Tag color="red" icon={<WarningOutlined />}>严重</Tag>;
      case 'high':
        return <Tag color="orange" icon={<WarningOutlined />}>高</Tag>;
      case 'medium':
        return <Tag color="yellow">中</Tag>;
      case 'low':
        return <Tag color="green">低</Tag>;
      default:
        return <Tag color="default">未知</Tag>;
    }
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'pending':
        return <Tag color="red">待处理</Tag>;
      case 'investigating':
        return <Tag color="processing">调查中</Tag>;
      case 'resolved':
        return <Tag color="success">已处理</Tag>;
      default:
        return <Tag color="default">未知</Tag>;
    }
  };

  const getEventTypeTag = (type: string) => {
    const typeMap = {
      suspicious_login: { color: 'orange', text: '异常登录' },
      cheat_detection: { color: 'red', text: '外挂检测' },
      payment_fraud: { color: 'purple', text: '支付欺诈' },
      account_sharing: { color: 'blue', text: '账号共享' },
      data_breach: { color: 'red', text: '数据泄露' }
    };
    const config = typeMap[type as keyof typeof typeMap] || { color: 'default', text: type };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '事件类型',
      key: 'type',
      width: 120,
      render: (record: any) => getEventTypeTag(record.type),
    },
    {
      title: '事件标题',
      key: 'title',
      width: 200,
      render: (record: any) => (
        <div>
          <div style={{ fontWeight: 500, marginBottom: 4 }}>
            {record.title}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.description}
          </div>
        </div>
      ),
    },
    {
      title: '风险等级',
      key: 'riskLevel',
      width: 100,
      render: (record: any) => getRiskLevelTag(record.riskLevel),
    },
    {
      title: '涉及玩家',
      key: 'player',
      width: 150,
      render: (record: any) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.playerName}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            ID: {record.playerId}
          </div>
        </div>
      ),
    },
    {
      title: 'IP地址',
      key: 'ip',
      width: 120,
      render: (record: any) => (
        <div>
          <div style={{ fontFamily: 'monospace', fontSize: '12px' }}>{record.ip}</div>
          <div style={{ fontSize: '11px', color: '#666' }}>{record.location}</div>
        </div>
      ),
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (record: any) => getStatusTag(record.status),
    },
    {
      title: '处理措施',
      dataIndex: 'actionTaken',
      key: 'actionTaken',
      width: 150,
      render: (action: string) => (
        action ? (
          <span style={{ color: '#52c41a' }}>{action}</span>
        ) : (
          <span style={{ color: '#999' }}>未处理</span>
        )
      ),
    },
    {
      title: '处理人',
      dataIndex: 'handledBy',
      key: 'handledBy',
      width: 120,
      render: (handler: string) => (
        handler ? handler : <span style={{ color: '#999' }}>未分配</span>
      ),
    },
    {
      title: '发生时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 160,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (record: any) => (
        <Space size="small">
          <Button 
            type="link" 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          {record.status === 'pending' && (
            <Button 
              type="link" 
              size="small" 
              icon={<SecurityScanOutlined />}
              onClick={() => handleProcess(record)}
            >
              处理
            </Button>
          )}
          <Button 
            type="link" 
            size="small" 
            danger
            icon={<StopOutlined />}
            onClick={() => handleBanPlayer(record)}
          >
            封禁
          </Button>
        </Space>
      ),
    },
  ];

  const handleViewDetail = (record: any) => {
    Modal.info({
      title: '安全事件详情',
      width: 700,
      content: (
        <div>
          <div style={{ marginBottom: 16 }}>
            <Space>
              {getEventTypeTag(record.type)}
              {getRiskLevelTag(record.riskLevel)}
              {getStatusTag(record.status)}
            </Space>
          </div>
          <div style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <strong>事件标题：</strong> {record.title}
              </Col>
              <Col span={12}>
                <strong>发生时间：</strong> {record.timestamp}
              </Col>
            </Row>
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>事件描述：</strong> {record.description}
          </div>
          <div style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <strong>涉及玩家：</strong> {record.playerName} (ID: {record.playerId})
              </Col>
              <Col span={12}>
                <strong>设备信息：</strong> {record.deviceInfo}
              </Col>
            </Row>
          </div>
          <div style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <strong>IP地址：</strong> {record.ip}
              </Col>
              <Col span={12}>
                <strong>地理位置：</strong> {record.location}
              </Col>
            </Row>
          </div>
          <div style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <strong>处理状态：</strong> {getStatusTag(record.status)}
              </Col>
              <Col span={12}>
                <strong>处理人：</strong> {record.handledBy || '未分配'}
              </Col>
            </Row>
          </div>
          {record.actionTaken && (
            <div style={{ marginBottom: 16 }}>
              <strong>处理措施：</strong>
              <div style={{ 
                marginTop: 8, 
                padding: 12, 
                backgroundColor: '#f6ffed', 
                borderRadius: 4,
                border: '1px solid #b7eb8f'
              }}>
                {record.actionTaken}
              </div>
            </div>
          )}
        </div>
      ),
    });
  };

  const handleProcess = (record: any) => {
    Modal.confirm({
      title: '处理安全事件',
      content: `确定要处理安全事件 "${record.title}" 吗？`,
      onOk() {
        message.success(`已开始处理安全事件: ${record.title}`);
      },
    });
  };

  const handleBanPlayer = (record: any) => {
    Modal.confirm({
      title: '封禁玩家',
      content: `确定要封禁玩家 "${record.playerName}" 吗？`,
      onOk() {
        message.success(`已封禁玩家: ${record.playerName}`);
      },
    });
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  // 计算统计数据
  const totalEvents = securityEventsData.length;
  const pendingEvents = securityEventsData.filter(e => e.status === 'pending').length;
  const criticalEvents = securityEventsData.filter(e => e.riskLevel === 'critical').length;
  const highRiskEvents = securityEventsData.filter(e => e.riskLevel === 'high').length;

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN]}>
      <div>
        <Title level={2}>安全管理</Title>
        
        {/* 安全统计 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={6} lg={6}>
            <Card>
              <Statistic
                title="总安全事件"
                value={totalEvents}
                prefix={<SecurityScanOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6} lg={6}>
            <Card>
              <Statistic
                title="待处理事件"
                value={pendingEvents}
                prefix={<WarningOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6} lg={6}>
            <Card>
              <Statistic
                title="严重风险"
                value={criticalEvents}
                prefix={<WarningOutlined />}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6} lg={6}>
            <Card>
              <Statistic
                title="高风险事件"
                value={highRiskEvents}
                prefix={<SafetyOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 安全警告 */}
        {(pendingEvents > 0 || criticalEvents > 0) && (
          <Alert
            message="安全警告"
            description={`当前有 ${pendingEvents} 个待处理安全事件，其中 ${criticalEvents} 个为严重风险事件，请及时处理。`}
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {/* 搜索和筛选 */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={8} lg={6}>
              <Input.Search
                placeholder="搜索事件标题"
                allowClear
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={24} sm={6} lg={4}>
              <Select
                placeholder="事件类型"
                style={{ width: '100%' }}
                value={eventType}
                onChange={setEventType}
              >
                <Select.Option value="all">全部类型</Select.Option>
                <Select.Option value="suspicious_login">异常登录</Select.Option>
                <Select.Option value="cheat_detection">外挂检测</Select.Option>
                <Select.Option value="payment_fraud">支付欺诈</Select.Option>
                <Select.Option value="account_sharing">账号共享</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={6} lg={4}>
              <Select
                placeholder="风险等级"
                style={{ width: '100%' }}
                value={riskLevel}
                onChange={setRiskLevel}
              >
                <Select.Option value="all">全部等级</Select.Option>
                <Select.Option value="critical">严重</Select.Option>
                <Select.Option value="high">高</Select.Option>
                <Select.Option value="medium">中</Select.Option>
                <Select.Option value="low">低</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={8} lg={6}>
              <RangePicker style={{ width: '100%' }} />
            </Col>
            <Col xs={24} sm={6} lg={4}>
              <Button type="primary" icon={<SearchOutlined />} block>
                搜索
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 安全事件列表 */}
        <Card>
          <Table
            columns={columns}
            dataSource={securityEventsData}
            loading={loading}
            pagination={{
              total: 89,
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            scroll={{ x: 1600 }}
          />
        </Card>
      </div>
    </ProtectedRoute>
  );
};

export default SecurityPage;
