'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Search,
  Shield,
  AlertTriangle,
  Ban,
  Eye,
  UserX,
  ShieldCheck,
  Calendar
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';

const SecurityPage: React.FC = () => {
  const [searchText, setSearchText] = useState('');
  const [riskLevel, setRiskLevel] = useState<string>('all');
  const [eventType, setEventType] = useState<string>('all');

  // 模拟安全事件数据
  const securityEventsData = [
    {
      id: 7001,
      type: 'suspicious_login',
      title: '异常登录检测',
      description: '玩家从异常地理位置登录',
      riskLevel: 'high',
      playerId: 12345,
      playerName: '玩家ABC',
      ip: '************',
      location: '美国纽约',
      deviceInfo: 'iPhone 13 Pro',
      timestamp: '2024-06-25 14:30:25',
      status: 'investigating',
      actionTaken: null,
      handledBy: '安全专员A'
    },
    {
      key: '2',
      id: 7002,
      type: 'cheat_detection',
      title: '外挂检测',
      description: '检测到玩家使用速度外挂',
      riskLevel: 'critical',
      playerId: 67890,
      playerName: '玩家XYZ',
      ip: '*************',
      location: '中国上海',
      deviceInfo: 'Windows 10',
      timestamp: '2024-06-25 13:45:12',
      status: 'resolved',
      actionTaken: '账号封禁7天',
      handledBy: '安全专员B'
    },
    {
      key: '3',
      id: 7003,
      type: 'payment_fraud',
      title: '支付欺诈',
      description: '检测到可疑的充值行为',
      riskLevel: 'high',
      playerId: 11111,
      playerName: '玩家DEF',
      ip: '*************',
      location: '中国北京',
      deviceInfo: 'Android 12',
      timestamp: '2024-06-25 12:20:30',
      status: 'pending',
      actionTaken: null,
      handledBy: null
    },
    {
      key: '4',
      id: 7004,
      type: 'account_sharing',
      title: '账号共享',
      description: '检测到账号在多个设备同时登录',
      riskLevel: 'medium',
      playerId: 22222,
      playerName: '玩家GHI',
      ip: '************',
      location: '中国广州',
      deviceInfo: 'Multiple devices',
      timestamp: '2024-06-25 11:15:45',
      status: 'resolved',
      actionTaken: '发送安全提醒',
      handledBy: '安全专员A'
    }
  ];

  const handleSearch = () => {
    console.log('搜索:', searchText);
  };

  // 计算统计数据
  const totalEvents = securityEventsData.length;
  const pendingEvents = securityEventsData.filter(e => e.status === 'pending').length;
  const criticalEvents = securityEventsData.filter(e => e.riskLevel === 'critical').length;
  const highRiskEvents = securityEventsData.filter(e => e.riskLevel === 'high').length;

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN]}>
      <MainLayout>
        <div className="p-6">
          <div className="mb-6 flex justify-between items-center">
            <h1 className="text-3xl font-bold text-gray-900">安全管理</h1>
          </div>

          {/* 安全统计 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总事件数</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {totalEvents}
                </div>
                <p className="text-xs text-muted-foreground">
                  安全事件总数
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">待处理</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">
                  {pendingEvents}
                </div>
                <p className="text-xs text-muted-foreground">
                  待处理事件
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">严重风险</CardTitle>
                <Ban className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {criticalEvents}
                </div>
                <p className="text-xs text-muted-foreground">
                  严重风险事件
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">高风险事件</CardTitle>
                <ShieldCheck className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {highRiskEvents}
                </div>
                <p className="text-xs text-muted-foreground">
                  高风险事件
                </p>
              </CardContent>
            </Card>
          </div>

          {/* 安全警告 */}
          {(pendingEvents > 0 || criticalEvents > 0) && (
            <Alert className="mb-6">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                当前有 {pendingEvents} 个待处理安全事件，其中 {criticalEvents} 个为严重风险事件，请及时处理。
              </AlertDescription>
            </Alert>
          )}

          {/* 搜索和筛选 */}
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索事件标题"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    className="pl-10"
                  />
                </div>

                <Select value={eventType} onValueChange={setEventType}>
                  <SelectTrigger>
                    <SelectValue placeholder="事件类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    <SelectItem value="suspicious_login">异常登录</SelectItem>
                    <SelectItem value="cheat_detection">外挂检测</SelectItem>
                    <SelectItem value="payment_fraud">支付欺诈</SelectItem>
                    <SelectItem value="account_sharing">账号共享</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={riskLevel} onValueChange={setRiskLevel}>
                  <SelectTrigger>
                    <SelectValue placeholder="风险等级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部等级</SelectItem>
                    <SelectItem value="critical">严重</SelectItem>
                    <SelectItem value="high">高</SelectItem>
                    <SelectItem value="medium">中</SelectItem>
                    <SelectItem value="low">低</SelectItem>
                  </SelectContent>
                </Select>

                <Button variant="outline">
                  <Calendar className="h-4 w-4 mr-2" />
                  日期范围
                </Button>

                <Button>
                  <Search className="h-4 w-4 mr-2" />
                  搜索
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 安全事件列表 */}
          <Card>
            <CardHeader>
              <CardTitle>安全事件列表</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>事件ID</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>标题</TableHead>
                      <TableHead>风险等级</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>发生时间</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {securityEventsData.map((event: any) => (
                      <TableRow key={event.id}>
                        <TableCell className="font-medium">{event.id}</TableCell>
                        <TableCell>{event.type}</TableCell>
                        <TableCell>{event.title}</TableCell>
                        <TableCell>
                          <Badge
                            variant={event.riskLevel === 'critical' ? 'destructive' :
                                   event.riskLevel === 'high' ? 'default' : 'secondary'}
                          >
                            {event.riskLevel}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{event.status || 'pending'}</Badge>
                        </TableCell>
                        <TableCell>{event.timestamp || '2024-06-27 10:30:00'}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              处理
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

export default SecurityPage;
