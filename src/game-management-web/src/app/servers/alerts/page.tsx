'use client';

import React, { useState, useEffect } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import {
  Bell,
  AlertTriangle,
  XCircle,
  CheckCircle,
  Eye,
  Trash2,
  <PERSON><PERSON><PERSON>Ccw,
  Alert<PERSON>ircle,
  <PERSON>,
  User,
  Settings,
  Plus,
  Filter,
  Search
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';

// TypeScript interfaces
interface ServerAlertDto {
  id: number;
  serverId: number;
  serverName: string;
  alertType: string;
  title: string;
  message: string;
  severity: number;
  status: number;
  createdAt: string;
  acknowledgedAt?: string;
  acknowledgedBy?: string;
  resolvedAt?: string;
  resolvedBy?: string;
  resolution?: string;
  metadata: Record<string, any>;
}

interface AlertRuleDto {
  id: number;
  name: string;
  description: string;
  metricType: string;
  condition: string;
  threshold: number;
  severity: number;
  isEnabled: boolean;
  serverId?: number;
  serverName?: string;
  createdAt: string;
  updatedAt?: string;
}

interface CreateAlertRuleDto {
  name: string;
  description?: string;
  metricType: string;
  condition: string;
  threshold: number;
  severity: number;
  isEnabled: boolean;
  serverId?: number;
}

// Alert severity and status mappings
const AlertSeverityMap = {
  1: { label: '低', color: 'bg-blue-100 text-blue-800', icon: Bell },
  2: { label: '中', color: 'bg-yellow-100 text-yellow-800', icon: AlertTriangle },
  3: { label: '高', color: 'bg-red-100 text-red-800', icon: XCircle },
  4: { label: '严重', color: 'bg-red-200 text-red-900', icon: AlertCircle }
};

const AlertStatusMap = {
  1: { label: '活跃', color: 'bg-red-100 text-red-800' },
  2: { label: '已确认', color: 'bg-yellow-100 text-yellow-800' },
  3: { label: '已解决', color: 'bg-green-100 text-green-800' }
};

const MetricTypes = [
  { value: 'CPU', label: 'CPU使用率' },
  { value: 'Memory', label: '内存使用率' },
  { value: 'Disk', label: '磁盘使用率' },
  { value: 'Network', label: '网络流量' },
  { value: 'ResponseTime', label: '响应时间' },
  { value: 'ActiveConnections', label: '活跃连接数' }
];

const Conditions = [
  { value: '>', label: '大于 (>)' },
  { value: '>=', label: '大于等于 (>=)' },
  { value: '<', label: '小于 (<)' },
  { value: '<=', label: '小于等于 (<=)' },
  { value: '==', label: '等于 (==)' }
];

// API functions
const alertsApi = {
  async getActiveAlerts(): Promise<ServerAlertDto[]> {
    const token = localStorage.getItem('token');
    const response = await fetch('http://localhost:5109/api/ServerMonitoring/alerts/active', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) throw new Error('Failed to fetch active alerts');
    return response.json();
  },

  async getServerAlerts(serverId: number): Promise<ServerAlertDto[]> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/ServerMonitoring/servers/${serverId}/alerts`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) throw new Error('Failed to fetch server alerts');
    return response.json();
  },

  async acknowledgeAlert(alertId: number, acknowledgedBy: string): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/ServerMonitoring/alerts/${alertId}/acknowledge`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(acknowledgedBy)
    });
    if (!response.ok) throw new Error('Failed to acknowledge alert');
  },

  async resolveAlert(alertId: number, resolvedBy: string, resolution: string): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/ServerMonitoring/alerts/${alertId}/resolve`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ resolvedBy, resolution })
    });
    if (!response.ok) throw new Error('Failed to resolve alert');
  },

  async deleteAlert(alertId: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/ServerMonitoring/alerts/${alertId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (!response.ok) throw new Error('Failed to delete alert');
  },

  async getAlertRules(): Promise<AlertRuleDto[]> {
    const token = localStorage.getItem('token');
    const response = await fetch('http://localhost:5109/api/ServerMonitoring/alert-rules', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) throw new Error('Failed to fetch alert rules');
    return response.json();
  },

  async createAlertRule(rule: CreateAlertRuleDto): Promise<AlertRuleDto> {
    const token = localStorage.getItem('token');
    const response = await fetch('http://localhost:5109/api/ServerMonitoring/alert-rules', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(rule)
    });
    if (!response.ok) throw new Error('Failed to create alert rule');
    return response.json();
  },

  async toggleAlertRule(ruleId: number, isEnabled: boolean): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/ServerMonitoring/alert-rules/${ruleId}/toggle`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(isEnabled)
    });
    if (!response.ok) throw new Error('Failed to toggle alert rule');
  },

  async deleteAlertRule(ruleId: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/ServerMonitoring/alert-rules/${ruleId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (!response.ok) throw new Error('Failed to delete alert rule');
  }
};

const AlertsPage: React.FC = () => {
  // State management
  const [alerts, setAlerts] = useState<ServerAlertDto[]>([]);
  const [alertRules, setAlertRules] = useState<AlertRuleDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'alerts' | 'rules'>('alerts');
  const [filterSeverity, setFilterSeverity] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  
  // Dialog states
  const [showResolveDialog, setShowResolveDialog] = useState(false);
  const [showRuleDialog, setShowRuleDialog] = useState(false);
  const [selectedAlert, setSelectedAlert] = useState<ServerAlertDto | null>(null);
  const [resolution, setResolution] = useState('');
  
  // New rule form
  const [newRule, setNewRule] = useState<CreateAlertRuleDto>({
    name: '',
    description: '',
    metricType: 'CPU',
    condition: '>',
    threshold: 80,
    severity: 2,
    isEnabled: true,
    serverId: undefined
  });

  // Data loading
  const loadAlerts = async () => {
    try {
      setLoading(true);
      setError(null);
      const alertData = await alertsApi.getActiveAlerts();
      setAlerts(alertData);
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载告警数据失败');
    } finally {
      setLoading(false);
    }
  };

  const loadAlertRules = async () => {
    try {
      setLoading(true);
      setError(null);
      const rulesData = await alertsApi.getAlertRules();
      setAlertRules(rulesData);
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载告警规则失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (activeTab === 'alerts') {
      loadAlerts();
    } else {
      loadAlertRules();
    }
  }, [activeTab]);

  // Event handlers
  const handleAcknowledgeAlert = async (alertId: number) => {
    try {
      setLoading(true);
      await alertsApi.acknowledgeAlert(alertId, 'System Admin');
      await loadAlerts();
    } catch (err) {
      setError(err instanceof Error ? err.message : '确认告警失败');
    } finally {
      setLoading(false);
    }
  };

  const handleResolveAlert = async () => {
    if (!selectedAlert || !resolution.trim()) return;
    
    try {
      setLoading(true);
      await alertsApi.resolveAlert(selectedAlert.id, 'System Admin', resolution);
      setShowResolveDialog(false);
      setSelectedAlert(null);
      setResolution('');
      await loadAlerts();
    } catch (err) {
      setError(err instanceof Error ? err.message : '解决告警失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAlert = async (alertId: number) => {
    if (!confirm('确定要删除这个告警吗？')) return;
    
    try {
      setLoading(true);
      await alertsApi.deleteAlert(alertId);
      await loadAlerts();
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除告警失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateRule = async () => {
    if (!newRule.name.trim()) return;
    
    try {
      setLoading(true);
      await alertsApi.createAlertRule(newRule);
      setShowRuleDialog(false);
      setNewRule({
        name: '',
        description: '',
        metricType: 'CPU',
        condition: '>',
        threshold: 80,
        severity: 2,
        isEnabled: true,
        serverId: undefined
      });
      await loadAlertRules();
    } catch (err) {
      setError(err instanceof Error ? err.message : '创建告警规则失败');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleRule = async (ruleId: number, isEnabled: boolean) => {
    try {
      setLoading(true);
      await alertsApi.toggleAlertRule(ruleId, isEnabled);
      await loadAlertRules();
    } catch (err) {
      setError(err instanceof Error ? err.message : '切换告警规则状态失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRule = async (ruleId: number) => {
    if (!confirm('确定要删除这个告警规则吗？')) return;
    
    try {
      setLoading(true);
      await alertsApi.deleteAlertRule(ruleId);
      await loadAlertRules();
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除告警规则失败');
    } finally {
      setLoading(false);
    }
  };

  // Filter alerts
  const filteredAlerts = alerts.filter(alert => {
    const matchesSeverity = filterSeverity === 'all' || alert.severity.toString() === filterSeverity;
    const matchesStatus = filterStatus === 'all' || alert.status.toString() === filterStatus;
    const matchesSearch = searchTerm === '' || 
      alert.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      alert.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      alert.serverName.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesSeverity && matchesStatus && matchesSearch;
  });

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.TECHNICAL_SUPPORT]}>
      <MainLayout>
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">监控告警管理</h1>
            <div className="flex space-x-2">
              <Button
                variant={activeTab === 'alerts' ? "default" : "outline"}
                onClick={() => setActiveTab('alerts')}
              >
                <Bell className="h-4 w-4 mr-2" />
                告警列表
              </Button>
              <Button
                variant={activeTab === 'rules' ? "default" : "outline"}
                onClick={() => setActiveTab('rules')}
              >
                <Settings className="h-4 w-4 mr-2" />
                告警规则
              </Button>
            </div>
          </div>

          {error && (
            <Alert className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {activeTab === 'alerts' ? (
            <div>
              {/* 告警筛选 */}
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle>筛选条件</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">严重程度</label>
                      <Select value={filterSeverity} onValueChange={setFilterSeverity}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部</SelectItem>
                          <SelectItem value="1">低</SelectItem>
                          <SelectItem value="2">中</SelectItem>
                          <SelectItem value="3">高</SelectItem>
                          <SelectItem value="4">严重</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="text-sm font-medium mb-2 block">状态</label>
                      <Select value={filterStatus} onValueChange={setFilterStatus}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部</SelectItem>
                          <SelectItem value="1">活跃</SelectItem>
                          <SelectItem value="2">已确认</SelectItem>
                          <SelectItem value="3">已解决</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="text-sm font-medium mb-2 block">搜索</label>
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="搜索告警..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    <div className="flex items-end">
                      <Button onClick={loadAlerts} disabled={loading} className="w-full">
                        <RotateCcw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                        刷新
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 告警列表 */}
              <Card>
                <CardHeader>
                  <CardTitle>活跃告警 ({filteredAlerts.length})</CardTitle>
                </CardHeader>
                <CardContent>
                  {loading && alerts.length === 0 ? (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                      <p className="mt-2 text-gray-500">加载中...</p>
                    </div>
                  ) : filteredAlerts.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <Bell className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>暂无告警数据</p>
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>告警信息</TableHead>
                          <TableHead>服务器</TableHead>
                          <TableHead>严重程度</TableHead>
                          <TableHead>状态</TableHead>
                          <TableHead>创建时间</TableHead>
                          <TableHead>操作</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredAlerts.map((alert) => {
                          const severityInfo = AlertSeverityMap[alert.severity as keyof typeof AlertSeverityMap];
                          const statusInfo = AlertStatusMap[alert.status as keyof typeof AlertStatusMap];
                          const SeverityIcon = severityInfo?.icon || Bell;

                          return (
                            <TableRow key={alert.id}>
                              <TableCell>
                                <div className="space-y-1">
                                  <div className="flex items-center space-x-2">
                                    <SeverityIcon className="h-4 w-4 text-gray-500" />
                                    <span className="font-medium">{alert.title}</span>
                                  </div>
                                  <p className="text-sm text-gray-600 max-w-md">{alert.message}</p>
                                  <div className="text-xs text-gray-400">
                                    类型: {alert.alertType}
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="font-medium">{alert.serverName}</div>
                                <div className="text-sm text-gray-500">ID: {alert.serverId}</div>
                              </TableCell>
                              <TableCell>
                                <Badge className={severityInfo?.color || 'bg-gray-100 text-gray-800'}>
                                  {severityInfo?.label || '未知'}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Badge className={statusInfo?.color || 'bg-gray-100 text-gray-800'}>
                                  {statusInfo?.label || '未知'}
                                </Badge>
                                {alert.acknowledgedBy && (
                                  <div className="text-xs text-gray-500 mt-1">
                                    <User className="h-3 w-3 inline mr-1" />
                                    {alert.acknowledgedBy}
                                  </div>
                                )}
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center text-sm">
                                  <Clock className="h-4 w-4 mr-1 text-gray-400" />
                                  {new Date(alert.createdAt).toLocaleString('zh-CN')}
                                </div>
                                {alert.resolvedAt && (
                                  <div className="text-xs text-green-600 mt-1">
                                    已解决: {new Date(alert.resolvedAt).toLocaleString('zh-CN')}
                                  </div>
                                )}
                              </TableCell>
                              <TableCell>
                                <div className="flex space-x-2">
                                  {alert.status === 1 && (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleAcknowledgeAlert(alert.id)}
                                      disabled={loading}
                                    >
                                      <CheckCircle className="h-3 w-3 mr-1" />
                                      确认
                                    </Button>
                                  )}

                                  {alert.status !== 3 && (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => {
                                        setSelectedAlert(alert);
                                        setShowResolveDialog(true);
                                      }}
                                      disabled={loading}
                                      className="text-green-600 hover:text-green-700"
                                    >
                                      <CheckCircle className="h-3 w-3 mr-1" />
                                      解决
                                    </Button>
                                  )}

                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleDeleteAlert(alert.id)}
                                    disabled={loading}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            </div>
          ) : (
            <div>
              {/* 告警规则管理 */}
              <Card className="mb-6">
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>告警规则</CardTitle>
                  <Dialog open={showRuleDialog} onOpenChange={setShowRuleDialog}>
                    <DialogTrigger asChild>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        新建规则
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md">
                      <DialogHeader>
                        <DialogTitle>创建告警规则</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium mb-2 block">规则名称</label>
                          <Input
                            placeholder="输入规则名称"
                            value={newRule.name}
                            onChange={(e) => setNewRule({ ...newRule, name: e.target.value })}
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium mb-2 block">描述</label>
                          <Textarea
                            placeholder="输入规则描述"
                            value={newRule.description}
                            onChange={(e) => setNewRule({ ...newRule, description: e.target.value })}
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium mb-2 block">监控指标</label>
                            <Select
                              value={newRule.metricType}
                              onValueChange={(value) => setNewRule({ ...newRule, metricType: value })}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {MetricTypes.map(type => (
                                  <SelectItem key={type.value} value={type.value}>
                                    {type.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <label className="text-sm font-medium mb-2 block">条件</label>
                            <Select
                              value={newRule.condition}
                              onValueChange={(value) => setNewRule({ ...newRule, condition: value })}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {Conditions.map(condition => (
                                  <SelectItem key={condition.value} value={condition.value}>
                                    {condition.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium mb-2 block">阈值</label>
                            <Input
                              type="number"
                              placeholder="输入阈值"
                              value={newRule.threshold}
                              onChange={(e) => setNewRule({ ...newRule, threshold: parseFloat(e.target.value) || 0 })}
                            />
                          </div>
                          <div>
                            <label className="text-sm font-medium mb-2 block">严重程度</label>
                            <Select
                              value={newRule.severity.toString()}
                              onValueChange={(value) => setNewRule({ ...newRule, severity: parseInt(value) })}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="1">低</SelectItem>
                                <SelectItem value="2">中</SelectItem>
                                <SelectItem value="3">高</SelectItem>
                                <SelectItem value="4">严重</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div className="flex justify-end space-x-2">
                          <Button variant="outline" onClick={() => setShowRuleDialog(false)}>
                            取消
                          </Button>
                          <Button onClick={handleCreateRule} disabled={loading || !newRule.name.trim()}>
                            创建
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </CardHeader>
                <CardContent>
                  {loading && alertRules.length === 0 ? (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                      <p className="mt-2 text-gray-500">加载中...</p>
                    </div>
                  ) : alertRules.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>暂无告警规则</p>
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>规则名称</TableHead>
                          <TableHead>监控指标</TableHead>
                          <TableHead>条件</TableHead>
                          <TableHead>严重程度</TableHead>
                          <TableHead>状态</TableHead>
                          <TableHead>操作</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {alertRules.map((rule) => {
                          const severityInfo = AlertSeverityMap[rule.severity as keyof typeof AlertSeverityMap];
                          const metricType = MetricTypes.find(m => m.value === rule.metricType);

                          return (
                            <TableRow key={rule.id}>
                              <TableCell>
                                <div>
                                  <div className="font-medium">{rule.name}</div>
                                  {rule.description && (
                                    <div className="text-sm text-gray-500 max-w-md">{rule.description}</div>
                                  )}
                                  {rule.serverName && (
                                    <div className="text-xs text-gray-400 mt-1">
                                      服务器: {rule.serverName}
                                    </div>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="font-medium">{metricType?.label || rule.metricType}</div>
                              </TableCell>
                              <TableCell>
                                <div className="font-mono text-sm">
                                  {rule.condition} {rule.threshold}
                                  {rule.metricType.includes('Usage') ? '%' : ''}
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge className={severityInfo?.color || 'bg-gray-100 text-gray-800'}>
                                  {severityInfo?.label || '未知'}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Badge className={rule.isEnabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                                  {rule.isEnabled ? '启用' : '禁用'}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <div className="flex space-x-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleToggleRule(rule.id, !rule.isEnabled)}
                                    disabled={loading}
                                  >
                                    {rule.isEnabled ? '禁用' : '启用'}
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleDeleteRule(rule.id)}
                                    disabled={loading}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {/* 解决告警对话框 */}
          <Dialog open={showResolveDialog} onOpenChange={setShowResolveDialog}>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>解决告警</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                {selectedAlert && (
                  <div className="p-3 bg-gray-50 rounded">
                    <div className="font-medium">{selectedAlert.title}</div>
                    <div className="text-sm text-gray-600">{selectedAlert.message}</div>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium mb-2 block">解决方案</label>
                  <Textarea
                    placeholder="请描述解决方案..."
                    value={resolution}
                    onChange={(e) => setResolution(e.target.value)}
                    rows={4}
                  />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setShowResolveDialog(false)}>
                    取消
                  </Button>
                  <Button
                    onClick={handleResolveAlert}
                    disabled={loading || !resolution.trim()}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    解决告警
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

export default AlertsPage;
