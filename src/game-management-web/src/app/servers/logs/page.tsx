'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Search,
  Download,
  Eye,
  Trash2,
  RotateCcw,
  Calendar
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';

const LogsPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [logLevel, setLogLevel] = useState<string>('all');
  const [serverId, setServerId] = useState<string>('all');

  // 模拟日志数据
  const logsData = [
    {
      key: '1',
      id: 5001,
      timestamp: '2024-06-25 14:30:25.123',
      level: 'ERROR',
      serverId: 2,
      serverName: '服务器2',
      module: 'GameEngine',
      message: 'Failed to process player action: timeout exception',
      details: 'java.net.SocketTimeoutException: Read timed out\n\tat java.net.SocketInputStream.socketRead0(Native Method)\n\tat java.net.SocketInputStream.socketRead(SocketInputStream.java:116)',
      playerId: 12345,
      playerName: '玩家ABC',
      ip: '*************'
    },
    {
      key: '2',
      id: 5002,
      timestamp: '2024-06-25 14:29:15.456',
      level: 'WARN',
      serverId: 2,
      serverName: '服务器2',
      module: 'DatabaseManager',
      message: 'Database connection pool nearly exhausted',
      details: 'Current active connections: 95/100. Consider increasing pool size.',
      playerId: null,
      playerName: null,
      ip: null
    },
    {
      key: '3',
      id: 5003,
      timestamp: '2024-06-25 14:28:30.789',
      level: 'INFO',
      serverId: 1,
      serverName: '服务器1',
      module: 'PlayerManager',
      message: 'Player login successful',
      details: 'Player logged in from IP: ************',
      playerId: 67890,
      playerName: '玩家XYZ',
      ip: '************'
    },
    {
      key: '4',
      id: 5004,
      timestamp: '2024-06-25 14:27:45.012',
      level: 'DEBUG',
      serverId: 1,
      serverName: '服务器1',
      module: 'ItemSystem',
      message: 'Item drop calculation completed',
      details: 'Monster ID: 1001, Drop rate: 15%, Items: [Sword+1, Health Potion]',
      playerId: 11111,
      playerName: '玩家DEF',
      ip: '*************'
    },
    {
      key: '5',
      id: 5005,
      timestamp: '2024-06-25 14:26:20.345',
      level: 'ERROR',
      serverId: 4,
      serverName: '服务器4',
      module: 'NetworkManager',
      message: 'Server connection lost',
      details: 'Connection to database server lost. Attempting to reconnect...',
      playerId: null,
      playerName: null,
      ip: null
    }
  ];

  const getLevelTag = (level: string) => {
    const levelConfig = {
      ERROR: { color: 'red', text: 'ERROR' },
      WARN: { color: 'orange', text: 'WARN' },
      INFO: { color: 'blue', text: 'INFO' },
      DEBUG: { color: 'default', text: 'DEBUG' }
    };
    const config = levelConfig[level as keyof typeof levelConfig] || levelConfig.DEBUG;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 180,
      render: (timestamp: string) => (
        <div style={{ fontSize: '12px', fontFamily: 'monospace' }}>
          {timestamp}
        </div>
      ),
    },
    {
      title: '级别',
      key: 'level',
      width: 80,
      render: (record: any) => getLevelTag(record.level),
    },
    {
      title: '服务器',
      dataIndex: 'serverName',
      key: 'serverName',
      width: 100,
    },
    {
      title: '模块',
      dataIndex: 'module',
      key: 'module',
      width: 120,
      render: (module: string) => (
        <Tag color="geekblue">{module}</Tag>
      ),
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message',
      width: 300,
      render: (message: string) => (
        <div style={{ 
          maxWidth: 280, 
          overflow: 'hidden', 
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {message}
        </div>
      ),
    },
    {
      title: '玩家',
      key: 'player',
      width: 120,
      render: (record: any) => (
        record.playerName ? (
          <div>
            <div style={{ fontSize: '12px', fontWeight: 500 }}>{record.playerName}</div>
            <div style={{ fontSize: '11px', color: '#666' }}>ID: {record.playerId}</div>
          </div>
        ) : (
          <span style={{ color: '#999' }}>-</span>
        )
      ),
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
      width: 120,
      render: (ip: string) => (
        ip ? (
          <span style={{ fontFamily: 'monospace', fontSize: '12px' }}>{ip}</span>
        ) : (
          <span style={{ color: '#999' }}>-</span>
        )
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (record: any) => (
        <Space size="small">
          <Button 
            type="link" 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          <Button 
            type="link" 
            size="small" 
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleViewDetail = (record: any) => {
    Modal.info({
      title: '日志详情',
      width: 800,
      content: (
        <div>
          <div style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <strong>时间：</strong> {record.timestamp}
              </Col>
              <Col span={12}>
                <strong>级别：</strong> {getLevelTag(record.level)}
              </Col>
            </Row>
          </div>
          <div style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <strong>服务器：</strong> {record.serverName}
              </Col>
              <Col span={12}>
                <strong>模块：</strong> <Tag color="geekblue">{record.module}</Tag>
              </Col>
            </Row>
          </div>
          {record.playerName && (
            <div style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={12}>
                  <strong>玩家：</strong> {record.playerName} (ID: {record.playerId})
                </Col>
                <Col span={12}>
                  <strong>IP地址：</strong> {record.ip}
                </Col>
              </Row>
            </div>
          )}
          <div style={{ marginBottom: 16 }}>
            <strong>消息：</strong>
            <div style={{ 
              marginTop: 8, 
              padding: 12, 
              backgroundColor: '#f5f5f5', 
              borderRadius: 4,
              fontFamily: 'monospace',
              fontSize: '13px'
            }}>
              {record.message}
            </div>
          </div>
          <div>
            <strong>详细信息：</strong>
            <div style={{ 
              marginTop: 8, 
              padding: 12, 
              backgroundColor: '#f5f5f5', 
              borderRadius: 4,
              fontFamily: 'monospace',
              fontSize: '12px',
              whiteSpace: 'pre-wrap',
              maxHeight: 300,
              overflow: 'auto'
            }}>
              {record.details}
            </div>
          </div>
        </div>
      ),
    });
  };

  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '删除日志',
      content: `确定要删除这条日志记录吗？此操作不可恢复。`,
      onOk() {
        message.success('日志记录已删除');
      },
    });
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
    message.info(`搜索: ${value}`);
  };

  const handleExport = () => {
    message.success('日志导出功能开发中...');
  };

  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      message.success('日志数据已刷新');
    }, 1000);
  };

  const handleClearLogs = () => {
    Modal.confirm({
      title: '清空日志',
      content: '确定要清空所有日志记录吗？此操作不可恢复。',
      onOk() {
        message.success('日志已清空');
      },
    });
  };

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER]}>
      <div>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
          <Title level={2}>日志管理</Title>
          <Space>
            <Button 
              icon={<DownloadOutlined />}
              onClick={handleExport}
            >
              导出日志
            </Button>
            <Button 
              icon={<ReloadOutlined />} 
              loading={loading}
              onClick={handleRefresh}
            >
              刷新
            </Button>
            <Button 
              danger
              onClick={handleClearLogs}
            >
              清空日志
            </Button>
          </Space>
        </div>
        
        {/* 搜索和筛选 */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={8} lg={6}>
              <Input.Search
                placeholder="搜索日志消息"
                allowClear
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={24} sm={6} lg={4}>
              <Select
                placeholder="日志级别"
                style={{ width: '100%' }}
                value={logLevel}
                onChange={setLogLevel}
              >
                <Select.Option value="all">全部级别</Select.Option>
                <Select.Option value="ERROR">ERROR</Select.Option>
                <Select.Option value="WARN">WARN</Select.Option>
                <Select.Option value="INFO">INFO</Select.Option>
                <Select.Option value="DEBUG">DEBUG</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={6} lg={4}>
              <Select
                placeholder="服务器"
                style={{ width: '100%' }}
                value={serverId}
                onChange={setServerId}
              >
                <Select.Option value="all">全部服务器</Select.Option>
                <Select.Option value="1">服务器1</Select.Option>
                <Select.Option value="2">服务器2</Select.Option>
                <Select.Option value="3">服务器3</Select.Option>
                <Select.Option value="4">服务器4</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={10} lg={6}>
              <RangePicker 
                style={{ width: '100%' }} 
                showTime
                format="YYYY-MM-DD HH:mm:ss"
              />
            </Col>
            <Col xs={24} sm={4} lg={4}>
              <Button type="primary" icon={<SearchOutlined />} block>
                搜索
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 日志列表 */}
        <Card>
          <Table
            columns={columns}
            dataSource={logsData}
            loading={loading}
            pagination={{
              total: 50000,
              pageSize: 50,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            scroll={{ x: 1200 }}
            size="small"
          />
        </Card>
      </div>
    </ProtectedRoute>
  );
};

export default LogsPage;
