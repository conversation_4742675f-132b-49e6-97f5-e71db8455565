'use client';

import React, { useState, useEffect } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import {
  Search,
  Download,
  Eye,
  Trash2,
  RotateCcw,
  Calendar,
  AlertCircle,
  Clock,
  Server,
  FileText,
  Filter,
  ChevronLeft,
  ChevronRight,
  Plus,
  Activity
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';

// TypeScript interfaces
interface ServerLogDto {
  id: number;
  serverId: number;
  level: number;
  message: string;
  exception?: string;
  source?: string;
  timestamp: string;
}

interface ServerStatusDto {
  id: number;
  name: string;
  host: string;
  port: number;
  status: number;
}

interface CreateServerLogDto {
  serverId: number;
  level: number;
  message: string;
  exception?: string;
  source?: string;
  timestamp?: string;
}

// Log level mapping
const LogLevelMap = {
  0: { label: '跟踪', color: 'bg-gray-100 text-gray-800', icon: FileText },
  1: { label: '调试', color: 'bg-blue-100 text-blue-800', icon: FileText },
  2: { label: '信息', color: 'bg-green-100 text-green-800', icon: FileText },
  3: { label: '警告', color: 'bg-yellow-100 text-yellow-800', icon: AlertCircle },
  4: { label: '错误', color: 'bg-red-100 text-red-800', icon: AlertCircle },
  5: { label: '严重', color: 'bg-red-200 text-red-900', icon: AlertCircle }
};

// API functions
const logsApi = {
  async getServers(): Promise<ServerStatusDto[]> {
    const token = localStorage.getItem('token');
    const response = await fetch('http://localhost:5109/api/ServerMonitoring/servers/status', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) throw new Error('Failed to fetch servers');
    return response.json();
  },

  async getServerLogs(serverId: number, page: number = 1, pageSize: number = 50, level?: number): Promise<ServerLogDto[]> {
    const token = localStorage.getItem('token');
    let url = `http://localhost:5109/api/ServerMonitoring/servers/${serverId}/logs?page=${page}&pageSize=${pageSize}`;
    if (level !== undefined && level !== -1) {
      url += `&level=${level}`;
    }

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) throw new Error('Failed to fetch server logs');
    return response.json();
  },

  async getAllServerLogs(page: number = 1, pageSize: number = 50, level?: number): Promise<ServerLogDto[]> {
    const token = localStorage.getItem('token');
    const servers = await this.getServers();
    const allLogs: ServerLogDto[] = [];

    for (const server of servers) {
      try {
        const logs = await this.getServerLogs(server.id, page, pageSize, level);
        allLogs.push(...logs);
      } catch (error) {
        console.error(`Failed to fetch logs for server ${server.id}:`, error);
      }
    }

    // Sort by timestamp descending
    return allLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  },

  async addServerLog(log: CreateServerLogDto): Promise<ServerLogDto> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/GameServer/${log.serverId}/logs`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(log)
    });
    if (!response.ok) throw new Error('Failed to add server log');
    return response.json();
  },

  async exportLogs(serverId?: number, level?: number, startDate?: string, endDate?: string): Promise<Blob> {
    const token = localStorage.getItem('token');
    let url = 'http://localhost:5109/api/ServerMonitoring/logs/export?';
    const params = new URLSearchParams();

    if (serverId && serverId !== -1) params.append('serverId', serverId.toString());
    if (level !== undefined && level !== -1) params.append('level', level.toString());
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);

    const response = await fetch(url + params.toString(), {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (!response.ok) throw new Error('Failed to export logs');
    return response.blob();
  }
};

const LogsPage: React.FC = () => {
  // State management
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState<ServerLogDto[]>([]);
  const [servers, setServers] = useState<ServerStatusDto[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<ServerLogDto[]>([]);
  const [error, setError] = useState<string>('');

  // Filter states
  const [searchText, setSearchText] = useState('');
  const [selectedLogLevel, setSelectedLogLevel] = useState<number>(-1);
  const [selectedServerId, setSelectedServerId] = useState<number>(-1);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  const [totalLogs, setTotalLogs] = useState(0);

  // Dialog states
  const [showLogDialog, setShowLogDialog] = useState(false);
  const [selectedLog, setSelectedLog] = useState<ServerLogDto | null>(null);
  const [showAddLogDialog, setShowAddLogDialog] = useState(false);
  const [newLog, setNewLog] = useState<CreateServerLogDto>({
    serverId: 1,
    level: 2,
    message: '',
    source: 'Manual Entry'
  });

  // Auto refresh
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  // Load initial data
  useEffect(() => {
    loadServers();
    loadLogs();
  }, []);

  // Auto refresh effect
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        loadLogs();
      }, 30000); // Refresh every 30 seconds
      setRefreshInterval(interval);
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [autoRefresh]);

  // Filter logs when filters change
  useEffect(() => {
    filterLogs();
  }, [logs, searchText, selectedLogLevel, selectedServerId, startDate, endDate]);

  const loadServers = async () => {
    try {
      const serversData = await logsApi.getServers();
      setServers(serversData);
    } catch (error) {
      console.error('Failed to load servers:', error);
      setError('加载服务器列表失败');
    }
  };

  const loadLogs = async () => {
    try {
      setLoading(true);
      setError('');

      let logsData: ServerLogDto[];
      if (selectedServerId === -1) {
        logsData = await logsApi.getAllServerLogs(currentPage, pageSize, selectedLogLevel === -1 ? undefined : selectedLogLevel);
      } else {
        logsData = await logsApi.getServerLogs(selectedServerId, currentPage, pageSize, selectedLogLevel === -1 ? undefined : selectedLogLevel);
      }

      setLogs(logsData);
      setTotalLogs(logsData.length);
    } catch (error) {
      console.error('Failed to load logs:', error);
      setError('加载日志失败');
    } finally {
      setLoading(false);
    }
  };

  const filterLogs = () => {
    let filtered = [...logs];

    // Text search
    if (searchText.trim()) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(log =>
        log.message.toLowerCase().includes(searchLower) ||
        (log.exception && log.exception.toLowerCase().includes(searchLower)) ||
        (log.source && log.source.toLowerCase().includes(searchLower))
      );
    }

    // Date range filter
    if (startDate) {
      const start = new Date(startDate);
      filtered = filtered.filter(log => new Date(log.timestamp) >= start);
    }

    if (endDate) {
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999); // End of day
      filtered = filtered.filter(log => new Date(log.timestamp) <= end);
    }

    setFilteredLogs(filtered);
  };

  const handleRefresh = () => {
    loadLogs();
  };

  const handleExport = async () => {
    try {
      setLoading(true);
      const blob = await logsApi.exportLogs(
        selectedServerId === -1 ? undefined : selectedServerId,
        selectedLogLevel === -1 ? undefined : selectedLogLevel,
        startDate,
        endDate
      );

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `server-logs-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Failed to export logs:', error);
      setError('导出日志失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAddLog = async () => {
    try {
      setLoading(true);
      await logsApi.addServerLog(newLog);
      setShowAddLogDialog(false);
      setNewLog({
        serverId: 1,
        level: 2,
        message: '',
        source: 'Manual Entry'
      });
      loadLogs();
    } catch (error) {
      console.error('Failed to add log:', error);
      setError('添加日志失败');
    } finally {
      setLoading(false);
    }
  };

  const getServerName = (serverId: number) => {
    const server = servers.find(s => s.id === serverId);
    return server ? server.name : `服务器 ${serverId}`;
  };

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.TECHNICAL_SUPPORT]}>
      <MainLayout>
        <div className="p-6">
          {/* Header */}
          <div className="mb-6 flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">服务器日志管理</h1>
              <p className="text-gray-600 mt-1">查看、搜索、分析和导出服务器日志</p>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setAutoRefresh(!autoRefresh)}
                className={autoRefresh ? 'bg-green-50 border-green-200' : ''}
              >
                <Activity className={`h-4 w-4 mr-2 ${autoRefresh ? 'text-green-600' : ''}`} />
                {autoRefresh ? '自动刷新中' : '自动刷新'}
              </Button>
              <Button onClick={handleRefresh} disabled={loading}>
                <RotateCcw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                刷新日志
              </Button>
              <Dialog open={showAddLogDialog} onOpenChange={setShowAddLogDialog}>
                <DialogTrigger asChild>
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    添加日志
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>添加服务器日志</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">服务器</label>
                      <Select
                        value={newLog.serverId.toString()}
                        onValueChange={(value) => setNewLog({ ...newLog, serverId: parseInt(value) })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {servers.map(server => (
                            <SelectItem key={server.id} value={server.id.toString()}>
                              {server.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="text-sm font-medium mb-2 block">日志级别</label>
                      <Select
                        value={newLog.level.toString()}
                        onValueChange={(value) => setNewLog({ ...newLog, level: parseInt(value) })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.entries(LogLevelMap).map(([level, info]) => (
                            <SelectItem key={level} value={level}>
                              {info.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="text-sm font-medium mb-2 block">日志消息</label>
                      <Textarea
                        placeholder="输入日志消息..."
                        value={newLog.message}
                        onChange={(e) => setNewLog({ ...newLog, message: e.target.value })}
                        rows={4}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium mb-2 block">来源</label>
                      <Input
                        placeholder="日志来源"
                        value={newLog.source || ''}
                        onChange={(e) => setNewLog({ ...newLog, source: e.target.value })}
                      />
                    </div>
                    <div className="flex justify-end space-x-2">
                      <Button variant="outline" onClick={() => setShowAddLogDialog(false)}>
                        取消
                      </Button>
                      <Button
                        onClick={handleAddLog}
                        disabled={loading || !newLog.message.trim()}
                      >
                        添加日志
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
              <Button onClick={handleExport} disabled={loading}>
                <Download className="h-4 w-4 mr-2" />
                导出日志
              </Button>
            </div>
          </div>

          {/* Error Alert */}
          {error && (
            <Alert className="mb-6 border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">{error}</AlertDescription>
            </Alert>
          )}

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">总日志数</p>
                    <p className="text-2xl font-bold text-gray-900">{filteredLogs.length}</p>
                  </div>
                  <FileText className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">错误日志</p>
                    <p className="text-2xl font-bold text-red-600">
                      {filteredLogs.filter(log => log.level >= 4).length}
                    </p>
                  </div>
                  <AlertCircle className="h-8 w-8 text-red-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">警告日志</p>
                    <p className="text-2xl font-bold text-yellow-600">
                      {filteredLogs.filter(log => log.level === 3).length}
                    </p>
                  </div>
                  <AlertCircle className="h-8 w-8 text-yellow-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">活跃服务器</p>
                    <p className="text-2xl font-bold text-green-600">{servers.length}</p>
                  </div>
                  <Server className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Filter className="h-5 w-5 mr-2" />
                日志筛选
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">搜索关键词</label>
                  <Input
                    placeholder="搜索日志内容..."
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">日志级别</label>
                  <Select
                    value={selectedLogLevel.toString()}
                    onValueChange={(value) => setSelectedLogLevel(parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择日志级别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="-1">全部级别</SelectItem>
                      {Object.entries(LogLevelMap).map(([level, info]) => (
                        <SelectItem key={level} value={level}>
                          {info.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">服务器</label>
                  <Select
                    value={selectedServerId.toString()}
                    onValueChange={(value) => setSelectedServerId(parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择服务器" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="-1">全部服务器</SelectItem>
                      {servers.map(server => (
                        <SelectItem key={server.id} value={server.id.toString()}>
                          {server.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">开始日期</label>
                  <Input
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">结束日期</label>
                  <Input
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                  />
                </div>
                <div className="flex items-end">
                  <Button
                    className="w-full"
                    onClick={loadLogs}
                    disabled={loading}
                  >
                    <Search className="h-4 w-4 mr-2" />
                    搜索
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Logs Table */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>日志记录 ({filteredLogs.length})</CardTitle>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">每页显示:</span>
                <Select
                  value={pageSize.toString()}
                  onValueChange={(value) => setPageSize(parseInt(value))}
                >
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="25">25</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              {loading && logs.length === 0 ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-500">加载中...</p>
                </div>
              ) : filteredLogs.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>暂无日志数据</p>
                  <p className="text-sm mt-2">尝试调整筛选条件或刷新数据</p>
                </div>
              ) : (
                <>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>时间</TableHead>
                        <TableHead>服务器</TableHead>
                        <TableHead>级别</TableHead>
                        <TableHead>来源</TableHead>
                        <TableHead>消息</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredLogs.slice((currentPage - 1) * pageSize, currentPage * pageSize).map((log) => {
                        const levelInfo = LogLevelMap[log.level as keyof typeof LogLevelMap];
                        const LevelIcon = levelInfo?.icon || FileText;

                        return (
                          <TableRow key={log.id}>
                            <TableCell>
                              <div className="flex items-center text-sm">
                                <Clock className="h-4 w-4 mr-1 text-gray-400" />
                                {new Date(log.timestamp).toLocaleString('zh-CN')}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <Server className="h-4 w-4 mr-2 text-gray-500" />
                                <span className="font-medium">{getServerName(log.serverId)}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge className={levelInfo?.color || 'bg-gray-100 text-gray-800'}>
                                <LevelIcon className="h-3 w-3 mr-1" />
                                {levelInfo?.label || '未知'}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <span className="text-sm text-gray-600">{log.source || '-'}</span>
                            </TableCell>
                            <TableCell>
                              <div className="max-w-md">
                                <p className="text-sm truncate" title={log.message}>
                                  {log.message}
                                </p>
                                {log.exception && (
                                  <p className="text-xs text-red-600 mt-1 truncate" title={log.exception}>
                                    异常: {log.exception}
                                  </p>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedLog(log);
                                  setShowLogDialog(true);
                                }}
                              >
                                <Eye className="h-3 w-3 mr-1" />
                                详情
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>

                  {/* Pagination */}
                  <div className="flex items-center justify-between mt-4">
                    <div className="text-sm text-gray-500">
                      显示 {Math.min((currentPage - 1) * pageSize + 1, filteredLogs.length)} - {Math.min(currentPage * pageSize, filteredLogs.length)} 条，共 {filteredLogs.length} 条记录
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                      >
                        <ChevronLeft className="h-4 w-4" />
                        上一页
                      </Button>
                      <span className="text-sm">
                        第 {currentPage} 页，共 {Math.ceil(filteredLogs.length / pageSize)} 页
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(Math.min(Math.ceil(filteredLogs.length / pageSize), currentPage + 1))}
                        disabled={currentPage >= Math.ceil(filteredLogs.length / pageSize)}
                      >
                        下一页
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Log Detail Dialog */}
          <Dialog open={showLogDialog} onOpenChange={setShowLogDialog}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>日志详情</DialogTitle>
              </DialogHeader>
              {selectedLog && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">时间</label>
                      <p className="text-sm">{new Date(selectedLog.timestamp).toLocaleString('zh-CN')}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">服务器</label>
                      <p className="text-sm">{getServerName(selectedLog.serverId)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">级别</label>
                      <div className="mt-1">
                        {(() => {
                          const levelInfo = LogLevelMap[selectedLog.level as keyof typeof LogLevelMap];
                          const LevelIcon = levelInfo?.icon || FileText;
                          return (
                            <Badge className={levelInfo?.color || 'bg-gray-100 text-gray-800'}>
                              <LevelIcon className="h-3 w-3 mr-1" />
                              {levelInfo?.label || '未知'}
                            </Badge>
                          );
                        })()}
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">来源</label>
                      <p className="text-sm">{selectedLog.source || '-'}</p>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">消息</label>
                    <div className="mt-1 p-3 bg-gray-50 rounded border">
                      <p className="text-sm whitespace-pre-wrap">{selectedLog.message}</p>
                    </div>
                  </div>
                  {selectedLog.exception && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">异常信息</label>
                      <div className="mt-1 p-3 bg-red-50 rounded border border-red-200">
                        <p className="text-sm text-red-800 whitespace-pre-wrap">{selectedLog.exception}</p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </DialogContent>
          </Dialog>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

export default LogsPage;
