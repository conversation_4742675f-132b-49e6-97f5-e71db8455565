'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Search,
  Download,
  Eye,
  Trash2,
  RotateCcw,
  Calendar
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';

const LogsPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [logLevel, setLogLevel] = useState<string>('all');
  const [serverId, setServerId] = useState<string>('all');

  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 2000);
  };

  const handleExport = () => {
    console.log('导出日志');
  };

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.TECHNICAL_SUPPORT]}>
      <MainLayout>
        <div className="p-6">
          <div className="mb-6 flex justify-between items-center">
            <h1 className="text-3xl font-bold text-gray-900">服务器日志</h1>
            <div className="flex gap-2">
              <Button onClick={handleRefresh} disabled={loading}>
                <RotateCcw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                刷新日志
              </Button>
              <Button onClick={handleExport}>
                <Download className="h-4 w-4 mr-2" />
                导出日志
              </Button>
            </div>
          </div>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>日志筛选</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">搜索关键词</label>
                  <Input
                    placeholder="搜索日志内容..."
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">日志级别</label>
                  <Select value={logLevel} onValueChange={setLogLevel}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择日志级别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部级别</SelectItem>
                      <SelectItem value="ERROR">错误</SelectItem>
                      <SelectItem value="WARN">警告</SelectItem>
                      <SelectItem value="INFO">信息</SelectItem>
                      <SelectItem value="DEBUG">调试</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">服务器</label>
                  <Select value={serverId} onValueChange={setServerId}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择服务器" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部服务器</SelectItem>
                      <SelectItem value="1">服务器1</SelectItem>
                      <SelectItem value="2">服务器2</SelectItem>
                      <SelectItem value="3">服务器3</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-end">
                  <Button className="w-full">
                    <Search className="h-4 w-4 mr-2" />
                    搜索
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>日志记录</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>服务器日志功能正在开发中...</p>
                <p className="text-sm mt-2">完整的日志管理功能即将上线</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

export default LogsPage;
