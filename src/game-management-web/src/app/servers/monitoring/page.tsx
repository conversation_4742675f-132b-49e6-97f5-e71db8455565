'use client';

import React, { useState, useEffect } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import {
  Bell,
  AlertTriangle,
  XCircle,
  CheckCircle,
  Eye,
  Trash2,
  RotateCcw,
  Server,
  Activity,
  Cpu,
  HardDrive,
  Network,
  Users,
  Clock,
  AlertCircle,
  Play,
  Pause,
  Settings
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';

// TypeScript interfaces
interface ServerStatusDto {
  id: number;
  name: string;
  host: string;
  port: number;
  status: number;
  version?: string;
  maxPlayers: number;
  currentPlayers: number;
  lastHeartbeat?: string;
  region?: string;
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  lastPing?: string;
  responseTime: number;
}

interface ServerMonitoringDto {
  id: number;
  serverId: number;
  serverName: string;
  timestamp: string;
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  networkIn: number;
  networkOut: number;
  activeConnections: number;
  responseTime: number;
  isHealthy: boolean;
  alertMessage?: string;
}

interface MonitoringStatsDto {
  totalServers: number;
  healthyServers: number;
  unhealthyServers: number;
  activeAlerts: number;
  criticalAlerts: number;
  averageCpuUsage: number;
  averageMemoryUsage: number;
  averageDiskUsage: number;
  lastUpdated: string;
}

// Server status mapping
const ServerStatusMap = {
  0: { label: '离线', color: 'bg-gray-100 text-gray-800', icon: XCircle },
  1: { label: '在线', color: 'bg-green-100 text-green-800', icon: CheckCircle },
  2: { label: '维护', color: 'bg-yellow-100 text-yellow-800', icon: Settings },
  3: { label: '错误', color: 'bg-red-100 text-red-800', icon: AlertTriangle }
};

// API functions
const monitoringApi = {
  async getServerStatus(): Promise<ServerStatusDto[]> {
    const token = localStorage.getItem('token');
    const response = await fetch('http://localhost:5109/api/ServerMonitoring/servers/status', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) throw new Error('Failed to fetch server status');
    return response.json();
  },

  async getMonitoringStats(): Promise<MonitoringStatsDto> {
    const token = localStorage.getItem('token');
    const response = await fetch('http://localhost:5109/api/ServerMonitoring/stats', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) throw new Error('Failed to fetch monitoring stats');
    return response.json();
  },

  async getServerMonitoring(serverId: number): Promise<ServerMonitoringDto[]> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/ServerMonitoring/servers/${serverId}/monitoring`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) throw new Error('Failed to fetch server monitoring data');
    return response.json();
  },

  async restartServer(serverId: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/ServerMonitoring/servers/${serverId}/restart`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (!response.ok) throw new Error('Failed to restart server');
  },

  async performHealthCheck(serverId: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:5109/api/ServerMonitoring/servers/${serverId}/health-check`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (!response.ok) throw new Error('Failed to perform health check');
  }
};

const MonitoringPage: React.FC = () => {
  // State management
  const [servers, setServers] = useState<ServerStatusDto[]>([]);
  const [stats, setStats] = useState<MonitoringStatsDto | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedServer, setSelectedServer] = useState<number | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Data loading
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      const [serverData, statsData] = await Promise.all([
        monitoringApi.getServerStatus(),
        monitoringApi.getMonitoringStats()
      ]);
      setServers(serverData);
      setStats(statsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载监控数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // Auto refresh every 30 seconds
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      loadData();
    }, 30000);

    return () => clearInterval(interval);
  }, [autoRefresh]);

  // Event handlers
  const handleRefresh = () => {
    loadData();
  };

  const handleRestartServer = async (serverId: number) => {
    if (!confirm('确定要重启这个服务器吗？')) return;

    try {
      setLoading(true);
      await monitoringApi.restartServer(serverId);
      await loadData();
    } catch (err) {
      setError(err instanceof Error ? err.message : '重启服务器失败');
    } finally {
      setLoading(false);
    }
  };

  const handleHealthCheck = async (serverId: number) => {
    try {
      setLoading(true);
      await monitoringApi.performHealthCheck(serverId);
      await loadData();
    } catch (err) {
      setError(err instanceof Error ? err.message : '健康检查失败');
    } finally {
      setLoading(false);
    }
  };

  // Helper functions
  const getUsageColor = (usage: number) => {
    if (usage >= 90) return 'text-red-600';
    if (usage >= 70) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getUsageBarColor = (usage: number) => {
    if (usage >= 90) return 'bg-red-500';
    if (usage >= 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.TECHNICAL_SUPPORT]}>
      <MainLayout>
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">服务器监控</h1>
            <div className="flex space-x-2">
              <Button
                variant={autoRefresh ? "default" : "outline"}
                onClick={() => setAutoRefresh(!autoRefresh)}
                size="sm"
              >
                <Activity className="h-4 w-4 mr-2" />
                {autoRefresh ? '自动刷新' : '手动刷新'}
              </Button>
              <Button onClick={handleRefresh} disabled={loading}>
                <RotateCcw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                刷新
              </Button>
            </div>
          </div>

          {error && (
            <Alert className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 监控统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总服务器</CardTitle>
                <Server className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.totalServers || 0}</div>
                <p className="text-xs text-muted-foreground">
                  在线: {stats?.healthyServers || 0} | 离线: {stats?.unhealthyServers || 0}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">平均CPU使用率</CardTitle>
                <Cpu className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getUsageColor(stats?.averageCpuUsage || 0)}`}>
                  {(stats?.averageCpuUsage || 0).toFixed(1)}%
                </div>
                <Progress
                  value={stats?.averageCpuUsage || 0}
                  className="mt-2"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">平均内存使用率</CardTitle>
                <HardDrive className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getUsageColor(stats?.averageMemoryUsage || 0)}`}>
                  {(stats?.averageMemoryUsage || 0).toFixed(1)}%
                </div>
                <Progress
                  value={stats?.averageMemoryUsage || 0}
                  className="mt-2"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">活跃告警</CardTitle>
                <Bell className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{stats?.activeAlerts || 0}</div>
                <p className="text-xs text-muted-foreground">
                  严重: {stats?.criticalAlerts || 0}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* 服务器状态列表 */}
          <Card>
            <CardHeader>
              <CardTitle>服务器状态</CardTitle>
            </CardHeader>
            <CardContent>
              {loading && servers.length === 0 ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-500">加载中...</p>
                </div>
              ) : servers.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Server className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>暂无服务器数据</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>服务器信息</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>玩家数</TableHead>
                      <TableHead>CPU使用率</TableHead>
                      <TableHead>内存使用率</TableHead>
                      <TableHead>磁盘使用率</TableHead>
                      <TableHead>响应时间</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {servers.map((server) => {
                      const statusInfo = ServerStatusMap[server.status as keyof typeof ServerStatusMap];
                      const StatusIcon = statusInfo?.icon || Server;

                      return (
                        <TableRow key={server.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                                <Server className="h-4 w-4 text-blue-600" />
                              </div>
                              <div>
                                <div className="font-medium">{server.name}</div>
                                <div className="text-sm text-gray-500">
                                  {server.host}:{server.port}
                                </div>
                                {server.version && (
                                  <div className="text-xs text-gray-400">
                                    版本: {server.version}
                                  </div>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={statusInfo?.color || 'bg-gray-100 text-gray-800'}>
                              <StatusIcon className="h-3 w-3 mr-1" />
                              {statusInfo?.label || '未知'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Users className="h-4 w-4 mr-1 text-gray-400" />
                              <span className="font-medium">{server.currentPlayers}</span>
                              <span className="text-gray-400">/{server.maxPlayers}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                              <div
                                className="bg-blue-600 h-1.5 rounded-full"
                                style={{ width: `${(server.currentPlayers / server.maxPlayers) * 100}%` }}
                              ></div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className={`font-medium ${getUsageColor(server.cpuUsage)}`}>
                              {server.cpuUsage.toFixed(1)}%
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                              <div
                                className={`h-1.5 rounded-full ${getUsageBarColor(server.cpuUsage)}`}
                                style={{ width: `${server.cpuUsage}%` }}
                              ></div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className={`font-medium ${getUsageColor(server.memoryUsage)}`}>
                              {server.memoryUsage.toFixed(1)}%
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                              <div
                                className={`h-1.5 rounded-full ${getUsageBarColor(server.memoryUsage)}`}
                                style={{ width: `${server.memoryUsage}%` }}
                              ></div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className={`font-medium ${getUsageColor(server.diskUsage)}`}>
                              {server.diskUsage.toFixed(1)}%
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                              <div
                                className={`h-1.5 rounded-full ${getUsageBarColor(server.diskUsage)}`}
                                style={{ width: `${server.diskUsage}%` }}
                              ></div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Clock className="h-4 w-4 mr-1 text-gray-400" />
                              <span className="font-medium">{server.responseTime.toFixed(0)}ms</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleHealthCheck(server.id)}
                                disabled={loading}
                              >
                                <Activity className="h-3 w-3" />
                              </Button>

                              {server.status === 1 ? (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleRestartServer(server.id)}
                                  disabled={loading}
                                  className="text-orange-600 hover:text-orange-700"
                                >
                                  <RotateCcw className="h-3 w-3" />
                                </Button>
                              ) : (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  disabled
                                  className="text-gray-400"
                                >
                                  <Play className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>

          {/* 最后更新时间 */}
          {stats && (
            <div className="mt-4 text-center text-sm text-gray-500">
              最后更新: {new Date(stats.lastUpdated).toLocaleString('zh-CN')}
              {autoRefresh && <span className="ml-2">• 自动刷新已启用</span>}
            </div>
          )}
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

export default MonitoringPage;
