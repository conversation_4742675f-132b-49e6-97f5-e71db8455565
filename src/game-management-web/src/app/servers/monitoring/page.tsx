'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Bell,
  AlertTriangle,
  XCircle,
  CheckCircle,
  Eye,
  Trash2,
  RotateCcw
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';

const MonitoringPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [alertLevel, setAlertLevel] = useState<string>('all');
  const [alertStatus, setAlertStatus] = useState<string>('all');

  // 模拟监控告警数据
  const alertsData = [
    {
      key: '1',
      id: 4001,
      title: '服务器2 CPU使用率过高',
      level: 'critical',
      status: 'active',
      serverId: 2,
      serverName: '服务器2',
      metric: 'CPU使用率',
      currentValue: 95.8,
      threshold: 90,
      unit: '%',
      description: 'CPU使用率持续超过90%阈值超过5分钟',
      createdAt: '2024-06-25 14:25:30',
      updatedAt: '2024-06-25 14:30:25',
      acknowledgedBy: null,
      resolvedAt: null,
      duration: '5分钟'
    },
    {
      key: '2',
      id: 4002,
      title: '服务器2 内存使用率告警',
      level: 'warning',
      status: 'active',
      serverId: 2,
      serverName: '服务器2',
      metric: '内存使用率',
      currentValue: 92.3,
      threshold: 85,
      unit: '%',
      description: '内存使用率超过85%阈值',
      createdAt: '2024-06-25 14:20:15',
      updatedAt: '2024-06-25 14:30:25',
      acknowledgedBy: null,
      resolvedAt: null,
      duration: '10分钟'
    },
    {
      key: '3',
      id: 4003,
      title: '服务器4 连接超时',
      level: 'critical',
      status: 'resolved',
      serverId: 4,
      serverName: '服务器4',
      metric: '连接状态',
      currentValue: 0,
      threshold: 1,
      unit: '',
      description: '服务器连接超时，无法访问',
      createdAt: '2024-06-24 18:45:30',
      updatedAt: '2024-06-24 19:15:20',
      acknowledgedBy: '系统管理员',
      resolvedAt: '2024-06-24 19:15:20',
      duration: '30分钟'
    },
    {
      key: '4',
      id: 4004,
      title: '服务器1 磁盘空间不足',
      level: 'warning',
      status: 'acknowledged',
      serverId: 1,
      serverName: '服务器1',
      metric: '磁盘使用率',
      currentValue: 78.5,
      threshold: 75,
      unit: '%',
      description: '磁盘使用率超过75%阈值',
      createdAt: '2024-06-25 10:30:00',
      updatedAt: '2024-06-25 11:00:15',
      acknowledgedBy: '运维工程师A',
      resolvedAt: null,
      duration: '4小时'
    }
  ];

  const getLevelTag = (level: string) => {
    switch (level) {
      case 'critical':
        return <Tag color="red">严重</Tag>;
      case 'warning':
        return <Tag color="orange">警告</Tag>;
      case 'info':
        return <Tag color="blue">信息</Tag>;
      default:
        return <Tag color="default">未知</Tag>;
    }
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'active':
        return <Tag color="red" icon={<ExclamationCircleOutlined />}>活跃</Tag>;
      case 'acknowledged':
        return <Tag color="orange" icon={<BellOutlined />}>已确认</Tag>;
      case 'resolved':
        return <Tag color="green" icon={<CheckCircleOutlined />}>已解决</Tag>;
      default:
        return <Tag color="default">未知</Tag>;
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '告警标题',
      key: 'title',
      width: 250,
      render: (record: any) => (
        <div>
          <div style={{ fontWeight: 500, marginBottom: 4 }}>
            {record.title}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.description}
          </div>
        </div>
      ),
    },
    {
      title: '级别',
      key: 'level',
      width: 100,
      render: (record: any) => getLevelTag(record.level),
    },
    {
      title: '状态',
      key: 'status',
      width: 120,
      render: (record: any) => getStatusTag(record.status),
    },
    {
      title: '服务器',
      dataIndex: 'serverName',
      key: 'serverName',
      width: 120,
    },
    {
      title: '监控指标',
      key: 'metric',
      width: 150,
      render: (record: any) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.metric}</div>
          <div style={{ fontSize: '12px', color: record.currentValue > record.threshold ? '#ff4d4f' : '#666' }}>
            当前: {record.currentValue}{record.unit} / 阈值: {record.threshold}{record.unit}
          </div>
        </div>
      ),
    },
    {
      title: '持续时间',
      dataIndex: 'duration',
      key: 'duration',
      width: 120,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (record: any) => (
        <Space size="small">
          <Button 
            type="link" 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          {record.status === 'active' && (
            <Button 
              type="link" 
              size="small" 
              icon={<CheckCircleOutlined />}
              onClick={() => handleAcknowledge(record)}
            >
              确认
            </Button>
          )}
          {record.status === 'acknowledged' && (
            <Button 
              type="link" 
              size="small" 
              icon={<CheckCircleOutlined />}
              onClick={() => handleResolve(record)}
            >
              解决
            </Button>
          )}
          <Button 
            type="link" 
            size="small" 
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleViewDetail = (record: any) => {
    Modal.info({
      title: '告警详情',
      width: 600,
      content: (
        <div>
          <div style={{ marginBottom: 16 }}>
            <Space>
              {getLevelTag(record.level)}
              {getStatusTag(record.status)}
            </Space>
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>告警标题：</strong> {record.title}
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>服务器：</strong> {record.serverName}
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>监控指标：</strong> {record.metric}
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>当前值：</strong> 
            <span style={{ color: record.currentValue > record.threshold ? '#ff4d4f' : '#52c41a', fontWeight: 500 }}>
              {record.currentValue}{record.unit}
            </span>
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>阈值：</strong> {record.threshold}{record.unit}
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>持续时间：</strong> {record.duration}
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>描述：</strong> {record.description}
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>创建时间：</strong> {record.createdAt}
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>最后更新：</strong> {record.updatedAt}
          </div>
          {record.acknowledgedBy && (
            <div style={{ marginBottom: 16 }}>
              <strong>确认人：</strong> {record.acknowledgedBy}
            </div>
          )}
          {record.resolvedAt && (
            <div style={{ marginBottom: 16 }}>
              <strong>解决时间：</strong> {record.resolvedAt}
            </div>
          )}
        </div>
      ),
    });
  };

  const handleAcknowledge = (record: any) => {
    Modal.confirm({
      title: '确认告警',
      content: `确定要确认告警 "${record.title}" 吗？`,
      onOk() {
        message.success(`已确认告警: ${record.title}`);
      },
    });
  };

  const handleResolve = (record: any) => {
    Modal.confirm({
      title: '解决告警',
      content: `确定要将告警 "${record.title}" 标记为已解决吗？`,
      onOk() {
        message.success(`已解决告警: ${record.title}`);
      },
    });
  };

  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '删除告警',
      content: `确定要删除告警 "${record.title}" 吗？此操作不可恢复。`,
      onOk() {
        message.success(`已删除告警: ${record.title}`);
      },
    });
  };

  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      message.success('告警数据已刷新');
    }, 1000);
  };

  // 计算统计数据
  const activeAlerts = alertsData.filter(a => a.status === 'active').length;
  const criticalAlerts = alertsData.filter(a => a.level === 'critical' && a.status === 'active').length;
  const warningAlerts = alertsData.filter(a => a.level === 'warning' && a.status === 'active').length;
  const acknowledgedAlerts = alertsData.filter(a => a.status === 'acknowledged').length;

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER]}>
      <div>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
          <Title level={2}>监控告警</Title>
          <Button 
            type="primary" 
            icon={<ReloadOutlined />} 
            loading={loading}
            onClick={handleRefresh}
          >
            刷新数据
          </Button>
        </div>

        {/* 告警统计 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={6} lg={6}>
            <Card>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Badge count={activeAlerts} style={{ backgroundColor: '#ff4d4f' }}>
                  <BellOutlined style={{ fontSize: 24, color: '#ff4d4f' }} />
                </Badge>
                <div style={{ marginLeft: 16 }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ff4d4f' }}>
                    {activeAlerts}
                  </div>
                  <div style={{ color: '#666' }}>活跃告警</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={6} lg={6}>
            <Card>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <ExclamationCircleOutlined style={{ fontSize: 24, color: '#ff4d4f' }} />
                <div style={{ marginLeft: 16 }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ff4d4f' }}>
                    {criticalAlerts}
                  </div>
                  <div style={{ color: '#666' }}>严重告警</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={6} lg={6}>
            <Card>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <ExclamationCircleOutlined style={{ fontSize: 24, color: '#faad14' }} />
                <div style={{ marginLeft: 16 }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#faad14' }}>
                    {warningAlerts}
                  </div>
                  <div style={{ color: '#666' }}>警告告警</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={6} lg={6}>
            <Card>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <CheckCircleOutlined style={{ fontSize: 24, color: '#52c41a' }} />
                <div style={{ marginLeft: 16 }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                    {acknowledgedAlerts}
                  </div>
                  <div style={{ color: '#666' }}>已确认</div>
                </div>
              </div>
            </Card>
          </Col>
        </Row>

        {/* 活跃告警提示 */}
        {activeAlerts > 0 && (
          <Alert
            message={`当前有 ${activeAlerts} 个活跃告警需要处理`}
            description={`其中包含 ${criticalAlerts} 个严重告警和 ${warningAlerts} 个警告告警，请及时处理。`}
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {/* 筛选条件 */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={8} lg={6}>
              <Select
                placeholder="告警级别"
                style={{ width: '100%' }}
                value={alertLevel}
                onChange={setAlertLevel}
              >
                <Select.Option value="all">全部级别</Select.Option>
                <Select.Option value="critical">严重</Select.Option>
                <Select.Option value="warning">警告</Select.Option>
                <Select.Option value="info">信息</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={8} lg={6}>
              <Select
                placeholder="告警状态"
                style={{ width: '100%' }}
                value={alertStatus}
                onChange={setAlertStatus}
              >
                <Select.Option value="all">全部状态</Select.Option>
                <Select.Option value="active">活跃</Select.Option>
                <Select.Option value="acknowledged">已确认</Select.Option>
                <Select.Option value="resolved">已解决</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={8} lg={6}>
              <RangePicker style={{ width: '100%' }} />
            </Col>
          </Row>
        </Card>

        {/* 告警列表 */}
        <Card>
          <Table
            columns={columns}
            dataSource={alertsData}
            loading={loading}
            pagination={{
              total: 156,
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            scroll={{ x: 1400 }}
          />
        </Card>
      </div>
    </ProtectedRoute>
  );
};

export default MonitoringPage;
