'use client';

import React, { useState, useEffect } from 'react';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import MainLayout from '@/components/Layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Progress } from '@/components/ui/progress';
import {
  Server,
  CheckCircle,
  AlertTriangle,
  XCircle,
  RotateCcw,
  Settings,
  Eye
} from 'lucide-react';
import { ROLES } from '@/contexts/AuthContext';

const ServerStatusPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // 模拟服务器状态数据
  const serversData = [
    {
      key: '1',
      id: 1,
      name: '服务器1',
      region: '华东',
      ip: '*************',
      port: 8080,
      status: 'online',
      playerCount: 1256,
      maxPlayers: 2000,
      cpuUsage: 45.2,
      memoryUsage: 68.5,
      diskUsage: 32.1,
      networkIn: 125.6,
      networkOut: 89.3,
      uptime: '15天3小时25分钟',
      version: '2.5.1',
      lastUpdate: '2024-06-25 14:30:25',
      maintenance: false
    },
    {
      key: '2',
      id: 2,
      name: '服务器2',
      region: '华南',
      ip: '*************',
      port: 8080,
      status: 'warning',
      playerCount: 1890,
      maxPlayers: 2000,
      cpuUsage: 85.7,
      memoryUsage: 92.3,
      diskUsage: 78.9,
      networkIn: 256.8,
      networkOut: 198.7,
      uptime: '8天12小时15分钟',
      version: '2.5.1',
      lastUpdate: '2024-06-25 14:28:12',
      maintenance: false
    },
    {
      key: '3',
      id: 3,
      name: '服务器3',
      region: '华北',
      ip: '*************',
      port: 8080,
      status: 'maintenance',
      playerCount: 0,
      maxPlayers: 2000,
      cpuUsage: 0,
      memoryUsage: 0,
      diskUsage: 45.6,
      networkIn: 0,
      networkOut: 0,
      uptime: '维护中',
      version: '2.5.0',
      lastUpdate: '2024-06-25 02:00:00',
      maintenance: true
    },
    {
      key: '4',
      id: 4,
      name: '服务器4',
      region: '华西',
      ip: '*************',
      port: 8080,
      status: 'offline',
      playerCount: 0,
      maxPlayers: 2000,
      cpuUsage: 0,
      memoryUsage: 0,
      diskUsage: 0,
      networkIn: 0,
      networkOut: 0,
      uptime: '离线',
      version: '2.4.8',
      lastUpdate: '2024-06-24 18:45:30',
      maintenance: false
    }
  ];

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'online':
        return <Tag color="success" icon={<CheckCircleOutlined />}>在线</Tag>;
      case 'warning':
        return <Tag color="warning" icon={<ExclamationCircleOutlined />}>警告</Tag>;
      case 'maintenance':
        return <Tag color="processing" icon={<SettingOutlined />}>维护中</Tag>;
      case 'offline':
        return <Tag color="error" icon={<CloseCircleOutlined />}>离线</Tag>;
      default:
        return <Tag color="default">未知</Tag>;
    }
  };

  const getUsageColor = (usage: number) => {
    if (usage >= 90) return '#ff4d4f';
    if (usage >= 70) return '#faad14';
    return '#52c41a';
  };

  const columns = [
    {
      title: '服务器',
      key: 'server',
      width: 200,
      render: (record: any) => (
        <div>
          <div style={{ fontWeight: 500, fontSize: '14px' }}>
            <CloudServerOutlined style={{ marginRight: 8 }} />
            {record.name}
          </div>
          <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
            {record.region} | {record.ip}:{record.port}
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      key: 'status',
      width: 120,
      render: (record: any) => getStatusTag(record.status),
    },
    {
      title: '在线玩家',
      key: 'players',
      width: 150,
      render: (record: any) => (
        <div>
          <div style={{ fontSize: '14px', fontWeight: 500 }}>
            {record.playerCount.toLocaleString()} / {record.maxPlayers.toLocaleString()}
          </div>
          <Progress 
            percent={Math.round((record.playerCount / record.maxPlayers) * 100)} 
            size="small" 
            showInfo={false}
            strokeColor={record.playerCount / record.maxPlayers > 0.9 ? '#ff4d4f' : '#1890ff'}
          />
        </div>
      ),
    },
    {
      title: 'CPU使用率',
      key: 'cpu',
      width: 120,
      render: (record: any) => (
        <div>
          <div style={{ fontSize: '12px', marginBottom: 4 }}>{record.cpuUsage}%</div>
          <Progress 
            percent={record.cpuUsage} 
            size="small" 
            showInfo={false}
            strokeColor={getUsageColor(record.cpuUsage)}
          />
        </div>
      ),
    },
    {
      title: '内存使用率',
      key: 'memory',
      width: 120,
      render: (record: any) => (
        <div>
          <div style={{ fontSize: '12px', marginBottom: 4 }}>{record.memoryUsage}%</div>
          <Progress 
            percent={record.memoryUsage} 
            size="small" 
            showInfo={false}
            strokeColor={getUsageColor(record.memoryUsage)}
          />
        </div>
      ),
    },
    {
      title: '磁盘使用率',
      key: 'disk',
      width: 120,
      render: (record: any) => (
        <div>
          <div style={{ fontSize: '12px', marginBottom: 4 }}>{record.diskUsage}%</div>
          <Progress 
            percent={record.diskUsage} 
            size="small" 
            showInfo={false}
            strokeColor={getUsageColor(record.diskUsage)}
          />
        </div>
      ),
    },
    {
      title: '网络流量',
      key: 'network',
      width: 120,
      render: (record: any) => (
        <div style={{ fontSize: '12px' }}>
          <div>↓ {record.networkIn} MB/s</div>
          <div>↑ {record.networkOut} MB/s</div>
        </div>
      ),
    },
    {
      title: '运行时间',
      dataIndex: 'uptime',
      key: 'uptime',
      width: 150,
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (record: any) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="link" 
              size="small" 
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="重启服务器">
            <Button 
              type="link" 
              size="small" 
              icon={<ReloadOutlined />}
              onClick={() => handleRestart(record)}
              disabled={record.status === 'offline'}
            />
          </Tooltip>
          <Tooltip title="服务器设置">
            <Button 
              type="link" 
              size="small" 
              icon={<SettingOutlined />}
              onClick={() => handleSettings(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleViewDetail = (record: any) => {
    Modal.info({
      title: `${record.name} 详细信息`,
      width: 600,
      content: (
        <div>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Card size="small" title="基本信息">
                <p><strong>服务器名称：</strong>{record.name}</p>
                <p><strong>地区：</strong>{record.region}</p>
                <p><strong>IP地址：</strong>{record.ip}:{record.port}</p>
                <p><strong>版本：</strong>{record.version}</p>
                <p><strong>状态：</strong>{getStatusTag(record.status)}</p>
              </Card>
            </Col>
            <Col span={12}>
              <Card size="small" title="性能指标">
                <p><strong>CPU使用率：</strong>{record.cpuUsage}%</p>
                <p><strong>内存使用率：</strong>{record.memoryUsage}%</p>
                <p><strong>磁盘使用率：</strong>{record.diskUsage}%</p>
                <p><strong>运行时间：</strong>{record.uptime}</p>
              </Card>
            </Col>
            <Col span={12}>
              <Card size="small" title="玩家信息">
                <p><strong>在线玩家：</strong>{record.playerCount}</p>
                <p><strong>最大容量：</strong>{record.maxPlayers}</p>
                <p><strong>负载率：</strong>{Math.round((record.playerCount / record.maxPlayers) * 100)}%</p>
              </Card>
            </Col>
            <Col span={12}>
              <Card size="small" title="网络流量">
                <p><strong>入站流量：</strong>{record.networkIn} MB/s</p>
                <p><strong>出站流量：</strong>{record.networkOut} MB/s</p>
                <p><strong>最后更新：</strong>{record.lastUpdate}</p>
              </Card>
            </Col>
          </Row>
        </div>
      ),
    });
  };

  const handleRestart = (record: any) => {
    Modal.confirm({
      title: '确认重启',
      content: `确定要重启 "${record.name}" 吗？这将断开所有在线玩家的连接。`,
      onOk() {
        message.success(`正在重启 ${record.name}...`);
      },
    });
  };

  const handleSettings = (record: any) => {
    message.info(`打开 ${record.name} 的设置页面`);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    // 模拟刷新延迟
    setTimeout(() => {
      setRefreshing(false);
      message.success('服务器状态已刷新');
    }, 1000);
  };

  // 计算统计数据
  const onlineServers = serversData.filter(s => s.status === 'online').length;
  const warningServers = serversData.filter(s => s.status === 'warning').length;
  const offlineServers = serversData.filter(s => s.status === 'offline').length;
  const maintenanceServers = serversData.filter(s => s.status === 'maintenance').length;
  const totalPlayers = serversData.reduce((sum, s) => sum + s.playerCount, 0);

  return (
    <ProtectedRoute requiredRoles={[ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER]}>
      <div>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
          <Title level={2}>服务器状态</Title>
          <Button 
            type="primary" 
            icon={<ReloadOutlined />} 
            loading={refreshing}
            onClick={handleRefresh}
          >
            刷新状态
          </Button>
        </div>
        
        {/* 统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={6} lg={6}>
            <Card>
              <Statistic
                title="在线服务器"
                value={onlineServers}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#3f8600' }}
                suffix={`/ ${serversData.length}`}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6} lg={6}>
            <Card>
              <Statistic
                title="警告服务器"
                value={warningServers}
                prefix={<ExclamationCircleOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6} lg={6}>
            <Card>
              <Statistic
                title="离线服务器"
                value={offlineServers}
                prefix={<CloseCircleOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6} lg={6}>
            <Card>
              <Statistic
                title="总在线玩家"
                value={totalPlayers}
                prefix={<CloudServerOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 服务器列表 */}
        <Card>
          <Table
            columns={columns}
            dataSource={serversData}
            loading={loading}
            pagination={false}
            scroll={{ x: 1200 }}
          />
        </Card>
      </div>
    </ProtectedRoute>
  );
};

export default ServerStatusPage;
