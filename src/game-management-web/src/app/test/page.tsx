'use client';

import React from 'react';
import { Card, Typography, Space, Button } from 'antd';
import { useAuth } from '@/contexts/AuthContext';

const { Title, Text } = Typography;

const TestPage: React.FC = () => {
  const { user, isAuthenticated, login, logout } = useAuth();

  const handleTestLogin = async () => {
    await login('1', '1');
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>测试页面</Title>
      
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card title="认证状态">
          <Space direction="vertical">
            <Text>认证状态: {isAuthenticated ? '已登录' : '未登录'}</Text>
            {user && (
              <>
                <Text>用户名: {user.username}</Text>
                <Text>显示名: {user.displayName}</Text>
                <Text>角色: {user.roles?.join(', ')}</Text>
              </>
            )}
            <Space>
              <Button type="primary" onClick={handleTestLogin}>
                测试登录
              </Button>
              <Button onClick={logout}>
                退出登录
              </Button>
            </Space>
          </Space>
        </Card>

        <Card title="API测试">
          <Text>这里可以测试各种API调用</Text>
        </Card>

        <Card title="组件测试">
          <Text>这里可以测试各种UI组件</Text>
        </Card>
      </Space>
    </div>
  );
};

export default TestPage;
