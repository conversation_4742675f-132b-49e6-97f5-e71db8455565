'use client';

import React, { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter, usePathname } from 'next/navigation';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: string[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredRoles = [] 
}) => {
  const { isAuthenticated, isLoading, hasAnyRole } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        // 未登录，重定向到登录页
        router.push('/login');
        return;
      }

      if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {
        // 没有权限，重定向到仪表板或显示无权限页面
        router.push('/dashboard');
        return;
      }
    }
  }, [isAuthenticated, isLoading, hasAnyRole, requiredRoles, router]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // 将重定向到登录页
  }

  if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {
    return null; // 将重定向到仪表板
  }

  // 认证通过，返回子组件
  return <>{children}</>;
};

export default ProtectedRoute;
