'use client';

import React, { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter, usePathname } from 'next/navigation';
import { Spin } from 'antd';
import MainLayout from '@/components/Layout/MainLayout';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: string[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredRoles = [] 
}) => {
  const { isAuthenticated, isLoading, hasAnyRole } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        // 未登录，重定向到登录页
        router.push('/login');
        return;
      }

      if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {
        // 没有权限，重定向到仪表板或显示无权限页面
        router.push('/dashboard');
        return;
      }
    }
  }, [isAuthenticated, isLoading, hasAnyRole, requiredRoles, router]);

  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
      }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // 将重定向到登录页
  }

  if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {
    return null; // 将重定向到仪表板
  }

  // 如果是登录页面，不使用主布局
  if (pathname === '/login') {
    return <>{children}</>;
  }

  // 使用主布局包装受保护的页面
  return (
    <MainLayout>
      {children}
    </MainLayout>
  );
};

export default ProtectedRoute;
