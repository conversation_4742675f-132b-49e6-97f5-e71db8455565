'use client';

import React, { useState } from 'react';
import {
  Menu,
  PanelLeft,
  LayoutDashboard,
  Users,
  Gamepad2,
  DollarSign,
  Server,
  Bell,
  BarChart3,
  Settings,
  LogOut,
  Shield,
  Link,
  User,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { useAuth, ROLES } from '@/contexts/AuthContext';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [expandedMenus, setExpandedMenus] = useState<string[]>([]);
  const { user, logout, hasRole, hasAnyRole } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  // 菜单项配置
  const menuItems = [
    {
      key: '/dashboard',
      icon: <LayoutDashboard className="h-4 w-4" />,
      label: '仪表板',
      roles: [], // 所有角色都可以访问
    },
    {
      key: '/operational-data',
      icon: <BarChart3 className="h-4 w-4" />,
      label: '运营数据',
      roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],
    },
    {
      key: '/players',
      icon: <Users className="h-4 w-4" />,
      label: '玩家管理',
      roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST, ROLES.CUSTOMER_SERVICE_MANAGER, ROLES.CUSTOMER_SERVICE_SPECIALIST],
    },
    {
      key: '/payments',
      icon: <DollarSign className="h-4 w-4" />,
      label: '支付管理',
      roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],
    },
    {
      key: '/games',
      icon: <Gamepad2 className="h-4 w-4" />,
      label: '游戏数据',
      children: [
        {
          key: '/games/activities',
          label: '活动管理',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],
        },
        {
          key: '/games/announcements',
          label: '公告管理',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],
        },
        {
          key: '/games/items',
          label: '道具管理',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],
        },
      ],
    },
    {
      key: '/servers',
      icon: <Server className="h-4 w-4" />,
      label: '服务器管理',
      children: [
        {
          key: '/servers/status',
          label: '服务器状态',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER],
        },
        {
          key: '/servers/monitoring',
          label: '监控告警',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER],
        },
        {
          key: '/servers/logs',
          label: '日志管理',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER],
        },
      ],
    },
    {
      key: '/customer-service',
      icon: <Bell className="h-4 w-4" />,
      label: '客服管理',
      roles: [ROLES.SYSTEM_ADMIN, ROLES.CUSTOMER_SERVICE_MANAGER, ROLES.CUSTOMER_SERVICE_SPECIALIST],
    },
    {
      key: '/security',
      icon: <Shield className="h-4 w-4" />,
      label: '安全管理',
      roles: [ROLES.SYSTEM_ADMIN],
    },
    {
      key: '/reports',
      icon: <BarChart3 className="h-4 w-4" />,
      label: '数据报表',
      roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],
    },
    {
      key: '/channels',
      icon: <Link className="h-4 w-4" />,
      label: '渠道管理',
      roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER],
    },
  ];

  // 过滤菜单项基于用户角色
  const filterMenuItems = (items: any[]): any[] => {
    return items.filter(item => {
      if (item.roles && item.roles.length > 0) {
        if (!hasAnyRole(item.roles)) {
          return false;
        }
      }

      if (item.children) {
        item.children = filterMenuItems(item.children);
        return item.children.length > 0;
      }

      return true;
    });
  };

  const filteredMenuItems = filterMenuItems(menuItems);

  const handleMenuClick = (key: string) => {
    router.push(key);
  };

  const toggleSubmenu = (key: string) => {
    setExpandedMenus(prev =>
      prev.includes(key)
        ? prev.filter(k => k !== key)
        : [...prev, key]
    );
  };

  const renderMenuItem = (item: any, level = 0) => {
    const isActive = pathname === item.key;
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedMenus.includes(item.key);

    if (hasChildren) {
      return (
        <div key={item.key} className="mb-1">
          <button
            onClick={() => toggleSubmenu(item.key)}
            className={`w-full flex items-center justify-between px-3 py-2 text-sm rounded-md transition-colors ${
              collapsed ? 'px-2' : 'px-3'
            } hover:bg-gray-700 text-gray-300`}
            style={{ paddingLeft: `${12 + level * 16}px` }}
          >
            <div className="flex items-center space-x-2">
              {item.icon}
              {!collapsed && <span>{item.label}</span>}
            </div>
            {!collapsed && (
              isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />
            )}
          </button>
          {!collapsed && isExpanded && (
            <div className="ml-4">
              {item.children.map((child: any) => renderMenuItem(child, level + 1))}
            </div>
          )}
        </div>
      );
    }

    return (
      <button
        key={item.key}
        onClick={() => handleMenuClick(item.key)}
        className={`w-full flex items-center space-x-2 px-3 py-2 text-sm rounded-md transition-colors mb-1 ${
          collapsed ? 'px-2' : 'px-3'
        } ${
          isActive
            ? 'bg-blue-600 text-white'
            : 'text-gray-300 hover:bg-gray-700'
        }`}
        style={{ paddingLeft: `${12 + level * 16}px` }}
      >
        {item.icon}
        {!collapsed && <span>{item.label}</span>}
      </button>
    );
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* 侧边栏 */}
      <div className={`bg-gray-900 text-white transition-all duration-300 ${
        collapsed ? 'w-16' : 'w-64'
      }`}>
        {/* Logo */}
        <div className="h-16 flex items-center justify-center border-b border-gray-700">
          <div className="text-white font-bold text-lg">
            {collapsed ? 'GM' : '游戏管理系统'}
          </div>
        </div>

        {/* 菜单 */}
        <nav className="mt-4 px-2">
          {filteredMenuItems.map(item => renderMenuItem(item))}
        </nav>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* 顶部导航栏 */}
        <header className="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCollapsed(!collapsed)}
            className="p-2"
          >
            <Menu className="h-5 w-5" />
          </Button>

          <div className="flex items-center space-x-4">
            <span className="text-gray-600">欢迎回来，</span>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-gray-600" />
              </div>
              <span className="font-medium">{user?.displayName || user?.username}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={logout}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <LogOut className="h-4 w-4 mr-1" />
                退出
              </Button>
            </div>
          </div>
        </header>

        {/* 内容区域 */}
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  );
};

export default MainLayout;
