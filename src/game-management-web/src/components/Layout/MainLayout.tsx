'use client';

import React, { useState } from 'react';
import { Layout, Menu, Avatar, Dropdown, Button, theme, Space, Typography } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  UserOutlined,
  TeamOutlined,
  AppstoreOutlined,
  DollarOutlined,
  CloudServerOutlined,
  BellOutlined,
  Bar<PERSON>hartOutlined,
  SettingOutlined,
  LogoutOutlined,
  SafetyOutlined,
} from '@ant-design/icons';
import { useAuth, ROLES } from '@/contexts/AuthContext';
import { useRouter, usePathname } from 'next/navigation';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const { user, logout, hasRole, hasAnyRole } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  // 菜单项配置
  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
      roles: [], // 所有角色都可以访问
    },
    {
      key: '/operational-data',
      icon: <BarChartOutlined />,
      label: '运营数据',
      roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],
    },
    {
      key: '/players',
      icon: <TeamOutlined />,
      label: '玩家管理',
      roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST, ROLES.CUSTOMER_SERVICE_MANAGER, ROLES.CUSTOMER_SERVICE_SPECIALIST],
    },
    {
      key: '/payments',
      icon: <DollarOutlined />,
      label: '支付管理',
      roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],
    },
    {
      key: '/games',
      icon: <AppstoreOutlined />,
      label: '游戏数据',
      children: [
        {
          key: '/games/activities',
          label: '活动管理',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],
        },
        {
          key: '/games/announcements',
          label: '公告管理',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],
        },
        {
          key: '/games/items',
          label: '道具管理',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],
        },
      ],
    },
    {
      key: '/servers',
      icon: <CloudServerOutlined />,
      label: '服务器管理',
      children: [
        {
          key: '/servers/status',
          label: '服务器状态',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER],
        },
        {
          key: '/servers/monitoring',
          label: '监控告警',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER],
        },
        {
          key: '/servers/logs',
          label: '日志管理',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER],
        },
      ],
    },
    {
      key: '/customer-service',
      icon: <BellOutlined />,
      label: '客服管理',
      children: [
        {
          key: '/customer-service/tickets',
          label: '工单管理',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.CUSTOMER_SERVICE_MANAGER, ROLES.CUSTOMER_SERVICE_SPECIALIST],
        },
        {
          key: '/customer-service/feedback',
          label: '反馈管理',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.CUSTOMER_SERVICE_MANAGER, ROLES.CUSTOMER_SERVICE_SPECIALIST],
        },
      ],
    },
    {
      key: '/security',
      icon: <SafetyOutlined />,
      label: '安全管理',
      children: [
        {
          key: '/security/audit-logs',
          label: '审计日志',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER],
        },
        {
          key: '/security/risk-control',
          label: '风控管理',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER],
        },
      ],
    },
    {
      key: '/reports',
      icon: <BarChartOutlined />,
      label: '数据报表',
      children: [
        {
          key: '/reports/revenue',
          label: '收入报表',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],
        },
        {
          key: '/reports/players',
          label: '玩家报表',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],
        },
        {
          key: '/reports/retention',
          label: '留存报表',
          roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER, ROLES.PRODUCT_SPECIALIST],
        },
      ],
    },
    {
      key: '/users',
      icon: <UserOutlined />,
      label: '用户管理',
      roles: [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER],
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      roles: [ROLES.SYSTEM_ADMIN],
    },
  ];

  // 过滤菜单项基于用户角色
  const filterMenuItems = (items: any[]): any[] => {
    return items.filter(item => {
      if (item.roles && item.roles.length > 0) {
        if (!hasAnyRole(item.roles)) {
          return false;
        }
      }
      
      if (item.children) {
        item.children = filterMenuItems(item.children);
        return item.children.length > 0;
      }
      
      return true;
    });
  };

  const filteredMenuItems = filterMenuItems(menuItems);

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => router.push('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '账户设置',
      onClick: () => router.push('/account-settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: logout,
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    router.push(key);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider trigger={null} collapsible collapsed={collapsed} theme="dark">
        <div style={{ 
          height: 64, 
          margin: 16, 
          background: 'rgba(255, 255, 255, 0.2)', 
          borderRadius: 6,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontWeight: 'bold',
          fontSize: collapsed ? 14 : 16,
        }}>
          {collapsed ? 'GM' : '游戏管理系统'}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[pathname]}
          items={filteredMenuItems}
          onClick={handleMenuClick}
        />
      </Sider>
      <Layout>
        <Header style={{ 
          padding: '0 16px', 
          background: colorBgContainer,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: '1px solid #f0f0f0',
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
            }}
          />
          
          <Space>
            <Text type="secondary">欢迎回来，</Text>
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <Space style={{ cursor: 'pointer' }}>
                <Avatar icon={<UserOutlined />} />
                <Text strong>{user?.displayName || user?.username}</Text>
              </Space>
            </Dropdown>
          </Space>
        </Header>
        <Content
          style={{
            margin: '16px',
            padding: 24,
            minHeight: 280,
            background: colorBgContainer,
            borderRadius: borderRadiusLG,
          }}
        >
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
