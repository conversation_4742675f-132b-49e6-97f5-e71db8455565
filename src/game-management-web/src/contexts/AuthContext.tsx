'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { authApi, User } from '@/lib/api';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  hasRole: (role: string) => boolean;
  hasAnyRole: (roles: string[]) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 检查本地存储中的用户信息
    if (typeof window !== 'undefined') {
      const storedUser = localStorage.getItem('user');
      const accessToken = localStorage.getItem('accessToken');

      if (storedUser && accessToken) {
        try {
          const parsedUser = JSON.parse(storedUser);
          setUser(parsedUser);
        } catch (error) {
          console.error('Error parsing stored user:', error);
          localStorage.removeItem('user');
          localStorage.removeItem('accessToken');
          localStorage.removeItem('refreshToken');
        }
      }
    }

    setIsLoading(false);
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      const response = await authApi.login({ username, password });
      const { token, user: userData } = response.data;

      // 存储认证信息
      if (typeof window !== 'undefined') {
        localStorage.setItem('accessToken', token);
        localStorage.setItem('user', JSON.stringify(userData));

        // 如果响应中有刷新令牌，也存储它
        if (response.data.refreshToken) {
          localStorage.setItem('refreshToken', response.data.refreshToken);
        }
      }

      setUser(userData);
      console.log('登录成功');
      return true;
    } catch (error: any) {
      console.error('Login error:', error);
      const errorMessage = error.response?.data?.message || '登录失败，请检查用户名和密码';
      console.error(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      if (typeof window !== 'undefined') {
        const refreshToken = localStorage.getItem('refreshToken');
        if (refreshToken) {
          await authApi.logout(refreshToken);
        }
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // 清除本地存储
      if (typeof window !== 'undefined') {
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('user');
      }
      
      setUser(null);
      console.log('已退出登录');

      // 重定向到登录页
      window.location.href = '/login';
    }
  };

  const hasRole = (role: string): boolean => {
    return user?.roles?.includes(role) || false;
  };

  const hasAnyRole = (roles: string[]): boolean => {
    return roles.some(role => hasRole(role));
  };

  const isAuthenticated = !!user;

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    logout,
    hasRole,
    hasAnyRole,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// 角色常量
export const ROLES = {
  SYSTEM_ADMIN: 'SystemAdmin',
  PRODUCT_MANAGER: 'ProductManager',
  PRODUCT_SPECIALIST: 'ProductSpecialist',
  PARTNER_MANAGER: 'PartnerManager',
  PARTNER_SPECIALIST: 'PartnerSpecialist',
  CUSTOMER_SERVICE_MANAGER: 'CustomerServiceManager',
  CUSTOMER_SERVICE_SPECIALIST: 'CustomerServiceSpecialist',
  VIEWER: 'Viewer',
} as const;

// 权限检查工具函数
export const checkPermission = (userRoles: string[], requiredRoles: string[]): boolean => {
  return requiredRoles.some(role => userRoles.includes(role));
};

// 管理员角色检查
export const isAdmin = (userRoles: string[]): boolean => {
  return checkPermission(userRoles, [ROLES.SYSTEM_ADMIN, ROLES.PRODUCT_MANAGER]);
};

// 客服权限检查
export const hasCustomerServicePermission = (userRoles: string[]): boolean => {
  return checkPermission(userRoles, [
    ROLES.SYSTEM_ADMIN,
    ROLES.PRODUCT_MANAGER,
    ROLES.CUSTOMER_SERVICE_MANAGER,
    ROLES.CUSTOMER_SERVICE_SPECIALIST,
  ]);
};

// 产品管理权限检查
export const hasProductPermission = (userRoles: string[]): boolean => {
  return checkPermission(userRoles, [
    ROLES.SYSTEM_ADMIN,
    ROLES.PRODUCT_MANAGER,
    ROLES.PRODUCT_SPECIALIST,
  ]);
};

// 渠道管理权限检查
export const hasPartnerPermission = (userRoles: string[]): boolean => {
  return checkPermission(userRoles, [
    ROLES.SYSTEM_ADMIN,
    ROLES.PRODUCT_MANAGER,
    ROLES.PARTNER_MANAGER,
    ROLES.PARTNER_SPECIALIST,
  ]);
};
