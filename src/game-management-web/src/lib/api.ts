import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// API 基础配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5108/api';

// 创建 axios 实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证令牌
apiClient.interceptors.request.use(
  (config) => {
    // 检查是否在浏览器环境中
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('accessToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理认证错误
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry && typeof window !== 'undefined') {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refreshToken');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, refreshToken);
          const { token } = response.data;

          localStorage.setItem('accessToken', token);
          originalRequest.headers.Authorization = `Bearer ${token}`;

          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        // 刷新令牌失败，清除本地存储并重定向到登录页
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('user');
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// API 接口类型定义
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  expiresAt: string;
  user: User;
}

export interface User {
  id: number;
  username: string;
  email: string;
  displayName?: string;
  createdAt: string;
  lastLoginAt?: string;
  isActive: boolean;
  roles: string[];
}

export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  displayName?: string;
  roles: string[];
}

export interface UpdateUserRequest {
  username?: string;
  email?: string;
  displayName?: string;
  isActive?: boolean;
  roles?: string[];
}

export interface Player {
  id: number;
  accountId: string;
  nickname: string;
  level: number;
  class: string;
  experience: number;
  gold: number;
  diamonds: number;
  vipLevel: number;
  lastLoginAt?: string;
  totalPlayTime: string;
  ipAddress?: string;
  serverId: number;
  serverName: string;
  isBanned: boolean;
  bannedUntil?: string;
  banReason?: string;
  createdAt: string;
}

export interface PlayerStats {
  totalPlayers: number;
  activePlayers: number;
  newPlayersToday: number;
  vipPlayers: number;
  averageLevel: number;
  levelDistribution: Record<string, number>;
  classDistribution: Record<string, number>;
}

// API 方法
export const authApi = {
  login: (data: LoginRequest) => 
    apiClient.post<LoginResponse>('/auth/login', data),
  
  logout: (refreshToken: string) => 
    apiClient.post('/auth/logout', refreshToken),
  
  register: (data: CreateUserRequest) => 
    apiClient.post<User>('/auth/register', data),
  
  refreshToken: (refreshToken: string) => 
    apiClient.post<LoginResponse>('/auth/refresh', refreshToken),
  
  changePassword: (currentPassword: string, newPassword: string) => 
    apiClient.post('/auth/change-password', { currentPassword, newPassword }),
  
  resetPassword: (email: string) => 
    apiClient.post('/auth/reset-password', { email }),
};

export const usersApi = {
  getUsers: () => 
    apiClient.get<User[]>('/users'),
  
  getUser: (id: number) => 
    apiClient.get<User>(`/users/${id}`),
  
  getUserByUsername: (username: string) => 
    apiClient.get<User>(`/users/by-username/${username}`),
  
  createUser: (data: CreateUserRequest) => 
    apiClient.post<User>('/users', data),
  
  updateUser: (id: number, data: UpdateUserRequest) => 
    apiClient.put<User>(`/users/${id}`, data),
  
  deleteUser: (id: number) => 
    apiClient.delete(`/users/${id}`),
  
  activateUser: (id: number) => 
    apiClient.post(`/users/${id}/activate`),
  
  deactivateUser: (id: number) => 
    apiClient.post(`/users/${id}/deactivate`),
  
  getUsersByRole: (role: string) => 
    apiClient.get<User[]>(`/users/by-role/${role}`),
};

export const playersApi = {
  getPlayers: (page: number = 1, pageSize: number = 20) => 
    apiClient.get<Player[]>(`/players?page=${page}&pageSize=${pageSize}`),
  
  getPlayer: (id: number) => 
    apiClient.get<Player>(`/players/${id}`),
  
  getPlayerByAccountId: (accountId: string) => 
    apiClient.get<Player>(`/players/by-account/${accountId}`),
  
  searchPlayers: (searchTerm: string) => 
    apiClient.get<Player[]>(`/players/search?searchTerm=${encodeURIComponent(searchTerm)}`),
  
  getPlayerStats: () => 
    apiClient.get<PlayerStats>('/players/stats'),
  
  getTopPlayersByLevel: (count: number = 10) => 
    apiClient.get<Player[]>(`/players/top-by-level?count=${count}`),
  
  getVipPlayers: (minVipLevel: number = 1) => 
    apiClient.get<Player[]>(`/players/vip?minVipLevel=${minVipLevel}`),
  
  updatePlayer: (id: number, data: any) => 
    apiClient.put(`/players/${id}`, data),
  
  banPlayer: (id: number, bannedUntil?: string, reason?: string) => 
    apiClient.post(`/players/${id}/ban`, { bannedUntil, reason }),
  
  unbanPlayer: (id: number) => 
    apiClient.post(`/players/${id}/unban`),
};

// 运营数据相关接口
export interface GlobalStats {
  totalRevenue: number;
  totalVisits: number;
  totalRegistrations: number;
  totalLogins: number;
  todayNewVisits: number;
  todayNewRegistrations: number;
  todayNewLogins: number;
  todayNewPayments: number;
  todayActiveUsers: number;
  arpu: number;
  averageOnlineUsers: number;
  maxOnlineUsers: number;
  lastUpdated: string;
}

export interface UserInfoStats {
  totalVisits: number;
  uniqueVisits: number;
  totalRegistrations: number;
  registrationsWithChannel: number;
  totalLogins: number;
  sameIpLogins: number;
  currentOnlineUsers: number;
  usersWithoutCharacter: number;
  usersNeverLoggedIn: number;
  registrationsByChannel: Record<string, number>;
  visitsByHour: Record<string, number>;
}

export interface PaymentInfoStats {
  totalRevenue: number;
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  failedOrders: number;
  averageOrderValue: number;
  revenueByMethod: Record<string, number>;
  ordersByStatus: Record<string, number>;
  topPayers: TopPayer[];
}

export interface TopPayer {
  accountId: string;
  nickname: string;
  totalAmount: number;
  orderCount: number;
  lastPaymentTime: string;
}

export interface ConversionAnalysis {
  visitToRegistrationRate: number;
  registrationToPaymentRate: number;
  registrationToCharacterCreationRate: number;
  characterCreationToFirstLoginRate: number;
  analysisDate: string;
  conversionByChannel: Record<string, number>;
}

export interface RetentionAnalysis {
  nextDayRetentionRate: number;
  sevenDayRetentionRate: number;
  monthlyRetentionRate: number;
  analysisDate: string;
  retentionByChannel: Record<string, number>;
  retentionByLevel: Record<string, number>;
}

export interface ActiveUserAnalysis {
  dau: number;
  wau: number;
  mau: number;
  averageSessionTime: number;
  activeUsersByHour: Record<string, number>;
  activeUsersByServer: Record<string, number>;
  analysisDate: string;
}

// 运营数据API方法
export const operationalDataApi = {
  // 全局统计
  getGlobalStats: () =>
    apiClient.get<GlobalStats>('/operational-data/global-stats'),

  getGlobalStatsByDate: (date: string) =>
    apiClient.get<GlobalStats>(`/operational-data/global-stats/${date}`),

  // 用户信息统计
  getUserInfoStats: () =>
    apiClient.get<UserInfoStats>('/operational-data/user-info-stats'),

  getUserInfoStatsByDateRange: (startDate: string, endDate: string) =>
    apiClient.get<UserInfoStats>(`/operational-data/user-info-stats/${startDate}/${endDate}`),

  // 付费信息统计
  getPaymentInfoStats: () =>
    apiClient.get<PaymentInfoStats>('/operational-data/payment-info-stats'),

  getPaymentInfoStatsByDateRange: (startDate: string, endDate: string) =>
    apiClient.get<PaymentInfoStats>(`/operational-data/payment-info-stats/${startDate}/${endDate}`),

  // 数据分析
  getConversionAnalysis: (date: string) =>
    apiClient.get<ConversionAnalysis>(`/operational-data/conversion-analysis/${date}`),

  getRetentionAnalysis: (date: string) =>
    apiClient.get<RetentionAnalysis>(`/operational-data/retention-analysis/${date}`),

  getActiveUserAnalysis: (date: string) =>
    apiClient.get<ActiveUserAnalysis>(`/operational-data/active-user-analysis/${date}`),

  // 记录数据
  recordVisit: (data: { referrer?: string; channel?: string; serverId?: number }) =>
    apiClient.post('/operational-data/record-visit', data),
};

export default apiClient;
