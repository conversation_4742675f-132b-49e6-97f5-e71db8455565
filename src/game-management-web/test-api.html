<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Connection Test</h1>
    <button onclick="testLogin()">Test Login</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('http://localhost:5109/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: '111',
                        password: '111111'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `<pre>Success: ${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<pre>Error: ${response.status} ${response.statusText}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<pre>Network Error: ${error.message}</pre>`;
            }
        }
    </script>
</body>
</html>
