using Xunit;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using GameManagement.API.Controllers;
using GameManagement.Core.Interfaces;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using System.Security.Claims;
using Microsoft.AspNetCore.Http;

namespace GameManagement.Tests.Controllers;

public class ChannelControllerTests
{
    private readonly Mock<IChannelService> _mockChannelService;
    private readonly Mock<ILogger<ChannelController>> _mockLogger;
    private readonly ChannelController _controller;

    public ChannelControllerTests()
    {
        _mockChannelService = new Mock<IChannelService>();
        _mockLogger = new Mock<ILogger<ChannelController>>();
        _controller = new ChannelController(_mockChannelService.Object, _mockLogger.Object);

        // Setup user context
        var user = new ClaimsPrincipal(new ClaimsIdentity(new Claim[]
        {
            new Claim(ClaimTypes.Name, "testuser"),
            new Claim(ClaimTypes.Role, "Admin")
        }, "mock"));

        _controller.ControllerContext = new ControllerContext()
        {
            HttpContext = new DefaultHttpContext() { User = user }
        };
    }

    [Fact]
    public async Task GetChannels_ReturnsOkResult_WithChannels()
    {
        // Arrange
        var channels = new List<ChannelDto>
        {
            new ChannelDto { Id = 1, Name = "Test Channel 1", Code = "TC001" },
            new ChannelDto { Id = 2, Name = "Test Channel 2", Code = "TC002" }
        };
        _mockChannelService.Setup(s => s.GetChannelsAsync(It.IsAny<int>(), It.IsAny<int>()))
            .ReturnsAsync(channels);

        // Act
        var result = await _controller.GetChannels();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedChannels = Assert.IsAssignableFrom<IEnumerable<ChannelDto>>(okResult.Value);
        Assert.Equal(2, returnedChannels.Count());
    }

    [Fact]
    public async Task GetChannel_ExistingId_ReturnsOkResult()
    {
        // Arrange
        var channel = new ChannelDto { Id = 1, Name = "Test Channel", Code = "TC001" };
        _mockChannelService.Setup(s => s.GetChannelByIdAsync(1))
            .ReturnsAsync(channel);

        // Act
        var result = await _controller.GetChannel(1);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedChannel = Assert.IsType<ChannelDto>(okResult.Value);
        Assert.Equal(1, returnedChannel.Id);
    }

    [Fact]
    public async Task GetChannel_NonExistingId_ReturnsNotFound()
    {
        // Arrange
        _mockChannelService.Setup(s => s.GetChannelByIdAsync(999))
            .ReturnsAsync((ChannelDto)null);

        // Act
        var result = await _controller.GetChannel(999);

        // Assert
        Assert.IsType<NotFoundResult>(result.Result);
    }

    [Fact]
    public async Task GetChannelByCode_ExistingCode_ReturnsOkResult()
    {
        // Arrange
        var channel = new ChannelDto { Id = 1, Name = "Test Channel", Code = "TC001" };
        _mockChannelService.Setup(s => s.GetChannelByCodeAsync("TC001"))
            .ReturnsAsync(channel);

        // Act
        var result = await _controller.GetChannelByCode("TC001");

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedChannel = Assert.IsType<ChannelDto>(okResult.Value);
        Assert.Equal("TC001", returnedChannel.Code);
    }

    [Fact]
    public async Task CreateChannel_ValidData_ReturnsCreatedResult()
    {
        // Arrange
        var createChannelDto = new CreateChannelDto
        {
            Name = "New Channel",
            Code = "NC001",
            Description = "New Channel Description",
            IsActive = true,
            ContactPerson = "John Doe",
            ContactEmail = "<EMAIL>",
            CommissionRate = 0.05m
        };

        var createdChannel = new ChannelDto
        {
            Id = 1,
            Name = "New Channel",
            Code = "NC001",
            Description = "New Channel Description",
            IsActive = true,
            ContactPerson = "John Doe",
            ContactEmail = "<EMAIL>",
            CommissionRate = 0.05m
        };

        _mockChannelService.Setup(s => s.CreateChannelAsync(createChannelDto))
            .ReturnsAsync(createdChannel);

        // Act
        var result = await _controller.CreateChannel(createChannelDto);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedChannel = Assert.IsType<ChannelDto>(createdResult.Value);
        Assert.Equal("New Channel", returnedChannel.Name);
    }

    [Fact]
    public async Task CreateChannel_DuplicateCode_ReturnsConflict()
    {
        // Arrange
        var createChannelDto = new CreateChannelDto
        {
            Name = "Duplicate Channel",
            Code = "DC001",
            IsActive = true,
            CommissionRate = 0.05m
        };

        _mockChannelService.Setup(s => s.CreateChannelAsync(createChannelDto))
            .ThrowsAsync(new InvalidOperationException("Channel code already exists"));

        // Act
        var result = await _controller.CreateChannel(createChannelDto);

        // Assert
        var conflictResult = Assert.IsType<ConflictObjectResult>(result.Result);
        Assert.Equal("Channel code already exists", conflictResult.Value);
    }

    [Fact]
    public async Task UpdateChannel_ValidData_ReturnsOkResult()
    {
        // Arrange
        var updateChannelDto = new UpdateChannelDto
        {
            Name = "Updated Channel",
            Description = "Updated Description"
        };

        var updatedChannel = new ChannelDto
        {
            Id = 1,
            Name = "Updated Channel",
            Description = "Updated Description"
        };

        _mockChannelService.Setup(s => s.UpdateChannelAsync(1, updateChannelDto))
            .ReturnsAsync(updatedChannel);

        // Act
        var result = await _controller.UpdateChannel(1, updateChannelDto);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedChannel = Assert.IsType<ChannelDto>(okResult.Value);
        Assert.Equal("Updated Channel", returnedChannel.Name);
    }

    [Fact]
    public async Task UpdateChannel_NonExistingChannel_ReturnsNotFound()
    {
        // Arrange
        var updateChannelDto = new UpdateChannelDto { Name = "Updated Channel" };
        _mockChannelService.Setup(s => s.UpdateChannelAsync(999, updateChannelDto))
            .ThrowsAsync(new InvalidOperationException("Channel not found"));

        // Act
        var result = await _controller.UpdateChannel(999, updateChannelDto);

        // Assert
        var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
        Assert.Equal("Channel not found", notFoundResult.Value);
    }

    [Fact]
    public async Task DeleteChannel_ExistingChannel_ReturnsNoContent()
    {
        // Arrange
        _mockChannelService.Setup(s => s.DeleteChannelAsync(1))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.DeleteChannel(1);

        // Assert
        Assert.IsType<NoContentResult>(result);
    }

    [Fact]
    public async Task DeleteChannel_NonExistingChannel_ReturnsNotFound()
    {
        // Arrange
        _mockChannelService.Setup(s => s.DeleteChannelAsync(999))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.DeleteChannel(999);

        // Assert
        Assert.IsType<NotFoundResult>(result);
    }

    [Fact]
    public async Task ActivateChannel_ExistingChannel_ReturnsOkResult()
    {
        // Arrange
        _mockChannelService.Setup(s => s.ActivateChannelAsync(1))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.ActivateChannel(1);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.NotNull(okResult.Value);
    }

    [Fact]
    public async Task DeactivateChannel_ExistingChannel_ReturnsOkResult()
    {
        // Arrange
        _mockChannelService.Setup(s => s.DeactivateChannelAsync(1))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.DeactivateChannel(1);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.NotNull(okResult.Value);
    }

    [Fact]
    public async Task GetActiveChannels_ReturnsOkResult_WithActiveChannels()
    {
        // Arrange
        var activeChannels = new List<ChannelDto>
        {
            new ChannelDto { Id = 1, Name = "Active Channel 1", IsActive = true },
            new ChannelDto { Id = 2, Name = "Active Channel 2", IsActive = true }
        };
        _mockChannelService.Setup(s => s.GetActiveChannelsAsync())
            .ReturnsAsync(activeChannels);

        // Act
        var result = await _controller.GetActiveChannels();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedChannels = Assert.IsAssignableFrom<IEnumerable<ChannelDto>>(okResult.Value);
        Assert.Equal(2, returnedChannels.Count());
        Assert.All(returnedChannels, channel => Assert.True(channel.IsActive));
    }

    [Fact]
    public async Task GetInactiveChannels_ReturnsOkResult_WithInactiveChannels()
    {
        // Arrange
        var inactiveChannels = new List<ChannelDto>
        {
            new ChannelDto { Id = 3, Name = "Inactive Channel 1", IsActive = false }
        };
        _mockChannelService.Setup(s => s.GetInactiveChannelsAsync())
            .ReturnsAsync(inactiveChannels);

        // Act
        var result = await _controller.GetInactiveChannels();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedChannels = Assert.IsAssignableFrom<IEnumerable<ChannelDto>>(okResult.Value);
        Assert.Single(returnedChannels);
        Assert.All(returnedChannels, channel => Assert.False(channel.IsActive));
    }

    [Fact]
    public async Task SearchChannels_ValidTerm_ReturnsOkResult()
    {
        // Arrange
        var searchResults = new List<ChannelDto>
        {
            new ChannelDto { Id = 1, Name = "Test Channel", Code = "TC001" }
        };
        _mockChannelService.Setup(s => s.SearchChannelsAsync("Test"))
            .ReturnsAsync(searchResults);

        // Act
        var result = await _controller.SearchChannels("Test");

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedChannels = Assert.IsAssignableFrom<IEnumerable<ChannelDto>>(okResult.Value);
        Assert.Single(returnedChannels);
    }

    [Fact]
    public async Task SearchChannels_EmptyTerm_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.SearchChannels("");

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
        Assert.Equal("Search term is required", badRequestResult.Value);
    }
}
