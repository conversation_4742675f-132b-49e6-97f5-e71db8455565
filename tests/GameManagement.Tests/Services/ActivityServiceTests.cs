using GameManagement.Core.Entities;
using GameManagement.Infrastructure.Data;
using GameManagement.Infrastructure.Services;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace GameManagement.Tests.Services;

public class ActivityServiceTests : IDisposable
{
    private readonly GameManagementDbContext _context;
    private readonly ActivityService _activityService;
    private readonly Mock<ILogger<ActivityService>> _mockLogger;

    public ActivityServiceTests()
    {
        var options = new DbContextOptionsBuilder<GameManagementDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new GameManagementDbContext(options);
        _mockLogger = new Mock<ILogger<ActivityService>>();
        _activityService = new ActivityService(_context, _mockLogger.Object);

        SeedTestData();
    }

    private void SeedTestData()
    {
        // Add test players
        var players = new List<Player>
        {
            new Player
            {
                Id = 1,
                AccountId = "player001",
                Nickname = "TestPlayer1",
                Level = 10,
                ServerId = 1,
                CreatedAt = DateTime.UtcNow.AddDays(-30),
                UpdatedAt = DateTime.UtcNow
            },
            new Player
            {
                Id = 2,
                AccountId = "player002",
                Nickname = "TestPlayer2",
                Level = 15,
                ServerId = 1,
                CreatedAt = DateTime.UtcNow.AddDays(-20),
                UpdatedAt = DateTime.UtcNow
            }
        };

        _context.Players.AddRange(players);

        // Add test activities
        var activities = new List<GameActivity>
        {
            new GameActivity
            {
                Id = 1,
                Name = "Test Activity 1",
                Description = "Test Description 1",
                Status = ActivityStatus.Active,
                StartTime = DateTime.UtcNow.AddDays(-5),
                EndTime = DateTime.UtcNow.AddDays(5),
                Conditions = "Level >= 5",
                Rewards = "100 coins",
                ParticipantCount = 2,
                CompletionCount = 1,
                CreatedAt = DateTime.UtcNow.AddDays(-10),
                UpdatedAt = DateTime.UtcNow
            },
            new GameActivity
            {
                Id = 2,
                Name = "Test Activity 2",
                Description = "Test Description 2",
                Status = ActivityStatus.Draft,
                StartTime = DateTime.UtcNow.AddDays(1),
                EndTime = DateTime.UtcNow.AddDays(10),
                Conditions = "Level >= 10",
                Rewards = "200 coins",
                ParticipantCount = 0,
                CompletionCount = 0,
                CreatedAt = DateTime.UtcNow.AddDays(-5),
                UpdatedAt = DateTime.UtcNow
            }
        };

        _context.GameActivities.AddRange(activities);

        // Add test player activities
        var playerActivities = new List<PlayerActivity>
        {
            new PlayerActivity
            {
                Id = 1,
                PlayerId = 1,
                ActivityId = 1,
                ParticipatedAt = DateTime.UtcNow.AddDays(-3),
                IsCompleted = true,
                CompletedAt = DateTime.UtcNow.AddDays(-1),
                RewardsReceived = "100 coins"
            },
            new PlayerActivity
            {
                Id = 2,
                PlayerId = 2,
                ActivityId = 1,
                ParticipatedAt = DateTime.UtcNow.AddDays(-2),
                IsCompleted = false
            }
        };

        _context.PlayerActivities.AddRange(playerActivities);
        _context.SaveChanges();
    }

    [Fact]
    public async Task GetActivityByIdAsync_ExistingId_ReturnsActivity()
    {
        // Act
        var result = await _activityService.GetActivityByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.Id);
        Assert.Equal("Test Activity 1", result.Name);
        Assert.Equal(ActivityStatus.Active, result.Status);
    }

    [Fact]
    public async Task GetActivityByIdAsync_NonExistingId_ReturnsNull()
    {
        // Act
        var result = await _activityService.GetActivityByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetActivitiesAsync_ReturnsAllActivities()
    {
        // Act
        var result = await _activityService.GetActivitiesAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());
    }

    [Fact]
    public async Task GetActiveActivitiesAsync_ReturnsOnlyActiveActivities()
    {
        // Act
        var result = await _activityService.GetActiveActivitiesAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.All(result, activity => Assert.Equal(ActivityStatus.Active, activity.Status));
    }

    [Fact]
    public async Task CreateActivityAsync_ValidData_CreatesActivity()
    {
        // Arrange
        var createDto = new CreateActivityDto
        {
            Name = "New Activity",
            Description = "New Description",
            StartTime = DateTime.UtcNow.AddDays(1),
            EndTime = DateTime.UtcNow.AddDays(10),
            Conditions = "Level >= 5",
            Rewards = "50 coins"
        };

        // Act
        var result = await _activityService.CreateActivityAsync(createDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("New Activity", result.Name);
        Assert.Equal(ActivityStatus.Draft, result.Status);
        Assert.Equal(0, result.ParticipantCount);
        Assert.Equal(0, result.CompletionCount);
    }

    [Fact]
    public async Task UpdateActivityAsync_ExistingActivity_UpdatesActivity()
    {
        // Arrange
        var updateDto = new UpdateActivityDto
        {
            Name = "Updated Activity",
            Description = "Updated Description"
        };

        // Act
        var result = await _activityService.UpdateActivityAsync(1, updateDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Activity", result.Name);
        Assert.Equal("Updated Description", result.Description);
    }

    [Fact]
    public async Task StartActivityAsync_DraftActivity_StartsActivity()
    {
        // Act
        var result = await _activityService.StartActivityAsync(2);

        // Assert
        Assert.True(result);

        var activity = await _context.GameActivities.FindAsync(2);
        Assert.Equal(ActivityStatus.Active, activity!.Status);
    }

    [Fact]
    public async Task StartActivityAsync_ActiveActivity_ReturnsFalse()
    {
        // Act
        var result = await _activityService.StartActivityAsync(1);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task PauseActivityAsync_ActiveActivity_PausesActivity()
    {
        // Act
        var result = await _activityService.PauseActivityAsync(1);

        // Assert
        Assert.True(result);

        var activity = await _context.GameActivities.FindAsync(1);
        Assert.Equal(ActivityStatus.Paused, activity!.Status);
    }

    [Fact]
    public async Task GetActivityStatsAsync_ExistingActivity_ReturnsStats()
    {
        // Act
        var result = await _activityService.GetActivityStatsAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.ActivityId);
        Assert.Equal("Test Activity 1", result.ActivityName);
        Assert.Equal(2, result.TotalParticipants);
        Assert.Equal(1, result.CompletedParticipants);
        Assert.Equal(50.0, result.CompletionRate);
    }

    [Fact]
    public async Task AddPlayerToActivityAsync_ValidData_AddsPlayer()
    {
        // Act
        var result = await _activityService.AddPlayerToActivityAsync(2, 1);

        // Assert
        Assert.True(result);

        var playerActivity = await _context.PlayerActivities
            .FirstOrDefaultAsync(pa => pa.ActivityId == 2 && pa.PlayerId == 1);
        Assert.NotNull(playerActivity);
        Assert.False(playerActivity.IsCompleted);

        var activity = await _context.GameActivities.FindAsync(2);
        Assert.Equal(1, activity!.ParticipantCount);
    }

    [Fact]
    public async Task CompleteActivityForPlayerAsync_ExistingParticipation_CompletesActivity()
    {
        // Act
        var result = await _activityService.CompleteActivityForPlayerAsync(1, 2, "200 coins");

        // Assert
        Assert.True(result);

        var playerActivity = await _context.PlayerActivities
            .FirstOrDefaultAsync(pa => pa.ActivityId == 1 && pa.PlayerId == 2);
        Assert.NotNull(playerActivity);
        Assert.True(playerActivity.IsCompleted);
        Assert.Equal("200 coins", playerActivity.RewardsReceived);
        Assert.NotNull(playerActivity.CompletedAt);

        var activity = await _context.GameActivities.FindAsync(1);
        Assert.Equal(2, activity!.CompletionCount);
    }

    [Fact]
    public async Task GetActivityParticipantsAsync_ExistingActivity_ReturnsParticipants()
    {
        // Act
        var result = await _activityService.GetActivityParticipantsAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());
        Assert.All(result, participant => Assert.Equal(1, participant.ActivityId));
    }

    [Fact]
    public async Task DeleteActivityAsync_ActiveActivity_ReturnsFalse()
    {
        // Act
        var result = await _activityService.DeleteActivityAsync(1);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task DeleteActivityAsync_DraftActivity_DeletesActivity()
    {
        // Act
        var result = await _activityService.DeleteActivityAsync(2);

        // Assert
        Assert.True(result);

        var activity = await _context.GameActivities.FindAsync(2);
        Assert.Null(activity);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
