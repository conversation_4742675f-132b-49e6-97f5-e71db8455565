using GameManagement.Core.Entities;
using GameManagement.Infrastructure.Data;
using GameManagement.Infrastructure.Services;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace GameManagement.Tests.Services;

public class AnnouncementServiceTests : IDisposable
{
    private readonly GameManagementDbContext _context;
    private readonly AnnouncementService _service;
    private readonly Mock<ILogger<AnnouncementService>> _mockLogger;

    public AnnouncementServiceTests()
    {
        var options = new DbContextOptionsBuilder<GameManagementDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new GameManagementDbContext(options);
        _mockLogger = new Mock<ILogger<AnnouncementService>>();
        _service = new AnnouncementService(_context, _mockLogger.Object);

        SeedTestData();
    }

    private void SeedTestData()
    {
        var announcements = new List<GameAnnouncement>
        {
            new GameAnnouncement
            {
                Id = 1,
                Title = "System Maintenance",
                Content = "Server maintenance scheduled for tonight",
                Type = AnnouncementType.Maintenance,
                StartTime = DateTime.UtcNow.AddHours(-1),
                EndTime = DateTime.UtcNow.AddHours(1),
                IsActive = true,
                Priority = 90,
                ViewCount = 150,
                CreatedAt = DateTime.UtcNow.AddDays(-1)
            },
            new GameAnnouncement
            {
                Id = 2,
                Title = "New Event",
                Content = "Exciting new event starting soon!",
                Type = AnnouncementType.Event,
                StartTime = DateTime.UtcNow.AddDays(1),
                EndTime = DateTime.UtcNow.AddDays(7),
                IsActive = true,
                Priority = 70,
                ViewCount = 85,
                CreatedAt = DateTime.UtcNow.AddHours(-2)
            },
            new GameAnnouncement
            {
                Id = 3,
                Title = "Game Update",
                Content = "New features and bug fixes",
                Type = AnnouncementType.Update,
                StartTime = DateTime.UtcNow.AddDays(-2),
                EndTime = DateTime.UtcNow.AddDays(-1),
                IsActive = false,
                Priority = 50,
                ViewCount = 200,
                CreatedAt = DateTime.UtcNow.AddDays(-3)
            }
        };

        _context.GameAnnouncements.AddRange(announcements);
        _context.SaveChanges();
    }

    [Fact]
    public async Task GetAnnouncementsAsync_ReturnsPagedResults()
    {
        // Act
        var result = await _service.GetAnnouncementsAsync(1, 2);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());
    }

    [Fact]
    public async Task GetAnnouncementByIdAsync_ExistingId_ReturnsAnnouncement()
    {
        // Act
        var result = await _service.GetAnnouncementByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("System Maintenance", result.Title);
        Assert.Equal(AnnouncementType.Maintenance, result.Type);
    }

    [Fact]
    public async Task GetAnnouncementByIdAsync_NonExistingId_ReturnsNull()
    {
        // Act
        var result = await _service.GetAnnouncementByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task CreateAnnouncementAsync_ValidData_CreatesAnnouncement()
    {
        // Arrange
        var createDto = new CreateGameAnnouncementDto
        {
            Title = "New Announcement",
            Content = "This is a test announcement",
            Type = AnnouncementType.News,
            StartTime = DateTime.UtcNow,
            Priority = 60
        };

        // Act
        var result = await _service.CreateAnnouncementAsync(createDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("New Announcement", result.Title);
        Assert.Equal(AnnouncementType.News, result.Type);
        Assert.True(result.IsActive);
        Assert.Equal(0, result.ViewCount);
    }

    [Fact]
    public async Task UpdateAnnouncementAsync_ExistingId_UpdatesAnnouncement()
    {
        // Arrange
        var updateDto = new UpdateGameAnnouncementDto
        {
            Title = "Updated Title",
            Priority = 95
        };

        // Act
        var result = await _service.UpdateAnnouncementAsync(1, updateDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Title", result.Title);
        Assert.Equal(95, result.Priority);
        Assert.NotNull(result.UpdatedAt);
    }

    [Fact]
    public async Task UpdateAnnouncementAsync_NonExistingId_ThrowsException()
    {
        // Arrange
        var updateDto = new UpdateGameAnnouncementDto
        {
            Title = "Updated Title"
        };

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => 
            _service.UpdateAnnouncementAsync(999, updateDto));
    }

    [Fact]
    public async Task DeleteAnnouncementAsync_ExistingId_DeletesAnnouncement()
    {
        // Act
        var result = await _service.DeleteAnnouncementAsync(3);

        // Assert
        Assert.True(result);
        var deleted = await _context.GameAnnouncements.FindAsync(3);
        Assert.Null(deleted);
    }

    [Fact]
    public async Task DeleteAnnouncementAsync_NonExistingId_ReturnsFalse()
    {
        // Act
        var result = await _service.DeleteAnnouncementAsync(999);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task ActivateAnnouncementAsync_ExistingId_ActivatesAnnouncement()
    {
        // Act
        var result = await _service.ActivateAnnouncementAsync(3);

        // Assert
        Assert.True(result);
        var announcement = await _context.GameAnnouncements.FindAsync(3);
        Assert.True(announcement.IsActive);
        Assert.NotNull(announcement.UpdatedAt);
    }

    [Fact]
    public async Task DeactivateAnnouncementAsync_ExistingId_DeactivatesAnnouncement()
    {
        // Act
        var result = await _service.DeactivateAnnouncementAsync(1);

        // Assert
        Assert.True(result);
        var announcement = await _context.GameAnnouncements.FindAsync(1);
        Assert.False(announcement.IsActive);
        Assert.NotNull(announcement.UpdatedAt);
    }

    [Fact]
    public async Task GetAnnouncementsByTypeAsync_ReturnsFilteredResults()
    {
        // Act
        var result = await _service.GetAnnouncementsByTypeAsync(AnnouncementType.Maintenance);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("System Maintenance", result.First().Title);
    }

    [Fact]
    public async Task GetActiveAnnouncementsAsync_ReturnsOnlyActiveAnnouncements()
    {
        // Act
        var result = await _service.GetActiveAnnouncementsAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result); // Only the maintenance announcement should be active and within time range
        Assert.All(result, a => Assert.True(a.IsActive));
    }

    [Fact]
    public async Task SearchAnnouncementsAsync_ReturnsMatchingResults()
    {
        // Act
        var result = await _service.SearchAnnouncementsAsync("maintenance");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Contains("maintenance", result.First().Title.ToLower());
    }

    [Fact]
    public async Task GetAnnouncementsByPriorityAsync_ReturnsFilteredResults()
    {
        // Act
        var result = await _service.GetAnnouncementsByPriorityAsync(70);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count()); // Maintenance (90) and Event (70)
        Assert.All(result, a => Assert.True(a.Priority >= 70));
    }

    [Fact]
    public async Task BatchActivateAnnouncementsAsync_ActivatesMultipleAnnouncements()
    {
        // Arrange
        var ids = new[] { 2, 3 };

        // Act
        var result = await _service.BatchActivateAnnouncementsAsync(ids);

        // Assert
        Assert.Equal(2, result);
        var announcements = await _context.GameAnnouncements
            .Where(a => ids.Contains(a.Id))
            .ToListAsync();
        Assert.All(announcements, a => Assert.True(a.IsActive));
    }

    [Fact]
    public async Task BatchDeleteAnnouncementsAsync_DeletesMultipleAnnouncements()
    {
        // Arrange
        var ids = new[] { 2, 3 };

        // Act
        var result = await _service.BatchDeleteAnnouncementsAsync(ids);

        // Assert
        Assert.Equal(2, result);
        var remainingCount = await _context.GameAnnouncements.CountAsync();
        Assert.Equal(1, remainingCount); // Only announcement 1 should remain
    }

    [Fact]
    public async Task IncrementViewCountAsync_ExistingId_IncrementsCount()
    {
        // Arrange
        var originalCount = (await _context.GameAnnouncements.FindAsync(1)).ViewCount;

        // Act
        var result = await _service.IncrementViewCountAsync(1);

        // Assert
        Assert.True(result);
        var announcement = await _context.GameAnnouncements.FindAsync(1);
        Assert.Equal(originalCount + 1, announcement.ViewCount);
    }

    [Fact]
    public async Task GetAnnouncementStatsAsync_ReturnsCorrectStats()
    {
        // Act
        var result = await _service.GetAnnouncementStatsAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.TotalAnnouncements);
        Assert.Equal(1, result.ActiveAnnouncements); // Only maintenance is currently active
        Assert.Equal(1, result.InactiveAnnouncements); // Only the update announcement is inactive
        Assert.Equal(435, result.TotalViews); // 150 + 85 + 200
        Assert.True(result.AverageViewsPerAnnouncement > 0);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
