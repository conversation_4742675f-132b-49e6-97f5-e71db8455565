using Xunit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using GameManagement.Infrastructure.Services;
using GameManagement.Infrastructure.Data;
using GameManagement.Core.Entities;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;

namespace GameManagement.Tests.Services;

public class ChannelServiceTests : IDisposable
{
    private readonly GameManagementDbContext _context;
    private readonly Mock<ILogger<ChannelService>> _mockLogger;
    private readonly ChannelService _channelService;

    public ChannelServiceTests()
    {
        var options = new DbContextOptionsBuilder<GameManagementDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new GameManagementDbContext(options);
        _mockLogger = new Mock<ILogger<ChannelService>>();
        _channelService = new ChannelService(_context, _mockLogger.Object);

        SeedTestData();
    }

    private void SeedTestData()
    {
        var channels = new List<Channel>
        {
            new Channel
            {
                Id = 1,
                Name = "Test Channel 1",
                Code = "TC001",
                Description = "Test Channel 1 Description",
                IsActive = true,
                ContactPerson = "John Doe",
                ContactEmail = "<EMAIL>",
                ContactPhone = "************",
                CommissionRate = 0.05m,
                CallbackUrl = "https://test1.com/callback",
                ApiKey = "test-api-key-1",
                ApiSecret = "test-api-secret-1",
                ContractStartDate = DateTime.UtcNow.AddDays(-30),
                ContractEndDate = DateTime.UtcNow.AddDays(330),
                CreatedAt = DateTime.UtcNow.AddDays(-30)
            },
            new Channel
            {
                Id = 2,
                Name = "Test Channel 2",
                Code = "TC002",
                Description = "Test Channel 2 Description",
                IsActive = false,
                ContactPerson = "Jane Smith",
                ContactEmail = "<EMAIL>",
                ContactPhone = "************",
                CommissionRate = 0.03m,
                CallbackUrl = "https://test2.com/callback",
                ContractStartDate = DateTime.UtcNow.AddDays(-60),
                ContractEndDate = DateTime.UtcNow.AddDays(-10), // Expired
                CreatedAt = DateTime.UtcNow.AddDays(-60)
            }
        };

        var userRegistrations = new List<UserRegistration>
        {
            new UserRegistration
            {
                Id = 1,
                ChannelId = 1,
                AccountId = "testuser1",
                IpAddress = "***********",
                RegistrationTime = DateTime.UtcNow.AddDays(-20),
                HasCreatedCharacter = true,
                FirstLoginTime = DateTime.UtcNow.AddDays(-19),
                CreatedAt = DateTime.UtcNow.AddDays(-20)
            },
            new UserRegistration
            {
                Id = 2,
                ChannelId = 1,
                AccountId = "testuser2",
                IpAddress = "***********",
                RegistrationTime = DateTime.UtcNow.AddDays(-15),
                HasCreatedCharacter = true,
                FirstLoginTime = DateTime.UtcNow.AddDays(-14),
                CreatedAt = DateTime.UtcNow.AddDays(-15)
            }
        };

        var paymentRecords = new List<PaymentRecord>
        {
            new PaymentRecord
            {
                Id = 1,
                ChannelId = 1,
                PlayerId = 1,
                OrderId = "ORDER001",
                Amount = 100.00m,
                Currency = "CNY",
                DiamondsPurchased = 1000,
                Status = PaymentStatus.Completed,
                PaymentMethod = "WeChat",
                TransactionId = "WX001",
                CompletedAt = DateTime.UtcNow.AddDays(-10),
                CreatedAt = DateTime.UtcNow.AddDays(-10)
            },
            new PaymentRecord
            {
                Id = 2,
                ChannelId = 1,
                PlayerId = 2,
                OrderId = "ORDER002",
                Amount = 200.00m,
                Currency = "CNY",
                DiamondsPurchased = 2000,
                Status = PaymentStatus.Completed,
                PaymentMethod = "Alipay",
                TransactionId = "ALI001",
                CompletedAt = DateTime.UtcNow.AddDays(-5),
                CreatedAt = DateTime.UtcNow.AddDays(-5)
            }
        };

        var channelData = new List<ChannelData>
        {
            new ChannelData
            {
                Id = 1,
                ChannelId = 1,
                Date = DateTime.UtcNow.AddDays(-10).Date,
                MetricName = "registrations",
                Value = 5,
                CreatedAt = DateTime.UtcNow.AddDays(-10)
            },
            new ChannelData
            {
                Id = 2,
                ChannelId = 1,
                Date = DateTime.UtcNow.AddDays(-5).Date,
                MetricName = "revenue",
                Value = 300.00m,
                CreatedAt = DateTime.UtcNow.AddDays(-5)
            }
        };

        _context.Channels.AddRange(channels);
        _context.UserRegistrations.AddRange(userRegistrations);
        _context.PaymentRecords.AddRange(paymentRecords);
        _context.ChannelData.AddRange(channelData);
        _context.SaveChanges();
    }

    [Fact]
    public async Task GetChannelsAsync_ReturnsAllChannels()
    {
        // Act
        var result = await _channelService.GetChannelsAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());
    }

    [Fact]
    public async Task GetChannelByIdAsync_ExistingId_ReturnsChannel()
    {
        // Act
        var result = await _channelService.GetChannelByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Channel 1", result.Name);
        Assert.Equal("TC001", result.Code);
    }

    [Fact]
    public async Task GetChannelByIdAsync_NonExistingId_ReturnsNull()
    {
        // Act
        var result = await _channelService.GetChannelByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetChannelByCodeAsync_ExistingCode_ReturnsChannel()
    {
        // Act
        var result = await _channelService.GetChannelByCodeAsync("TC001");

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Channel 1", result.Name);
        Assert.Equal(1, result.Id);
    }

    [Fact]
    public async Task GetChannelByCodeAsync_NonExistingCode_ReturnsNull()
    {
        // Act
        var result = await _channelService.GetChannelByCodeAsync("NONEXISTENT");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task CreateChannelAsync_ValidData_CreatesChannel()
    {
        // Arrange
        var createChannelDto = new CreateChannelDto
        {
            Name = "New Test Channel",
            Code = "NTC001",
            Description = "New Test Channel Description",
            IsActive = true,
            ContactPerson = "New Contact",
            ContactEmail = "<EMAIL>",
            ContactPhone = "************",
            CommissionRate = 0.04m,
            CallbackUrl = "https://newtest.com/callback"
        };

        // Act
        var result = await _channelService.CreateChannelAsync(createChannelDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("New Test Channel", result.Name);
        Assert.Equal("NTC001", result.Code);
        Assert.True(result.IsActive);
        Assert.NotNull(result.ApiKey);
        Assert.NotNull(result.ApiKey);
    }

    [Fact]
    public async Task CreateChannelAsync_DuplicateCode_ThrowsException()
    {
        // Arrange
        var createChannelDto = new CreateChannelDto
        {
            Name = "Duplicate Channel",
            Code = "TC001", // Existing code
            Description = "Duplicate Channel Description",
            IsActive = true,
            ContactPerson = "Duplicate Contact",
            ContactEmail = "<EMAIL>",
            ContactPhone = "************",
            CommissionRate = 0.04m
        };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(
            () => _channelService.CreateChannelAsync(createChannelDto));
    }

    [Fact]
    public async Task UpdateChannelAsync_ExistingChannel_UpdatesChannel()
    {
        // Arrange
        var updateChannelDto = new UpdateChannelDto
        {
            Name = "Updated Channel Name",
            Description = "Updated Description",
            ContactPerson = "Updated Contact",
            ContactEmail = "<EMAIL>",
            CommissionRate = 0.06m
        };

        // Act
        var result = await _channelService.UpdateChannelAsync(1, updateChannelDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Channel Name", result.Name);
        Assert.Equal("Updated Description", result.Description);
        Assert.Equal("Updated Contact", result.ContactPerson);
        Assert.Equal("<EMAIL>", result.ContactEmail);
        Assert.Equal(0.06m, result.CommissionRate);
    }

    [Fact]
    public async Task UpdateChannelAsync_NonExistingChannel_ThrowsException()
    {
        // Arrange
        var updateChannelDto = new UpdateChannelDto
        {
            Name = "Updated Channel Name"
        };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(
            () => _channelService.UpdateChannelAsync(999, updateChannelDto));
    }

    [Fact]
    public async Task DeleteChannelAsync_ExistingChannelWithoutData_DeletesChannel()
    {
        // Arrange - Create a channel without associated data
        var channel = new Channel
        {
            Id = 3,
            Name = "Delete Test Channel",
            Code = "DTC001",
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };
        _context.Channels.Add(channel);
        await _context.SaveChangesAsync();

        // Act
        var result = await _channelService.DeleteChannelAsync(3);

        // Assert
        Assert.True(result);
        var deletedChannel = await _context.Channels.FindAsync(3);
        Assert.Null(deletedChannel);
    }

    [Fact]
    public async Task DeleteChannelAsync_ExistingChannelWithData_SoftDeletesChannel()
    {
        // Act
        var result = await _channelService.DeleteChannelAsync(1);

        // Assert
        Assert.True(result);
        var channel = await _context.Channels.FindAsync(1);
        Assert.NotNull(channel);
        Assert.False(channel.IsActive); // Soft deleted
    }

    [Fact]
    public async Task DeleteChannelAsync_NonExistingChannel_ReturnsFalse()
    {
        // Act
        var result = await _channelService.DeleteChannelAsync(999);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task ActivateChannelAsync_ExistingChannel_ActivatesChannel()
    {
        // Act
        var result = await _channelService.ActivateChannelAsync(2);

        // Assert
        Assert.True(result);
        var channel = await _context.Channels.FindAsync(2);
        Assert.NotNull(channel);
        Assert.True(channel.IsActive);
        Assert.NotNull(channel.ApiKey);
        Assert.NotNull(channel.ApiSecret);
    }

    [Fact]
    public async Task DeactivateChannelAsync_ExistingChannel_DeactivatesChannel()
    {
        // Act
        var result = await _channelService.DeactivateChannelAsync(1);

        // Assert
        Assert.True(result);
        var channel = await _context.Channels.FindAsync(1);
        Assert.NotNull(channel);
        Assert.False(channel.IsActive);
    }

    [Fact]
    public async Task GetActiveChannelsAsync_ReturnsOnlyActiveChannels()
    {
        // Act
        var result = await _channelService.GetActiveChannelsAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.All(result, channel => Assert.True(channel.IsActive));
    }

    [Fact]
    public async Task GetInactiveChannelsAsync_ReturnsOnlyInactiveChannels()
    {
        // Act
        var result = await _channelService.GetInactiveChannelsAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.All(result, channel => Assert.False(channel.IsActive));
    }

    [Fact]
    public async Task SearchChannelsAsync_WithValidTerm_ReturnsMatchingChannels()
    {
        // Act
        var result = await _channelService.SearchChannelsAsync("Test Channel 1");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Test Channel 1", result.First().Name);
    }

    [Fact]
    public async Task GetChannelsByCommissionRangeAsync_ValidRange_ReturnsChannelsInRange()
    {
        // Act
        var result = await _channelService.GetChannelsByCommissionRangeAsync(0.03m, 0.05m);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());
    }

    [Fact]
    public async Task GetChannelsWithActiveContractsAsync_ReturnsChannelsWithActiveContracts()
    {
        // Act
        var result = await _channelService.GetChannelsWithActiveContractsAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(1, result.First().Id);
    }

    [Fact]
    public async Task GetChannelsWithExpiringContractsAsync_ReturnsChannelsWithExpiringContracts()
    {
        // Arrange - Add a channel with contract expiring soon
        var channel = new Channel
        {
            Id = 4,
            Name = "Expiring Channel",
            Code = "EC001",
            IsActive = true,
            ContractStartDate = DateTime.UtcNow.AddDays(-30),
            ContractEndDate = DateTime.UtcNow.AddDays(15), // Expires in 15 days
            CreatedAt = DateTime.UtcNow.AddDays(-30)
        };
        _context.Channels.Add(channel);
        await _context.SaveChangesAsync();

        // Act
        var result = await _channelService.GetChannelsWithExpiringContractsAsync(30);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Expiring Channel", result.First().Name);
    }

    [Fact]
    public async Task AddChannelDataAsync_ValidData_AddsChannelData()
    {
        // Act
        var result = await _channelService.AddChannelDataAsync(1, "test_metric", 100.5m, DateTime.UtcNow.Date, "test data");

        // Assert
        Assert.True(result);
        var channelData = await _context.ChannelData
            .FirstOrDefaultAsync(cd => cd.ChannelId == 1 && cd.MetricName == "test_metric");
        Assert.NotNull(channelData);
        Assert.Equal(100.5m, channelData.Value);
    }

    [Fact]
    public async Task GetChannelDataAsync_ExistingChannel_ReturnsChannelData()
    {
        // Act
        var result = await _channelService.GetChannelDataAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());
    }

    [Fact]
    public async Task GetChannelDataByMetricAsync_ExistingMetric_ReturnsFilteredData()
    {
        // Act
        var result = await _channelService.GetChannelDataByMetricAsync(1, "registrations");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("registrations", result.First().MetricName);
    }

    [Fact]
    public async Task UpdateChannelDataAsync_ExistingData_UpdatesData()
    {
        // Act
        var result = await _channelService.UpdateChannelDataAsync(1, 150.0m, "updated data");

        // Assert
        Assert.True(result);
        var channelData = await _context.ChannelData.FindAsync(1);
        Assert.NotNull(channelData);
        Assert.Equal(150.0m, channelData.Value);
        Assert.Equal("updated data", channelData.AdditionalData);
    }

    [Fact]
    public async Task DeleteChannelDataAsync_ExistingData_DeletesData()
    {
        // Act
        var result = await _channelService.DeleteChannelDataAsync(1);

        // Assert
        Assert.True(result);
        var channelData = await _context.ChannelData.FindAsync(1);
        Assert.Null(channelData);
    }

    [Fact]
    public async Task GetChannelStatsAsync_ReturnsCorrectStats()
    {
        // Act
        var result = await _channelService.GetChannelStatsAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.TotalChannels);
        Assert.Equal(1, result.ActiveChannels);
        Assert.Equal(1, result.InactiveChannels);
        Assert.Equal(300.0m, result.TotalRevenue);
        Assert.Equal(2, result.TotalRegistrations);
        Assert.Equal(2, result.TotalPayments);
    }

    [Fact]
    public async Task GetChannelPerformanceAsync_ExistingChannel_ReturnsPerformance()
    {
        // Act
        var result = await _channelService.GetChannelPerformanceAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.ChannelId);
        Assert.Equal("Test Channel 1", result.ChannelName);
        Assert.Equal(2, result.TotalRegistrations);
        Assert.Equal(2, result.TotalPayments);
        Assert.Equal(300.0m, result.TotalRevenue);
        Assert.Equal(15.0m, result.TotalCommission); // 300 * 0.05
    }

    [Fact]
    public async Task GetChannelPerformanceAsync_NonExistingChannel_ThrowsException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(
            () => _channelService.GetChannelPerformanceAsync(999));
    }

    [Fact]
    public async Task UpdateChannelCommissionRateAsync_ExistingChannel_UpdatesRate()
    {
        // Act
        var result = await _channelService.UpdateChannelCommissionRateAsync(1, 0.08m);

        // Assert
        Assert.True(result);
        var channel = await _context.Channels.FindAsync(1);
        Assert.NotNull(channel);
        Assert.Equal(0.08m, channel.CommissionRate);
    }

    [Fact]
    public async Task CalculateChannelCommissionAsync_ExistingChannel_ReturnsCorrectCommission()
    {
        // Act
        var result = await _channelService.CalculateChannelCommissionAsync(1);

        // Assert
        Assert.Equal(15.0m, result); // 300 * 0.05
    }

    [Fact]
    public async Task GenerateApiKeyAsync_ExistingChannel_GeneratesApiKey()
    {
        // Arrange - Clear existing API key
        var channel = await _context.Channels.FindAsync(2);
        channel.ApiKey = null;
        channel.ApiSecret = null;
        await _context.SaveChangesAsync();

        // Act
        var result = await _channelService.GenerateApiKeyAsync(2);

        // Assert
        Assert.True(result);
        var updatedChannel = await _context.Channels.FindAsync(2);
        Assert.NotNull(updatedChannel);
        Assert.NotNull(updatedChannel.ApiKey);
        Assert.NotNull(updatedChannel.ApiSecret);
    }

    [Fact]
    public async Task ValidateApiKeyAsync_ValidKey_ReturnsTrue()
    {
        // Act
        var result = await _channelService.ValidateApiKeyAsync("test-api-key-1");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ValidateApiKeyAsync_InvalidKey_ReturnsFalse()
    {
        // Act
        var result = await _channelService.ValidateApiKeyAsync("invalid-key");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task GetChannelByApiKeyAsync_ValidKey_ReturnsChannel()
    {
        // Act
        var result = await _channelService.GetChannelByApiKeyAsync("test-api-key-1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.Id);
        Assert.Equal("Test Channel 1", result.Name);
    }

    [Fact]
    public async Task GetChannelByApiKeyAsync_InvalidKey_ReturnsNull()
    {
        // Act
        var result = await _channelService.GetChannelByApiKeyAsync("invalid-key");

        // Assert
        Assert.Null(result);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
