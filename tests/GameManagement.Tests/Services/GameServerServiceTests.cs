using GameManagement.Core.Entities;
using GameManagement.Infrastructure.Data;
using GameManagement.Infrastructure.Services;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace GameManagement.Tests.Services;

public class GameServerServiceTests : IDisposable
{
    private readonly GameManagementDbContext _context;
    private readonly GameServerService _service;
    private readonly Mock<ILogger<GameServerService>> _mockLogger;

    public GameServerServiceTests()
    {
        var options = new DbContextOptionsBuilder<GameManagementDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new GameManagementDbContext(options);
        _mockLogger = new Mock<ILogger<GameServerService>>();
        _service = new GameServerService(_context, _mockLogger.Object);

        SeedTestData();
    }

    private void SeedTestData()
    {
        var servers = new List<GameServer>
        {
            new GameServer
            {
                Id = 1,
                Name = "Test Server 1",
                Host = "127.0.0.1",
                Port = 8080,
                Status = ServerStatus.Online,
                Version = "1.0.0",
                MaxPlayers = 1000,
                CurrentPlayers = 500,
                Region = "US-East",
                CreatedAt = DateTime.UtcNow.AddDays(-30),
                LastHeartbeat = DateTime.UtcNow.AddMinutes(-1)
            },
            new GameServer
            {
                Id = 2,
                Name = "Test Server 2",
                Host = "*********",
                Port = 8081,
                Status = ServerStatus.Offline,
                Version = "1.0.1",
                MaxPlayers = 2000,
                CurrentPlayers = 0,
                Region = "EU-West",
                CreatedAt = DateTime.UtcNow.AddDays(-20),
                LastHeartbeat = DateTime.UtcNow.AddHours(-2)
            }
        };

        var players = new List<Player>
        {
            new Player
            {
                Id = 1,
                AccountId = "ACC001",
                Nickname = "TestPlayer1",
                ServerId = 1,
                Level = 50,
                Experience = 125000,
                TotalPlayTime = TimeSpan.FromHours(100),
                LastLoginAt = DateTime.UtcNow.AddHours(-1),
                CreatedAt = DateTime.UtcNow.AddDays(-10)
            },
            new Player
            {
                Id = 2,
                AccountId = "ACC002",
                Nickname = "TestPlayer2",
                ServerId = 1,
                Level = 25,
                Experience = 50000,
                TotalPlayTime = TimeSpan.FromHours(50),
                LastLoginAt = DateTime.UtcNow.AddDays(-1),
                CreatedAt = DateTime.UtcNow.AddDays(-5)
            }
        };

        var paymentRecords = new List<PaymentRecord>
        {
            new PaymentRecord
            {
                Id = 1,
                PlayerId = 1,
                OrderId = "ORDER001",
                Amount = 9.99m,
                Currency = "USD",
                Status = PaymentStatus.Completed,
                PaymentMethod = "CreditCard",
                CompletedAt = DateTime.UtcNow.AddDays(-1),
                CreatedAt = DateTime.UtcNow.AddDays(-1)
            },
            new PaymentRecord
            {
                Id = 2,
                PlayerId = 2,
                OrderId = "ORDER002",
                Amount = 19.99m,
                Currency = "USD",
                Status = PaymentStatus.Completed,
                PaymentMethod = "PayPal",
                CompletedAt = DateTime.UtcNow.AddHours(-2),
                CreatedAt = DateTime.UtcNow.AddHours(-2)
            }
        };

        _context.GameServers.AddRange(servers);
        _context.Players.AddRange(players);
        _context.PaymentRecords.AddRange(paymentRecords);
        _context.SaveChanges();
    }

    [Fact]
    public async Task GetAllServersAsync_ShouldReturnAllServers()
    {
        // Act
        var result = await _service.GetAllServersAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());
        
        var serverList = result.ToList();
        Assert.Contains(serverList, s => s.Name == "Test Server 1");
        Assert.Contains(serverList, s => s.Name == "Test Server 2");
    }

    [Fact]
    public async Task GetServerByIdAsync_WithValidId_ShouldReturnServer()
    {
        // Act
        var result = await _service.GetServerByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Server 1", result.Name);
        Assert.Equal("127.0.0.1", result.Host);
        Assert.Equal(8080, result.Port);
        Assert.Equal(ServerStatus.Online, result.Status);
    }

    [Fact]
    public async Task GetServerByIdAsync_WithInvalidId_ShouldReturnNull()
    {
        // Act
        var result = await _service.GetServerByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetServerByNameAsync_WithValidName_ShouldReturnServer()
    {
        // Act
        var result = await _service.GetServerByNameAsync("Test Server 1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.Id);
        Assert.Equal("Test Server 1", result.Name);
    }

    [Fact]
    public async Task CreateServerAsync_WithValidData_ShouldCreateServer()
    {
        // Arrange
        var createDto = new CreateServerDto
        {
            Name = "New Test Server",
            Host = "*************",
            Port = 9000,
            Status = ServerStatus.Online,
            Version = "2.0.0",
            MaxPlayers = 1500,
            Region = "Asia-Pacific"
        };

        // Act
        var result = await _service.CreateServerAsync(createDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("New Test Server", result.Name);
        Assert.Equal("*************", result.Host);
        Assert.Equal(9000, result.Port);
        Assert.Equal(ServerStatus.Online, result.Status);
        Assert.Equal("2.0.0", result.Version);
        Assert.Equal(1500, result.MaxPlayers);
        Assert.Equal(0, result.CurrentPlayers);
        Assert.Equal("Asia-Pacific", result.Region);

        // Verify it was saved to database
        var savedServer = await _context.GameServers.FindAsync(result.Id);
        Assert.NotNull(savedServer);
        Assert.Equal("New Test Server", savedServer.Name);
    }

    [Fact]
    public async Task UpdateServerAsync_WithValidData_ShouldUpdateServer()
    {
        // Arrange
        var updateDto = new UpdateServerDto
        {
            Name = "Updated Server Name",
            MaxPlayers = 3000,
            CurrentPlayers = 1500
        };

        // Act
        var result = await _service.UpdateServerAsync(1, updateDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Server Name", result.Name);
        Assert.Equal(3000, result.MaxPlayers);
        Assert.Equal(1500, result.CurrentPlayers);

        // Verify it was updated in database
        var updatedServer = await _context.GameServers.FindAsync(1);
        Assert.NotNull(updatedServer);
        Assert.Equal("Updated Server Name", updatedServer.Name);
        Assert.Equal(3000, updatedServer.MaxPlayers);
        Assert.Equal(1500, updatedServer.CurrentPlayers);
    }

    [Fact]
    public async Task UpdateServerAsync_WithInvalidId_ShouldReturnNull()
    {
        // Arrange
        var updateDto = new UpdateServerDto
        {
            Name = "Updated Server Name"
        };

        // Act
        var result = await _service.UpdateServerAsync(999, updateDto);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateServerStatusAsync_WithValidId_ShouldUpdateStatus()
    {
        // Act
        var result = await _service.UpdateServerStatusAsync(1, ServerStatus.Maintenance);

        // Assert
        Assert.True(result);

        // Verify it was updated in database
        var updatedServer = await _context.GameServers.FindAsync(1);
        Assert.NotNull(updatedServer);
        Assert.Equal(ServerStatus.Maintenance, updatedServer.Status);
    }

    [Fact]
    public async Task GetServersByStatusAsync_ShouldReturnServersWithSpecificStatus()
    {
        // Act
        var result = await _service.GetServersByStatusAsync(ServerStatus.Online);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Test Server 1", result.First().Name);
    }

    [Fact]
    public async Task GetOnlineServersAsync_ShouldReturnOnlineServers()
    {
        // Act
        var result = await _service.GetOnlineServersAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(ServerStatus.Online, result.First().Status);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
