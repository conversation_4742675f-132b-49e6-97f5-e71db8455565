using GameManagement.Core.Entities;
using GameManagement.Infrastructure.Data;
using GameManagement.Infrastructure.Services;
using GameManagement.Shared.DTOs;
using GameManagement.Shared.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace GameManagement.Tests.Services;

public class ItemServiceTests : IDisposable
{
    private readonly GameManagementDbContext _context;
    private readonly ItemService _itemService;
    private readonly Mock<ILogger<ItemService>> _mockLogger;

    public ItemServiceTests()
    {
        var options = new DbContextOptionsBuilder<GameManagementDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new GameManagementDbContext(options);
        _mockLogger = new Mock<ILogger<ItemService>>();
        _itemService = new ItemService(_context, _mockLogger.Object);

        SeedTestData();
    }

    private void SeedTestData()
    {
        var items = new List<GameItem>
        {
            new GameItem
            {
                Id = 1,
                ItemId = "SWORD_001",
                Name = "铁剑",
                Description = "基础铁制长剑",
                Type = ItemType.Weapon,
                Rarity = ItemRarity.Common,
                Level = 1,
                Price = 100.00m,
                Properties = "{\"attack\": 10, \"durability\": 100}",
                IsActive = true,
                IconUrl = "/icons/sword_001.png",
                MaxStack = 1,
                IsTradeable = true,
                CreatedAt = DateTime.UtcNow.AddDays(-10),
                UpdatedAt = DateTime.UtcNow.AddDays(-10)
            },
            new GameItem
            {
                Id = 2,
                ItemId = "POTION_001",
                Name = "生命药水",
                Description = "恢复100点生命值",
                Type = ItemType.Consumable,
                Rarity = ItemRarity.Common,
                Level = 1,
                Price = 50.00m,
                Properties = "{\"heal\": 100}",
                IsActive = true,
                IconUrl = "/icons/potion_001.png",
                MaxStack = 99,
                IsTradeable = true,
                CreatedAt = DateTime.UtcNow.AddDays(-5),
                UpdatedAt = DateTime.UtcNow.AddDays(-5)
            },
            new GameItem
            {
                Id = 3,
                ItemId = "ARMOR_001",
                Name = "皮甲",
                Description = "基础皮制护甲",
                Type = ItemType.Armor,
                Rarity = ItemRarity.Uncommon,
                Level = 5,
                Price = 200.00m,
                Properties = "{\"defense\": 15}",
                IsActive = false,
                IconUrl = "/icons/armor_001.png",
                MaxStack = 1,
                IsTradeable = false,
                CreatedAt = DateTime.UtcNow.AddDays(-3),
                UpdatedAt = DateTime.UtcNow.AddDays(-1)
            }
        };

        _context.GameItems.AddRange(items);
        _context.SaveChanges();
    }

    [Fact]
    public async Task GetItemByIdAsync_ExistingItem_ReturnsItem()
    {
        // Act
        var result = await _itemService.GetItemByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.Id);
        Assert.Equal("SWORD_001", result.ItemId);
        Assert.Equal("铁剑", result.Name);
    }

    [Fact]
    public async Task GetItemByIdAsync_NonExistingItem_ReturnsNull()
    {
        // Act
        var result = await _itemService.GetItemByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetItemByItemIdAsync_ExistingItem_ReturnsItem()
    {
        // Act
        var result = await _itemService.GetItemByItemIdAsync("SWORD_001");

        // Assert
        Assert.NotNull(result);
        Assert.Equal("SWORD_001", result.ItemId);
        Assert.Equal("铁剑", result.Name);
    }

    [Fact]
    public async Task GetItemsAsync_ReturnsPagedItems()
    {
        // Act
        var result = await _itemService.GetItemsAsync(1, 2);

        // Assert
        Assert.Equal(2, result.Count());
    }

    [Fact]
    public async Task SearchItemsAsync_WithValidTerm_ReturnsMatchingItems()
    {
        // Act
        var result = await _itemService.SearchItemsAsync("剑");

        // Assert
        Assert.Single(result);
        Assert.Equal("铁剑", result.First().Name);
    }

    [Fact]
    public async Task CreateItemAsync_ValidItem_CreatesSuccessfully()
    {
        // Arrange
        var createDto = new CreateGameItemDto
        {
            ItemId = "SHIELD_001",
            Name = "木盾",
            Description = "基础木制盾牌",
            Type = ItemType.Armor,
            Rarity = ItemRarity.Common,
            Level = 1,
            Price = 80.00m,
            Properties = "{\"defense\": 5}",
            IsActive = true,
            MaxStack = 1,
            IsTradeable = true
        };

        // Act
        var result = await _itemService.CreateItemAsync(createDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("SHIELD_001", result.ItemId);
        Assert.Equal("木盾", result.Name);
        Assert.True(result.IsActive);
    }

    [Fact]
    public async Task CreateItemAsync_DuplicateItemId_ThrowsException()
    {
        // Arrange
        var createDto = new CreateGameItemDto
        {
            ItemId = "SWORD_001", // Already exists
            Name = "另一把剑",
            Type = ItemType.Weapon,
            Rarity = ItemRarity.Common
        };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _itemService.CreateItemAsync(createDto));
    }

    [Fact]
    public async Task UpdateItemAsync_ExistingItem_UpdatesSuccessfully()
    {
        // Arrange
        var updateDto = new UpdateGameItemDto
        {
            Name = "更新的铁剑",
            Price = 150.00m,
            IsActive = false
        };

        // Act
        var result = await _itemService.UpdateItemAsync(1, updateDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("更新的铁剑", result.Name);
        Assert.Equal(150.00m, result.Price);
        Assert.False(result.IsActive);
    }

    [Fact]
    public async Task DeleteItemAsync_ExistingItem_DeletesSuccessfully()
    {
        // Act
        var result = await _itemService.DeleteItemAsync(1);

        // Assert
        Assert.True(result);
        
        var deletedItem = await _itemService.GetItemByIdAsync(1);
        Assert.Null(deletedItem);
    }

    [Fact]
    public async Task ActivateItemAsync_ExistingItem_ActivatesSuccessfully()
    {
        // Act
        var result = await _itemService.ActivateItemAsync(3); // Item 3 is inactive

        // Assert
        Assert.True(result);
        
        var activatedItem = await _itemService.GetItemByIdAsync(3);
        Assert.True(activatedItem!.IsActive);
    }

    [Fact]
    public async Task DeactivateItemAsync_ExistingItem_DeactivatesSuccessfully()
    {
        // Act
        var result = await _itemService.DeactivateItemAsync(1); // Item 1 is active

        // Assert
        Assert.True(result);
        
        var deactivatedItem = await _itemService.GetItemByIdAsync(1);
        Assert.False(deactivatedItem!.IsActive);
    }

    [Fact]
    public async Task GetItemsByTypeAsync_ReturnsCorrectItems()
    {
        // Act
        var result = await _itemService.GetItemsByTypeAsync(ItemType.Weapon);

        // Assert
        Assert.Single(result);
        Assert.Equal("铁剑", result.First().Name);
    }

    [Fact]
    public async Task GetItemsByRarityAsync_ReturnsCorrectItems()
    {
        // Act
        var result = await _itemService.GetItemsByRarityAsync(ItemRarity.Common);

        // Assert
        Assert.Equal(2, result.Count());
    }

    [Fact]
    public async Task GetActiveItemsAsync_ReturnsOnlyActiveItems()
    {
        // Act
        var result = await _itemService.GetActiveItemsAsync();

        // Assert
        Assert.Equal(2, result.Count()); // Only items 1 and 2 are active
        Assert.All(result, item => Assert.True(item.IsActive));
    }

    [Fact]
    public async Task GetTradeableItemsAsync_ReturnsOnlyTradeableItems()
    {
        // Act
        var result = await _itemService.GetTradeableItemsAsync();

        // Assert
        Assert.Equal(2, result.Count()); // Only items 1 and 2 are tradeable and active
        Assert.All(result, item => Assert.True(item.IsTradeable));
    }

    [Fact]
    public async Task UpdateItemPriceAsync_ExistingItem_UpdatesPrice()
    {
        // Act
        var result = await _itemService.UpdateItemPriceAsync(1, 120.00m);

        // Assert
        Assert.True(result);
        
        var updatedItem = await _itemService.GetItemByIdAsync(1);
        Assert.Equal(120.00m, updatedItem!.Price);
    }

    [Fact]
    public async Task GetItemStatsAsync_ReturnsCorrectStats()
    {
        // Act
        var result = await _itemService.GetItemStatsAsync();

        // Assert
        Assert.Equal(3, result.TotalItems);
        Assert.Equal(2, result.ActiveItems);
        Assert.Equal(1, result.InactiveItems);
        Assert.Equal(2, result.TradeableItems);
        Assert.Equal(1, result.NonTradeableItems);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
