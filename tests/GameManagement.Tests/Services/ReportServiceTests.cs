using GameManagement.Core.Entities;
using GameManagement.Infrastructure.Data;
using GameManagement.Infrastructure.Services;
using GameManagement.Shared.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace GameManagement.Tests.Services;

public class ReportServiceTests : IDisposable
{
    private readonly GameManagementDbContext _context;
    private readonly ReportService _reportService;
    private readonly Mock<ILogger<ReportService>> _mockLogger;

    public ReportServiceTests()
    {
        var options = new DbContextOptionsBuilder<GameManagementDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new GameManagementDbContext(options);
        _mockLogger = new Mock<ILogger<ReportService>>();
        _reportService = new ReportService(_context, _mockLogger.Object);

        SeedTestData();
    }

    private void SeedTestData()
    {
        // Add test servers
        var server1 = new GameServer
        {
            Id = 1,
            Name = "Test Server 1",
            Status = ServerStatus.Online,
            CreatedAt = DateTime.UtcNow.AddDays(-30)
        };

        var server2 = new GameServer
        {
            Id = 2,
            Name = "Test Server 2",
            Status = ServerStatus.Online,
            CreatedAt = DateTime.UtcNow.AddDays(-30)
        };

        _context.GameServers.AddRange(server1, server2);

        // Add test players
        var players = new List<Player>
        {
            new Player
            {
                Id = 1,
                AccountId = "player1",
                Nickname = "Player 1",
                Level = 25,
                ServerId = 1,
                LastLoginAt = DateTime.UtcNow.AddHours(-2),
                CreatedAt = DateTime.UtcNow.AddDays(-7)
            },
            new Player
            {
                Id = 2,
                AccountId = "player2",
                Nickname = "Player 2",
                Level = 45,
                ServerId = 1,
                LastLoginAt = DateTime.UtcNow.AddDays(-1),
                CreatedAt = DateTime.UtcNow.AddDays(-14)
            },
            new Player
            {
                Id = 3,
                AccountId = "player3",
                Nickname = "Player 3",
                Level = 15,
                ServerId = 2,
                LastLoginAt = DateTime.UtcNow.AddDays(-3),
                CreatedAt = DateTime.UtcNow.AddDays(-21)
            }
        };

        _context.Players.AddRange(players);

        // Add test payment records
        var payments = new List<PaymentRecord>
        {
            new PaymentRecord
            {
                Id = 1,
                PlayerId = 1,
                Amount = 9.99m,
                Status = PaymentStatus.Completed,
                PaymentMethod = "Credit Card",
                CompletedAt = DateTime.UtcNow.AddDays(-1),
                CreatedAt = DateTime.UtcNow.AddDays(-1)
            },
            new PaymentRecord
            {
                Id = 2,
                PlayerId = 2,
                Amount = 19.99m,
                Status = PaymentStatus.Completed,
                PaymentMethod = "PayPal",
                CompletedAt = DateTime.UtcNow.AddDays(-2),
                CreatedAt = DateTime.UtcNow.AddDays(-2)
            },
            new PaymentRecord
            {
                Id = 3,
                PlayerId = 1,
                Amount = 4.99m,
                Status = PaymentStatus.Pending,
                PaymentMethod = "Credit Card",
                CreatedAt = DateTime.UtcNow.AddHours(-1)
            }
        };

        _context.PaymentRecords.AddRange(payments);

        // Add test activities
        var activities = new List<GameActivity>
        {
            new GameActivity
            {
                Id = 1,
                Name = "Daily Login Bonus",
                StartTime = DateTime.UtcNow.AddDays(-30),
                EndTime = DateTime.UtcNow.AddDays(30),
                CreatedAt = DateTime.UtcNow.AddDays(-30)
            },
            new GameActivity
            {
                Id = 2,
                Name = "Weekend Event",
                StartTime = DateTime.UtcNow.AddDays(-2),
                EndTime = DateTime.UtcNow.AddDays(2),
                CreatedAt = DateTime.UtcNow.AddDays(-5)
            }
        };

        _context.GameActivities.AddRange(activities);

        _context.SaveChanges();
    }

    [Fact]
    public async Task GetDashboardStatsAsync_ShouldReturnCorrectStats()
    {
        // Act
        var result = await _reportService.GetDashboardStatsAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.TotalPlayers);
        Assert.Equal(2, result.OnlineServers);
        Assert.Equal(2, result.ActiveActivities);
        Assert.True(result.TodayRevenue >= 0);
        Assert.True(result.MonthRevenue >= 0);
        Assert.NotNull(result.RevenueChart);
        Assert.NotNull(result.PlayerChart);
    }

    [Fact]
    public async Task GetRevenueReportAsync_ShouldReturnCorrectRevenue()
    {
        // Arrange
        var startDate = DateTime.UtcNow.AddDays(-7).Date;
        var endDate = DateTime.UtcNow.Date;

        // Act
        var result = await _reportService.GetRevenueReportAsync(startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(startDate, result.StartDate);
        Assert.Equal(endDate, result.EndDate);
        Assert.Equal(29.98m, result.TotalRevenue); // Only completed payments
        Assert.Equal(2, result.TotalTransactions);
        Assert.Equal(14.99m, result.AverageTransactionValue);
        Assert.NotNull(result.RevenueByMethod);
        Assert.NotNull(result.RevenueByServer);
        Assert.NotNull(result.DailyRevenue);
    }

    [Fact]
    public async Task GetPlayerReportAsync_ShouldReturnCorrectPlayerStats()
    {
        // Arrange
        var startDate = DateTime.UtcNow.AddDays(-30).Date;
        var endDate = DateTime.UtcNow.Date;

        // Act
        var result = await _reportService.GetPlayerReportAsync(startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(startDate, result.StartDate);
        Assert.Equal(endDate, result.EndDate);
        Assert.True(result.NewPlayers >= 0); // Players created in the period (may be 0 due to date filtering)
        Assert.True(result.ActivePlayers >= 0);
        Assert.NotNull(result.PlayersByLevel);
        Assert.NotNull(result.PlayersByServer);
        Assert.NotNull(result.DailyPlayers);
    }

    [Fact]
    public async Task GetRetentionReportAsync_ShouldReturnRetentionData()
    {
        // Arrange
        var startDate = DateTime.UtcNow.AddDays(-30).Date;
        var endDate = DateTime.UtcNow.AddDays(-20).Date;

        // Act
        var result = await _reportService.GetRetentionReportAsync(startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(startDate, result.StartDate);
        Assert.Equal(endDate, result.EndDate);
        Assert.True(result.Day1Retention >= 0);
        Assert.True(result.Day7Retention >= 0);
        Assert.True(result.Day30Retention >= 0);
        Assert.NotNull(result.RetentionByLevel);
        Assert.NotNull(result.RetentionByServer);
    }

    [Fact]
    public async Task ExportReportAsync_JsonFormat_ShouldReturnValidJson()
    {
        // Arrange
        var startDate = DateTime.UtcNow.AddDays(-7).Date;
        var endDate = DateTime.UtcNow.Date;

        // Act
        var result = await _reportService.ExportReportAsync("revenue", startDate, endDate, "json");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Length > 0);
        
        // Verify it's valid JSON
        var jsonString = System.Text.Encoding.UTF8.GetString(result);
        Assert.Contains("totalRevenue", jsonString);
    }

    [Fact]
    public async Task ExportReportAsync_CsvFormat_ShouldReturnValidCsv()
    {
        // Arrange
        var startDate = DateTime.UtcNow.AddDays(-7).Date;
        var endDate = DateTime.UtcNow.Date;

        // Act
        var result = await _reportService.ExportReportAsync("revenue", startDate, endDate, "csv");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Length > 0);
        
        // Verify it's CSV format
        var csvString = System.Text.Encoding.UTF8.GetString(result);
        Assert.Contains("Date,Revenue", csvString);
    }

    [Fact]
    public async Task ExportReportAsync_InvalidReportType_ShouldThrowException()
    {
        // Arrange
        var startDate = DateTime.UtcNow.AddDays(-7).Date;
        var endDate = DateTime.UtcNow.Date;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => 
            _reportService.ExportReportAsync("invalid", startDate, endDate, "json"));
    }

    [Fact]
    public async Task ExportReportAsync_InvalidFormat_ShouldThrowException()
    {
        // Arrange
        var startDate = DateTime.UtcNow.AddDays(-7).Date;
        var endDate = DateTime.UtcNow.Date;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => 
            _reportService.ExportReportAsync("revenue", startDate, endDate, "invalid"));
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
